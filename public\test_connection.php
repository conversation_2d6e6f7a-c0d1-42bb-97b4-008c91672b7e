<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el sistema de logging
require_once 'config/logger.php';

echo "<h1>Prueba de Conexión a la Base de Datos</h1>";

try {
    // Registrar inicio de la prueba
    logInfo("Iniciando prueba de conexión a la base de datos");

    // Incluir la configuración base
    require_once 'config/config.php';

    echo "<p style='color: green; font-weight: bold;'>✓ Conexión a la base de datos establecida correctamente.</p>";
    echo "<p>Información de la conexión:</p>";
    echo "<ul>";
    echo "<li>Host: " . $conn->host_info . "</li>";
    echo "<li>Servidor: " . $conn->server_info . "</li>";
    echo "<li>Charset: " . $conn->character_set_name() . "</li>";
    echo "</ul>";

    // Verificar si la tabla existe
    $tableCheckQuery = "SHOW TABLES LIKE 'tb_registros'";
    $tableCheckResult = $conn->query($tableCheckQuery);

    if ($tableCheckResult && $tableCheckResult->num_rows > 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ La tabla 'tb_registros' existe.</p>";

        // Mostrar estructura de la tabla
        $structureQuery = "DESCRIBE tb_registros";
        $structureResult = $conn->query($structureQuery);

        if ($structureResult && $structureResult->num_rows > 0) {
            echo "<h2>Estructura de la tabla 'tb_registros':</h2>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";

            while ($row = $structureResult->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }

            echo "</table>";
        } else {
            echo "<p style='color: red;'>Error al obtener la estructura de la tabla: " . $conn->error . "</p>";
        }

        // Mostrar los primeros 10 registros
        $dataQuery = "SELECT * FROM tb_registros LIMIT 10";
        $dataResult = $conn->query($dataQuery);

        if ($dataResult && $dataResult->num_rows > 0) {
            echo "<h2>Primeros 10 registros:</h2>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";

            // Encabezados de la tabla
            echo "<tr>";
            while ($fieldInfo = $dataResult->fetch_field()) {
                echo "<th>" . $fieldInfo->name . "</th>";
            }
            echo "</tr>";

            // Datos
            while ($row = $dataResult->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }

            echo "</table>";
        } else {
            echo "<p>No hay registros en la tabla o error al consultar: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ La tabla 'tb_registros' no existe.</p>";

        // Mostrar código SQL para crear la tabla
        echo "<h2>SQL para crear la tabla:</h2>";
        echo "<pre style='background-color: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        echo "CREATE TABLE IF NOT EXISTS tb_registros (
    id INT NOT NULL AUTO_INCREMENT,
    nombres VARCHAR(100) NOT NULL,
    apellidos VARCHAR(100) NOT NULL,
    rut VARCHAR(10) NOT NULL,
    fechaNacimiento VARCHAR(10) NOT NULL,
    sexo VARCHAR(20) NOT NULL,
    telefono VARCHAR(10) NOT NULL,
    region VARCHAR(50) NOT NULL,
    comuna VARCHAR(50) NOT NULL,
    direccion VARCHAR(150) NOT NULL,
    fecha_registro TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
        echo "</pre>";

        // Botón para crear la tabla
        echo "<form method='post'>";
        echo "<input type='hidden' name='create_table' value='1'>";
        echo "<button type='submit' style='padding: 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer;'>Crear tabla</button>";
        echo "</form>";

        // Procesar la creación de la tabla si se envió el formulario
        if (isset($_POST['create_table'])) {
            $createTableSQL = "CREATE TABLE IF NOT EXISTS tb_registros (
                id INT NOT NULL AUTO_INCREMENT,
                nombres VARCHAR(100) NOT NULL,
                apellidos VARCHAR(100) NOT NULL,
                rut VARCHAR(10) NOT NULL,
                fechaNacimiento VARCHAR(10) NOT NULL,
                sexo VARCHAR(20) NOT NULL,
                telefono VARCHAR(10) NOT NULL,
                region VARCHAR(50) NOT NULL,
                comuna VARCHAR(50) NOT NULL,
                direccion VARCHAR(150) NOT NULL,
                fecha_registro TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            if ($conn->query($createTableSQL)) {
                echo "<p style='color: green; font-weight: bold;'>✓ Tabla 'tb_registros' creada correctamente.</p>";
                echo "<p>Recargue la página para ver la estructura de la tabla.</p>";
            } else {
                echo "<p style='color: red;'>Error al crear la tabla: " . $conn->error . "</p>";
            }
        }
    }

    // Cerrar la conexión
    $conn->close();

} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>

<p><a href="register.php" style="display: inline-block; margin-top: 20px; padding: 10px; background-color: #8e44ad; color: white; text-decoration: none; border-radius: 4px;">Volver al formulario de registro</a></p>
