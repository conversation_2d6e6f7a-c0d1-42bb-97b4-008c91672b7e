/**
 * Script para manejar los dropdowns de notificaciones y mensajes
 */
document.addEventListener('DOMContentLoaded', function() {
    // Referencias a los elementos del DOM
    const notificationsBtn = document.getElementById('notificationsBtn');
    const messagesBtn = document.getElementById('messagesBtn');
    const notificationsDropdown = document.getElementById('notificationsDropdown');
    const messagesDropdown = document.getElementById('messagesDropdown');
    
    // Función para mostrar/ocultar un dropdown
    function toggleDropdown(dropdown) {
        // Cerrar todos los dropdowns primero
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            if (menu !== dropdown) {
                menu.classList.remove('show');
            }
        });
        
        // Alternar el dropdown actual
        dropdown.classList.toggle('show');
    }
    
    // Evento para el botón de notificaciones
    if (notificationsBtn && notificationsDropdown) {
        notificationsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown(notificationsDropdown);
        });
    }
    
    // Evento para el botón de mensajes
    if (messagesBtn && messagesDropdown) {
        messagesBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown(messagesDropdown);
        });
    }
    
    // Cerrar dropdowns al hacer clic fuera de ellos
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-container')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
    
    // Evitar que los clics dentro del dropdown lo cierren
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        menu.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });
    
    // Manejar los botones de cerrar notificación
    document.querySelectorAll('.notification-close').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const notificationItem = this.closest('.notification-item');
            if (notificationItem) {
                // Animación de desvanecimiento
                notificationItem.style.opacity = '0';
                notificationItem.style.height = notificationItem.offsetHeight + 'px';
                notificationItem.style.transition = 'opacity 0.3s, height 0.3s 0.3s';
                
                setTimeout(() => {
                    notificationItem.style.height = '0';
                    notificationItem.style.padding = '0';
                    notificationItem.style.margin = '0';
                    notificationItem.style.overflow = 'hidden';
                    
                    // Actualizar contador de no leídos
                    if (notificationItem.classList.contains('unread')) {
                        const unreadCount = document.querySelector('.notifications-dropdown .unread-count');
                        if (unreadCount) {
                            const currentCount = parseInt(unreadCount.textContent);
                            if (currentCount > 0) {
                                unreadCount.textContent = (currentCount - 1) + ' Sin leer';
                            }
                        }
                        
                        // Actualizar indicador en el botón
                        const indicator = document.querySelector('#notificationsBtn .notification-indicator');
                        if (indicator) {
                            const currentCount = parseInt(indicator.textContent);
                            if (currentCount > 0) {
                                indicator.textContent = currentCount - 1;
                                if (currentCount - 1 === 0) {
                                    indicator.style.display = 'none';
                                }
                            }
                        }
                    }
                    
                    // Eliminar después de la animación
                    setTimeout(() => {
                        notificationItem.remove();
                    }, 300);
                }, 300);
            }
        });
    });
    
    // Manejar el botón "Ver Todas"
    const viewAllBtns = document.querySelectorAll('.view-all-btn');
    viewAllBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Aquí se podría redirigir a una página con todas las notificaciones/mensajes
            alert('Redirigiendo a la página de todas las notificaciones/mensajes');
        });
    });
});
