<?php
require_once '../config/session_config.php';

function runSessionTest($description, $test) {
    echo "<div class='test-case'>";
    echo "<h3>$description</h3>";
    try {
        $result = $test();
        echo "<div class='result " . ($result ? "success" : "failure") . "'>";
        echo $result ? "✓ ÉXITO" : "✗ FALLO";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='result error'>";
        echo "⚠ ERROR: " . $e->getMessage();
        echo "</div>";
    }
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> de Sesión</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #e6ffe6;
            color: #006600;
        }
        .failure {
            background: #ffe6e6;
            color: #660000;
        }
        .error {
            background: #fff3e6;
            color: #663300;
        }
        .session-info {
            background: #f5f5f5;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>Pruebas Detalladas de Sesión</h1>
    
    <?php
    // Test 1: Inicialización de sesión
    runSessionTest("Inicialización de Sesión", function() {
        $session = SessionManager::getInstance();
        return $session->initializeSession();
    });

    // Test 2: Creación de sesión con datos de usuario
    runSessionTest("Creación de Sesión de Usuario", function() {
        $session = SessionManager::getInstance();
        $userData = [
            'user_id' => 1,
            'username' => 'test_user',
            'role' => 'user'
        ];
        return $session->createSession($userData);
    });

    // Test 3: Validación de sesión
    runSessionTest("Validación de Sesión", function() {
        $session = SessionManager::getInstance();
        return $session->validateSession();
    });

    // Test 4: Verificación de permisos
    runSessionTest("Verificación de Permisos de Usuario", function() {
        $session = SessionManager::getInstance();
        return $session->checkPermission('user');
    });

    // Test 5: Verificación de permisos de admin (debería fallar para usuario normal)
    runSessionTest("Verificación de Permisos de Admin", function() {
        $session = SessionManager::getInstance();
        return !$session->checkPermission('admin');
    });

    // Mostrar información actual de la sesión
    $session = SessionManager::getInstance();
    $sessionInfo = $session->getSessionInfo();
    ?>

    <h2>Estado Actual de la Sesión</h2>
    <div class="session-info">
        <table>
            <tr>
                <th>Propiedad</th>
                <th>Valor</th>
            </tr>
            <?php foreach ($sessionInfo as $key => $value): ?>
            <tr>
                <td><?php echo htmlspecialchars($key); ?></td>
                <td>
                    <?php 
                    if (is_array($value)) {
                        echo "<pre>" . htmlspecialchars(print_r($value, true)) . "</pre>";
                    } else {
                        echo htmlspecialchars($value);
                    }
                    ?>
                </td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <h2>Variables de Entorno y Configuración</h2>
    <div class="session-info">
        <table>
            <tr>
                <th>Variable</th>
                <th>Valor</th>
            </tr>
            <tr>
                <td>session.save_handler</td>
                <td><?php echo ini_get('session.save_handler'); ?></td>
            </tr>
            <tr>
                <td>session.save_path</td>
                <td><?php echo ini_get('session.save_path'); ?></td>
            </tr>
            <tr>
                <td>session.gc_maxlifetime</td>
                <td><?php echo ini_get('session.gc_maxlifetime'); ?></td>
            </tr>
            <tr>
                <td>open_basedir</td>
                <td><?php echo ini_get('open_basedir'); ?></td>
            </tr>
        </table>
    </div>

    <h2>Información del Servidor</h2>
    <div class="session-info">
        <table>
            <tr>
                <th>Variable</th>
                <th>Valor</th>
            </tr>
            <tr>
                <td>PHP Version</td>
                <td><?php echo phpversion(); ?></td>
            </tr>
            <tr>
                <td>Server Software</td>
                <td><?php echo $_SERVER['SERVER_SOFTWARE']; ?></td>
            </tr>
            <tr>
                <td>Document Root</td>
                <td><?php echo $_SERVER['DOCUMENT_ROOT']; ?></td>
            </tr>
            <tr>
                <td>HTTP Cookie</td>
                <td><?php echo isset($_SERVER['HTTP_COOKIE']) ? $_SERVER['HTTP_COOKIE'] : 'No disponible'; ?></td>
            </tr>
        </table>
    </div>
</body>
</html>