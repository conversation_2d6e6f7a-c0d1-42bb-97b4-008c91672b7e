// Importaciones necesarias
import { updateResponsiveView } from '../modules/products/product-list.js';
import { initializeCharts } from '../components/charts.js';
import { closeEditPanel } from '../modules/products/product-edit.js';

/**
 * Muestra una sección específica y oculta las demás.
 * @param {string} sectionId - ID de la sección a mostrar.
 */
export function showSection(sectionId) {
    console.log(`Intentando mostrar sección: ${sectionId}`);
    const sections = document.querySelectorAll('.content-section');
    let sectionFound = false;
    sections.forEach(section => {
        if (section.id === sectionId) {
            section.style.display = 'block'; // O 'flex', 'grid' según el layout
            section.classList.add('active'); // Agregar clase active para CSS
            sectionFound = true;
            console.log(`Sección ${sectionId} mostrada y marcada como activa.`);
        } else {
            section.style.display = 'none';
            section.classList.remove('active'); // Remover clase active
        }
    });
    if (!sectionFound) {
        console.warn(`Sección con ID ${sectionId} no encontrada.`);
    }
}

/**
 * Actualiza el estado activo de los botones de pestañas.
 * @param {string} activeTabId - ID del botón de pestaña a activar.
 */
export function updateActiveTab(activeTabId) {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        if (button.id === activeTabId) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}

// Funciones específicas para mostrar cada sección
export function showProductsSection() {
    showSection('productsSection');
}

export function showEditProductSection() {
    showSection('editProductSection');
}

export function showStoreInfoSection() {
    showSection('storeInfoSection');
}

export function showStatsSection() {
    showSection('statsSection');

    // Intentar inicializar los gráficos si Chart.js está disponible
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    } else {
        console.warn('Chart.js no está disponible para inicializar los gráficos');
    }
}

export function showCategoriesSection() {
    showSection('categoriesSection');
}

// Helper function
function getElementSafe(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`Elemento con ID '${id}' no encontrado en el DOM`);
    }
    return element;
}