/**
 * Estilos para el overlay del sidebar
 * Se muestra cuando el sidebar está expandido en mobile
 */

/* Overlay para el sidebar en pantallas pequeñas */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Cuando el sidebar está expandido en mobile */
@media (max-width: 992px) {
  #sidebar.expanded ~ .sidebar-overlay {
    display: block;
    opacity: 1;
  }
}

/* Asegurar que el z-index sea correcto */
#sidebar {
  z-index: 999;
}

#aside-toggle {
  z-index: 1000;
}
