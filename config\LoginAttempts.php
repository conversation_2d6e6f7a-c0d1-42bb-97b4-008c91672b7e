<?php
namespace Config;

class LoginAttempts {
    private $conn;
    private $max_attempts = 5;
    private $lockout_duration = 300; // 5 minutos en segundos
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->createTableIfNotExists();
    }
    
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS login_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(255) NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            attempts INT DEFAULT 0,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            locked_until TIMESTAMP NULL,
            UNIQUE KEY idx_username_ip (username, ip_address)
        )";
        
        $this->conn->query($sql);
    }
    
    public function recordAttempt($username, $ip) {
        $stmt = $this->conn->prepare("INSERT INTO login_attempts (username, ip_address, attempts) 
                                    VALUES (?, ?, 1) 
                                    ON DUPLICATE KEY UPDATE 
                                    attempts = attempts + 1, 
                                    last_attempt = CURRENT_TIMESTAMP");
        
        $stmt->bind_param("ss", $username, $ip);
        $stmt->execute();
    }
    
    public function checkLockout($username, $ip) {
        $stmt = $this->conn->prepare("SELECT locked_until 
                                    FROM login_attempts 
                                    WHERE username = ? AND ip_address = ?");
        
        $stmt->bind_param("ss", $username, $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if ($row['locked_until'] && strtotime($row['locked_until']) > time()) {
                return $row['locked_until'];
            }
        }
        return null;
    }
    
    public function resetAttempts($username, $ip) {
        $stmt = $this->conn->prepare("UPDATE login_attempts 
                                    SET attempts = 0, 
                                    locked_until = NULL 
                                    WHERE username = ? AND ip_address = ?");
        
        $stmt->bind_param("ss", $username, $ip);
        $stmt->execute();
    }
    
    public function checkAttempts($username, $ip) {
        $stmt = $this->conn->prepare("SELECT attempts, last_attempt 
                                    FROM login_attempts 
                                    WHERE username = ? AND ip_address = ?");
        
        $stmt->bind_param("ss", $username, $ip);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            
            // Verificar si el tiempo de bloqueo ha expirado
            if (strtotime($row['last_attempt']) + $this->lockout_duration < time()) {
                $this->resetAttempts($username, $ip);
                return ['attempts' => 0, 'locked' => false];
            }
            
            return [
                'attempts' => $row['attempts'],
                'locked' => $row['attempts'] >= $this->max_attempts
            ];
        }
        
        return ['attempts' => 0, 'locked' => false];
    }
}
