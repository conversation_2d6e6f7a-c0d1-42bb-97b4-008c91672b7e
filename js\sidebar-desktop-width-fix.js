/**
 * Solución específica para el ancho del sidebar en resoluciones grandes
 * Este script se ejecuta después de final-sidebar-fix.js para asegurar que el sidebar tenga el ancho correcto en desktop
 */

(function() {
    console.log('Aplicando solución para el ancho del sidebar en desktop...');

    // Función para corregir el ancho del sidebar en desktop
    function fixSidebarWidth() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        const isDesktop = window.innerWidth >= 993;

        if (!sidebar || !isDesktop) {
            return;
        }

        console.log('Corrigiendo ancho del sidebar en desktop...');

        // Obtener el estado guardado del sidebar
        const savedState = localStorage.getItem('asideCollapsed');

        if (savedState === 'true') {
            // Sidebar colapsado
            sidebar.classList.add('collapsed');
            sidebar.classList.remove('expanded');
            sidebar.style.width = '70px';
            sidebar.style.minWidth = '70px';
            sidebar.style.maxWidth = '70px';

            if (mainContent) {
                mainContent.classList.add('sidebar-collapsed');
                mainContent.style.marginLeft = '70px';
                mainContent.style.width = 'calc(100% - 70px)';
            }

            // Ocultar elementos del sidebar
            const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
            elementsToHide.forEach(el => {
                el.style.display = 'none';
            });

            // Ajustar alineación de iconos
            const navLinks = sidebar.querySelectorAll('.nav-link, .sidebar-header, .sidebar-user');
            navLinks.forEach(el => {
                el.style.justifyContent = 'center';
                el.style.padding = '15px 0';
                el.style.textAlign = 'center';
            });

            // Asegurar que los iconos sean visibles
            const icons = sidebar.querySelectorAll('.nav-link i, .user-avatar i');
            icons.forEach(el => {
                el.style.marginRight = '0';
                el.style.fontSize = '1.2em';
                el.style.display = 'flex';
                el.style.justifyContent = 'center';
                el.style.width = '100%';
            });
        } else {
            // Sidebar expandido
            sidebar.classList.remove('collapsed');
            sidebar.classList.add('expanded');
            sidebar.style.width = '250px';
            sidebar.style.minWidth = '250px';
            sidebar.style.maxWidth = '250px';

            if (mainContent) {
                mainContent.classList.remove('sidebar-collapsed');
                mainContent.style.marginLeft = '250px';
                mainContent.style.width = 'calc(100% - 250px)';
            }

            // Asegurar que los elementos del sidebar sean visibles
            const elementsToShow = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
            elementsToShow.forEach(el => {
                el.style.display = 'block';
                el.style.opacity = '1';
                el.style.visibility = 'visible';
            });

            // Restaurar alineación de elementos
            const navLinks = sidebar.querySelectorAll('.nav-link');
            navLinks.forEach(el => {
                el.style.justifyContent = 'flex-start';
                el.style.padding = '10px 20px';
                el.style.textAlign = 'left';
            });

            // Restaurar alineación de cabecera y usuario
            const headerUser = sidebar.querySelectorAll('.sidebar-header, .sidebar-user');
            headerUser.forEach(el => {
                el.style.justifyContent = 'flex-start';
                el.style.padding = '15px 20px';
                el.style.textAlign = 'left';
            });

            // Restaurar iconos
            const icons = sidebar.querySelectorAll('.nav-link i');
            icons.forEach(el => {
                el.style.marginRight = '10px';
                el.style.fontSize = '1em';
                el.style.display = 'inline-block';
                el.style.width = 'auto';
            });
        }

        console.log('Ancho del sidebar corregido en desktop');
    }

    // Función para manejar el clic en el botón de toggle
    function setupToggleButton() {
        const toggleBtn = document.getElementById('aside-toggle');
        if (!toggleBtn) return;

        // Clonar el botón para eliminar eventos existentes
        const newToggleBtn = toggleBtn.cloneNode(true);
        if (toggleBtn.parentNode) {
            toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
        }

        // Añadir nuevo evento de clic
        newToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const sidebar = document.getElementById('sidebar');
            if (!sidebar) return;

            const isDesktop = window.innerWidth >= 993;
            if (!isDesktop) return; // Solo manejar en desktop

            const isCollapsed = sidebar.classList.contains('collapsed');

            // Cambiar estado
            if (isCollapsed) {
                // Expandir
                localStorage.setItem('asideCollapsed', 'false');
            } else {
                // Colapsar
                localStorage.setItem('asideCollapsed', 'true');
            }

            // Aplicar cambios
            fixSidebarWidth();
        });
    }

    // Ejecutar las funciones de corrección
    function init() {
        fixSidebarWidth();
        setupToggleButton();
    }

    // Ejecutar cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // También ejecutar después de que todo haya cargado
    window.addEventListener('load', () => {
        setTimeout(init, 500);
    });

    // Ejecutar inmediatamente
    setTimeout(init, 0);

    // Ejecutar cuando cambie el tamaño de la ventana
    window.addEventListener('resize', () => {
        if (window.innerWidth >= 993) {
            fixSidebarWidth();
        }
    });
})();
