/**
 * Estilos para el botón toggle del sidebar en el header
 * Versión mejorada basada en el ejemplo compartido
 */

/* Botón toggle en el header */
.header-toggle-btn {
  position: relative !important;
  width: 40px !important;
  height: 40px !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-right: 15px !important;
  z-index: 1001 !important;
}

.header-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.25) !important;
}

.header-toggle-btn:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3) !important;
}

.header-toggle-btn i {
  font-size: 18px !important;
}

/* Estilo para el icono animado del toggle */
.header-toggle-btn .animated-arrow {
  position: relative !important;
  width: 18px !important;
  height: 18px !important;
}

.header-toggle-btn .animated-arrow span {
  display: block !important;
  position: absolute !important;
  height: 2px !important;
  width: 100% !important;
  background: white !important;
  border-radius: 9px !important;
  opacity: 1 !important;
  left: 0 !important;
  transform: rotate(0deg) !important;
  transition: .25s ease-in-out !important;
}

.header-toggle-btn .animated-arrow span:nth-child(1) {
  top: 0px !important;
}

.header-toggle-btn .animated-arrow span:nth-child(2) {
  top: 8px !important;
}

.header-toggle-btn .animated-arrow span:nth-child(3) {
  top: 16px !important;
}

/* Animación cuando el sidebar está abierto */
#sidebar:not(.collapsed) ~ header .header-toggle-btn .animated-arrow span:nth-child(1),
.header-toggle-btn.active .animated-arrow span:nth-child(1) {
  top: 8px !important;
  transform: rotate(135deg) !important;
}

#sidebar:not(.collapsed) ~ header .header-toggle-btn .animated-arrow span:nth-child(2),
.header-toggle-btn.active .animated-arrow span:nth-child(2) {
  opacity: 0 !important;
  left: -60px !important;
}

#sidebar:not(.collapsed) ~ header .header-toggle-btn .animated-arrow span:nth-child(3),
.header-toggle-btn.active .animated-arrow span:nth-child(3) {
  top: 8px !important;
  transform: rotate(-135deg) !important;
}

/* Asegurar que el botón siempre sea visible */
.header-toggle-btn {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ajustar esquina superior izquierda del sidebar */
#sidebar {
  border-top-left-radius: 0;
}

/* Ocultar el botón toggle original del sidebar */
#aside-toggle {
  display: none !important;
}

/* Ajustar el header para el botón toggle */
.modern-header {
  padding-left: 15px !important;
  display: flex !important;
  align-items: center !important;
  z-index: 1000 !important;
}

/* Asegurar que el botón esté visible en modo responsive */
@media (max-width: 992px) {
  .header-toggle-btn {
    display: flex !important;
  }

  .modern-header {
    padding-left: 15px !important;
  }
}