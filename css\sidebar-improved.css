/**
 * Sidebar mejorado - Solución completa
 * Diseñado para mejorar la apariencia y organización del sidebar
 */

/* ===== ESTILOS BASE DEL SIDEBAR ===== */
#sidebar {
  position: fixed;
  width: 250px;
  height: 100vh;
  background: linear-gradient(135deg, #6a1b9a, #8e24aa);
  color: white;
  z-index: 998;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  top: 0;
  left: 0;
  padding-top: 60px; /* Espacio para el header */
}

/* ===== CABECERA DEL SIDEBAR ===== */
.sidebar-header {
  padding: 20px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.sidebar-logo {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 18px;
  color: white;
  margin-bottom: 4px;
}

.sidebar-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* ===== INFORMACIÓN DEL USUARIO ===== */
.sidebar-user {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.user-avatar i {
  font-size: 20px;
  color: white;
}

.user-info {
  overflow: hidden;
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== NAVEGACIÓN PRINCIPAL ===== */
.sidebar-nav {
  padding: 0;
}

.nav-section {
  margin-bottom: 10px;
}

.nav-section-title {
  padding: 10px 20px;
  font-size: 12px;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 0.5px;
  font-weight: 600;
  text-align: left;
  margin: 0;
}

.nav-items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all 0.2s ease;
  text-align: left;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-left-color: #f3d516;
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border-left-color: #f3d516;
}

.nav-link i {
  width: 20px;
  margin-right: 10px;
  font-size: 16px;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
}

/* ===== BOTÓN TOGGLE DEL SIDEBAR ===== */
/* Eliminado el botón toggle duplicado - ahora solo se usa el del header */

/* ===== RESPONSIVE ===== */
@media screen and (min-width: 993px) {
  /* Sidebar colapsado */
  #sidebar.collapsed {
    width: 70px;
  }

  /* Contenido principal con sidebar expandido */
  .main-content {
    margin-left: 250px;
    width: calc(100% - 250px);
    transition: all 0.3s ease;
  }

  /* Contenido principal con sidebar colapsado */
  #sidebar.collapsed + .main-content,
  #sidebar.collapsed ~ .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
  }

  /* Elementos a ocultar en modo colapsado */
  #sidebar.collapsed .sidebar-logo,
  #sidebar.collapsed .sidebar-subtitle,
  #sidebar.collapsed .user-info,
  #sidebar.collapsed .nav-section-title,
  #sidebar.collapsed .nav-link span {
    display: none;
  }

  /* Alineación de elementos en modo colapsado */
  #sidebar.collapsed .sidebar-user,
  #sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 15px 0;
  }

  #sidebar.collapsed .nav-link i {
    margin-right: 0;
    font-size: 18px;
  }

  #sidebar.collapsed .user-avatar {
    margin-right: 0;
  }
}

/* Pantallas medianas y pequeñas */
@media screen and (max-width: 992px) {
  #sidebar {
    width: 70px;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    bottom: 0 !important;
    padding-top: 60px; /* Espacio para el header */
  }

  #sidebar.expanded {
    width: 250px;
  }

  .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
  }

  /* Ocultar elementos en modo colapsado */
  #sidebar:not(.expanded) .sidebar-logo,
  #sidebar:not(.expanded) .sidebar-subtitle,
  #sidebar:not(.expanded) .user-info,
  #sidebar:not(.expanded) .nav-section-title,
  #sidebar:not(.expanded) .nav-link span {
    display: none;
  }

  /* Alineación de elementos en modo colapsado */
  #sidebar:not(.expanded) .sidebar-user,
  #sidebar:not(.expanded) .nav-link {
    justify-content: center;
    padding: 15px 0;
  }

  #sidebar:not(.expanded) .nav-link i {
    margin-right: 0;
    font-size: 18px;
  }

  #sidebar:not(.expanded) .user-avatar {
    margin-right: 0;
  }

  /* Ajustes en modo expandido */
  #sidebar.expanded .nav-link {
    justify-content: flex-start;
    padding: 10px 20px;
  }

  #sidebar.expanded .nav-link i {
    margin-right: 10px;
  }

  #sidebar.expanded .sidebar-user {
    padding: 15px 20px;
  }

  #sidebar.expanded .user-avatar {
    margin-right: 12px;
  }
}

/* ===== ESTILOS ESPECÍFICOS ===== */
/* Color activo */
.nav-link.active i {
  color: #f3d516;
}

/* Animación para el botón toggle eliminada */

/* Scrollbar personalizado */
#sidebar::-webkit-scrollbar {
  width: 5px;
}

#sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

#sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

#sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Eliminado el botón Restaurar UI */
