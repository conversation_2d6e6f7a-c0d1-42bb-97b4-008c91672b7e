<?php
/**
 * LoginHandler.php
 * 
 * Manejador de procesos de login y autenticación
 */

namespace Config;

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/AuthService.php';
require_once __DIR__ . '/SecurityService.php';
require_once __DIR__ . '/SessionManager.php';
require_once __DIR__ . '/logger.php';

class LoginHandler {
    private $conn;
    private $authService;
    private $securityService;
    private $sessionManager;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $conn;
        $this->conn = $conn;
        $this->authService = AuthService::getInstance($conn);
        $this->securityService = SecurityService::getInstance();
        $this->sessionManager = \SessionManager::getInstance();
    }
    
    /**
     * Procesa una solicitud de login
     */
    public function processLogin() {
        // Verificar si es un método POST
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirectToLogin();
            return;
        }
        
        // Limpiar buffer de salida
        while (ob_get_level()) ob_end_clean();
        ob_start();
        
        try {
            // Verificar token CSRF
            $csrf_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
            if (!$this->securityService->validateCsrfToken($csrf_token)) {
                logWarning('Intento de login con token CSRF inválido');
                throw new \Exception('security_violation');
            }
            
            // Detectar actividad sospechosa
            if ($this->securityService->detectSuspiciousActivity()) {
                logWarning('Actividad sospechosa detectada en intento de login');
                throw new \Exception('security_violation');
            }
            
            // Validar y limpiar datos de entrada
            $username = isset($_POST['username']) ? trim($_POST['username']) : '';
            $password = isset($_POST['password']) ? $_POST['password'] : '';
            $remember = isset($_POST['remember']) && $_POST['remember'] === 'on';
            
            // Verificar campos requeridos
            if (empty($username) || empty($password)) {
                throw new \Exception('empty');
            }
            
            // Aplicar delay aleatorio para prevenir timing attacks
            usleep(random_int(100000, 300000));
            
            // Verificar credenciales
            $userData = $this->authService->verifyCredentials($username, $password);
            
            // Si llegamos aquí, el login fue exitoso
            // Registrar el login
            $this->authService->registerSuccessfulLogin($userData['id'], $userData['username']);
            
            // Crear sesión de usuario
            $this->sessionManager->createUserSession([
                'id' => $userData['id'],
                'username' => $userData['username'],
                'email' => $userData['email'],
                'role' => $userData['role'],
                'user_id' => $userData['id']
            ]);
            
            // Establecer cookie de "recordar" si se solicitó
            if ($remember) {
                $this->setRememberMeCookie($userData['id']);
            }
            
            // Determinar URL de redirección
            $redirect_url = $this->getRedirectUrl();
            
            // Limpiar el buffer y headers
            if (ob_get_length()) ob_end_clean();
            
            // Prevenir caché
            header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
            header("Cache-Control: post-check=0, pre-check=0", false);
            header("Pragma: no-cache");
            
            // Redirigir
            header('Location: ' . $redirect_url);
            exit();
            
        } catch (\Exception $e) {
            $errorType = $e->getMessage();
            logError('Error en proceso de login', ['error_type' => $errorType]);
            
            // Destruir sesión para errores de seguridad graves
            if (in_array($errorType, ['session_hijacking', 'security_violation'])) {
                $this->sessionManager->destroySession();
            }
            
            // Guardar la URL de retorno si existe
            if (isset($_POST['return_to'])) {
                $_SESSION['return_to'] = $_POST['return_to'];
            }
            
            // Limpiar buffer antes de redireccionar
            if (ob_get_length()) ob_end_clean();
            
            // Redirigir con error
            header('Location: ' . BASE_URL . '/public/login.php?error=' . urlencode($errorType));
            exit();
        }
    }
    
    /**
     * Establece cookie para la funcionalidad "recordarme"
     *
     * @param int $userId ID del usuario
     * @return bool
     */
    private function setRememberMeCookie($userId) {
        // Generar token único
        $selector = bin2hex(random_bytes(16));
        $validator = bin2hex(random_bytes(32));
        
        // Crear hash para almacenar en la base de datos
        $hashedValidator = hash('sha256', $validator);
        
        // Establecer fecha de expiración (30 días)
        $expires = time() + (86400 * 30);
        
        try {
            // Eliminar tokens anteriores para este usuario
            $stmt = $this->conn->prepare("DELETE FROM auth_tokens WHERE user_id = ? AND type = 'remember'");
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            
            // Guardar nuevo token en la base de datos
            $stmt = $this->conn->prepare("
                INSERT INTO auth_tokens (user_id, selector, token, expires, type) 
                VALUES (?, ?, ?, FROM_UNIXTIME(?), 'remember')
            ");
            
            $stmt->bind_param("issi", $userId, $selector, $hashedValidator, $expires);
            $success = $stmt->execute();
            
            if (!$success) {
                logError('Error al guardar token remember me', ['error' => $stmt->error]);
                return false;
            }
            
            // Valor para la cookie: selector:validator
            $cookieValue = $selector . ':' . $validator;
            
            // Establecer cookie
            $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $httponly = true;
            
            setcookie(
                'remember_me',
                $cookieValue,
                [
                    'expires' => $expires,
                    'path' => COOKIE_PATH,
                    'domain' => COOKIE_DOMAIN,
                    'secure' => $secure,
                    'httponly' => $httponly,
                    'samesite' => 'Lax'
                ]
            );
            
            return true;
            
        } catch (\Exception $e) {
            logError('Error al establecer remember me', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Valida una cookie "recordarme" para autoingreso
     *
     * @return array|false Datos del usuario o false si la cookie no es válida
     */
    public function validateRememberMeCookie() {
        if (!isset($_COOKIE['remember_me'])) {
            return false;
        }
        
        $cookie = $_COOKIE['remember_me'];
        $parts = explode(':', $cookie);
        
        if (count($parts) !== 2) {
            $this->clearRememberMeCookie();
            return false;
        }
        
        list($selector, $validator) = $parts;
        
        try {
            $stmt = $this->conn->prepare("
                SELECT t.user_id, t.token, u.username, u.email, u.role
                FROM auth_tokens t
                JOIN users u ON t.user_id = u.id
                WHERE t.selector = ?
                AND t.expires > NOW()
                AND t.type = 'remember'
                AND u.is_active = 1
            ");
            
            $stmt->bind_param("s", $selector);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 0) {
                $this->clearRememberMeCookie();
                return false;
            }
            
            $token = $result->fetch_assoc();
            $hashedValidator = hash('sha256', $validator);
            
            if (!hash_equals($token['token'], $hashedValidator)) {
                // Token inválido, posible robo de cookie
                $this->revokeRememberTokensForUser($token['user_id']);
                $this->clearRememberMeCookie();
                logWarning('Posible robo de cookie remember me', ['user_id' => $token['user_id']]);
                return false;
            }
            
            // Token válido, renovar
            $this->renewRememberMeCookie($token['user_id'], $selector, $validator);
            
            // Devolver datos del usuario
            return [
                'id' => $token['user_id'],
                'username' => $token['username'],
                'email' => $token['email'],
                'role' => $token['role']
            ];
            
        } catch (\Exception $e) {
            logError('Error al validar cookie remember me', ['error' => $e->getMessage()]);
            $this->clearRememberMeCookie();
            return false;
        }
    }
    
    /**
     * Renueva una cookie "recordarme"
     *
     * @param int $userId ID del usuario
     * @param string $selector Selector actual
     * @param string $validator Validator actual
     * @return bool
     */
    private function renewRememberMeCookie($userId, $selector, $validator) {
        // Establecer nueva fecha de expiración (30 días)
        $expires = time() + (86400 * 30);
        
        try {
            // Actualizar expiración en la base de datos
            $stmt = $this->conn->prepare("
                UPDATE auth_tokens 
                SET expires = FROM_UNIXTIME(?) 
                WHERE user_id = ? AND selector = ? AND type = 'remember'
            ");
            
            $stmt->bind_param("iis", $expires, $userId, $selector);
            $success = $stmt->execute();
            
            if (!$success) {
                logError('Error al renovar token remember me', ['error' => $stmt->error]);
                return false;
            }
            
            // Renovar cookie
            $cookieValue = $selector . ':' . $validator;
            $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $httponly = true;
            
            setcookie(
                'remember_me',
                $cookieValue,
                [
                    'expires' => $expires,
                    'path' => COOKIE_PATH,
                    'domain' => COOKIE_DOMAIN,
                    'secure' => $secure,
                    'httponly' => $httponly,
                    'samesite' => 'Lax'
                ]
            );
            
            return true;
            
        } catch (\Exception $e) {
            logError('Error al renovar cookie remember me', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Revoca todos los tokens "recordarme" para un usuario
     *
     * @param int $userId ID del usuario
     * @return bool
     */
    private function revokeRememberTokensForUser($userId) {
        try {
            $stmt = $this->conn->prepare("DELETE FROM auth_tokens WHERE user_id = ? AND type = 'remember'");
            $stmt->bind_param("i", $userId);
            return $stmt->execute();
        } catch (\Exception $e) {
            logError('Error al revocar tokens remember me', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Elimina la cookie "recordarme"
     */
    private function clearRememberMeCookie() {
        setcookie(
            'remember_me',
            '',
            [
                'expires' => time() - 3600,
                'path' => COOKIE_PATH,
                'domain' => COOKIE_DOMAIN,
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );
    }
    
    /**
     * Determina la URL de redirección después del login
     *
     * @return string URL de redirección
     */
    private function getRedirectUrl() {
        $default_url = BASE_URL . '/public/tienda_adm.php';
        
        // Verificar si hay una URL de retorno en POST
        if (isset($_POST['return_to']) && !empty($_POST['return_to'])) {
            $return_to = $_POST['return_to'];
            if ($this->securityService->isSecureRedirectUrl($return_to, BASE_URL)) {
                return $return_to;
            }
        }
        
        // Verificar si hay una URL de retorno en SESSION
        if (isset($_SESSION['return_to']) && !empty($_SESSION['return_to'])) {
            $return_to = $_SESSION['return_to'];
            unset($_SESSION['return_to']); // Limpiar después de usar
            
            if ($this->securityService->isSecureRedirectUrl($return_to, BASE_URL)) {
                return $return_to;
            }
        }
        
        return $default_url;
    }
    
    /**
     * Redirige al usuario a la página de login
     *
     * @param string $error Código de error opcional
     */
    private function redirectToLogin($error = null) {
        $url = BASE_URL . '/public/login.php';
        
        if ($error) {
            $url .= '?error=' . urlencode($error);
        }
        
        header('Location: ' . $url);
        exit();
    }
    
    /**
     * Procesa una solicitud de logout
     */
    public function processLogout() {
        // Desactivar la cookie "recordarme" si existe
        if (isset($_COOKIE['remember_me'])) {
            $this->clearRememberMeCookie();
            
            // Eliminar el token de la base de datos
            if (isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
                $this->revokeRememberTokensForUser($userId);
            }
        }
        
        // Destruir la sesión
        $this->sessionManager->destroySession();
        
        // Redirigir a la página de login con mensaje de éxito
        header('Location: ' . BASE_URL . '/public/login.php?message=logout_success');
        exit();
    }
    
    /**
     * Verifica si un usuario está autenticado mediante "recordarme"
     * Si hay una cookie válida, crea una sesión para el usuario
     */
    public function checkRememberMeAuthentication() {
        // Verificar si el usuario ya está autenticado
        if ($this->sessionManager->isLoggedIn()) {
            return;
        }
        
        // Intentar autenticar con cookie "recordarme"
        $userData = $this->validateRememberMeCookie();
        
        if ($userData) {
            // Crear sesión de usuario
            $this->sessionManager->createUserSession($userData);
            
            // Registrar el auto-login
            logInfo('Usuario autenticado automáticamente con cookie remember me', [
                'username' => $userData['username'],
                'user_id' => $userData['id']
            ]);
        }
    }
}