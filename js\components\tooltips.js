/**
 * tooltips.js
 * Componente modular para manejar tooltips en la aplicación
 */

/**
 * Inicializa los tooltips en la página
 * Esta función configura los tooltips para que se muestren al hacer clic en el ícono
 */
export function initTooltips() {
    console.log('Inicializando tooltips mejorados...');

    // Seleccionar todos los íconos de tooltip
    const tooltipIcons = document.querySelectorAll('.tooltip-icon, .info-icon');
    console.log(`Encontrados ${tooltipIcons.length} íconos de tooltip`);

    // Ocultar todos los tooltips al inicio
    const allTooltips = document.querySelectorAll('.tooltip-content, .info-tooltip');
    allTooltips.forEach(tooltip => {
        tooltip.classList.remove('active');
        tooltip.style.display = 'none';
        tooltip.style.opacity = '0';
        tooltip.style.visibility = 'hidden';
    });

    // Agregar listener de clic a cada icono
    tooltipIcons.forEach(icon => {
        // Eliminar listeners previos para evitar duplicados
        const newIcon = icon.cloneNode(true);
        icon.parentNode.replaceChild(newIcon, icon);

        // Agregar nuevo listener
        newIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Evitar que el clic se propague
            console.log('Clic en icono de tooltip');

            // Determinar qué clase de tooltip estamos manejando
            const isInfoIcon = newIcon.classList.contains('info-icon');
            const tooltipSelector = isInfoIcon ? '.info-tooltip' : '.tooltip-content';

            // Obtener el tooltip asociado a este icono
            let tooltip;
            if (isInfoIcon) {
                // Para info-icon, el tooltip está dentro del contenedor padre
                const container = newIcon.closest('.info-icon-container') || newIcon.closest('.info-tooltip-container');
                tooltip = container ? container.querySelector(tooltipSelector) : null;
                console.log('Contenedor encontrado:', container);
            } else {
                // Para tooltip-icon, el tooltip es el siguiente elemento hermano
                tooltip = newIcon.nextElementSibling;
                if (!tooltip || !tooltip.matches(tooltipSelector)) {
                    // Buscar en el contenedor padre si no es el siguiente elemento
                    const container = newIcon.closest('.tooltip-wrapper');
                    tooltip = container ? container.querySelector(tooltipSelector) : null;
                }
            }

            if (!tooltip) {
                console.error('No se encontró el tooltip asociado al icono');
                return;
            }

            console.log('Tooltip encontrado:', tooltip);

            // Cerrar todos los otros tooltips primero
            allTooltips.forEach(tip => {
                if (tip !== tooltip) {
                    tip.classList.remove('active');
                    tip.style.display = 'none';
                    tip.style.opacity = '0';
                    tip.style.visibility = 'hidden';
                }
            });

            // Alternar visibilidad de este tooltip
            if (tooltip.classList.contains('active')) {
                console.log('Ocultando tooltip');
                tooltip.classList.remove('active');
                tooltip.style.display = 'none';
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            } else {
                console.log('Mostrando tooltip');
                tooltip.classList.add('active');
                tooltip.style.display = 'block';
                tooltip.style.opacity = '1';
                tooltip.style.visibility = 'visible';
            }
        });
    });

    // Cerrar tooltips al hacer clic en cualquier parte del documento
    document.addEventListener('click', function(e) {
        if (!e.target.classList.contains('tooltip-icon') && !e.target.classList.contains('info-icon')) {
            allTooltips.forEach(tooltip => {
                tooltip.classList.remove('active');
                tooltip.style.display = 'none';
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            });
        }
    });

    // Evitar que los clics dentro del tooltip lo cierren
    allTooltips.forEach(tooltip => {
        tooltip.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Cerrar tooltips al presionar la tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            allTooltips.forEach(tooltip => {
                tooltip.classList.remove('active');
                tooltip.style.display = 'none';
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            });
        }
    });

    // Asegurarse de que los tooltips estén correctamente inicializados después de un breve retraso
    setTimeout(() => {
        console.log('Verificando tooltips después de retraso...');
        allTooltips.forEach(tooltip => {
            if (!tooltip.classList.contains('active')) {
                tooltip.style.display = 'none';
                tooltip.style.opacity = '0';
                tooltip.style.visibility = 'hidden';
            }
        });
    }, 500);
}
