/**
 * Estilos para los dropdowns de notificaciones y mensajes
 */

/* Contenedor del dropdown */
.dropdown-container {
  position: relative;
  display: inline-block;
}

/* Menú dropdown */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 300px;
  max-height: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  display: none;
  margin-top: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Flecha del dropdown */
.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 16px;
  height: 16px;
  background-color: #fff;
  transform: rotate(45deg);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}

/* Encabezado del dropdown */
.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dropdown-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #333;
}

.unread-count {
  background-color: #f0f7ff;
  color: #3498db;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

/* Cuerpo del dropdown */
.dropdown-body {
  max-height: 280px;
  overflow-y: auto;
}

/* Pie del dropdown */
.dropdown-footer {
  padding: 10px 15px;
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.view-all-btn {
  background-color: #7e3ff2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 7px 15px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
}

.view-all-btn:hover {
  background-color: #6a2ee0;
}

/* Estilos para las notificaciones */
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f9f9f9;
}

.notification-item.unread {
  background-color: #f0f7ff;
}

.notification-item.unread:hover {
  background-color: #e6f2ff;
}

.notification-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.notification-icon i {
  font-size: 12px;
  color: white;
}

.notification-icon.purple {
  background-color: #7e3ff2;
}

.notification-icon.blue {
  background-color: #3498db;
}

.notification-icon.green {
  background-color: #2ecc71;
}

.notification-icon.orange {
  background-color: #f39c12;
}

.notification-icon.red {
  background-color: #e74c3c;
}

.notification-content {
  flex: 1;
  min-width: 0;
  padding-right: 5px;
}

.notification-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
  line-height: 1.3;
  /* Permitir múltiples líneas */
  white-space: normal;
  overflow: visible;
}

.notification-text {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  /* Permitir múltiples líneas */
  white-space: normal;
  overflow: visible;
}

.notification-close {
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  padding: 3px;
  margin-left: 5px;
  font-size: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 2px;
}

.notification-item:hover .notification-close {
  opacity: 1;
}

.notification-close:hover {
  color: #666;
}

.highlight {
  color: #7e3ff2;
  font-weight: 600;
}

/* Estilos para los mensajes */
.message-item {
  display: flex;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s;
}

.message-item:hover {
  background-color: #f9f9f9;
}

.message-item.unread {
  background-color: #f0f7ff;
}

.message-item.unread:hover {
  background-color: #e6f2ff;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e1e1e1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.message-avatar i {
  color: #888;
  font-size: 16px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-sender {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 3px;
}

.message-preview {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 3px;
}

.message-time {
  font-size: 11px;
  color: #999;
}

/* Responsive */
@media (max-width: 576px) {
  .dropdown-menu {
    width: 280px;
    right: -100px;
  }

  .dropdown-menu::before {
    right: 120px;
  }

  .notification-item {
    padding: 8px 12px;
  }

  .notification-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }

  .notification-title {
    font-size: 12px;
  }

  .notification-text {
    font-size: 11px;
  }

  .dropdown-header {
    padding: 10px 12px;
  }

  .dropdown-footer {
    padding: 8px 12px;
  }

  .view-all-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* Mostrar dropdown cuando está activo */
.dropdown-menu.show {
  display: block;
}
