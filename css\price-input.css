/**
 * Estilos para los campos de precio
 */

/* Contenedor del input para posicionamiento relativo */
.price-input-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

/* Estilo para el símbolo de moneda */
.price-display {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #333;
    font-weight: 500;
    pointer-events: none;
    z-index: 10;
}

/* Ajuste para los inputs con símbolo de moneda */
input.price-input {
    padding-left: 25px !important;
}

/* Estilo para precios en tablas */
.product-price.formatted {
    font-weight: 600;
}

/* Estilo para precio actual */
.current-price {
    font-weight: 600;
    color: #333;
    display: block;
}

/* Estilo para precio original (tachado) */
.original-price {
    text-decoration: line-through;
    color: #888;
    font-size: 0.9em;
    display: block;
}

/* Contenedor para precios con descuento */
.price-container {
    display: flex;
    flex-direction: column;
}
