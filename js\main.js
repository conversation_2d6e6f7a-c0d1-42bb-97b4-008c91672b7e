// Módulo principal

// Importar módulos principales - TODAS las importaciones deben estar aquí arriba
import { initApp } from './core/app.js';
import { showSection, updateActiveTab, showProductsSection, showEditProductSection, showStoreInfoSection, showStatsSection, showCategoriesSection } from './core/ui.js';
import { setupOverlays, resetOverlay } from './core/utils.js';
import { initSidebar, toggleSidebar } from './components/sidebar.js';
import { initFilterHandlers, closeFilterPanel } from './modules/products/product-filter.js';
import { closeEditPanel, setupEditProductPanel } from './modules/products/product-edit.js';

// Importar funcionalidades de productos
import { loadProducts } from './modules/products/product-api.js';
import { setupTableButtons, updateResponsiveView, renderProductTable } from './modules/products/product-list.js';
import { createProductCards } from './modules/products/product-cards.js';

// Importar funcionalidades de categorías
import { initCategoriesModule } from './modules/categories/categories.js';

// Importar funcionalidades de estadísticas
import { initStatsModule } from './modules/stats/stats.js';

// Importar componente de tooltips
import { initTooltips } from './components/tooltips.js';

// Función para restaurar la interactividad cuando algo falle
function restoreInteractivity() {
    // 1. Ocultar todos los overlays
    const overlays = document.querySelectorAll('.overlay, .edit-overlay, #editOverlay, #filterOverlay');
    overlays.forEach(overlay => {
        overlay.style.display = 'none';
        overlay.classList.remove('active');
    });

    // 2. Ocultar o cerrar paneles laterales que puedan estar bloqueando
    const panels = document.querySelectorAll('.edit-container, #editProductPanel, #filterContainer');
    panels.forEach(panel => {
        panel.style.display = 'none';
        panel.classList.remove('active');
    });

    // 3. Restaurar evento de clic en el botón del aside
    const asideToggle = document.getElementById('aside-toggle');
    if (asideToggle) {
        // Eliminar todos los eventos antiguos
        const newAsideToggle = asideToggle.cloneNode(true);
        asideToggle.parentNode.replaceChild(newAsideToggle, asideToggle);

        // Asignar evento de clic
        newAsideToggle.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('collapsed');
                // Actualizar ícono
                const icon = this.querySelector('#toggle-icon');
                if (icon) {
                    icon.classList.toggle('fa-bars');
                    icon.classList.toggle('fa-times');
                }
            }
        });
    }
}

// Botón de emergencia eliminado

// Función principal para inicializar la aplicación
async function initializeApplication() {
    try {

        // Inicializar app core y módulos
        await initApp();
        initSidebar();
        setupEditProductPanel();
        initFilterHandlers();
        initCategoriesModule();
        initStatsModule();

        // Inicializar tooltips
        initTooltips();

        // Cargar datos iniciales
        await loadProducts();

        // Configurar listeners para pestañas principales
        setupMainTabs();

        // Mostrar sección inicial (Dashboard)
        showSection('statsSection');
        updateActiveTab('dashboardLink');

        // Configurar la vista responsive
        updateResponsiveView();
        window.addEventListener('resize', updateResponsiveView);
    } catch (error) {
        console.error('Error al inicializar la aplicación:', error);
    }
}

// Función para configurar las pestañas principales
function setupMainTabs() {
    // Las pestañas principales han sido eliminadas y reemplazadas por la navegación del sidebar
    console.log('Navegación principal ahora se maneja a través del sidebar');
    return;
}

// Función para inicializar eventos del overlay
function initOverlayEvents() {
    try {
        const editOverlay = document.getElementById('editOverlay');
        const filterOverlay = document.getElementById('filterOverlay');

        // Cerrar el panel de edición al hacer clic en el overlay
        if (editOverlay) {
            editOverlay.addEventListener('click', function(e) {
                if (e.target === this) {
                    // Importar la función closeEditPanel si es necesario
                    if (typeof closeEditPanel === 'function') {
                        closeEditPanel();
                    } else if (window.productEdit && typeof window.productEdit.closeEditPanel === 'function') {
                        window.productEdit.closeEditPanel();
                    }
                }
            });
        }

        // Cerrar el filtro al hacer clic en su overlay
        if (filterOverlay) {
            filterOverlay.addEventListener('click', function(e) {
                if (e.target === this) {
                    const filterContainer = document.getElementById('filterContainer');
                    if (filterContainer) {
                        filterContainer.style.display = 'none';
                    }
                    this.style.display = 'none';
                }
            });
        }
    } catch (error) {
        // Silenciar errores para evitar mensajes en la consola
        console.debug('Overlay events initialization skipped');
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    initializeApplication();

    // Inicializar los eventos del overlay
    initOverlayEvents();
});

// Añadir manejo global de errores para módulos
window.addEventListener('error', function(e) {
    try {
        // Filtrar errores conocidos para no mostrarlos en la consola
        const errorMsg = e.error ? e.error.toString() : e.message || '';
        const errorStack = e.error && e.error.stack ? e.error.stack : '';

        // Ignorar errores específicos que sabemos que no afectan la funcionalidad
        if (errorMsg.includes("Cannot read properties of null (reading 'addEventListener')") ||
            errorStack.includes('tienda_adm.php:1509:26')) {
            // Silenciar estos errores específicos
            e.preventDefault();
            e.stopPropagation();
            return;
        }

        // Para otros errores, mostrar en la consola
        console.error('Error global:', errorMsg);
        if (errorStack) {
            console.error('Stack:', errorStack);
        }

        // Mostrar notificación de error al usuario si la función está disponible
        if (typeof showNotification === 'function') {
            showNotification('Ha ocurrido un error. Por favor, recargue la página.', 'error', 10000);
        }
    } catch (innerError) {
        // Evitar errores en cascada
        console.error('Error al manejar otro error:', innerError);
    }
});