
# Server configuration
$SERVER = "servidor-linux"  # Usar el host configurado en lugar de la dirección IP directa
$PORT = "22222"
# No especificar KEY_PATH ya que está configurado en el host
$DEST_BASE = "/var/www/aunclick"

# Project root
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path

# Solicitar número de archivos a transferir
$fileCount = Read-Host "Ingrese el número de archivos más recientes a transferir (predeterminado: 4)"

# Si no se ingresa un valor o no es un número válido, usar 4 como valor predeterminado
if ([string]::IsNullOrWhiteSpace($fileCount) -or -not [int]::TryParse($fileCount, [ref]$null)) {
    $fileCount = 4
    Write-Host "Usando valor predeterminado: $fileCount archivos" -ForegroundColor Yellow
}
else {
    $fileCount = [int]$fileCount
    Write-Host "Se transferirán los $fileCount archivos más recientes de cada directorio" -ForegroundColor Cyan
}

# Estructura completa de directorios JS
$jsDirs = @(
    "js",
    "js/components",
    "js/core",
    "js/modules",
    "js/modules/products",
    "js/modules/categories",
    "js/modules/stats"
)

# Otros directorios principales
$mainDirs = @(
    "config",
    "css",
    "images",
    "public",
    "public/js",
    "public/API/productos",
    "public/API/usuarios"
)

# Combinar todos los directorios
$allDirs = $jsDirs + $mainDirs

Write-Host "Starting file transfer of most recent files..." -ForegroundColor Green

# Obtener la hora de hace 2 horas
$twoHoursAgo = (Get-Date).AddHours(-2)

foreach ($dir in $allDirs) {
    $dirPath = Join-Path -Path $projectRoot -ChildPath $dir

    if (Test-Path $dirPath) {
        Write-Host "`nProcessing directory: $dir" -ForegroundColor Cyan

        # Filtrar archivos modificados en las últimas 2 horas
        $files = Get-ChildItem -Path $dirPath -File |
                Where-Object {
                    $_.LastWriteTime -gt $twoHoursAgo
                } |
                Sort-Object LastWriteTime -Descending |
                Select-Object -First $fileCount

        if ($files.Count -eq 0) {
            Write-Host "  No files modified in the last 2 hours found in $dir" -ForegroundColor Yellow
            continue
        }

        Write-Host "  Found $($files.Count) files modified in the last 2 hours:" -ForegroundColor Yellow
        foreach ($file in $files) {
            $localPath = $file.FullName
            $destPath = "$DEST_BASE/$dir/$($file.Name)"
            $lastModified = $file.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")

            Write-Host "  - $($file.Name) (Last modified: $lastModified)" -ForegroundColor Yellow

            # Transferir archivo usando el host configurado (similar a send_tqw_actual.ps1)
            & scp -P $PORT $localPath "${SERVER}:${destPath}"

            if ($LASTEXITCODE -eq 0) {
                Write-Host "    Success: $($file.Name)" -ForegroundColor Green
            } else {
                Write-Host "    Error transferring: $($file.Name)" -ForegroundColor Red
            }
        }
    }
}

# Asegurar permisos correctos solo para los archivos transferidos
Write-Host "`nSetting permissions..." -ForegroundColor Yellow

# Establecer permisos
$chmodFilesCmd = "find $DEST_BASE -type f -exec chmod 644 {} \;"
$chmodDirsCmd = "find $DEST_BASE -type d -exec chmod 755 {} \;"
& ssh -p $PORT $SERVER "$chmodFilesCmd; $chmodDirsCmd"

Write-Host "`nTransfer of recent files completed successfully" -ForegroundColor Green
