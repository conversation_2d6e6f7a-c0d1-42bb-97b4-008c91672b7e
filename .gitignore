# Security - Never commit these files
config/.env
config/.env.local
config/.env.production
*.key
*.pem
*.p12
*.pfx

# Database credentials and backups
*.sql
*.db
*.sqlite
*.sqlite3
database_backup_*

# Log files
logs/
*.log
error_log
access_log

# Temporary files
tmp/
temp/
cache/
*.tmp
*.temp

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
**/.claude/settings.local.json

# PHP specific
vendor/
composer.lock
.phpunit.result.cache

# Configuration files with sensitive data
config/local_config.php
config/production_config.php
config/database_credentials.php

# Backup files
*.bak
*.backup
*.old

# System files
.htaccess.backup
.htpasswd
robots.txt.backup

# Upload directories (if they contain user data)
uploads/private/
uploads/temp/

# Session files
sessions/
tmp/sessions/

# Error and debug files
debug.log
error.log
php_errors.log
last_error.txt

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Documentation with sensitive info
INSTALL.md
DEPLOYMENT.md
CREDENTIALS.md

# Test files with real data
test_data/
fixtures/real_*

# SSL certificates
ssl/
certificates/
*.crt
*.csr

# Environment specific configs
.env.*
!.env.example
