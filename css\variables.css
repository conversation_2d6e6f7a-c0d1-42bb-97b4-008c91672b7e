/* =========================================
   VARIABLES GLOBALES Y ESTILOS BASE
   ========================================= */
   :root {
    /* Colores principales */
    --purple-primary: #6a1b9a;
    --purple-light: #9c4dcc;
    --purple-dark: #38006b;
    --green-primary: #43a047;
    --green-light: #76d275;
    --green-dark: #00701a;
    --yellow-accent: #ffd54f;
    --yellow-light: #fff7c4;
    --red-error: #f44336;
    --blue-info: #2196f3;
    
    /* Grises */
    --gray-bg: #f5f5f5;
    --gray-light: #e0e0e0;
    --gray-medium: #9e9e9e;
    --gray-dark: #616161;
    
    /* Textos */
    --text-primary: rgba(0,0,0,.9);
    --text-secondary: rgba(0,0,0,.55);
    
    /* Dimensiones */
    --max-width: 1400px;
    --sidebar-width: 250px;
    --header-height: 60px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* Prevenir zoom y comportamientos táctiles no deseados */
html {
    touch-action: manipulation;
    height: 100%;
    overflow: hidden;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: var(--gray-bg);
    color: var(--text-primary);
    height: 100%;
    overflow: hidden;
    position: fixed;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
}

/* Habilitar scroll solo en el contenedor principal */
.admin-layout {
    height: 100%;
    overflow: hidden;
}

.main-content {
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
}