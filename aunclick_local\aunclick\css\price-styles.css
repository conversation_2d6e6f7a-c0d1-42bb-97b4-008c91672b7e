/**
 * Estilos para precios con descuento
 */

/* Contenedor de precios */
.price-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
}

/* Precio actual */
.current-price {
    font-weight: bold;
    color: #333;
    font-size: 1rem;
}

/* Precio original tachado */
.original-price {
    text-decoration: line-through;
    color: #888;
    font-size: 0.85rem;
}

/* Estilos para precios en la tabla */
.admin-table td.product-price {
    min-width: 100px;
}

/* Estilos para precios formateados */
.currency-formatted {
    font-weight: bold;
}

/* Destacar fila actualizada */
.updated-row {
    animation: highlight-row 3s ease-in-out;
}

@keyframes highlight-row {
    0% { background-color: rgba(255, 255, 0, 0.3); }
    100% { background-color: transparent; }
}
