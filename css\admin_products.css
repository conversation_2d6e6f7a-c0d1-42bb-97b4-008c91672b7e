/* Variables de color para toda la aplicación */
:root {
    --primary: #4f46e5;
    --primary-hover: #4338ca;
    --primary-light: #eef2ff;
    --danger: #ef4444;
    --danger-hover: #dc2626;
    --danger-light: #fee2e2;
    --success: #10b981;
    --success-hover: #059669;
    --success-light: #d1fae5;
    --warning: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Estilos para la barra de acciones administrativa */
.admin-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
    position: relative;
    z-index: 10;
}

/* Estilos para el contenedor de búsqueda */
.search-container {
    flex: 1;
    max-width: 400px;
    margin-left: auto;
    position: relative;
    z-index: 10;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background-color: var(--white);
}

.search-input {
    width: 100%;
    padding: 0.85rem 2.5rem 0.85rem 2.25rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    color: var(--gray-800);
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.search-input:hover {
    border-color: var(--gray-400);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--gray-500);
    font-size: 0.9rem;
}

.clear-search {
    position: absolute;
    right: 0.75rem;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    display: none;
    padding: 0;
    font-size: 0.9rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.clear-search:hover {
    opacity: 1;
    color: var(--danger);
}

/* Fix para Safari - agregar prefijos para propiedades no soportadas */
.table th {
    -webkit-hyphens: auto;
    -ms-hyphens: auto;
    hyphens: auto;
}

.image-modal {
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.product-modal {
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
}

/* Base styles for fusion cards - Aplicado a todos los tamaños de pantalla */
.fusion-card {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
    font-family: var(--font-sans);
    margin-bottom: 15px;
    box-sizing: border-box;
}

/* NUEVO: Botones de acción en la parte inferior */
.fusion-card-actions {
    display: flex;
    width: 100%;
    height: auto;
    overflow: visible;
    padding: 0;
    margin: 0;
    opacity: 1;
    visibility: visible;
    margin-top: auto !important;
}

.fusion-edit-button {
    display: flex;
    width: 75%;
    background-color: var(--primary);
    color: white;
    padding: 14px;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.fusion-edit-button:hover {
    background-color: var(--primary-hover);
}

.fusion-delete-button {
    display: flex;
    width: 25%;
    background-color: var(--danger);
    color: white;
    padding: 14px;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.fusion-delete-button:hover {
    background-color: var(--danger-hover);
}

.fusion-edit-button i, .fusion-delete-button i {
    margin-right: 8px;
    display: inline-block;
}

/* Mensaje de no resultados */
.no-results-message {
    width: 100%;
    padding: 3rem 1rem;
    text-align: center;
    background-color: var(--white);
    border-radius: var(--radius);
    margin: 2rem 0;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.no-results-message i {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 1rem;
}

.no-results-message p {
    color: var(--gray-700);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.btn-clear-search {
    background-color: var(--primary-light);
    color: var(--primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-clear-search:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Estilos para la tabla y su contenedor */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 2rem;
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
    border: 1px solid var(--gray-100);
    overflow-x: auto;
    margin: 20px 0;
}

.table-wrapper {
    overflow-x: auto; /* Ensure horizontal scrolling */
}

/* Ajustes para la tabla */
.table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--font-sans);
    font-size: 0.92rem;
    table-layout: fixed;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table thead {
    position: sticky;
    top: 0;
    z-index: 10;
}

.table th {
    background-color: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    padding: 1.2rem 1.5rem;
    text-align: left;
    text-transform: uppercase;
    font-size: 0.72rem;
    letter-spacing: 0.05em;
    border: 1px solid var(--gray-200);
    transition: background-color 0.2s;
    white-space: normal;
    word-wrap: break-word;
    hyphens: auto;
    min-height: 60px;
    vertical-align: middle;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    color: #333;
}

.table th:first-child {
    border-top-left-radius: var(--radius);
    padding-left: 1.5rem;
}

.table th:last-child {
    border-top-right-radius: var(--radius);
    padding-right: 1.5rem;
}

/* Ajustes para filas de la tabla */
.table tr {
    height: 80px;
}

/* Eliminar dobles bordes que puedan aparecer */
.table tr:not(:last-child) td {
    border-bottom-width: 1px;
}

/* Ajuste para todas las celdas */
.table th,
.table td {
    padding: 1.2rem 1.5rem;
    border: 1px solid var(--gray-200);
    color: var(--gray-700);
    vertical-align: middle;
    height: 80px;
    transition: all 0.2s ease;
    box-sizing: border-box;
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table td:first-child {
    padding-left: 1.5rem;
}

.table td:last-child {
    padding-right: 1.5rem;
}

.table tr:hover td {
    background-color: var(--gray-50);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:last-child td:first-child {
    border-bottom-left-radius: var(--radius);
}

.table tr:last-child td:last-child {
    border-bottom-right-radius: var(--radius);
}

/* Estilos para imágenes en la tabla */
.table td img {
    width: 52px;
    height: 52px;
    object-fit: cover;
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 2px solid var(--white);
}

.table td img:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-light);
}

.product-image-preview {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.product-name-cell {
    min-width: 200px;
}

.product-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.product-category {
    font-size: 0.85em;
    color: #666;
}

.product-price {
    font-weight: 500;
}

.original-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.85em;
}

.low-stock {
    color: #dc3545;
    font-weight: 500;
}

.status-badge,
.condition-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

/* Estados */
.status-publicado { background-color: #28a745; color: white; }
.status-borrador { background-color: #ffc107; color: #333; }
.status-agotado { background-color: #dc3545; color: white; }

/* Condiciones */
.condition-destacado { background-color: #007bff; color: white; }
.condition-oferta { background-color: #28a745; color: white; }
.condition-liquidacion { background-color: #dc3545; color: white; }
.condition-nuevo { background-color: #17a2b8; color: white; }
.condition-exclusivo { background-color: #6f42c1; color: white; }
.condition-ninguno { background-color: #6c757d; color: white; }

.table-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px;
    border: none;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: #f8f9fa;
}

.edit-btn {
    color: #007bff;
}

.delete-btn {
    color: #dc3545;
}

/* Responsive */
@media (max-width: 768px) {
    .table-wrapper {
        overflow-x: auto;
    }

    .table {
        min-width: 800px;
    }
}

/* Estilos para columnas específicas */
.table td:nth-child(4) {  /* Columna precio */
    font-weight: 600;
    color: var(--gray-800);
}

.table td:nth-child(6) {  /* Columna stock */
    font-weight: 600;
}

.table .low-stock {
    color: var(--danger);
    font-weight: 600;
    position: relative;
}

.table .low-stock::before {
    content: "";
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--danger);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: translateY(-50%) scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        transform: translateY(-50%) scale(1);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        transform: translateY(-50%) scale(0.95);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* Estilos para botones de acciones */
.actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    padding: 0;
    min-width: 90px;
    height: 100%;
    width: 100%;
}

td.actions {
    padding: 0.75rem;
    text-align: center;
    vertical-align: middle;
    height: 80px;
    box-sizing: border-box;
    width: 120px;
    border-bottom: 1px solid var(--gray-200);
}

.actions button {
    width: 38px;
    height: 38px;
    padding: 0;
    border: none;
    border-radius: var(--radius-sm);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    box-shadow: var(--shadow-sm);
    margin: 0;
}

.actions button i {
    font-size: 1rem;
    transition: transform 0.2s ease;
}

.actions button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.actions button:active {
    transform: translateY(-1px);
}

.actions button:hover i {
    transform: scale(1.1);
}

/* Estilos específicos para botón de editar */
.actions button:not(.delete) {
    background-color: var(--primary);
    color: white;
}

.actions button:not(.delete):hover {
    background-color: var(--primary-hover);
}

/* Estilos específicos para botón de eliminar */
.actions .delete {
    background-color: var(--danger);
    color: white;
}

.actions .delete:hover {
    background-color: var(--danger-hover);
}

/* Tooltips para los botones */
.actions button::before {
    content: attr(title);
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%) scale(0.8);
    padding: 5px 10px;
    border-radius: var(--radius-sm);
    background-color: var(--gray-900);
    color: white;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-md);
}

.actions button::after {
    content: "";
    position: absolute;
    top: -14px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg) scale(0.8);
    width: 8px;
    height: 8px;
    background-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.actions button:hover::before, .actions button:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) scale(1);
}

.actions button:hover::after {
    transform: translateX(-50%) rotate(45deg) scale(1);
}

/* Estilos para paginación o resumen bajo la tabla */
.table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background-color: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
}

/* Botón para nuevo producto con diseño renovado */
.new-product-btn {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 0.85rem 1.75rem;
    border-radius: var(--radius);
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: var(--shadow-md);
    transition: all 0.25s ease;
    margin-bottom: 0;
}

/* Resto de estilos para formularios */
.form-group {
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.form-group.half-width {
    width: 48%;
}

.form-row {
    display: flex;
    justify-content: space-between;
    gap: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
    font-size: 0.92rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.85rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    color: var(--gray-800);
    background-color: var(--white);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

.form-group input:hover,
.form-group textarea:hover,
.form-group select:hover {
    border-color: var(--gray-400);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-400);
}

.form-group input[name="precio"] {
    text-align: right;
    font-family: var(--font-sans);
    letter-spacing: 0.5px;
    font-weight: 600;
    color: var(--gray-800);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.form-actions button {
    padding: 0.85rem 1.75rem;
    border: none;
    font-weight: 500;
    cursor: pointer;
    border-radius: var(--radius);
    transition: all 0.2s ease;
    font-size: 0.95rem;
}

.form-actions button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-save {
    background-color: var(--primary);
    color: white;
}

.btn-save:hover {
    background-color: var(--primary-hover);
}

.btn-cancel {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.btn-cancel:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

/* Estilos para el modal de imagen */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(5px);
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-modal.show {
    opacity: 1;
}

.image-modal-content {
    max-width: 90%;
    max-height: 90vh;
    margin: auto;
    display: block;
    box-shadow: var(--shadow-xl);
    object-fit: contain;
    border-radius: var(--radius);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.image-modal.show .image-modal-content {
    transform: scale(1);
}

.image-modal img {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 90vh;
    border-radius: var(--radius);
}

.image-modal-close {
    position: absolute;
    top: 20px;
    right: 25px;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s ease;
}

.image-modal-close:hover {
    opacity: 1;
    transform: rotate(90deg);
}

/* Estilos para los modales */
.product-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-modal.show {
    opacity: 1;
}

.modal-content {
    background: white;
    padding: 2rem;
    border-radius: var(--radius-lg);
    max-width: 550px;
    width: 90%;
    position: relative;
    box-shadow: var(--shadow-xl);
    transform: translateY(20px);
    transition: transform 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

.product-modal.show .modal-content {
    transform: translateY(0);
}

.modal-content h2 {
    color: var(--gray-800);
    margin-bottom: 1.5rem;
    font-weight: 600;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.modalClose {
    position: absolute;
    top: 1.25rem;
    right: 1.5rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-500);
    transition: all 0.2s ease;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modalClose:hover {
    color: var(--gray-900);
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

/* Contenedor principal */
.container {
    max-width: 1280px;
    margin: 130px auto 0;
    padding: 2rem;
}

/* Media queries para responsive */
@media (max-width: 1024px) {
    .container {
        padding: 1.5rem;
    }

    .admin-actions {
        flex-direction: column;
        align-items: stretch;
        margin-top: 40px;
    }

    .search-container {
        max-width: 100%;
        margin-left: 0;
        margin-top: 0.5rem;
    }

    .table th,
    .table td {
        padding: 1rem;
    }

    .table th:first-child,
    .table td:first-child {
        padding-left: 1.5rem;
    }

    .table th:last-child,
    .table td:last-child {
        padding-right: 1.5rem;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .form-group.half-width {
        width: 100%;
    }
}

/* Media query principal para móvil */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
    }

    .no-results-message {
        padding: 2rem 1rem;
    }

    .no-results-message i {
        font-size: 2.5rem;
    }

    .no-results-message p {
        font-size: 1rem;
    }

    /* Ocultar tabla y elementos no necesarios */
    .table-wrapper,
    .table thead,
    .table tbody tr {
        display: none !important;
    }

    /* Configuración del contenedor */
    .table-container {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 8px !important;
        justify-content: space-between !important;
        padding: 8px !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        background: transparent !important;
        box-shadow: none !important;
        border: none !important;
        overflow: visible !important;
        border-radius: var(--radius);
    }

    /* Limpiar estilos residuales */
    .product-card-header,
    .product-card-body,
    .product-card-row,
    .product-card-column,
    .card-cell {
        display: none !important;
    }

    /* Estilos base para tarjetas en móvil */
    .fusion-card,
    div.fusion-card,
    .table-container .fusion-card,
    body .fusion-card,
    .table-container .fusion-card[data-id] {
        width: calc(50% - 4px) !important;
        flex: 0 0 calc(50% - 4px) !important;
        float: none !important;
        margin: 0 0 8px 0 !important;
        display: block !important;
        max-width: calc(50% - 4px) !important;
        min-width: calc(50% - 4px) !important;
        box-sizing: border-box !important;
        position: relative !important;
        visibility: visible !important;
        opacity: 1 !important;
        background-color: white !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
        overflow: hidden !important;
    }

    /* Ajustes para elementos internos */
    .fusion-card-header {
        position: relative;
        width: 100%;
        height: 140px;
        overflow: hidden;
    }

    .fusion-card-header img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .fusion-content {
        padding: 10px !important;
        display: flex !important;
        flex-direction: column !important;
        flex-grow: 1 !important;
    }

    .fusion-title {
        font-size: 13px !important;
        font-weight: 600 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        margin-bottom: 6px !important;
        color: #1f2937 !important;
        line-height: 1.3 !important;
    }

    .fusion-description {
        display: none !important; /* Ocultar descripción según lo solicitado */
    }

    .fusion-metrics {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 8px !important;
        padding-bottom: 8px !important;
        border-bottom: 1px solid #e5e7eb !important;
    }

    .fusion-metric {
        flex: 1 !important;
    }

    .fusion-metric-value {
        font-size: 14px !important;
        font-weight: 700 !important;
        color: #111827 !important;
        margin: 0 !important;
    }

    .fusion-metric-label {
        font-size: 11px !important;
        color: #6b7280 !important;
    }

    .fusion-location {
        display: flex !important;
        align-items: center !important;
        color: #6b7280 !important;
        font-size: 10px !important;
        margin-bottom: 8px !important;
    }

    .fusion-location i {
        color: var(--primary) !important;
        margin-right: 6px !important;
    }

    /* Estilo para el estado del producto (disponible/último) */
    .fusion-status {
        position: absolute;
        top: 8px;
        right: 8px;
        background-color: #10b981; /* Color verde para disponible */
        color: white;
        padding: 4px 8px;
        border-radius: 30px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        z-index: 2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .fusion-status-low {
        background-color: #f59e0b; /* Color naranja para bajo stock */
    }

    /* Botones de acción */
    .fusion-card-actions {
        display: flex !important;
        width: 100% !important;
        height: auto !important;
        overflow: visible !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 1 !important;
        visibility: visible !important;
        margin-top: auto !important;
    }

    .fusion-edit-button {
        flex: 3 !important;
        width: 75% !important;
        background-color: var(--primary) !important;
        color: white !important;
        padding: 8px !important;
        justify-content: center !important;
        align-items: center !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        border: none !important;
        cursor: pointer !important;
        transition: background-color 0.2s !important;
        display: flex !important;
    }

    .fusion-delete-button {
        flex: 1 !important;
        width: 25% !important;
        background-color: var(--danger) !important;
        color: white !important;
        padding: 8px !important;
        justify-content: center !important;
        align-items: center !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        border: none !important;
        cursor: pointer !important;
        transition: background-color 0.2s !important;
        display: flex !important;
    }

    .fusion-edit-button:hover {
        background-color: var(--primary-hover) !important;
    }

    .fusion-delete-button:hover {
        background-color: var(--danger-hover) !important;
    }

    .fusion-edit-button i,
    .fusion-delete-button i {
        margin-right: 8px !important;
        display: inline-block !important;
    }

    /* Definir variables para iOS */
    html {
        --layout-init: 1;
    }
}

/* Media query para tablets y dispositivos medianos */
@media (min-width: 400px) and (max-width: 768px) {
    .fusion-card-header {
        height: 140px !important;
    }

    .fusion-content {
        padding: 12px !important;
    }

    .fusion-title {
        font-size: 14px !important;
    }

    .table-container {
        gap: 16px !important;
        padding: 10px !important;
    }
}

/* Regla específica para iPhone y dispositivos similares */
@media only screen and (min-width: 390px) and (max-width: 430px) {
    .fusion-card-header {
        height: 110px !important;
    }

    .fusion-content {
        padding: 10px !important;
    }

    .fusion-title {
        font-size: 13px !important;
    }

    .fusion-metrics {
        margin-bottom: 8px !important;
        padding-bottom: 8px !important;
    }

    .fusion-metric-value {
        font-size: 14px !important;
    }

    .fusion-location {
        font-size: 10px !important;
    }
}

/* Asegurar 2 por fila incluso en dispositivos muy pequeños */
@media (max-width: 389px) {
    .fusion-card,
    div.fusion-card,
    .table-container .fusion-card {
        width: calc(50% - 4px) !important;
        flex: 0 0 calc(50% - 4px) !important;
        max-width: calc(50% - 4px) !important;
        min-width: calc(50% - 4px) !important;
        float: none !important;
        margin: 0 0 8px 0 !important;
    }
}



.fusion-condition {
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: #4f46e5; /* Color primario (azul) para condición */
    color: white;
    padding: 4px 8px;
    border-radius: 30px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    z-index: 2;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Estilos para el contenedor de filtros */
.filter-container {
    position: fixed !important;
    top: 0 !important;
    right: -450px !important;
    width: 400px !important;
    height: 100vh !important;
    background-color: white !important;
    box-shadow: 0 0 35px rgba(0, 0, 0, 0.25) !important;
    z-index: 9999 !important;
    transition: right 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    overflow: hidden !important;
    border-left: 1px solid rgba(0,0,0,0.1) !important;
    transform: none !important;
    display: none !important;
}

.filter-container.show {
    right: 0 !important;
    display: block !important;
}

.filter-content {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 0 !important;
    background: linear-gradient(135deg, #f8fafc, #ffffff) !important;
}

.filter-header {
    padding: 1.5rem !important;
    border-bottom: 1px solid rgba(79, 70, 229, 0.2) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    background: linear-gradient(to right, #4f46e5, #7e3af2) !important;
    color: white !important;
}

.filter-header h3 {
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: white !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.btn-close-filter {
    background: rgba(255, 255, 255, 0.25) !important;
    border: none !important;
    color: white !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 50% !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    opacity: 0.9 !important;
}

.btn-close-filter:hover {
    background: rgba(255, 255, 255, 0.35) !important;
    transform: rotate(90deg) !important;
    opacity: 1 !important;
}

.filter-body {
    padding: 1.75rem !important;
    overflow-y: auto !important;
    flex: 1 !important;
}

.filter-group {
    margin-bottom: 1.75rem !important;
    position: relative !important;
}

.filter-group label {
    display: block !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-bottom: 0.7rem !important;
    font-size: 0.95rem !important;
    position: relative !important;
}

.filter-group label::after {
    content: '' !important;
    display: block !important;
    width: 40px !important;
    height: 3px !important;
    background: linear-gradient(to right, #4f46e5, transparent) !important;
    margin-top: 5px !important;
    border-radius: 2px !important;
}

.filter-row {
    display: flex !important;
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
}

.filter-row .filter-group {
    flex: 1 !important;
    margin-bottom: 0 !important;
}

.filter-group input[type="text"],
.filter-group input[type="number"],
.filter-group select {
    width: 100% !important;
    padding: 0.95rem 1.1rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    font-size: 0.95rem !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
    background-color: #f8fafc !important;
    color: #1f2937 !important;
}

.filter-group input[type="text"]::placeholder,
.filter-group input[type="number"]::placeholder {
    color: #9ca3af !important;
}

.filter-group input[type="text"]:focus,
.filter-group input[type="number"]:focus,
.filter-group select:focus {
    outline: none !important;
    border-color: #4f46e5 !important;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25) !important;
    background-color: white !important;
}

.filter-group input[type="text"]:hover,
.filter-group input[type="number"]:hover,
.filter-group select:hover {
    border-color: #6366f1 !important;
}

.filter-options {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.75rem !important;
    margin-top: 0.75rem !important;
}

.filter-checkbox {
    display: flex !important;
    align-items: center !important;
    gap: 0.6rem !important;
    padding: 0.7rem 1.1rem !important;
    background-color: #f5f7fa !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    font-size: 0.9rem !important;
}

.filter-checkbox:hover {
    background-color: #eef2ff !important;
    border-color: #6366f1 !important;
    color: #4f46e5 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.filter-checkbox input[type="checkbox"] {
    margin: 0 !important;
    accent-color: #4f46e5 !important;
    width: 18px !important;
    height: 18px !important;
    cursor: pointer !important;
}

.filter-checkbox input[type="checkbox"]:checked + span {
    font-weight: 600 !important;
    color: #4f46e5 !important;
}

.filter-actions {
    display: flex !important;
    gap: 1.25rem !important;
    margin-top: 2.5rem !important;
    padding-top: 1.5rem !important;
    border-top: 1px solid #e5e7eb !important;
}

.filter-actions button {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.6rem !important;
    padding: 1rem !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.25s ease !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Estilo específico para el botón de resetear */
.filter-actions button#resetFilters {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
    border: 1px solid #e5e7eb !important;
}

.filter-actions button#resetFilters:hover {
    background-color: #e5e7eb !important;
    color: #1f2937 !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
    transform: translateY(-2px) !important;
}

/* Estilo específico para el botón de aplicar */
.filter-actions button#applyFilters {
    background: linear-gradient(to right, #4f46e5, #6366f1) !important;
    color: white !important;
}

.filter-actions button#applyFilters:hover {
    background: linear-gradient(to right, #4338ca, #4f46e5) !important;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
    transform: translateY(-2px) !important;
}

/* Estilo para las etiquetas de los checkbox */
.filter-checkbox span {
    font-size: 0.9rem !important;
    color: #374151 !important;
    transition: all 0.2s ease !important;
}

/* Medias queries para responsividad */
@media (max-width: 576px) {
    .filter-container {
        width: 85% !important;
        right: -85% !important;
    }
}

/* Estilos para notificaciones */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    z-index: 9000;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification-success {
    background: linear-gradient(to right, #10b981, #059669);
}

.notification-error {
    background: linear-gradient(to right, #ef4444, #dc2626);
}

.notification i {
    font-size: 1.25rem;
}

.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip-container .tooltip-text {
    visibility: hidden;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--gray-900);
    color: white;
    text-align: center;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    width: max-content;
    max-width: 250px;
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.tooltip-container .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--gray-900) transparent transparent transparent;
}

.tooltip-container:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Contenedor del checkbox y su etiqueta */
.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    margin-bottom: 8px;
}

/* Estilos para el ícono de información */
.info-icon {
    color: var(--gray-400);
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.info-icon:hover {
    color: var(--primary);
}

/* Contenedor del tooltip */
.info-tooltip-container {
    position: relative;
    display: inline-block;
}

/* Estilos del tooltip */
.info-tooltip {
    display: none;
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--gray-900);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    width: max-content;
    max-width: 250px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Flecha del tooltip */
.info-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: var(--gray-900) transparent transparent transparent;
}

/* Clase activa para mostrar el tooltip */
.info-tooltip.active {
    display: block;
}

/* Estilos para el ícono de información */
.info-icon {
    color: var(--primary);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
    vertical-align: middle;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    padding: 5px;
    width: 26px;
    height: 26px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.info-icon:hover {
    color: var(--primary-hover);
    transform: scale(1.1);
    background-color: rgba(0, 123, 255, 0.2);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

/* Contenedor del tooltip */
.info-tooltip-container, .info-icon-container {
    position: relative;
    display: inline-block;
    margin-left: 8px;
    vertical-align: middle;
}

/* Estilos del tooltip */
.info-tooltip {
    --arrow-top: 100%;
    --arrow-bottom: auto;
    --arrow-border: var(--gray-900) transparent transparent transparent;

    display: none !important; /* Oculto por defecto y con !important para forzarlo */
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--gray-900);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    width: max-content;
    max-width: 300px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    text-align: left;
    line-height: 1.5;
    font-weight: normal;
    animation: tooltipFadeIn 0.3s ease-in-out;
    pointer-events: none; /* Evita que el tooltip interfiera con los clics */
}

/* Flecha del tooltip */
.info-tooltip::after {
    content: '';
    position: absolute;
    top: var(--arrow-top);
    bottom: var(--arrow-bottom);
    left: 50%;
    transform: translateX(-50%);
    border-width: 8px;
    border-style: solid;
    border-color: var(--arrow-border);
}

/* Clase activa para mostrar el tooltip */
.info-tooltip.active {
    display: block !important; /* Anular el display: none !important */
    animation: tooltipFadeIn 0.3s ease-in-out;
    z-index: 9999; /* Asegurar que esté por encima de otros elementos */
}

/* Animación para el tooltip */
@keyframes tooltipFadeIn {
    from { opacity: 0; transform: translateX(-50%) translateY(10px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

/* Ajustes específicos para diferentes contextos */
.radio-group .info-tooltip-container,
.edit-radio .info-tooltip-container {
    margin-left: 8px;
}

.radio-group .info-tooltip,
.edit-radio .info-tooltip {
    min-width: 250px;
    left: -50px;
    transform: none;
}

.radio-group .info-tooltip::after,
.edit-radio .info-tooltip::after {
    left: 60px;
    transform: none;
}

.filter-checkbox .info-tooltip-container {
    margin-left: 8px;
}

.filter-checkbox .info-tooltip {
    min-width: 250px;
    left: -50px;
    transform: none;
}

.filter-checkbox .info-tooltip::after {
    left: 60px;
    transform: none;
}

/* Ajustes para el tooltip en el label de Estado */
label .info-icon-container .info-tooltip {
    position: absolute;
    bottom: 10em;
    right: -432px;
    margin-left: -5px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    background: rgba(0, 0, 0, 0.85);
    z-index: 1000;
    width: 350px;
    max-width: 90vw;
    white-space: normal;
    text-align: left;
    overflow: visible;
}

label .info-icon-container .info-tooltip::after {
    left: 20px;
    transform: none;
}

/* Ajuste para evitar que el tooltip se corte */
.edit-group {
    position: relative;
    overflow: visible;
}
