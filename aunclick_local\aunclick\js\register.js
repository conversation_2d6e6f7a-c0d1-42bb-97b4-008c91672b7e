/* =========================================
   INICIALIZACIÓN DEL FORMULARIO DE REGISTRO
   ========================================= */
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM completamente cargado');

    // Se eliminaron todas las funciones relacionadas con tipos de negocio y categorías

    // Manejo de pasos
    const steps = document.querySelectorAll('.step');
    const formSteps = document.querySelectorAll('.form-step');

    console.log('Pasos encontrados:', steps.length);
    console.log('Pasos de formulario encontrados:', formSteps.length);

    // Inicializar los pasos al cargar la página
    formSteps.forEach((step, index) => {
        if (index === 0) {
            step.style.display = 'block';
            step.classList.add('active');
        } else {
            step.style.display = 'none';
            step.classList.remove('active');
        }
    });

    function showStep(stepNumber) {
        console.log('Intentando mostrar paso:', stepNumber);

        // Verificar que el número de paso sea válido
        if (stepNumber < 1 || stepNumber > formSteps.length) {
            console.error('Número de paso inválido:', stepNumber);
            return;
        }

        // Ocultar todos los pasos
        formSteps.forEach((step, index) => {
            console.log(`Ocultando paso ${index + 1}: ${step.id}`);
            step.style.display = 'none';
            step.classList.remove('active');
        });

        steps.forEach(step => {
            step.classList.remove('active');
        });

        // Mostrar el paso actual
        const currentStep = formSteps[stepNumber - 1];
        const currentStepIndicator = steps[stepNumber - 1];

        if (currentStep) {
            console.log(`Mostrando paso ${stepNumber}: ${currentStep.id}`);
            currentStep.style.display = 'block';
            currentStep.classList.add('active');

            // Forzar la visibilidad con !important
            currentStep.style.cssText = 'display: block !important;';
        } else {
            console.error(`No se encontró el paso ${stepNumber}`);
        }

        if (currentStepIndicator) {
            currentStepIndicator.classList.add('active');
        } else {
            console.error(`No se encontró el indicador para el paso ${stepNumber}`);
        }

        console.log('Paso mostrado:', stepNumber);

        // Verificar que el paso se haya mostrado correctamente
        setTimeout(() => {
            console.log(`Estado del paso ${stepNumber} después de mostrar:`, {
                display: currentStep ? currentStep.style.display : 'no encontrado',
                isActive: currentStep ? currentStep.classList.contains('active') : false
            });
        }, 100);
    }

    // Función validateStep1 eliminada ya que ha sido reemplazada por validateStep1Fields

    // Validación de nombres y apellidos
    const nombresInput = document.getElementById('nombres');
    const apellidosInput = document.getElementById('apellidos');

    // Validación de RUT
    const rutInput = document.getElementById('rut');

    // Función genérica para mostrar popups
    function showPopup(title, message, className) {
        // Remover cualquier popup existente primero
        const existingPopups = document.querySelectorAll('.popup-overlay');
        existingPopups.forEach(popup => {
            document.body.removeChild(popup);
        });

        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = `popup-overlay ${className}-popup-overlay`;
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = `${className}-popup-content`;
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '350px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '18px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const titleElement = document.createElement('h3');
        titleElement.textContent = title;
        titleElement.style.marginTop = '0';
        titleElement.style.marginBottom = '8px';
        titleElement.style.fontSize = '16px';
        titleElement.style.color = '#6a1b9a'; // Color morado
        titleElement.style.borderBottom = '1px solid #eee';
        titleElement.style.paddingBottom = '8px';

        const messageElement = document.createElement('p');
        messageElement.textContent = message;
        messageElement.style.fontSize = '14px';
        messageElement.style.margin = '8px 0';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(titleElement);
        popupContent.appendChild(messageElement);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Función para cerrar el popup
        const closePopup = () => {
            if (document.body.contains(popupOverlay)) {
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup
        closeButton.onclick = closePopup;
        popupOverlay.onclick = (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        };
    }

    // Función para mostrar popup de error en RUT
    function showRutErrorPopup() {
        showPopup(
            'Error en RUT',
            'El RUT debe contener solo números y opcionalmente la letra K al final. Máximo 9 caracteres en total.',
            'rut-error'
        );
    }

    // Validar RUT cuando pierde el foco
    if (rutInput) {
        // Validar mientras escribe para permitir solo números y K al final
        rutInput.addEventListener('input', function() {
            let value = this.value;

            // Eliminar cualquier carácter que no sea número o K/k
            value = value.replace(/[^0-9kK]/g, '');

            // Si hay una K/k, asegurarse de que esté al final y solo haya una
            if (value.match(/[kK]/)) {
                // Eliminar todas las K/k
                value = value.replace(/[kK]/g, '');

                // Si hay caracteres y estamos en la posición final, agregar una K
                if (value.length > 0 && this.selectionStart >= this.value.length) {
                    value += 'K';
                }
            }

            // Limitar a 9 caracteres
            if (value.length > 9) {
                value = value.substring(0, 9);
            }

            this.value = value;
        });

        // Validar cuando pierde el foco
        rutInput.addEventListener('blur', function() {
            const rut = this.value.trim();

            // Verificar que el RUT tenga al menos 1 carácter
            if (rut.length === 0) {
                this.classList.add('error');
                return;
            }

            // Verificar que solo contenga números y opcionalmente K al final
            const rutRegex = /^[0-9]+[kK]?$/;
            if (!rutRegex.test(rut)) {
                this.classList.add('error');
                showRutErrorPopup();
            } else {
                this.classList.remove('error');
            }
        });
    }

    // Función para mostrar popup de error en teléfono
    function showTelefonoErrorPopup() {
        showPopup(
            'Error en teléfono',
            'El teléfono debe contener exactamente 8 dígitos numéricos.',
            'telefono-error'
        );
    }

    // Validación del campo de teléfono
    const telefonoInput = document.getElementById('telefono');
    if (telefonoInput) {
        // Validar mientras escribe para permitir solo números
        telefonoInput.addEventListener('input', function() {
            let value = this.value;

            // Eliminar cualquier carácter que no sea número
            const numericValue = value.replace(/\D/g, '');

            // Si el valor ha cambiado, actualizar el campo
            if (value !== numericValue) {
                this.value = numericValue;
            }
        });

        // Validar cuando pierde el foco
        telefonoInput.addEventListener('blur', function() {
            const telefono = this.value.trim();

            // Verificar que el teléfono tenga exactamente 8 dígitos
            if (telefono.length !== 8 || !/^\d{8}$/.test(telefono)) {
                this.classList.add('error');
                showTelefonoErrorPopup();
            } else {
                this.classList.remove('error');
            }
        });
    }

    // Función para mostrar popup de error en apellidos
    function showApellidosErrorPopup() {
        showPopup(
            'Error en apellidos',
            'Ha de ingresar sus dos apellidos.',
            'apellidos-error'
        );
    }

    // Función para mostrar popup de error en nombres
    function showNombresErrorPopup() {
        showPopup(
            'Error en nombres',
            'Debe ingresar al menos un nombre.',
            'nombres-error'
        );
    }

    // Validar nombres cuando pierde el foco
    if (nombresInput) {
        nombresInput.addEventListener('blur', function() {
            const nombres = this.value.trim();
            if (nombres === '') {
                this.classList.add('error');
                showNombresErrorPopup();
            } else {
                this.classList.remove('error');
                // Convertir a minúsculas
                this.value = nombres.toLowerCase();
            }
        });
    }

    // Validar apellidos cuando pierde el foco
    if (apellidosInput) {
        apellidosInput.addEventListener('blur', function() {
            const apellidos = this.value.trim();
            // Verificar si hay al menos dos palabras (dos apellidos)
            const palabras = apellidos.split(/\s+/).filter(word => word.length > 0);

            if (palabras.length < 2) {
                this.classList.add('error');
                showApellidosErrorPopup();
            } else {
                this.classList.remove('error');
                // Convertir a minúsculas
                this.value = apellidos.toLowerCase();
            }
        });
    }

    // Función para mostrar popup de campos faltantes
    function showMissingFieldsPopup(missingFields) {
        // Remover cualquier popup existente primero
        const existingPopups = document.querySelectorAll('.popup-overlay');
        existingPopups.forEach(popup => {
            document.body.removeChild(popup);
        });

        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'popup-overlay missing-fields-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'missing-fields-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '350px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '18px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const title = document.createElement('h3');
        title.textContent = 'Campos incompletos';
        title.style.marginTop = '0';
        title.style.marginBottom = '8px';
        title.style.fontSize = '16px';
        title.style.color = '#6a1b9a'; // Color morado
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '8px';

        const message = document.createElement('p');
        message.textContent = 'Por favor, complete todos los campos requeridos:';
        message.style.fontSize = '14px';
        message.style.margin = '8px 0';

        const fieldsList = document.createElement('ul');
        fieldsList.style.textAlign = 'left';
        fieldsList.style.paddingLeft = '20px';
        fieldsList.style.margin = '10px 0';

        missingFields.forEach(field => {
            const item = document.createElement('li');
            item.textContent = field;
            item.style.fontSize = '13px';
            item.style.margin = '5px 0';
            fieldsList.appendChild(item);
        });

        popupContent.appendChild(closeButton);
        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupContent.appendChild(fieldsList);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Función para cerrar el popup
        const closePopup = () => {
            if (document.body.contains(popupOverlay)) {
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup
        closeButton.onclick = closePopup;
        popupOverlay.onclick = (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        };
    }

    // Función para validar todos los campos del paso 1 y actualizar el estado del botón
    function validateStep1Fields() {
        console.log('Ejecutando validateStep1Fields');
        const step1 = document.getElementById('step1');
        if (!step1) {
            console.error('No se encontró el elemento step1');
            return { isValid: false, missingFields: ['Error: No se encontró el formulario'] };
        }

        const requiredInputs = step1.querySelectorAll('input[required], select[required]');
        console.log('Campos requeridos encontrados:', requiredInputs.length);

        const step1NextButton = document.getElementById('step1-next');
        let isValid = true;
        let missingFields = [];

        // Validar campos requeridos
        requiredInputs.forEach(input => {
            const label = step1.querySelector(`label[for="${input.id}"]`);
            const labelText = label ? label.textContent : input.name;

            console.log(`Validando campo: ${input.id || input.name}, valor: "${input.value}", requerido: ${input.required}`);

            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
                missingFields.push(labelText);
                console.log(`Campo inválido: ${labelText}`);
            } else {
                input.classList.remove('error');
                console.log(`Campo válido: ${labelText}`);
            }
        });

        // Validación específica para apellidos (debe tener dos palabras)
        const apellidosInput = document.getElementById('apellidos');
        if (apellidosInput && apellidosInput.value.trim()) {
            const palabras = apellidosInput.value.trim().split(/\s+/).filter(word => word.length > 0);
            if (palabras.length < 2) {
                apellidosInput.classList.add('error');
                isValid = false;
                if (!missingFields.includes('Apellidos')) {
                    missingFields.push('Apellidos (debe ingresar dos apellidos)');
                }
                console.log('Apellidos inválidos: debe tener dos palabras');
            }
        }

        // Habilitar o deshabilitar el botón según la validación
        if (step1NextButton) {
            if (isValid) {
                step1NextButton.removeAttribute('disabled');
                step1NextButton.classList.remove('btn-disabled');
                console.log('Botón habilitado');
            } else {
                step1NextButton.setAttribute('disabled', 'disabled');
                step1NextButton.classList.add('btn-disabled');
                console.log('Botón deshabilitado');
            }
        } else {
            console.error('No se encontró el botón step1-next');
        }

        console.log('Resultado de validación:', { isValid, missingFields });
        return { isValid, missingFields };
    }

    // Agregar eventos de input a todos los campos del paso 1 para validación en tiempo real
    const step1 = document.getElementById('step1');
    if (step1) {
        const step1Inputs = step1.querySelectorAll('input, select');

        step1Inputs.forEach(input => {
            ['input', 'change', 'blur'].forEach(eventType => {
                input.addEventListener(eventType, function() {
                    validateStep1Fields();
                });
            });
        });
    }

    // Validar campos al cargar la página
    // Nota: Este evento está anidado dentro del DOMContentLoaded principal,
    // lo que podría causar problemas. Lo dejamos por compatibilidad pero
    // agregamos nuestra propia inicialización al final del evento principal.

    // Botón siguiente del paso 1
    const step1NextButton = document.getElementById('step1-next');
    if (step1NextButton) {
        // Asegurarse de que el botón no esté deshabilitado por defecto
        step1NextButton.removeAttribute('disabled');
        step1NextButton.classList.remove('btn-disabled');

        // Agregar un log para depuración
        console.log('Configurando evento click para el botón del paso 1');

        // Eliminar cualquier evento click previo para evitar duplicados
        step1NextButton.removeEventListener('click', step1NextHandler);

        // Definir el manejador de eventos como una función nombrada para poder eliminarla
        function step1NextHandler() {
            console.log('Botón del paso 1 clickeado');
            const validation = validateStep1Fields();
            console.log('Resultado de validación:', validation);

            if (validation.isValid) {
                console.log('Validación exitosa, avanzando al paso 2');
                showStep(2);
            } else {
                console.log('Validación fallida, mostrando popup de campos faltantes');
                showMissingFieldsPopup(validation.missingFields);
            }
        }

        // Agregar el evento click
        step1NextButton.addEventListener('click', step1NextHandler);

        // Agregar un evento adicional para asegurarnos de que el botón funcione
        step1NextButton.onclick = function() {
            console.log('Evento onclick alternativo activado');
            step1NextHandler();
        };
    } else {
        console.error('No se encontró el botón del paso 1');
    }

    // Botón anterior del paso 2
    document.querySelector('#step2 .btn-prev').addEventListener('click', function() {
        showStep(1);
    });

    // Botón anterior del paso 3
    document.querySelector('#step3 .btn-prev').addEventListener('click', function() {
        showStep(2);
    });

    // Botón siguiente del paso 3
    const step3NextBtn = document.getElementById('step3-next');

    // Función para verificar si todos los campos del paso 3 están completos
    function checkStep3Fields() {
        // 1. Verificar tipo de negocio
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        let tipoNegocioSeleccionado = false;
        let tipoNegocioValue = '';

        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                tipoNegocioSeleccionado = true;
                tipoNegocioValue = radio.value;
            }
        });

        if (!tipoNegocioSeleccionado) {
            return false;
        }

        // 2. Verificar descripción del negocio
        const descripcionNegocio = document.getElementById('descripcion_negocio');
        if (!descripcionNegocio.value.trim()) {
            return false;
        }

        // 3. Verificar categorías seleccionadas
        let categoriasContainer;
        if (tipoNegocioValue === 'venta') {
            categoriasContainer = document.getElementById('selected-venta-container');
        } else if (tipoNegocioValue === 'servicios') {
            categoriasContainer = document.getElementById('selected-servicios-container');
        } else if (tipoNegocioValue === 'arriendo') {
            categoriasContainer = document.getElementById('selected-arriendo-container');
        }

        // Verificar si hay categorías seleccionadas
        if (!categoriasContainer ||
            !categoriasContainer.querySelector('.selected-categories-grid') ||
            !categoriasContainer.querySelector('.selected-category-tag')) {
            return false;
        }

        return true;
    }

    // Función para actualizar el estado del botón Siguiente
    function updateStep3NextButton() {
        if (step3NextBtn) {
            if (checkStep3Fields()) {
                step3NextBtn.disabled = false;
                step3NextBtn.classList.remove('btn-disabled');
            } else {
                step3NextBtn.disabled = true;
                step3NextBtn.classList.add('btn-disabled');
            }
        }
    }

    // Deshabilitar el botón Siguiente por defecto
    if (step3NextBtn) {
        step3NextBtn.disabled = true;
        step3NextBtn.classList.add('btn-disabled');

        console.log('Botón siguiente del paso 3 encontrado');
        step3NextBtn.addEventListener('click', function() {
            console.log('Botón siguiente del paso 3 clickeado');
            if (validateStep3()) {
                console.log('Validación del paso 3 exitosa, determinando qué paso 4 mostrar');

                // Determinar qué tipo de negocio se seleccionó
                const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
                let tipoNegocioSeleccionado = '';

                tipoNegocioRadios.forEach(radio => {
                    if (radio.checked) {
                        tipoNegocioSeleccionado = radio.value;
                    }
                });

                console.log('Tipo de negocio seleccionado:', tipoNegocioSeleccionado);

                // Mostrar el paso 4 correspondiente según el tipo de negocio
                if (tipoNegocioSeleccionado === 'venta') {
                    // Mostrar paso 4 para venta de productos
                    showCustomStep('step4-productos');
                } else if (tipoNegocioSeleccionado === 'servicios' || tipoNegocioSeleccionado === 'arriendo') {
                    // Mostrar paso 4 para servicios o arriendos
                    showCustomStep('step4-servicios');
                } else {
                    console.error('Tipo de negocio no reconocido');
                }
            } else {
                console.log('Validación del paso 3 fallida');
            }
        });
    } else {
        console.error('Botón siguiente del paso 3 no encontrado');
    }

    // Agregar eventos para actualizar el estado del botón cuando cambian los campos

    // 1. Evento para tipo de negocio
    // Usar los radio buttons ya definidos anteriormente
    tipoNegocioRadios.forEach(radio => {
        radio.addEventListener('change', updateStep3NextButton);
    });

    // 2. Evento para descripción del negocio
    const descripcionNegocio = document.getElementById('descripcion_negocio');
    if (descripcionNegocio) {
        descripcionNegocio.addEventListener('input', updateStep3NextButton);
    }

    // 3. Evento para categorías (se actualiza cuando se confirma la selección)
    document.getElementById('categoriesPopupConfirm').addEventListener('click', function() {
        // Esperar un momento para que se actualice el DOM con las categorías seleccionadas
        setTimeout(updateStep3NextButton, 100);
    });

    // Verificar el estado del botón cuando se muestra el paso 3
    document.getElementById('step2-next').addEventListener('click', function() {
        // Esperar un momento para que se muestre el paso 3
        setTimeout(updateStep3NextButton, 100);
    });

    /**
     * Muestra un paso específico por su ID
     * @param {string} stepId - El ID del paso a mostrar
     */
    function showCustomStep(stepId) {
        // Ocultar todos los pasos
        formSteps.forEach(step => {
            step.style.display = 'none';
            step.classList.remove('active');
        });
        steps.forEach(step => step.classList.remove('active'));

        // Mostrar el paso específico
        const customStep = document.getElementById(stepId);
        if (customStep) {
            customStep.style.display = 'block';
            customStep.classList.add('active');

            // Activar el indicador del paso 4
            steps[3].classList.add('active');

            // Si estamos mostrando el paso 4 (productos o servicios), verificar selección de plan
            if (stepId === 'step4-productos' || stepId === 'step4-servicios') {
                // Verificar si hay un plan seleccionado y actualizar estado de los botones
                checkPlanSelection();
            }

            console.log('Mostrando paso personalizado:', stepId);
        } else {
            console.error('Paso personalizado no encontrado:', stepId);
        }
    }

    // Botón anterior del paso 4A (productos)
    document.querySelector('#step4-productos .btn-prev').addEventListener('click', function() {
        showStep(3);
    });

    // Botón anterior del paso 4B (servicios)
    document.querySelector('#step4-servicios .btn-prev').addEventListener('click', function() {
        showStep(3);
    });

    // Botón siguiente del paso 4A (productos) para ir al paso 5
    document.getElementById('submit-form').addEventListener('click', function() {
        // Verificar si se seleccionó un plan (aunque el botón ya debería estar deshabilitado si no hay selección)
        const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

        if (!planSeleccionado) {
            alert('Por favor, seleccione un plan antes de continuar.');
            return;
        }

        // Si se seleccionó el plan gratuito, mostrar el popup de bienvenida
        if (planSeleccionado.value === 'gratuita') {
            showWelcomePopup();
        } else {
            // Si se seleccionó plan normal o premium, mostrar el paso 5
            showCustomStep('step5-pago');
            // Activar el indicador del paso 5
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => step.classList.remove('active'));
            if (steps[4]) steps[4].classList.add('active');
        }
    });

    // Botón siguiente del paso 4B (servicios) para ir al paso 5
    document.getElementById('submit-form-servicios').addEventListener('click', function() {
        // Verificar si se seleccionó un plan (aunque el botón ya debería estar deshabilitado si no hay selección)
        const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

        if (!planSeleccionado) {
            alert('Por favor, seleccione un plan antes de continuar.');
            return;
        }

        // Si se seleccionó el plan gratuito, mostrar el popup de bienvenida
        if (planSeleccionado.value === 'gratuita') {
            showWelcomePopup();
        } else {
            // Si se seleccionó plan normal o premium, mostrar el paso 5
            showCustomStep('step5-pago');
            // Activar el indicador del paso 5
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => step.classList.remove('active'));
            if (steps[4]) steps[4].classList.add('active');
        }
    });

    // Función para mostrar el popup de bienvenida
    function showWelcomePopup() {
        // Crear un popup simple con el mensaje antiguo
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '300px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const title = document.createElement('h3');
        title.textContent = '45.236.129.200 dice';
        title.style.marginTop = '0';
        title.style.marginBottom = '15px';
        title.style.fontSize = '14px';
        title.style.fontWeight = 'normal';
        title.style.color = '#333';
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '8px';

        const message = document.createElement('p');
        message.textContent = 'Gracias por registrarte con el plan gratuito.';
        message.style.fontSize = '14px';
        message.style.margin = '15px 0';

        const acceptButton = document.createElement('button');
        acceptButton.textContent = 'Aceptar';
        acceptButton.style.backgroundColor = '#0078d7';
        acceptButton.style.color = 'white';
        acceptButton.style.border = 'none';
        acceptButton.style.padding = '5px 15px';
        acceptButton.style.borderRadius = '2px';
        acceptButton.style.cursor = 'pointer';
        acceptButton.style.fontSize = '14px';
        acceptButton.style.float = 'right';

        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupContent.appendChild(acceptButton);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Función para cerrar el popup y enviar el formulario
        const closePopup = () => {
            if (document.body.contains(popupOverlay)) {
                document.body.removeChild(popupOverlay);
                document.getElementById('registerForm').submit();
            }
        };

        // Configurar eventos para cerrar el popup
        acceptButton.onclick = closePopup;
    }

    // Obtener el botón siguiente del paso 2
    const step2NextBtn = document.getElementById('step2-next');

    // Deshabilitar el botón por defecto
    if (step2NextBtn) {
        step2NextBtn.disabled = true;
        step2NextBtn.classList.add('btn-disabled');
    }

    // Función para verificar si todos los campos obligatorios del paso 2 están completos
    function checkStep2Fields() {
        // Obtener todos los campos obligatorios del paso 2
        const step2 = document.getElementById('step2');
        if (!step2) return false;

        const requiredInputs = step2.querySelectorAll('input[required], select[required]');
        let isValid = true;

        // Verificar que todos los campos obligatorios tengan valor
        requiredInputs.forEach(input => {
            // Excluir el correo de respaldo que es opcional
            if (input.name !== 'tipo_negocio' && input.id !== 'backup_email' && !input.value.trim()) {
                isValid = false;
            }
        });

        // Verificar formato de contraseña
        if (password && (!password.value.trim() || !validarFormatoPassword(password.value))) {
            isValid = false;
        }

        // Verificar que las contraseñas coincidan
        if (password && confirmPassword &&
            (password.value.trim() !== confirmPassword.value.trim())) {
            isValid = false;
        }

        // Verificar formato de correo electrónico principal
        if (emailInput && (!emailInput.value.trim() || !isValidEmail(emailInput.value))) {
            isValid = false;
        }

        // Verificar que se haya seleccionado una opción de local físico
        const localFisicoRadios = document.getElementsByName('local_fisico');
        let localFisicoSeleccionado = false;

        localFisicoRadios.forEach(radio => {
            if (radio.checked) {
                localFisicoSeleccionado = true;
            }
        });

        if (!localFisicoSeleccionado) {
            isValid = false;
        }

        // IMPORTANTE: El correo de respaldo es completamente opcional
        // No debe afectar la validación aunque tenga formato incorrecto

        // Actualizar estado del botón
        if (step2NextBtn) {
            if (isValid) {
                step2NextBtn.disabled = false;
                step2NextBtn.classList.remove('btn-disabled');
            } else {
                step2NextBtn.disabled = true;
                step2NextBtn.classList.add('btn-disabled');
            }
        }

        return isValid;
    }

    // Agregar eventos para verificar campos cuando cambian
    const step2 = document.getElementById('step2');
    if (step2) {
        const allInputs = step2.querySelectorAll('input, select');
        allInputs.forEach(input => {
            input.addEventListener('input', checkStep2Fields);
            input.addEventListener('change', checkStep2Fields);
            input.addEventListener('blur', checkStep2Fields);
        });
    }

    // Verificar campos cuando se muestra el paso 2
    document.querySelector('#step1 .btn-next').addEventListener('click', function() {
        // Esperar un momento para que se muestre el paso 2
        setTimeout(checkStep2Fields, 100);
    });

    // Botón siguiente del paso 2
    if (step2NextBtn) {
        step2NextBtn.addEventListener('click', function() {
            if (validateStep2()) {
                showStep(3);
            }
        });
    }

    // Botón anterior del paso 5
    document.querySelector('#step5-pago .btn-prev').addEventListener('click', function() {
        // Determinar a qué paso 4 volver según el tipo de negocio seleccionado
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        let tipoNegocioSeleccionado = '';

        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                tipoNegocioSeleccionado = radio.value;
            }
        });

        if (tipoNegocioSeleccionado === 'venta') {
            showCustomStep('step4-productos');
        } else {
            showCustomStep('step4-servicios');
        }
    });

    // Botón finalizar del paso 5
    document.getElementById('finalizar-registro').addEventListener('click', function() {
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');

        // Verificar si se ha seleccionado una opción
        if (!facturaRadio.checked && !boletaRadio.checked) {
            alert('Por favor, seleccione si requiere factura o boleta antes de continuar.');
            return;
        }

        // Si seleccionó factura, verificar que todos los campos estén llenos
        if (facturaRadio.checked && !validateFacturaFields()) {
            alert('Por favor, complete todos los campos de facturación antes de continuar.');
            return;
        }

        // Si todo está correcto, enviar el formulario
        document.getElementById('registerForm').submit();
    });

    // Obtener el botón finalizar del paso 5
    const finalizarRegistroBtn = document.getElementById('finalizar-registro');

    // Deshabilitar el botón finalizar por defecto
    if (finalizarRegistroBtn) {
        finalizarRegistroBtn.disabled = true;
        finalizarRegistroBtn.classList.add('btn-disabled');
    }

    // Función para validar los campos de factura
    function validateFacturaFields() {
        // Obtener todos los campos del formulario de factura
        const empresa = document.getElementById('empresa');
        const rutEmpresa = document.getElementById('rut_empresa');
        const direccionEmpresa = document.getElementById('direccion_empresa');
        const giroEmpresa = document.getElementById('giro_empresa');
        const telefonoEmpresa = document.getElementById('telefono_empresa');
        const correoEmpresa = document.getElementById('correo_empresa');

        // Verificar que todos los campos estén llenos
        if (empresa && rutEmpresa && direccionEmpresa && giroEmpresa && telefonoEmpresa && correoEmpresa) {
            return empresa.value.trim() !== '' &&
                   rutEmpresa.value.trim() !== '' &&
                   direccionEmpresa.value.trim() !== '' &&
                   giroEmpresa.value.trim() !== '' &&
                   telefonoEmpresa.value.trim() !== '' &&
                   correoEmpresa.value.trim() !== '';
        }

        return false;
    }

    // Función para verificar la selección de documento y habilitar/deshabilitar el botón finalizar
    function checkDocumentoSelection() {
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');

        if (finalizarRegistroBtn) {
            if (boletaRadio && boletaRadio.checked) {
                // Si seleccionó boleta, habilitar el botón
                finalizarRegistroBtn.disabled = false;
                finalizarRegistroBtn.classList.remove('btn-disabled');
            } else if (facturaRadio && facturaRadio.checked) {
                // Si seleccionó factura, verificar que todos los campos estén llenos
                if (validateFacturaFields()) {
                    finalizarRegistroBtn.disabled = false;
                    finalizarRegistroBtn.classList.remove('btn-disabled');
                } else {
                    finalizarRegistroBtn.disabled = true;
                    finalizarRegistroBtn.classList.add('btn-disabled');
                }
            } else {
                // Si no ha seleccionado ninguna opción, deshabilitar el botón
                finalizarRegistroBtn.disabled = true;
                finalizarRegistroBtn.classList.add('btn-disabled');
            }
        }
    }

    // Mostrar/ocultar campos de facturación según la selección
    const facturaRadio = document.getElementById('factura');
    const boletaRadio = document.getElementById('boleta');
    const facturaFields = document.getElementById('factura-fields');

    if (facturaRadio && boletaRadio && facturaFields) {
        facturaRadio.addEventListener('change', function() {
            if (this.checked) {
                facturaFields.style.display = 'block';
                // Verificar campos al seleccionar factura
                checkDocumentoSelection();
            }
        });

        boletaRadio.addEventListener('change', function() {
            if (this.checked) {
                facturaFields.style.display = 'none';
                // Habilitar botón al seleccionar boleta
                checkDocumentoSelection();
            }
        });

        // Agregar evento de cambio a los campos de factura
        const facturaInputs = facturaFields.querySelectorAll('input');
        facturaInputs.forEach(input => {
            input.addEventListener('input', checkDocumentoSelection);
        });
    }

    // Verificar selección al cargar la página o al mostrar el paso 5
    document.addEventListener('DOMContentLoaded', function() {
        checkDocumentoSelection();
    });

    // Verificar selección cuando se muestra el paso 5
    // Modificamos la función existente en lugar de crear una nueva
    const originalShowCustomStep = showCustomStep;
    showCustomStep = function(stepId) {
        // Llamar a la implementación original
        originalShowCustomStep(stepId);

        // Si estamos mostrando el paso 5, verificar selección de documento
        if (stepId === 'step5-pago') {
            checkDocumentoSelection();
        }
    }

    // Botón anterior del paso 2
    document.querySelector('#step2 .btn-prev').addEventListener('click', function() {
        showStep(1);
    });

    // Validación de campos de teléfono del negocio
    const telefonoNegocio = document.getElementById('telefono_negocio');
    const whatsappNegocio = document.getElementById('whatsapp_negocio');

    // Función para validar formato de teléfono chileno
    function validarTelefonoChileno(telefono) {
        // Debe ser solo números
        if (!/^\d+$/.test(telefono)) {
            return false;
        }

        // Debe tener entre 8 y 9 dígitos
        if (telefono.length < 8 || telefono.length > 9) {
            return false;
        }

        // Si tiene 9 dígitos, debe comenzar con 9 (celular)
        if (telefono.length === 9 && telefono.charAt(0) !== '9') {
            return false;
        }

        // Si tiene 8 dígitos, debe comenzar con un código de área válido
        // Códigos de área de Chile: 2 (Santiago), 32, 33, 34, 35, 41, 42, 43, 45, 51, 52, 53, 55, 57, 58, 61, 63, 64, 65, 67, 71, 72, 73, 75
        if (telefono.length === 8) {
            const codigosArea = ['2', '32', '33', '34', '35', '41', '42', '43', '45', '51', '52', '53', '55', '57', '58', '61', '63', '64', '65', '67', '71', '72', '73', '75'];
            const primerosDosDigitos = telefono.substring(0, 2);
            const primerDigito = telefono.charAt(0);

            // Verificar si comienza con algún código de área válido
            if (!codigosArea.includes(primerosDosDigitos) && !codigosArea.includes(primerDigito)) {
                return false;
            }
        }

        return true;
    }

    // Función para aplicar validación a un campo de teléfono
    function configurarValidacionTelefono(inputElement) {
        if (!inputElement) return;

        // Permitir solo números al escribir
        inputElement.addEventListener('input', function() {
            // Remover caracteres no numéricos
            this.value = this.value.replace(/\D/g, '');

            // Validar el formato
            if (this.value.length > 0) {
                if (validarTelefonoChileno(this.value)) {
                    this.classList.remove('error');
                    this.classList.add('valid');
                } else {
                    this.classList.remove('valid');
                    this.classList.add('error');
                }
            } else {
                // Si está vacío, quitar ambas clases
                this.classList.remove('error', 'valid');
            }
        });

        // Validar al perder el foco
        inputElement.addEventListener('blur', function() {
            if (this.value.length > 0 && !validarTelefonoChileno(this.value)) {
                this.classList.add('error');
                this.classList.remove('valid');
            }
        });
    }

    // Configurar validación para ambos campos de teléfono
    configurarValidacionTelefono(telefonoNegocio);
    configurarValidacionTelefono(whatsappNegocio);

    // Remover errores al escribir
    document.querySelectorAll('input, select').forEach(input => {
        // Excluir los campos de teléfono que ya tienen su propia validación
        if (input.id !== 'telefono_negocio' && input.id !== 'whatsapp_negocio') {
            input.addEventListener('input', function() {
                this.classList.remove('error');
                if (this.type === 'radio') {
                    document.querySelectorAll(`input[name="${this.name}"]`).forEach(radio => {
                        radio.parentElement.classList.remove('error');
                    });
                }
            });
        }
    });

    // Toggle contraseña
    const togglePassword = document.querySelectorAll('.toggle-password');
    togglePassword.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });
    });

    // Validación de contraseñas
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');

    // Función para validar formato de contraseña
    function validarFormatoPassword(pass) {
        // Debe tener al menos 8 caracteres, una mayúscula y un número
        const regex = /^(?=.*[A-Z])(?=.*\d).{8,}$/;
        return regex.test(pass);
    }

    // Validar formato de contraseña mientras se escribe
    password.addEventListener('input', function() {
        if (this.value && !validarFormatoPassword(this.value)) {
            this.classList.add('error');
        } else {
            this.classList.remove('error');
        }
    });

    // Validar formato de contraseña cuando pierde el foco
    password.addEventListener('blur', function() {
        if (this.value && !validarFormatoPassword(this.value)) {
            this.classList.add('error');
            // Mostrar popup de error de formato de contraseña
            showPasswordFormatPopup();
        } else {
            this.classList.remove('error');
        }
    });

    // Función para mostrar popup de formato de contraseña inválido
    function showPasswordFormatPopup() {
        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'password-format-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'password-format-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '350px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '18px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const title = document.createElement('h3');
        title.textContent = 'Formato de contraseña inválido';
        title.style.marginTop = '0';
        title.style.marginBottom = '8px';
        title.style.fontSize = '16px';
        title.style.color = '#6a1b9a'; // Color morado
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '8px';

        const message = document.createElement('p');
        message.textContent = 'La contraseña debe tener al menos 8 caracteres, incluir al menos una letra mayúscula y un número.';
        message.style.fontSize = '14px';
        message.style.margin = '8px 0';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Variable para controlar si ya se cerró el popup
        let popupClosed = false;

        // Función para cerrar el popup
        const closePopup = () => {
            if (!popupClosed) {
                popupClosed = true;
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup (una sola vez)
        closeButton.addEventListener('click', closePopup);
        popupOverlay.addEventListener('click', (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        });
    }

    // Validar coincidencia de contraseñas
    confirmPassword.addEventListener('blur', function() {
        if (this.value && this.value !== password.value) {
            this.classList.add('error');
            showPasswordMismatchPopup();
        } else {
            this.classList.remove('error');
        }
    });

    // Función para mostrar popup de contraseñas no coincidentes
    function showPasswordMismatchPopup() {
        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'password-mismatch-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'password-mismatch-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '350px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '18px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const title = document.createElement('h3');
        title.textContent = 'Las contraseñas no coinciden';
        title.style.marginTop = '0';
        title.style.marginBottom = '8px';
        title.style.fontSize = '16px';
        title.style.color = '#6a1b9a'; // Color morado
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '8px';

        const message = document.createElement('p');
        message.textContent = 'Por favor, asegúrese de que ambas contraseñas sean idénticas.';
        message.style.fontSize = '14px';
        message.style.margin = '8px 0';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Variable para controlar si ya se cerró el popup
        let popupClosed = false;

        // Función para cerrar el popup
        const closePopup = () => {
            if (!popupClosed) {
                popupClosed = true;
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup (una sola vez)
        closeButton.addEventListener('click', closePopup);
        popupOverlay.addEventListener('click', (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        });
    }

    // Eliminada la función showPopup que no se utilizaba

    // Configurar validación para el campo de correo electrónico principal
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            validarFormatoEmail(this);
        });

        emailInput.addEventListener('blur', function() {
            validarFormatoEmail(this, true);
        });
    }

    // Configurar validación para el campo de correo electrónico de respaldo
    // Este campo es opcional, por lo que solo mostraremos feedback visual
    // pero no bloqueará el avance al siguiente paso
    const backupEmailInput = document.getElementById('backup_email');
    if (backupEmailInput) {
        backupEmailInput.addEventListener('input', function() {
            // Solo validar si hay contenido
            if (this.value.trim() !== '') {
                // Solo aplicar estilos visuales, sin mostrar popup
                if (!isValidEmail(this.value)) {
                    this.classList.add('error');
                    this.classList.remove('valid');
                } else {
                    this.classList.remove('error');
                    this.classList.add('valid');
                }
            } else {
                this.classList.remove('error', 'valid');
            }
        });

        backupEmailInput.addEventListener('blur', function() {
            // No mostrar popup ni bloquear avance, solo feedback visual
            if (this.value.trim() !== '') {
                if (!isValidEmail(this.value)) {
                    this.classList.add('error');
                    this.classList.remove('valid');
                } else {
                    this.classList.remove('error');
                    this.classList.add('valid');
                }
            } else {
                this.classList.remove('error', 'valid');
            }
        });
    }

    // Función para validar el formato de email y mostrar errores
    function validarFormatoEmail(inputElement, mostrarMensaje = false) {
        if (inputElement.value.trim() === '') {
            inputElement.classList.remove('error', 'valid');
            return;
        }

        if (!isValidEmail(inputElement.value)) {
            inputElement.classList.add('error');
            inputElement.classList.remove('valid');

            if (mostrarMensaje) {
                showEmailFormatPopup();
            }
            return false;
        } else {
            inputElement.classList.remove('error');
            inputElement.classList.add('valid');
            return true;
        }
    }

    // Función para mostrar popup de formato de correo inválido
    function showEmailFormatPopup() {
        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'email-format-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'email-format-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '15px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '350px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '5px';
        closeButton.style.right = '5px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '18px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const title = document.createElement('h3');
        title.textContent = 'Formato de correo inválido';
        title.style.marginTop = '0';
        title.style.marginBottom = '8px';
        title.style.fontSize = '16px';
        title.style.color = '#6a1b9a'; // Color morado
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '8px';

        const message = document.createElement('p');
        message.textContent = 'Por favor, ingrese un correo electrónico válido (ejemplo: <EMAIL>).';
        message.style.fontSize = '14px';
        message.style.margin = '8px 0';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Variable para controlar si ya se cerró el popup
        let popupClosed = false;

        // Función para cerrar el popup
        const closePopup = () => {
            if (!popupClosed) {
                popupClosed = true;
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup (una sola vez)
        closeButton.addEventListener('click', closePopup);
        popupOverlay.addEventListener('click', (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        });
    }

    // Validación del paso 2
    function validateStep2() {
        const step2 = document.getElementById('step2');
        const requiredInputs = step2.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredInputs.forEach(input => {
            // Excluir los inputs de tipo_negocio que ahora están en el paso 3
            if (input.name !== 'tipo_negocio' && !input.value.trim()) {
                input.classList.add('error');
                isValid = false;
            } else {
                input.classList.remove('error');
            }
        });

        // Validar formato de contraseña
        if (password && password.value.trim() !== '') {
            if (!validarFormatoPassword(password.value)) {
                password.classList.add('error');
                isValid = false;
                // Mostrar un popup con el error de formato de contraseña
                showPasswordFormatPopup();
            }
        }

        // Validar que las contraseñas coincidan
        if (password && confirmPassword && password.value.trim() !== '' && confirmPassword.value.trim() !== '') {
            if (password.value !== confirmPassword.value) {
                confirmPassword.classList.add('error');
                isValid = false;
                showPasswordMismatchPopup();
            }
        }

        // Validar el correo electrónico principal
        const emailInput = document.getElementById('email');
        if (emailInput && emailInput.value.trim() !== '') {
            if (!isValidEmail(emailInput.value)) {
                emailInput.classList.add('error');
                isValid = false;
                showEmailFormatPopup();
            }
        }

        // Aplicar estilo visual al email de respaldo si tiene formato incorrecto,
        // pero NO bloquear el avance al siguiente paso ya que es opcional
        const backupEmail = document.getElementById('backup_email');
        if (backupEmail && backupEmail.value.trim() !== '') {
            if (!isValidEmail(backupEmail.value)) {
                backupEmail.classList.add('error');
                // No afectamos isValid porque es un campo opcional
            } else {
                backupEmail.classList.remove('error');
                backupEmail.classList.add('valid');
            }
        }

        // Validar teléfonos del negocio si tienen algún valor
        const telefonoNegocio = document.getElementById('telefono_negocio');
        const whatsappNegocio = document.getElementById('whatsapp_negocio');

        if (telefonoNegocio && telefonoNegocio.value.trim() !== '') {
            if (!validarTelefonoChileno(telefonoNegocio.value)) {
                telefonoNegocio.classList.add('error');
                isValid = false;
                alert('El formato del teléfono del negocio no es válido. Debe ser un número chileno válido.');
            }
        }

        if (whatsappNegocio && whatsappNegocio.value.trim() !== '') {
            if (!validarTelefonoChileno(whatsappNegocio.value)) {
                whatsappNegocio.classList.add('error');
                isValid = false;
                alert('El formato del WhatsApp del negocio no es válido. Debe ser un número chileno válido.');
            }
        }

        return isValid;
    }

    // Función auxiliar para validar formato de email
    function isValidEmail(email) {
        // Expresión regular más completa para validar correos electrónicos
        // Verifica:
        // - Caracteres válidos antes del @
        // - Dominio válido después del @
        // - Extensión de dominio válida (.com, .org, etc.)
        // - No permite espacios ni caracteres especiales no permitidos
        const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return regex.test(email);
    }

    // Función para mostrar un popup de error con los campos faltantes
    function showMissingFieldsPopup(missingFields) {
        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '20px';
        popupContent.style.borderRadius = '5px';
        popupContent.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.3)';
        popupContent.style.maxWidth = '400px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '10px';
        closeButton.style.right = '10px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '20px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';

        const title = document.createElement('h3');
        title.textContent = 'Campos faltantes';
        title.style.marginTop = '0';
        title.style.color = '#6a1b9a';
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '10px';

        const message = document.createElement('p');
        message.textContent = 'Por favor, complete los siguientes campos antes de continuar:';

        const list = document.createElement('ul');
        list.style.paddingLeft = '20px';
        list.style.marginBottom = '20px';

        missingFields.forEach(field => {
            const item = document.createElement('li');
            item.textContent = field;
            item.style.margin = '5px 0';
            list.appendChild(item);
        });

        const okButton = document.createElement('button');
        okButton.textContent = 'Entendido';
        okButton.style.backgroundColor = '#6a1b9a';
        okButton.style.color = 'white';
        okButton.style.border = 'none';
        okButton.style.padding = '8px 16px';
        okButton.style.borderRadius = '4px';
        okButton.style.cursor = 'pointer';
        okButton.style.display = 'block';
        okButton.style.margin = '0 auto';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupContent.appendChild(list);
        popupContent.appendChild(okButton);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Configurar eventos para cerrar el popup
        closeButton.addEventListener('click', () => {
            document.body.removeChild(popupOverlay);
        });

        okButton.addEventListener('click', () => {
            document.body.removeChild(popupOverlay);
        });

        popupOverlay.addEventListener('click', (event) => {
            if (event.target === popupOverlay) {
                document.body.removeChild(popupOverlay);
            }
        });
    }

    // Validación del paso 3
    function validateStep3() {
        console.log('Iniciando validación del paso 3');
        const missingFields = [];
        let isValid = true;

        // 1. Validar que se haya seleccionado un tipo de negocio
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        let tipoNegocioSeleccionado = false;
        let tipoNegocioValue = '';

        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                tipoNegocioSeleccionado = true;
                tipoNegocioValue = radio.value;
            }
        });

        if (!tipoNegocioSeleccionado) {
            // Marcar error en los radio buttons
            tipoNegocioRadios.forEach(radio => {
                radio.parentElement.classList.add('error');
            });
            isValid = false;
            missingFields.push('Tipo de negocio');
        } else {
            tipoNegocioRadios.forEach(radio => {
                radio.parentElement.classList.remove('error');
            });
        }

        // 2. Validar que se haya ingresado una descripción del negocio
        const descripcionNegocio = document.getElementById('descripcion_negocio');
        if (!descripcionNegocio.value.trim()) {
            descripcionNegocio.classList.add('error');
            isValid = false;
            missingFields.push('Descripción del negocio');
        } else {
            descripcionNegocio.classList.remove('error');
        }

        // 3. Validar que se hayan seleccionado categorías
        // Determinar qué contenedor de categorías verificar según el tipo de negocio
        let categoriasContainer;
        if (tipoNegocioValue === 'venta') {
            categoriasContainer = document.getElementById('selected-venta-container');
        } else if (tipoNegocioValue === 'servicios') {
            categoriasContainer = document.getElementById('selected-servicios-container');
        } else if (tipoNegocioValue === 'arriendo') {
            categoriasContainer = document.getElementById('selected-arriendo-container');
        }

        // Verificar si hay categorías seleccionadas
        if (categoriasContainer && (!categoriasContainer.querySelector('.selected-categories-grid') ||
            !categoriasContainer.querySelector('.selected-category-tag'))) {
            // No hay categorías seleccionadas
            document.getElementById('selector-generico').classList.add('error');
            isValid = false;
            missingFields.push('Categorías del negocio');
        } else {
            document.getElementById('selector-generico').classList.remove('error');
        }

        // Si hay campos faltantes, mostrar el popup
        if (!isValid) {
            showMissingFieldsPopup(missingFields);
        }

        console.log('Validación del paso 3 completada:', isValid ? 'exitosa' : 'fallida');
        return isValid;
    }

    // Inicialización final para asegurar que el botón del paso 1 funcione correctamente
    console.log('Realizando inicialización final del formulario');

    // Asegurarse de que el botón del paso 1 esté habilitado y funcione
    const finalStep1Button = document.getElementById('step1-next');
    if (finalStep1Button) {
        console.log('Inicializando botón del paso 1 al final del evento DOMContentLoaded');

        // Remover cualquier atributo disabled
        finalStep1Button.removeAttribute('disabled');
        finalStep1Button.classList.remove('btn-disabled');

        // Agregar un manejador de eventos directo
        finalStep1Button.onclick = function(event) {
            console.log('Botón del paso 1 clickeado (manejador final)');
            event.preventDefault();

            // Validar campos
            const validation = validateStep1Fields();

            // Si es válido, avanzar al siguiente paso
            if (validation.isValid) {
                console.log('Validación exitosa en manejador final, avanzando al paso 2');
                showStep(2);
            } else {
                console.log('Validación fallida en manejador final');
                showMissingFieldsPopup(validation.missingFields);
            }

            return false;
        };

        // Verificar el estado del botón
        console.log('Estado final del botón del paso 1:', {
            id: finalStep1Button.id,
            disabled: finalStep1Button.disabled,
            hasDisabledAttribute: finalStep1Button.hasAttribute('disabled'),
            hasDisabledClass: finalStep1Button.classList.contains('btn-disabled'),
            display: window.getComputedStyle(finalStep1Button).display,
            visibility: window.getComputedStyle(finalStep1Button).visibility,
            clickHandlers: finalStep1Button.onclick ? 'Tiene onclick' : 'Sin onclick'
        });
    } else {
        console.error('No se encontró el botón del paso 1 en la inicialización final');
    }
});













