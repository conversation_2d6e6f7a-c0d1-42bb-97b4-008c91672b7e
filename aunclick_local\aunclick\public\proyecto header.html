<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <title>MercadoLibre Header Clone</title>
    <style>
/* Variables globales del tema */
:root {
    --yellow: #fff159;
    --green: #4CAF50;
    --green-dark: #50ad55;
    --green-light: #81C784;
    --yellow-light: #FFF9C4;
    --text-primary: rgba(0,0,0,.9);
    --text-secondary: rgba(0,0,0,.55);
    --max-width: 1200px;
}

/* Reset básico de elementos */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Estilos base del documento */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.5;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Contenedor principal */
.container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 16px;
}

/* Estilos del header principal */
.header {
    background: linear-gradient(45deg, var(--green) 40%, var(--yellow-light) 90%);
    width: 100%;
}

/* Sección superior del header */
.header-top {
    padding: 8px 0;
}

.header-top .container {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 24px;
    align-items: center;
}

/* Estilos del logo */
.logo {
    padding: 8px 0;
}

.logo img {
    height: 34px;
    width: auto;
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-family: 'Montserrat', sans-serif;
}

.logo-text .city {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    letter-spacing: -0.5px;
}

.logo-text .tagline {
    font-size: 14px;
    color: var(--blue);
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Estilos de la barra de búsqueda */
.search-bar {
    position: relative;
    max-width: 600px;
    width: 100%;
}

.search-bar input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #000;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,.2);
    font-size: 16px;
}

.search-button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    padding: 0 12px;
    border: none;
    background: none;
    cursor: pointer;
    color: #000;
}

/* Banner promocional */
.promo-banner img {
    height: 39px;
    width: auto;
}

/* Navegación principal */
.header-nav {
    background: linear-gradient(-45deg, var(--green-dark) 30%, var(--green-light) 100%);
    padding: 8px 0;
    border-top: 1px solid rgba(0,0,0,.1);
}

.header-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Listas de navegación */
.nav-categories, .nav-account {
    display: flex;
    list-style: none;
    gap: 10px;
}

.nav-categories {
    justify-content: center;
    flex: 1;
    gap: 2px;
    align-items: center;
    padding: 0;
    margin: 0;
    position: relative;
}

/* Enlaces de navegación */
.nav-categories li a, .nav-account li a {
    color: white;
    font-weight: 500;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
}

/* Efectos hover de navegación */
.nav-categories li a:hover, .nav-account li a:hover {
    color: #ffff80;
    text-shadow: 0 0 2px rgba(0,0,0,0.2);
}

/* Elemento de ubicación */
.location-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    color: white;
    margin-left: -20px;
}

.location-item i {
    color: white;
    margin-right: 2px;
}

/* Efectos hover de ubicación */
.location-item:hover, .location-item:hover i {
    color: #FFEB3B;
    transition: color 0.3s ease;
}

/* Menú desplegable de categorías */
.category-dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
    display: none;
    position: fixed;
    background: #ffffff;
    min-width: 180px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    z-index: 9999;
    border-radius: 4px;
    padding: 4px 0;
    margin-top: 10px;
    border: 2px solid #f7d618;
    text-align: left;
}

/* Elementos del menú desplegable */
.dropdown-content li {
    padding: 4px 15px;
    list-style: none;
}

.dropdown-content li:hover {
    background-color: #f5f5f5;
}

.dropdown-content li a {
    color: #4e6444;
    font-weight: 500;
    text-align: left;
    display: block;
    line-height: 1.7;
}

/* Estado activo del menú desplegable */
.dropdown-content.show {
    display: block !important;
}

/* Icono del menú móvil */
.menu-icon {
    display: none;
    font-size: 24px;
    color: var(--text-primary);
    cursor: pointer;
}

.mobile-menu {
    display: none;
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: white;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.mobile-menu.active {
    display: block;
}

.mobile-menu-tabs {
    display: flex;
    justify-content: space-around;
    background: white;
    border-bottom: 1px solid #e0e0e0;
}

.mobile-menu-tab {
    flex: 1;
    padding: 10px;
    color: #333;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mobile-menu-tab i {
    font-size: 20px;
    margin-bottom: 5px;
}

.mobile-menu-tab.active {
    color: var(--green);
    border-bottom-color: var(--green);
    background: #f5f5f5;
}

.mobile-menu-content {
    display: none;
    background: #f5f5f5;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.mobile-menu-content.active {
    display: block;
}

.mobile-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu-list li {
    background: white;
    margin-bottom: 0px;
}

.mobile-menu-list li a {
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    border-radius: 8px;
}


/* Eliminamos la flecha */
.mobile-menu-list li a::after {
    display: none;
}
.mobile-account-nav {
    margin-top: 11px;
    background: linear-gradient(-65deg, var(--green-light) 40%, var(--green-dark) 90%);
    padding: 8px 0;
}

.mobile-account-list {
    list-style: none;
    display: flex;
    justify-content: space-around;
    padding: 0;
    margin: 0;
}

.mobile-account-list li a {
    font-size: 12px;  /* Texto más pequeño */
    color: white;
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.mobile-account-list li a i {
    font-size: 12px;  /* Iconos más pequeños */
    text-decoration: none;
}


/* Media queries para dispositivos móviles y tablets */
@media screen and (max-width: 768px) {
    body {
        padding-top: 60px;
    }

    .header-top {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1001;
        background: linear-gradient(45deg, var(--green) 40%, var(--yellow-light) 90%);
    }

    .menu-icon {
        display: block;
        padding: 5px;
    }

    .header-top .container {
        grid-template-columns: auto 1fr auto;
        gap: 10px;
    }

    .logo img {
        height: 28px;
    }

    .search-bar {
        max-width: 100%;
        transform: scale(0.95);
    }
    
    .search-bar input {
        padding: 8px 12px;
        font-size: 14px;
        height: 35px;
    }

    .search-button {
        padding: 0 8px;
    }

    .promo-banner {
        display: none;
    }

    .nav-categories, 
    .nav-account,
    .header-nav {
        display: none;
    }

    .mobile-menu {
        display: none;
    }

    .mobile-menu.active {
        display: block;
    }
}

@media (max-width: 480px) {
    .logo-text .city {
        font-size: 18px;
    }

    .logo-text .tagline {
        font-size: 11px;
    }

    .search-bar input {
        font-size: 13px;
    }
}
@media screen and (max-width: 768px) {
    .mobile-account-nav {
        display: block;
    }
}

</style>
</head>
<body>
    <header class="header">
        <div class="header-top">
            <div class="container">
                <div class="logo">
                    <div class="logo-text">
                        <span class="city">Villarrica</span>
                        <span class="tagline">a un CLICK</span>
                    </div>
                </div>
                <div class="search-bar">
                    <input type="text" placeholder="Buscar productos y +...">
                    <button class="search-button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                    </button>
                </div>
                <div class="menu-icon">
                    <i class="fas fa-bars"></i>
                </div>
                <div class="promo-banner">
                    <img src="https://http2.mlstatic.com/D_NQ_957153-MLA69318147677_052023-OO.webp" alt="Suscríbete al nivel 6">
                </div>
            </div>
        </div>
        <nav class="header-nav">
            <div class="container">
                <div class="nav-left">
                    <ul>
                        <li class="location-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Busqueda por ubicación</span>
                        </li>
                    </ul>
                </div>
                <div class="nav-center">
                    <ul class="nav-categories">
                        <li class="category-dropdown">
                            <a href="#">Categorías <span class="arrow">▾</span></a>
                            <ul class="dropdown-content">
                                <li><a href="#">Electrónicos</a></li>
                                <li><a href="#">Moda y Accesorios</a></li>
                                <li><a href="#">Hogar y Jardín</a></li>
                                <li><a href="#">Deportes</a></li>
                                <li><a href="#">Libros</a></li>
                                <li><a href="#">Juguetes</a></li>
                                <li><a href="#">Belleza</a></li>
                                <li><a href="#">Mascotas</a></li>
                                <li><a href="#">Alimentos</a></li>
                                <li><a href="#">Música</a></li>
                            </ul>
                        </li>
                        <li><a href="#">Ofertas</a></li>
                        <li class="arriendos-dropdown">
                            <a href="#">Arriendos <span class="arrow">▾</span></a>
                            <ul class="dropdown-content">
                                <li><a href="#">Departamentos</a></li>
                                <li><a href="#">Casas</a></li>
                                <li><a href="#">Oficinas</a></li>
                                <li><a href="#">Locales Comerciales</a></li>
                                <li><a href="#">Bodegas</a></li>
                                <li><a href="#">Estacionamientos</a></li>
                            </ul>
                        </li>
                        <li class="servicios-dropdown">
                            <a href="#">Servicios <span class="arrow">▾</span></a>
                            <ul class="dropdown-content">
                                <li><a href="#">Plomería</a></li>
                                <li><a href="#">Electricidad</a></li>
                                <li><a href="#">Carpintería</a></li>
                                <li><a href="#">Jardinería</a></li>
                                <li><a href="#">Limpieza</a></li>
                                <li><a href="#">Pintura</a></li>
                            </ul>
                        </li>
                        <li><a href="#">Moda</a></li>
                    </ul>
                </div>
                <div class="nav-right">
                    <ul class="nav-account">
                        <li><a href="#"><i class="fas fa-user-plus"></i>&nbsp;&nbsp;Crea tu cuenta</a></li>
                        <li><a href="#"><i class="fas fa-sign-in-alt"></i>&nbsp;&nbsp;Ingresa</a></li>
                        <li><a href="#"><i class="fas fa-envelope"></i>&nbsp;&nbsp;Contacto</a></li>
                    </ul>
                </div>                
            </div>
        </nav>
    </header>
    
    <!-- Menú móvil mejorado -->
    <div class="mobile-menu">
        <div class="mobile-account-nav">
            <ul class="mobile-account-list">
                <li><a href="#"><i class="fas fa-user-plus"></i> Crear tu cuenta</a></li>
                <li><a href="#"><i class="fas fa-sign-in-alt"></i> Ingresar</a></li>
                <li><a href="#"><i class="fas fa-envelope"></i> Contacto</a></li>
            </ul>
        </div>
    
        <div class="mobile-menu-tabs">
            <div class="mobile-menu-tab active" data-tab="categorias">
                <span>Categorías</span>
            </div>
            <div class="mobile-menu-tab" data-tab="servicios">
                <span>Servicios</span>
            </div>
            <div class="mobile-menu-tab" data-tab="arriendos">
                <span>Arriendos</span>
            </div>
        </div>
    
        <div class="mobile-menu-content active" data-content="categorias">
            <ul class="mobile-menu-list">
                <li><a href="#">Electrónicos</a></li>
                <li><a href="#">Moda y Accesorios</a></li>
                <li><a href="#">Hogar y Jardín</a></li>
                <li><a href="#">Deportes</a></li>
                <li><a href="#">Libros</a></li>
                <li><a href="#">Juguetes</a></li>
                <li><a href="#">Belleza</a></li>
                <li><a href="#">Mascotas</a></li>
                <li><a href="#">Alimentos</a></li>
                <li><a href="#">Música</a></li>
            </ul>
        </div>
    
        <div class="mobile-menu-content" data-content="servicios">
            <ul class="mobile-menu-list">
                <li><a href="#">Plomería</a></li>
                <li><a href="#">Electricidad</a></li>
                <li><a href="#">Carpintería</a></li>
                <li><a href="#">Jardinería</a></li>
                <li><a href="#">Limpieza</a></li>
                <li><a href="#">Pintura</a></li>
            </ul>
        </div>
    
        <div class="mobile-menu-content" data-content="arriendos">
            <ul class="mobile-menu-list">
                <li><a href="#">Departamentos</a></li>
                <li><a href="#">Casas</a></li>
                <li><a href="#">Oficinas</a></li>
                <li><a href="#">Locales Comerciales</a></li>
                <li><a href="#">Bodegas</a></li>
                <li><a href="#">Estacionamientos</a></li>
            </ul>
        </div>
    </div>
    
    
    <script>
   document.addEventListener('DOMContentLoaded', function() {
    // Código para el dropdown original
    const categoryDropdown = document.querySelector('.category-dropdown');
    const dropdownContent = document.querySelector('.dropdown-content');

    if (categoryDropdown && dropdownContent) {
        categoryDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropdownContent.classList.toggle('show');
        });

        document.addEventListener('click', function(e) {
            if (!categoryDropdown.contains(e.target)) {
                dropdownContent.classList.remove('show');
            }
        });
    }

    // Código para el menú móvil
    const menuIcon = document.querySelector('.menu-icon');
    const mobileMenu = document.querySelector('.mobile-menu');
    const menuTabs = document.querySelectorAll('.mobile-menu-tab');
    const menuContents = document.querySelectorAll('.mobile-menu-content');

    if (menuIcon && mobileMenu) {
        menuIcon.addEventListener('click', function(e) {
            e.stopPropagation();
            mobileMenu.classList.toggle('active');
        });

        menuTabs.forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.stopPropagation();
                
                // Desactivar todos los tabs y contenidos
                menuTabs.forEach(t => t.classList.remove('active'));
                menuContents.forEach(c => c.classList.remove('active'));
                
                // Activar el tab seleccionado y su contenido
                this.classList.add('active');
                const contentId = this.getAttribute('data-tab');
                const content = document.querySelector(`[data-content="${contentId}"]`);
                if (content) {
                    content.classList.add('active');
                }
            });
        });

        // Cerrar el menú móvil al hacer click fuera de él
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !menuIcon.contains(e.target)) {
                mobileMenu.classList.remove('active');
            }
        });
    }

    // Código para arriendos dropdown
    const arriendosDropdown = document.querySelector('.arriendos-dropdown');
    const arriendosContent = arriendosDropdown.querySelector('.dropdown-content');

    if (arriendosDropdown && arriendosContent) {
        arriendosDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            arriendosContent.classList.toggle('show');
        });

        document.addEventListener('click', function(e) {
            if (!arriendosDropdown.contains(e.target)) {
                arriendosContent.classList.remove('show');
            }
        });
    }

    // Código para servicios dropdown
    const serviciosDropdown = document.querySelector('.servicios-dropdown');
    const serviciosContent = serviciosDropdown.querySelector('.dropdown-content');

    if (serviciosDropdown && serviciosContent) {
        serviciosDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            serviciosContent.classList.toggle('show');
        });

        document.addEventListener('click', function(e) {
            if (!serviciosDropdown.contains(e.target)) {
                serviciosContent.classList.remove('show');
            }
        });
    }

    // Código para cerrar menú móvil en cambio de tamaño
    window.addEventListener('resize', function() {
        const mobileMenu = document.querySelector('.mobile-menu');
        const menuContents = document.querySelectorAll('.mobile-menu-content');
        
        if (window.innerWidth > 768) {
            mobileMenu.classList.remove('active');
            menuContents.forEach(content => {
                content.classList.remove('active');
            });
        }
    });
});

    </script>
    <!-- script para modulo de arriendos -->
     <script>
        const arriendosDropdown = document.querySelector('.arriendos-dropdown');
const arriendosContent = arriendosDropdown.querySelector('.dropdown-content');

if (arriendosDropdown && arriendosContent) {
    arriendosDropdown.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        arriendosContent.classList.toggle('show');
    });

    document.addEventListener('click', function(e) {
        if (!arriendosDropdown.contains(e.target)) {
            arriendosContent.classList.remove('show');
        }
    });
}

     </script>
<!-- codigos modulo de servicios -->
 <script>
    const serviciosDropdown = document.querySelector('.servicios-dropdown');
const serviciosContent = serviciosDropdown.querySelector('.dropdown-content');

if (serviciosDropdown && serviciosContent) {
    serviciosDropdown.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        serviciosContent.classList.toggle('show');
    });

    document.addEventListener('click', function(e) {
        if (!serviciosDropdown.contains(e.target)) {
            serviciosContent.classList.remove('show');
        }
    });
}

 </script>
</body>
</html>