// Función para generar números aleatorios
function getRandomNumber(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Función para generar datos aleatorios
function generateData() {
    const rows = getRandomNumber(5, 10);
    const data = [];
    for (let i = 0; i < rows; i++) {
        data.push({
            id: i + 1,
            number: getRandomNumber(1, 100),
            value: getRandomNumber(100, 1000)
        });
    }
    return data;
}

// Función para renderizar la tabla
function renderTable(data) {
    const tableBody = document.getElementById('tableBody');
    tableBody.innerHTML = '';
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.id}</td>
            <td>${row.number}</td>
            <td>${row.value}</td>
        `;
        tableBody.appendChild(tr);
    });
}

// Variable global para almacenar los datos
let tableData = generateData();

// Renderizar tabla inicial
renderTable(tableData);

// Event Listeners para los botones
document.getElementById('generateBtn').addEventListener('click', () => {
    tableData = generateData();
    renderTable(tableData);
});

document.getElementById('sortBtn').addEventListener('click', () => {
    tableData.sort((a, b) => a.value - b.value);
    renderTable(tableData);
});

document.getElementById('colorBtn').addEventListener('click', () => {
    const rows = document.querySelectorAll('#tableBody tr');
    rows.forEach(row => {
        const randomColor = `hsl(${Math.random() * 360}, 70%, 90%)`;
        row.style.backgroundColor = randomColor;
    });
});

// Funcionalidad del menú responsive
document.addEventListener('DOMContentLoaded', () => {
    const menuToggle = document.getElementById('menuToggle');
    const headerNav = document.querySelector('.header-nav');

    menuToggle.addEventListener('click', () => {
        headerNav.classList.toggle('active');
    });

    // Cerrar menú al hacer clic fuera
    document.addEventListener('click', (e) => {
        if (!headerNav.contains(e.target) && !menuToggle.contains(e.target)) {
            headerNav.classList.remove('active');
        }
    });
});