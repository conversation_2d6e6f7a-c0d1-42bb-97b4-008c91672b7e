/**
 * Script para la funcionalidad del header moderno
 */
document.addEventListener('DOMContentLoaded', function() {
    // Botón de pantalla completa
    const fullscreenToggle = document.getElementById('fullscreenToggle');
    if (fullscreenToggle) {
        fullscreenToggle.addEventListener('click', function() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.error(`Error al intentar mostrar pantalla completa: ${err.message}`);
                });
                fullscreenToggle.innerHTML = '<i class="fas fa-compress"></i>';
                document.body.classList.add('fullscreen-mode');
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                    fullscreenToggle.innerHTML = '<i class="fas fa-expand"></i>';
                    document.body.classList.remove('fullscreen-mode');
                }
            }
        });
    }

    // Botón de cambio de tema
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        // Comprobar si existe un tema guardado
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }

        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            
            if (document.body.classList.contains('dark-theme')) {
                localStorage.setItem('theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                localStorage.setItem('theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    }

    // Selector de idioma
    const languageSelector = document.getElementById('languageSelector');
    if (languageSelector) {
        languageSelector.addEventListener('click', function() {
            // Aquí se implementaría el cambio de idioma real
            const languages = ['ES', 'EN', 'FR'];
            const currentLang = languageSelector.querySelector('span').textContent;
            const currentIndex = languages.indexOf(currentLang);
            const nextIndex = (currentIndex + 1) % languages.length;
            
            languageSelector.querySelector('span').textContent = languages[nextIndex];
        });
    }

    // Perfil de usuario (dropdown)
    const userProfile = document.getElementById('userProfile');
    if (userProfile) {
        userProfile.addEventListener('click', function() {
            // Implementar menú desplegable para el perfil
            console.log('Perfil de usuario clickeado');
        });
    }

    // Menú de navegación para móvil
    const menuToggle = document.getElementById('menuToggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            // Implementar apertura del menú en móvil
            console.log('Menú móvil clickeado');
        });
    }
});
