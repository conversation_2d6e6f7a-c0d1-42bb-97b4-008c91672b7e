console.log('🔍 Ejecutando diagnóstico de módulos...');

// Lista de módulos a verificar
const modulesToCheck = [
    { path: './core/app.js', expectedExports: ['initApp'] },
    { path: './core/ui.js', expectedExports: ['showSection'] },
    { path: './core/utils.js', expectedExports: ['setupOverlays', 'resetOverlay'] },
    { path: './components/sidebar.js', expectedExports: ['initSidebar'] },
    { path: './components/notifications.js', expectedExports: ['showNotification'] },
    { path: './modules/products/product-api.js', expectedExports: ['loadProducts'] },
    { path: './modules/products/product-list.js', expectedExports: ['setupTableButtons', 'updateResponsiveView'] },
    { path: './modules/products/product-cards.js', expectedExports: ['createProductCards'] },
    { path: './modules/products/product-edit.js', expectedExports: ['setupEditProductPanel'] },
    { path: './modules/products/product-filter.js', expectedExports: ['initFilterHandlers'] },
    { path: './modules/categories/categories.js', expectedExports: ['initCategoriesModule'] },
    { path: './modules/stats/stats.js', expectedExports: ['initStatsModule'] }
];

// Función para verificar un módulo
async function checkModule(module) {
    try {
        const imported = await import(module.path);
        const actualExports = Object.keys(imported);
        
        // Verificar exportaciones esperadas
        const missingExports = module.expectedExports.filter(exp => !actualExports.includes(exp));
        
        return {
            path: module.path,
            success: missingExports.length === 0,
            actualExports,
            missingExports,
            error: missingExports.length > 0 ? `Exportaciones faltantes: ${missingExports.join(', ')}` : null
        };
    } catch (error) {
        return {
            path: module.path,
            success: false,
            actualExports: [],
            missingExports: module.expectedExports,
            error: error.message
        };
    }
}

// Verificar todos los módulos
async function runDiagnostics() {
    const results = [];
    for (const module of modulesToCheck) {
        console.log(`🔍 Revisando módulo: ${module.path}`);
        const result = await checkModule(module);
        results.push(result);
        console.log(`${result.success ? '✅' : '❌'} ${module.path}: ${result.success ? 'OK' : result.error}`);
    }
    
    // Mostrar resultados
    console.log('\n📊 RESULTADOS DEL DIAGNÓSTICO:');
    console.log(`Total: ${results.length}, OK: ${results.filter(r => r.success).length}, Error: ${results.filter(r => !r.success).length}`);
    
    // Crear log visual
    const diagEl = document.createElement('div');
    diagEl.id = 'module-diagnostics';
    diagEl.style.cssText = 'position:fixed; top:10px; right:10px; background:rgba(0,0,0,0.9); color:#fff; padding:15px; border-radius:5px; z-index:10000; font-family:monospace; max-width:500px; max-height:80vh; overflow:auto;';
    
    diagEl.innerHTML = `
        <h3 style="margin:0 0 10px; color:#4CAF50">Diagnóstico de Módulos</h3>
        <div>Total: ${results.length}, ✅ OK: ${results.filter(r => r.success).length}, ❌ Error: ${results.filter(r => !r.success).length}</div>
        <div style="margin-top:10px; border-top:1px solid #555; padding-top:10px;">
            ${results.map(r => `
                <div style="margin:5px 0; color:${r.success ? '#4CAF50' : '#F44336'}; border-left:3px solid ${r.success ? '#4CAF50' : '#F44336'}; padding-left:8px;">
                    ${r.success ? '✅' : '❌'} ${r.path}
                    ${r.error ? `<div style="margin-left:20px; font-size:11px; color:#FF9800">${r.error}</div>` : ''}
                    ${r.actualExports.length > 0 ? `<div style="margin-left:20px; font-size:11px; color:#90CAF9">Exportaciones: ${r.actualExports.join(', ')}</div>` : ''}
                </div>
            `).join('')}
        </div>
        <button id="close-diag" style="margin-top:10px; padding:5px 10px; background:#333; border:none; color:#fff; border-radius:3px; cursor:pointer;">Cerrar</button>
    `;
    
    document.body.appendChild(diagEl);
    document.getElementById('close-diag').addEventListener('click', () => diagEl.remove());
}

// Ejecutar diagnóstico cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', runDiagnostics);