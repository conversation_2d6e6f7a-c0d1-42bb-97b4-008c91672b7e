<?php
/**
 * Script para actualizar contraseñas a bcrypt
 * 
 * Este script lee todas las contraseñas en texto plano y las convierte 
 * a hashes bcrypt para mayor seguridad.
 */

// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Incluir configuración
require_once '../config/config.php';
require_once '../config/logger.php';

// Verificar si se está ejecutando desde línea de comandos o navegador
$is_cli = (php_sapi_name() === 'cli');

function output($message) {
    global $is_cli;
    if ($is_cli) {
        echo $message . PHP_EOL;
    } else {
        echo $message . "<br>";
    }
    logInfo($message);
}

// Verificar admin o CLI
if (!$is_cli) {
    session_start();
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'premium') {
        die("Acceso denegado. Este script solo puede ser ejecutado por administradores.");
    }
    echo "<pre>";
}

output("Iniciando actualización de hashes de contraseñas...");

// Obtener todos los usuarios
$stmt = $conn->prepare("SELECT id, username, email, password FROM users");
if (!$stmt) {
    output("Error al preparar la consulta: " . $conn->error);
    die();
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    output("No se encontraron usuarios para actualizar.");
    die();
}

output("Se encontraron " . $result->num_rows . " usuarios.");

// Variable para contar actualizaciones
$updated = 0;

// Preparar consulta para actualizar contraseñas
$update_stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
if (!$update_stmt) {
    output("Error al preparar la consulta de actualización: " . $conn->error);
    die();
}

// Recorrer usuarios y actualizar contraseñas
while ($user = $result->fetch_assoc()) {
    $id = $user['id'];
    $username = $user['username'];
    $password = $user['password'];
    
    // Verificar si la contraseña ya es un hash bcrypt
    if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $password)) {
        output("Usuario $username ya tiene un hash bcrypt. Omitiendo.");
        continue;
    }
    
    // Generar el hash bcrypt
    $hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    
    // Actualizar en la base de datos
    $update_stmt->bind_param("si", $hash, $id);
    if ($update_stmt->execute()) {
        output("Usuario $username actualizado correctamente.");
        $updated++;
    } else {
        output("Error al actualizar contraseña para $username: " . $update_stmt->error);
    }
}

output("Actualización completada. Se actualizaron $updated usuarios.");

// Cerrar conexiones
$update_stmt->close();
$stmt->close();
$conn->close();

if (!$is_cli) {
    echo "</pre>";
    echo "<a href='../public/admin_dashboard.php'>Volver al panel de administración</a>";
}