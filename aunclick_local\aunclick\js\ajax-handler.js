/**
 * ajax-handler.js
 * Script para manejar todas las solicitudes AJAX del formulario de registro
 */

// Objeto global para manejar las solicitudes AJAX
const AjaxHandler = {
    /**
     * Envía una solicitud AJAX al servidor
     * @param {string} url - URL a la que se enviará la solicitud
     * @param {FormData|Object} data - Datos a enviar (FormData u objeto)
     * @param {Function} successCallback - Función a ejecutar en caso de éxito
     * @param {Function} errorCallback - Función a ejecutar en caso de error
     */
    sendRequest: function(url, data, successCallback, errorCallback) {
        console.log('Enviando solicitud AJAX a:', url);
        console.log('Datos a enviar:', data instanceof FormData ? 'FormData Object' : data);

        // Crear objeto XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', url, true);

        // Configurar timeout
        xhr.timeout = 30000; // 30 segundos

        // Configurar manejadores de eventos
        xhr.onload = function() {
            console.log('Respuesta recibida. Status:', xhr.status);
            console.log('Respuesta texto:', xhr.responseText);

            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('Respuesta parseada:', response);

                    if (response.success) {
                        console.log('Solicitud exitosa');
                        if (typeof successCallback === 'function') {
                            successCallback(response);
                        }
                    } else {
                        console.error('Error en la respuesta:', response.message);
                        if (typeof errorCallback === 'function') {
                            errorCallback(response.message);
                        } else {
                            // Mostrar error genérico si no hay callback
                            window.showErrorPopup(
                                'Error en la solicitud',
                                response.message || 'Hubo un error al procesar la solicitud.',
                                []
                            );
                        }
                    }
                } catch (e) {
                    console.error('Error al parsear la respuesta JSON:', e);
                    console.error('Texto de la respuesta:', xhr.responseText);

                    if (typeof errorCallback === 'function') {
                        errorCallback('Error al procesar la respuesta del servidor.');
                    } else {
                        // Mostrar error genérico si no hay callback
                        window.showErrorPopup(
                            'Error en la respuesta',
                            'Hubo un error al procesar la respuesta del servidor.',
                            []
                        );
                    }
                }
            } else {
                console.error('Error HTTP:', xhr.status);

                if (typeof errorCallback === 'function') {
                    errorCallback('Error de conexión: ' + xhr.status);
                } else {
                    // Mostrar error genérico si no hay callback
                    window.showErrorPopup(
                        'Error de conexión',
                        'No se pudo conectar con el servidor. Código: ' + xhr.status,
                        []
                    );
                }
            }
        };

        xhr.onerror = function(e) {
            console.error('Error de red:', e);

            if (typeof errorCallback === 'function') {
                errorCallback('Error de conexión con el servidor.');
            } else {
                // Mostrar error genérico si no hay callback
                window.showErrorPopup(
                    'Error de conexión',
                    'No se pudo conectar con el servidor. Verifique su conexión a internet.',
                    []
                );
            }
        };

        xhr.ontimeout = function() {
            console.error('Timeout en la conexión');

            if (typeof errorCallback === 'function') {
                errorCallback('La conexión con el servidor ha tardado demasiado tiempo.');
            } else {
                // Mostrar error genérico si no hay callback
                window.showErrorPopup(
                    'Error de conexión',
                    'La conexión con el servidor ha tardado demasiado tiempo. Intente nuevamente.',
                    []
                );
            }
        };

        // Enviar la solicitud
        try {
            if (data instanceof FormData) {
                xhr.send(data);
            } else {
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.send(JSON.stringify(data));
            }
            console.log('Solicitud enviada');
        } catch (e) {
            console.error('Error al enviar la solicitud:', e);

            if (typeof errorCallback === 'function') {
                errorCallback('Error al enviar la solicitud: ' + e.message);
            } else {
                // Mostrar error genérico si no hay callback
                window.showErrorPopup(
                    'Error de conexión',
                    'No se pudo enviar la solicitud al servidor: ' + e.message,
                    []
                );
            }
        }
    },

    /**
     * Envía los datos del paso 1 al servidor
     * @param {Object} formData - Datos del formulario
     * @param {Function} successCallback - Función a ejecutar en caso de éxito
     * @param {Function} errorCallback - Función a ejecutar en caso de error
     */
    submitStep1: function(formData, successCallback, errorCallback) {
        this.sendRequest('process_step1.php', formData, successCallback, errorCallback);
    },

    /**
     * Envía los datos del paso 2 al servidor
     * @param {Object} formData - Datos del formulario
     * @param {Function} successCallback - Función a ejecutar en caso de éxito
     * @param {Function} errorCallback - Función a ejecutar en caso de error
     */
    submitStep2: function(formData, successCallback, errorCallback) {
        this.sendRequest('process_step2.php', formData, successCallback, errorCallback);
    },

    /**
     * Envía los datos del paso 3 al servidor
     * @param {Object} formData - Datos del formulario
     * @param {Function} successCallback - Función a ejecutar en caso de éxito
     * @param {Function} errorCallback - Función a ejecutar en caso de error
     */
    submitStep3: function(formData, successCallback, errorCallback) {
        this.sendRequest('process_step3.php', formData, successCallback, errorCallback);
    },

    /**
     * Envía los datos del paso 4 al servidor
     * @param {Object} formData - Datos del formulario
     * @param {Function} successCallback - Función a ejecutar en caso de éxito
     * @param {Function} errorCallback - Función a ejecutar en caso de error
     */
    submitStep4: function(formData, successCallback, errorCallback) {
        this.sendRequest('process_step4.php', formData, successCallback, errorCallback);
    }
};

// Asegurarse de que el objeto esté disponible globalmente
window.AjaxHandler = AjaxHandler;




