// Importar closeEditPanel desde el módulo correspondiente
import { closeEditPanel } from '../modules/products/product-edit.js';
import { closeFilterPanel } from '../modules/products/product-filter.js';

// Función para formatear precios
function formatPrice(price) {
    if (!price) return 'CLP $0';

    // Si el precio ya es una cadena y contiene símbolos de moneda, devolver tal cual
    if (typeof price === 'string' && (price.includes('$') || price.includes('CLP'))) {
        return price;
    }

    // Asegurarse de que el precio sea un número
    let numericPrice = price;
    if (typeof price === 'string') {
        // Eliminar cualquier carácter no numérico excepto el punto decimal
        numericPrice = parseFloat(price.replace(/[^0-9.]/g, ''));
    }

    // Verificar si es un número válido
    if (isNaN(numericPrice)) {
        return 'CLP $0';
    }

    return new Intl.NumberFormat('es-CL', {
        style: 'currency',
        currency: 'CLP'
    }).format(numericPrice);
}

// Función para truncar texto
function truncateText(text, maxLength = 100) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Configurar overlays para edición y filtros
function setupOverlays() {
    const editOverlay = document.getElementById('editOverlay');
    const filterOverlay = document.getElementById('filterOverlay');

    if (editOverlay) {
        editOverlay.addEventListener('click', closeEditPanel);
    }

    if (filterOverlay) {
        filterOverlay.addEventListener('click', closeFilterPanel);
    }

    console.log('Overlays configurados correctamente');
}

// Función extra para garantizar que el overlay se reinicie correctamente
function resetOverlay() {
    console.log('Reseteo completo del overlay...');

    const editOverlay = document.getElementById('editOverlay');
    if (editOverlay) {
        // Eliminar cualquier estilo inline
        editOverlay.removeAttribute('style');

        // Aplicar estilos directo que garanticen que está oculto
        editOverlay.style.display = 'none';
        editOverlay.style.opacity = '0';
        editOverlay.style.visibility = 'hidden';
        editOverlay.style.pointerEvents = 'none';
        editOverlay.style.zIndex = '-1';

        // Eliminar cualquier clase que pueda estar causando problemas
        editOverlay.className = 'overlay overlay-reset';
    }

    // Asegurar que el body puede hacer scroll normalmente
    document.body.style.overflow = '';
    document.body.classList.remove('overlay-active');
}

// Función para forzar la eliminación del overlay
function forceRemoveOverlay() {
    const overlay = document.getElementById('editOverlay');
    if (overlay) {
        overlay.style.display = 'none';
        overlay.style.opacity = '0';
        overlay.style.visibility = 'hidden';
        overlay.style.pointerEvents = 'none';
        overlay.style.zIndex = '-1';
        document.body.style.overflow = '';
    }
}

// Exportar funciones
export {
    setupOverlays,
    resetOverlay,
    formatPrice,
    truncateText
};