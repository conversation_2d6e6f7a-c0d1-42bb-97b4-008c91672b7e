/* Estilos para el Dashboard */

/* Estilos para las tarjetas de estadísticas */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(106, 27, 154, 0.1);
    color: #6a1b9a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 15px;
}

.stat-icon.products {
    background-color: rgba(106, 27, 154, 0.1);
    color: #6a1b9a;
}

.stat-icon.users {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.stat-icon.views {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.stat-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
}

.stat-trend.positive {
    color: #4caf50;
}

.stat-trend.negative {
    color: #f44336;
}

.stat-trend i {
    margin-right: 5px;
}

/* Estilos para los gráficos */
.chart-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.chart-card h3 {
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Estilos para la tabla de resumen */
.summary-table {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-top: 30px;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.summary-actions {
    display: flex;
    gap: 10px;
}

.summary-actions button {
    padding: 5px 10px;
    font-size: 12px;
}

/* Estilos para el filtro de fecha */
.date-filter {
    margin-right: 15px;
}

.date-filter select {
    padding: 8px 12px;
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    background-color: white;
    font-size: 14px;
    min-width: 150px;
}

/* Estilos responsivos */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .charts-container {
        grid-template-columns: 1fr !important;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    }
    
    .section-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .date-filter {
        margin-right: 0;
        width: 100%;
    }
    
    .date-filter select {
        width: 100%;
    }
}

/* Animaciones para los gráficos */
.chart-card {
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

.chart-card:nth-child(1) {
    animation-delay: 0.1s;
}

.chart-card:nth-child(2) {
    animation-delay: 0.2s;
}

.chart-card:nth-child(3) {
    animation-delay: 0.3s;
}

.chart-card:nth-child(4) {
    animation-delay: 0.4s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estilos para los botones del Dashboard */
#refreshDashboard {
    display: flex;
    align-items: center;
    gap: 5px;
}

#refreshDashboard i {
    transition: transform 0.3s ease;
}

#refreshDashboard:hover i {
    transform: rotate(180deg);
}

/* Estilos para la tabla de resumen */
.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th {
    background-color: #f5f5f5;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
}

.admin-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
}

.admin-table tbody tr:hover {
    background-color: #f9f9f9;
}

/* Estilos para los botones de exportación */
.btn-outline-secondary {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #666;
    padding: 5px 10px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background-color: #f5f5f5;
    color: #333;
}

.btn-sm {
    font-size: 12px;
    padding: 5px 10px;
}
