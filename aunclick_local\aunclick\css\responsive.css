/* =========================================
   ESTILOS RESPONSIVOS
   ========================================= */

/* Pantallas grandes (hasta 1200px) */
@media screen and (max-width: 1200px) {
    /* Ajuste para stats-grid: 2x2 */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(2, auto);
        gap: 15px;
    }

    .stat-card {
        width: 100%;
        min-width: auto;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .admin-table th, .admin-table td {
        padding: 8px 10px;
    }

    .product-name {
        font-size: 13px;
    }

    .product-category {
        font-size: 11px;
    }

    .tab-btn {
        padding: 12px 15px;
        font-size: 13px;
    }

    .charts-container {
        grid-template-columns: 1fr !important;
    }

    /* Reducción de textos */
    .section-title {
        font-size: 17px;
    }

    .form-section-title {
        font-size: 15px;
    }
}

/* Pantallas medianas (hasta 992px) */
@media screen and (max-width: 992px) {
    /* Ajuste de barra lateral en estado normal (colapsado en responsive) */
    .sidebar {
        width: 70px;
        transition: all 0.3s ease;
        position: fixed;
        z-index: 1050; /* Aumentar z-index para estar por encima del overlay */
        top: 0;
        left: 0;
        height: 100vh;
        overflow-y: auto;
    }

    .sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span {
        display: none;
    }

    .sidebar-header, .sidebar-user {
        justify-content: center;
        padding: 15px 0;
    }

    .nav-link {
        justify-content: center;
        padding: 15px 0;
    }

    .nav-link i {
        margin-right: 0;
    }

    /* Contenido principal siempre con el mismo ancho y margen */
    .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
        position: relative;
        transition: none !important; /* Eliminar transición para evitar cambios de tamaño */
        left: 0 !important; /* Forzar posición */
        transform: none !important; /* Evitar transformaciones */
    }

    /* Barra lateral expandida (cuando se hace clic en el botón) */
    .sidebar.expanded {
        width: 250px; /* Ancho fijo en lugar de variable */
        box-shadow: 0 0 15px rgba(0,0,0,0.2);
        left: 0;
        position: fixed;
        z-index: 9999; /* Z-index muy alto para asegurar que esté por encima de todo */
    }

    .sidebar.expanded .sidebar-logo,
    .sidebar.expanded .sidebar-subtitle,
    .sidebar.expanded .user-info,
    .sidebar.expanded .nav-section-title,
    .sidebar.expanded .nav-link span {
        display: block;
    }

    .sidebar.expanded .sidebar-header,
    .sidebar.expanded .sidebar-user {
        justify-content: flex-start;
        padding: 15px 20px;
    }

    .sidebar.expanded .nav-link {
        justify-content: flex-start;
        padding: 10px 20px;
    }

    .sidebar.expanded .nav-link i {
        margin-right: 10px;
    }

    /* El contenido principal NO se mueve cuando el sidebar está expandido */
    .sidebar.expanded + .main-content {
        margin-left: 70px; /* Forzar que se mantenga el margen original */
        width: calc(100% - 70px); /* Forzar que se mantenga el ancho original */
    }

    /* Estilos para el overlay del sidebar */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040; /* Entre el contenido y el sidebar */
        display: none;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: auto; /* Asegurar que reciba eventos de clic */
    }

    /* Cuando el overlay está visible */
    .sidebar-overlay.visible {
        display: block !important;
        opacity: 1 !important;
    }

    /* Clase para el body cuando el sidebar está expandido */
    body.sidebar-expanded {
        overflow: hidden !important;
    }

    /* Ajustes para el botón de toggle */
    #aside-toggle {
        top: 10px;
        right: -15px;
        width: 30px;
        height: 30px;
    }

    /* Ajustes para formularios */
    .form-grid {
        grid-template-columns: 1fr;
    }

    /* Ajustes para la tabla */
    .admin-table {
        font-size: 12px;
    }

    .product-image-preview {
        width: 50px;
        height: 50px;
    }

    .table-actions {
        flex-wrap: wrap;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    /* Ajustes para las pestañas principales */
    .main-tabs {
        overflow-x: auto;
        flex-wrap: nowrap;
        padding-bottom: 5px;
        /* Usar propiedades estándar para mejorar la compatibilidad */
        overflow-scrolling: touch; /* Versión estándar */
    }

    .main-tabs::-webkit-scrollbar {
        display: none; /* Oculta scrollbar en Chrome/Safari */
    }

    .tab-btn {
        white-space: nowrap;
    }

    /* Reducción de textos */
    .stat-value {
        font-size: 22px;
    }

    .stat-label {
        font-size: 13px;
    }

    .filter-container {
        width: 320px;
    }

    .filter-options {
        grid-template-columns: 1fr;
    }

    /* Ajustes para el contenedor de filtros */
    .filter-container {
        width: 75% !important;
        right: -75% !important;
    }

    .filter-container.show {
        right: 0 !important;
    }

    /* Ajustes para el panel de edición de productos */
    .edit-container {
        width: 75% !important;
        right: -75% !important;
    }

    .edit-container.show {
        right: 0 !important;
    }

    /* Ajuste para section-body */
    .section-body {
        padding: 0;
    }
}

/* Pantallas pequeñas (hasta 768px) */
@media screen and (max-width: 768px) {
    .container {
        padding: 15px 10px;
    }

    /* Mantener stats-grid en 2x2 pero ajustar tamaño */
    .stats-grid {
        gap: 10px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 10px;
    }

    /* Ajustes para categorías */
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* Ajustes para la tabla */
    .admin-table {
        display: none; /* Ocultar tabla en móviles */
    }

    /* Mostrar contenedor de tarjetas */
    .product-cards-container {
        display: flex !important; /* Ensure it's set to flex */
        flex-wrap: wrap !important; /* Allow cards to wrap onto multiple lines */
        gap: 15px; /* Add space between cards */
        width: 100% !important; /* Ensure container takes full width */
        justify-content: center !important; /* Center cards if space allows */
        max-width: 100% !important; /* Asegurar que no exceda el ancho disponible */
        box-sizing: border-box !important; /* Incluir padding en el cálculo del ancho */
        padding: 10px !important; /* Agregar un poco de padding */
        position: relative !important; /* Asegurar posicionamiento relativo */
        left: 0 !important; /* Asegurar que no se desplace */
        margin-left: 0 !important; /* Eliminar margen izquierdo */
        transform: none !important; /* Evitar transformaciones */
    }

    /* Basic styles for product cards in mobile view */
    /* Ideally, more detailed styles should be in components.css */
    .product-card {
        display: flex;
        flex-direction: column;
        width: 100%; /* Default to full width on small screens */
        background-color: var(--white); /* Give it a background */
        border-radius: var(--border-radius); /* Add rounded corners */
        box-shadow: var(--box-shadow); /* Add shadow for visibility */
        overflow: hidden; /* Prevent content overflow */
        min-height: 150px; /* Ensure cards have some height */
    }

    /* Optional: Adjust card width for slightly larger small screens (e.g., 2 columns) */
    @media screen and (min-width: 577px) and (max-width: 768px) {
        .product-card {
            /* Calculate width for 2 columns, accounting for the gap */
            width: calc(50% - 7.5px);
        }
    }

    /* Ajustes para secciones */
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .section-actions {
        width: 100%;
        justify-content: space-between;
    }

    /* Ajustes para botones */
    .btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    /* Reducción de textos */
    .section-title {
        font-size: 16px;
    }

    .form-section-title {
        font-size: 14px;
    }

    .form-label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .form-hint {
        font-size: 11px;
    }

    /* Ajustes para dropzone */
    .dropzone {
        padding: 20px;
    }

    .dropzone-icon {
        font-size: 30px;
    }

    .dropzone-title {
        font-size: 14px;
    }

    .dropzone-hint {
        font-size: 12px;
    }

    /* Mejoras para botones en dispositivos móviles */
    /* Aumentar área de toque para botones */
    .btn,
    .action-btn,
    .card-btn,
    .nav-link,
    .tab-btn,
    #aside-toggle {
        min-height: 44px;  /* Área de toque mínima recomendada por Apple */
        min-width: 44px;   /* Área de toque mínima recomendada por Apple */
        cursor: pointer;
        -webkit-tap-highlight-color: rgba(0,0,0,0.1); /* Retroalimentación visual ligera */
        position: relative; /* Para asegurar que el z-index funcione */
        z-index: 10;        /* Valor suficiente para estar por encima de otros elementos */
        touch-action: manipulation; /* Optimización para eventos táctiles */
    }

    /* Aumentar relleno para targets táctiles más fáciles */
    .nav-link {
        padding: 12px 16px;
    }

    /* Asegurar que los botones de tarjeta tengan tamaño adecuado */
    .card-btn.edit-btn {
        padding: 10px;
        font-size: 14px;
    }

    .card-btn.delete-btn {
        width: 44px;
        height: 44px;
    }

    /* Eliminar delay en eventos táctiles */
    * {
        touch-action: manipulation;
    }

    /* Asegurar que los botones de accordion tienen suficiente espacio */
    .sidebar-nav .nav-item {
        margin-bottom: 8px;
    }

    /* Aumentar contraste y retroalimentación visual */
    .btn:active,
    .action-btn:active,
    .card-btn:active,
    .nav-link:active,
    .tab-btn:active {
        opacity: 0.7;
        transform: scale(0.98);
    }

    .filter-container {
        width: 100%;
        max-width: 100%;
        height: 100vh;
    }

    .filter-content {
        border-radius: 0;
    }

    .filter-header h3 {
        font-size: 16px;
    }

    .filter-body {
        padding: 15px;
    }

    .filter-row {
        flex-direction: column;
        gap: 10px;
    }
}

/* Pantallas muy pequeñas (hasta 576px) */
@media screen and (max-width: 576px) {
    /* Ajustes para el contenedor */
    .container {
        padding: 10px 5px;
    }

    /* Mantener stats-grid en 2x2 pero reducir más el tamaño */
    .stats-grid {
        gap: 8px;
    }

    .stat-card {
        padding: 12px;
    }

    .stat-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
        margin-right: 8px;
    }

    .stat-value {
        font-size: 16px;
        margin-bottom: 2px;
    }

    .stat-label {
        font-size: 11px;
    }

    /* Ajustes para cabeceras de sección */
    .section-title {
        font-size: 15px;
    }

    /* Ajustes para galería de imágenes */
    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Ajustes para la tabla - mantenemos todas las columnas con scroll */
    .admin-table {
        min-width: 700px; /* Asegura que todas las columnas sean visibles con scroll */
    }

    .product-name {
        font-size: 12px;
        line-height: 1.3;
    }

    .product-category {
        font-size: 10px;
    }

    /* Ajustes para las acciones de tabla */
    .table-actions {
        flex-wrap: nowrap;
    }

    .action-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
    }

    /* Ajustes para formularios */
    .form-control {
        padding: 8px 10px;
        font-size: 13px;
    }

    /* Ajustes para dropzone */
    .dropzone {
        padding: 15px;
    }

    .dropzone-icon {
        font-size: 30px;
    }

    .dropzone-title {
        font-size: 14px;
    }

    .dropzone-hint {
        font-size: 11px;
    }

    /* Ajustes para badges y etiquetas */
    .status-badge, .condition-badge {
        font-size: 10px;
        padding: 3px 6px;
    }

    .product-discount {
        font-size: 10px;
        padding: 2px 5px;
    }

    /* Ajustes para precios */
    .product-price {
        font-size: 12px;
    }

    .product-original-price {
        font-size: 10px;
    }

    /* Ajustes para main-tabs */
    .main-tabs {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 5px;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 13px;
    }

    .form-section {
        margin-bottom: 15px;
    }

    .admin-table th:first-child,
    .admin-table td:first-child {
        position: sticky;
        left: 0;
        background-color: white;
        z-index: 1;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    /* Mejora para formularios en móvil */
    .form-group {
        margin-bottom: 15px;
    }

    /* Hacer los selects más fáciles de tocar */
    select.form-control {
        height: 38px;
    }

    /* Mejorar visualización de filtros */
    .filter-group label {
        font-size: 14px;
    }

    .filter-checkbox {
        font-size: 13px;
        padding: 10px 8px;
    }

    .btn-close-filter {
        width: 36px;
        height: 36px;
    }
}

/* Ajustes para dispositivos muy pequeños */
@media screen and (max-width: 400px) {
    /* Reducir aún más el padding */
    .container {
        padding: 5px;
    }

    /* Ajustar tamaño de fuente general */
    body {
        font-size: 13px;
    }

    /* Mantener la tabla con scroll horizontal */
    .admin-table {
        min-width: 700px;
    }

    /* Indicador visual de scroll horizontal */
    .admin-table-container:after {
        content: '← Desliza →';
        display: block;
        text-align: center;
        padding: 5px;
        font-size: 11px;
        color: var(--text-secondary);
        background-color: var(--gray-bg);
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    /* Ajustar tamaño de botones */
    .btn {
        padding: 5px 8px;
        font-size: 11px;
        gap: 4px;
    }

    /* Ajustar pestañas */
    .tab-btn {
        padding: 8px 10px;
        font-size: 11px;
    }

    .tab-btn i {
        font-size: 14px;
    }

    /* Ajustar formularios */
    .form-section-body {
        padding: 15px 10px;
    }

    /* Ajustar checkbox y radio */
    .checkbox-label, .radio-label {
        font-size: 12px;
    }

    /* Ajustar paginación */
    .pagination-item {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

/* Ajustes adicionales para pantallas muy pequeñas */
@media screen and (max-width: 360px) {
    .product-cards-container {
        gap: 8px;
        padding: 8px;
    }

    .card-image {
        height: 100px;
    }

    .card-title {
        font-size: 12px;
    }
}


/* Ocultar elementos con la="nav-section-title" cuando el sidebar está contraído */
aside.collapsed [la="nav-section-title"],
#sidebar.collapsed [la="nav-section-title"] {
    display: none !important;
}

/* En modo móvil, ocultar cuando no está expandido */
@media screen and (max-width: 992px) {
    #sidebar:not(.expanded) [la="nav-section-title"] {
        display: none !important;
    }
}