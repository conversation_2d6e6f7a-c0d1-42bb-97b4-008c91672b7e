<?php
/**
 * Sistema de logging centralizado
 * Este archivo proporciona funciones para registrar eventos y errores de manera consistente
 */

// Definir constantes para los niveles de log
define('LOG_ERROR', 'ERROR');
define('LOG_WARNING', 'WARNING');
define('LOG_INFO', 'INFO');
define('LOG_DEBUG', 'DEBUG');

// Definir la ruta del archivo de log
$log_directory = __DIR__ . '/../logs';
$log_file = $log_directory . '/app_' . date('Y-m-d') . '.log';

// Crear el directorio de logs si no existe
if (!file_exists($log_directory)) {
    mkdir($log_directory, 0755, true);
}

/**
 * Registra un mensaje en el archivo de log
 * 
 * @param string $level Nivel del log (ERROR, WARNING, INFO, DEBUG)
 * @param string $message Mensaje a registrar
 * @param array $context Datos adicionales para el contexto del mensaje
 * @return bool Éxito o fracaso de la operación
 */
function logMessage($level, $message, $context = []) {
    global $log_file;
    
    // Obtener información de la llamada
    $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
    $caller = isset($backtrace[1]) ? $backtrace[1] : $backtrace[0];
    
    $file = isset($caller['file']) ? basename($caller['file']) : 'unknown';
    $line = isset($caller['line']) ? $caller['line'] : 'unknown';
    $function = isset($caller['function']) ? $caller['function'] : 'unknown';
    
    // Formatear el mensaje
    $timestamp = date('Y-m-d H:i:s');
    $contextString = !empty($context) ? ' - ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
    $logEntry = "[$timestamp] [$level] [$file:$line] [$function] - $message$contextString" . PHP_EOL;
    
    // Escribir en el archivo de log
    $result = error_log($logEntry, 3, $log_file);
    
    // También enviar a error_log del sistema si es un error
    if ($level === LOG_ERROR) {
        error_log("$level: $message", 0);
    }
    
    return $result;
}

/**
 * Registra un mensaje de error
 * 
 * @param string $message Mensaje de error
 * @param array $context Datos adicionales para el contexto del error
 */
function logError($message, $context = []) {
    return logMessage(LOG_ERROR, $message, $context);
}

/**
 * Registra un mensaje de advertencia
 * 
 * @param string $message Mensaje de advertencia
 * @param array $context Datos adicionales para el contexto
 */
function logWarning($message, $context = []) {
    return logMessage(LOG_WARNING, $message, $context);
}

/**
 * Registra un mensaje informativo
 * 
 * @param string $message Mensaje informativo
 * @param array $context Datos adicionales para el contexto
 */
function logInfo($message, $context = []) {
    return logMessage(LOG_INFO, $message, $context);
}

/**
 * Registra un mensaje de depuración
 * 
 * @param string $message Mensaje de depuración
 * @param array $context Datos adicionales para el contexto
 */
function logDebug($message, $context = []) {
    return logMessage(LOG_DEBUG, $message, $context);
}

/**
 * Registra una excepción
 * 
 * @param Exception $exception La excepción a registrar
 * @param array $context Datos adicionales para el contexto
 */
function logException($exception, $context = []) {
    $context['exception'] = [
        'message' => $exception->getMessage(),
        'code' => $exception->getCode(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ];
    
    return logError('Excepción: ' . $exception->getMessage(), $context);
}
