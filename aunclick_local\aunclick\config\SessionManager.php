<?php
require_once __DIR__ . '/SessionTracker.php';

class SessionManager {
    private static $instance = null;
    private $sessionLifetime;
    private $cookiePath;
    private $cookieDomain;
    private $sessionName = 'VILLARRICA_SESSION';
    private $sessionTracker = null;
    
    private function __construct() {
        $this->sessionLifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 86400;
        // Usar BASE_URL que ya está definido en config.php
        $this->cookiePath = defined('BASE_URL') ? BASE_URL : '/projects/villarrica_click';
        $this->cookieDomain = defined('COOKIE_DOMAIN') ? COOKIE_DOMAIN : '';
        
        // Eliminar otras cookies de sesión si existen
        if (isset($_COOKIE['PHPSESSID'])) {
            setcookie('PHPSESSID', '', time() - 3600, '/');
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function initializeSession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            error_log("SessionManager::initializeSession - Sesión ya activa, ID: " . session_id());
            
            // Validar la sesión activa
            if (!$this->isValid()) {
                error_log("SessionManager::initializeSession - Sesión activa inválida, regenerando...");
                $this->regenerateSession();
            }
            return;
        }

        // Configurar sesión
        ini_set('session.use_strict_mode', 1);
        ini_set('session.use_cookies', 1);
        ini_set('session.use_only_cookies', 1);
        session_name($this->sessionName);
        
        $cookie_params = [
            'lifetime' => $this->sessionLifetime,
            'path' => $this->cookiePath,
            'domain' => $this->cookieDomain,
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Lax'
        ];
        
        session_set_cookie_params($cookie_params);

        // Asegurar que la ruta de las cookies coincida con BASE_URL
        $cookieParams = session_get_cookie_params();
        session_set_cookie_params(
            $cookieParams['lifetime'],
            BASE_URL, // Usar BASE_URL para la ruta de las cookies
            $cookieParams['domain'],
            $cookieParams['secure'],
            $cookieParams['httponly']
        );

        // Iniciar sesión
        $result = session_start();
        error_log("SessionManager::initializeSession - Session start resultado: " . ($result ? "éxito" : "fallo"));
        error_log("SessionManager::initializeSession - Nueva sesión iniciada, ID: " . session_id());
        error_log("SessionManager::initializeSession - Cookie params: " . print_r(session_get_cookie_params(), true));
        
        if (!$this->isValid()) {
            error_log("SessionManager::initializeSession - Sesión inválida, regenerando...");
            $this->regenerateSession();
        }
    }

    public function updateCookiePath($path) {
        $this->cookiePath = rtrim($path, '/');
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Actualizar la cookie con la nueva ruta
            setcookie(session_name(), session_id(), [
                'expires' => time() + $this->sessionLifetime,
                'path' => $this->cookiePath,
                'domain' => $this->cookieDomain,
                'secure' => false,
                'httponly' => true,
                'samesite' => 'Lax'
            ]);
        }
        error_log("SessionManager::updateCookiePath - Ruta de cookie actualizada a: " . $this->cookiePath);
    }

    public function getCookiePath() {
        return $this->cookiePath;
    }

    public function getCookieDomain() {
        return $this->cookieDomain;
    }

    public function isValid() {
        if (!isset($_SESSION['created_at'])) {
            error_log("SessionManager::isValid - Falta created_at en sesión");
            return false;
        }

        $session_age = time() - $_SESSION['created_at'];
        if ($session_age > $this->sessionLifetime) {
            error_log("SessionManager::isValid - Sesión expirada. Edad: {$session_age}s, Límite: {$this->sessionLifetime}s");
            return false;
        }

        if (!isset($_SESSION['last_activity'])) {
            error_log("SessionManager::isValid - Falta last_activity en sesión");
            return false;
        }

        $inactivity_time = time() - $_SESSION['last_activity'];
        if ($inactivity_time > 1800) { // 30 minutos
            error_log("SessionManager::isValid - Sesión expirada por inactividad. Tiempo: {$inactivity_time}s");
            return false;
        }

        // Verificar IP si está configurada
        if (isset($_SESSION['ip']) && $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
            error_log("SessionManager::isValid - IP no coincide. Sesión: {$_SESSION['ip']}, Actual: {$_SERVER['REMOTE_ADDR']}");
            return false;
        }

        return true;
    }

    public function regenerateSession() {
        // Guardar datos importantes
        $oldData = $_SESSION;
        
        // Limpiar sesión
        $_SESSION = [];
        
        // Regenerar ID
        if (session_regenerate_id(true)) {
            // Restaurar datos básicos
            $_SESSION['created_at'] = time();
            $_SESSION['last_activity'] = time();
            $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
            $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
            
            // Restaurar datos de autenticación si existían
            if (isset($oldData['user_id'])) {
                $_SESSION['user_id'] = $oldData['user_id'];
                $_SESSION['username'] = $oldData['username'] ?? null;
                $_SESSION['role'] = $oldData['role'] ?? null;
                $_SESSION['logged_in'] = $oldData['logged_in'] ?? false;
            }
            
            error_log("SessionManager::regenerateSession - Sesión regenerada exitosamente. Nuevo ID: " . session_id());
            error_log("SessionManager::regenerateSession - Datos restaurados: " . print_r($_SESSION, true));
        } else {
            error_log("SessionManager::regenerateSession - Error al regenerar sesión");
        }
    }

    public function createUserSession($userData) {
        global $conn; // Usamos la conexión global de la base de datos
        
        error_log("SessionManager::createUserSession - Iniciando con datos: " . print_r($userData, true));
        
        // Regenerar sesión para obtener nueva ID
        $this->regenerateSession();
        
        // Establecer datos de usuario
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['username'] = $userData['username'];
        $_SESSION['role'] = $userData['role'];
        $_SESSION['logged_in'] = true;
        $_SESSION['created_at'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
        
        // Inicializar tracker de sesiones y registrarla en la BD
        $this->sessionTracker = SessionTracker::getInstance($conn);
        $this->sessionTracker->registerSession($userData['id'], $userData['username']);
        
        // Forzar escritura de sesión
        session_write_close();
        session_start();
        
        error_log("SessionManager::createUserSession - Sesión creada para usuario: " . $userData['username']);
        error_log("SessionManager::createUserSession - Datos de sesión final: " . print_r($_SESSION, true));
    }

    public function updateActivity() {
        global $conn;
        
        $_SESSION['last_activity'] = time();
        
        // Si es una nueva página, registrar la visita
        if (isset($_SESSION['current_page']) && $_SESSION['current_page'] !== $_SERVER['REQUEST_URI']) {
            // Actualizar tiempo en la página anterior
            if ($this->sessionTracker === null) {
                $this->sessionTracker = SessionTracker::getInstance($conn);
            }
            
            $this->sessionTracker->updateTimeSpent($_SESSION['current_page']);
            $this->sessionTracker->trackPageView();
        }
        
        // Actualizar página actual
        $_SESSION['current_page'] = $_SERVER['REQUEST_URI'];
    }

    public function destroySession() {
        global $conn;
        
        // Registrar cierre de sesión en la BD si el usuario estaba logueado
        if ($this->isLoggedIn() && isset($_SESSION['user_id'])) {
            if ($this->sessionTracker === null) {
                $this->sessionTracker = SessionTracker::getInstance($conn);
            }
            $this->sessionTracker->logoutSession();
        }
        
        // Guardar el estado actual de la sesión para debugging
        $oldSessionId = session_id();
        error_log("SessionManager::destroySession - ID antes de destruir: " . $oldSessionId);
        
        // Limpiar todas las variables de sesión
        $_SESSION = [];
        
        // Eliminar la cookie de sesión
        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 3600,
                BASE_URL, // Usar BASE_URL para la ruta de las cookies
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
            unset($_COOKIE[session_name()]);
        }
        
        // Si hay otras cookies de sesión, también eliminarlas
        if (isset($_COOKIE['PHPSESSID'])) {
            setcookie('PHPSESSID', '', time() - 3600, '/');
            unset($_COOKIE['PHPSESSID']);
        }
        
        // Destruir la sesión
        session_destroy();
        
        // Forzar la escritura y cierre de la sesión
        session_write_close();
        
        error_log("SessionManager::destroySession - Sesión destruida. ID anterior: " . $oldSessionId);
        error_log("SessionManager::destroySession - Cookies después de destruir: " . print_r($_COOKIE, true));
    }

    public function isLoggedIn() {
        $loggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
        error_log("SessionManager::isLoggedIn - Estado: " . ($loggedIn ? 'true' : 'false'));
        return $loggedIn;
    }

    public function requireLogin() {
        if (!$this->isLoggedIn() && !headers_sent()) {
            header('Location: ' . BASE_URL . '/public/login.php?error=unauthorized&reason=login_required');
            exit();
        } else if (!$this->isLoggedIn()) {
            exit('No autorizado. Por favor <a href="' . BASE_URL . '/public/login.php">inicie sesión</a>.');
        }
    }

    public function requireAdmin() {
        $this->requireLogin();
        
        $allowed_roles = ['admin', 'premium', 'pro'];
        if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], $allowed_roles)) {
            error_log("SessionManager::requireAdmin - Usuario no tiene permisos suficientes - Role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'no definido'));
            if (!headers_sent()) {
                header('Location: ' . BASE_URL . '/public/profile.php?error=unauthorized&reason=insufficient_permissions');
                exit();
            } else {
                exit('Permisos insuficientes. <a href="' . BASE_URL . '/public/profile.php">Volver al perfil</a>.');
            }
        }
    }

    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'role' => $_SESSION['role']
        ];
    }

    public function validateSession() {
        global $conn;
        
        error_log("SessionManager::validateSession - Iniciando validación");
        
        if (session_status() !== PHP_SESSION_ACTIVE) {
            error_log("SessionManager::validateSession - Sesión no activa, inicializando");
            $this->initializeSession();
        }

        // Actualizar última actividad
        if (isset($_SESSION['last_activity'])) {
            $_SESSION['last_activity'] = time();
        }

        $isValid = $this->isValid();
        error_log("SessionManager::validateSession - Sesión válida: " . ($isValid ? 'true' : 'false'));

        $isLoggedIn = $this->isLoggedIn();
        error_log("SessionManager::validateSession - Usuario logueado: " . ($isLoggedIn ? 'true' : 'false'));
        
        // Si el usuario está logueado, verificar si necesitamos registrar la sesión
        if ($isLoggedIn && !isset($this->sessionTracker)) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
            // Registrar sesión si no está registrada
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                $this->sessionTracker->registerSession($_SESSION['user_id'], $_SESSION['username']);
            }
        }
        
        return $isLoggedIn && $isValid;
    }
    
    /**
     * Obtener estadísticas de la sesión del usuario actual
     */
    public function getCurrentUserStats() {
        global $conn;
        
        if (!$this->isLoggedIn() || !isset($_SESSION['user_id'])) {
            return null;
        }
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getUserSessionStats($_SESSION['user_id']);
    }
    
    /**
     * Obtener estadísticas generales del sitio
     */
    public function getSiteStats($days = 30) {
        global $conn;
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getSiteStats($days);
    }
    
    /**
     * Obtener páginas más visitadas
     */
    public function getMostVisitedPages($limit = 10) {
        global $conn;
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getMostVisitedPages($limit);
    }
}