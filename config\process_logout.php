<?php
/**
 * process_logout.php
 * 
 * Script para procesar la solicitud de logout
 */

// Limpiar buffer de salida para prevenir problemas de encabezados
while (ob_get_level()) ob_end_clean();
ob_start();

// Incluir los archivos necesarios
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/SecurityService.php';
require_once __DIR__ . '/SessionManager.php';
require_once __DIR__ . '/LoginHandler.php';

// Inicializar el manejador de login
$loginHandler = new \Config\LoginHandler();

// Procesar la solicitud de logout
$loginHandler->processLogout();