<?php
/**
 * Script para modificar el comportamiento de redirección en login.php
 * Este script crea una copia de seguridad del archivo original y luego
 * modifica el comportamiento de redirección para permitir el acceso a la página de login
 */

// Configuración
$login_file = '/var/www/aunclick/public/login.php';
$backup_file = '/var/www/aunclick/public/login.php.bak';

// Verificar si el archivo existe
if (!file_exists($login_file)) {
    die("Error: El archivo $login_file no existe.\n");
}

// Crear copia de seguridad
if (!copy($login_file, $backup_file)) {
    die("Error: No se pudo crear una copia de seguridad del archivo.\n");
}

echo "Se ha creado una copia de seguridad en $backup_file\n";

// Leer el contenido del archivo
$content = file_get_contents($login_file);
if ($content === false) {
    die("Error: No se pudo leer el archivo $login_file.\n");
}

// Buscar el bloque de código que realiza la redirección
$pattern = '/\/\/ Verificar si el usuario ya está autenticado\s*if \(\$sessionManager->isLoggedIn\(\)\) \{.*?\/\/ Redirigir a tienda_adm\.php.*?\$redirect_url = BASE_URL \. \'\/public\/tienda_adm\.php\';/s';

// Nuevo código que permite forzar la visualización del formulario de login
$replacement = '// Verificar si el usuario ya está autenticado
if ($sessionManager->isLoggedIn() && !isset($_GET[\'force_login\'])) {
    $currentUser = $sessionManager->getCurrentUser();
    error_log("Login.php - Usuario ya autenticado: " . $currentUser[\'username\']);
    
    // Forzar escritura de sesión antes de redireccionar
    session_write_close();
    
    // Redirigir a tienda_adm.php
    $redirect_url = BASE_URL . \'/public/tienda_adm.php\';';

// Realizar la sustitución
$new_content = preg_replace($pattern, $replacement, $content);

// Verificar si se realizó algún cambio
if ($new_content === $content) {
    echo "Advertencia: No se encontró el patrón de redirección o no se realizaron cambios.\n";
} else {
    // Escribir el contenido modificado de vuelta al archivo
    if (file_put_contents($login_file, $new_content) === false) {
        die("Error: No se pudo escribir en el archivo $login_file.\n");
    }
    echo "El archivo ha sido modificado correctamente.\n";
    echo "Ahora puedes acceder a la página de login usando ?force_login=1 en la URL.\n";
}

echo "Proceso completado.\n";
?>
