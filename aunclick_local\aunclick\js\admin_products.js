document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado completamente - Iniciando script de tienda_adm');

    // IMPORTANTE: Obtener referencia al sidebar
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) {
        console.error('No se encontró el elemento sidebar con ID "sidebar"');
    }

    // Referencias a elementos principales con verificación de existencia
    const productsSection = document.getElementById('productsSection');
    const editProductSection = document.getElementById('editProductSection');
    const storeInfoSection = document.getElementById('storeInfoSection');
    const statsSection = document.getElementById('statsSection');
    const categoriesSection = document.getElementById('categoriesSection');

    // Referencias a las pestañas principales
    const productsTab = document.getElementById('productsTab');
    const editProductTab = document.getElementById('editProductTab');
    const storeTab = document.getElementById('storeTab');

    // Referencias a los enlaces del menú lateral
    const statsLink = document.getElementById('statsLink');
    const dashboardLink = document.getElementById('dashboardLink');
    const productsNavLink = document.getElementById('productsNavLink');
    const categoriesLink = document.getElementById('categoriesLink');

    // Referencias para el toggle del sidebar
    const asideToggle = document.getElementById('aside-toggle');
    const toggleIcon = document.getElementById('toggle-icon');

    // Verificar elementos críticos
    if (!asideToggle) {
        console.error('No se encontró el botón de toggle del sidebar con ID "aside-toggle"');
    }

    if (!toggleIcon) {
        console.error('No se encontró el icono del toggle con ID "toggle-icon"');
    }

    // TOGGLE DE SIDEBAR SIMPLIFICADO
    function toggleSidebar() {
        if (!sidebar) return;

        if (window.innerWidth < 992) {
            // En móvil: toggleamos la clase 'expanded'
            sidebar.classList.toggle('expanded');
        } else {
            // En desktop: toggleamos la clase 'collapsed'
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
        }

        // Actualizar el icono
        if (toggleIcon) {
            if ((window.innerWidth < 992 && sidebar.classList.contains('expanded')) ||
                (window.innerWidth >= 992 && !sidebar.classList.contains('collapsed'))) {
                toggleIcon.className = 'fas fa-chevron-left';
            } else {
                toggleIcon.className = 'fas fa-chevron-right';
            }
        }

        console.log('Sidebar toggled:', window.innerWidth < 992 ? 'mobile' : 'desktop');
    }

    // Función para inicializar el estado del sidebar
    function initSidebar() {
        if (!sidebar || !toggleIcon) return;

        const asideCollapsed = localStorage.getItem('asideCollapsed') === 'true';

        if (window.innerWidth < 992) {
            // En móvil siempre iniciamos collapsed (sin la clase expanded)
            sidebar.classList.remove('expanded');
            toggleIcon.className = 'fas fa-chevron-right';
        } else {
            // En desktop aplicamos preferencia del usuario
            if (asideCollapsed) {
                sidebar.classList.add('collapsed');
                toggleIcon.className = 'fas fa-chevron-right';
            } else {
                sidebar.classList.remove('collapsed');
                toggleIcon.className = 'fas fa-chevron-left';
            }
        }
    }

    // CREAR TARJETAS PARA PRODUCTOS EN VISTA MÓVIL
    function createProductCards() {
        console.log("Creando tarjetas de productos");
        const productCardsContainer = document.getElementById('product-cards-container');
        if (!productCardsContainer) {
            console.error("No se encontró el contenedor de tarjetas con ID 'product-cards-container'");
            return;
        }

        // Limpiar tarjetas existentes
        productCardsContainer.innerHTML = '';

        // Obtener filas de la tabla de productos
        const productRows = document.querySelectorAll('.admin-table tbody tr');
        if (productRows.length === 0) {
            console.warn("No se encontraron filas de productos en la tabla");
            return;
        }

        console.log(`Procesando ${productRows.length} productos`);

        // Crear tarjeta para cada producto
        productRows.forEach((row, index) => {
            try {
                // Crear un elemento de tarjeta
                const card = document.createElement('div');
                card.className = 'product-card';
                card.dataset.rowIndex = index;

                // Obtener datos del producto de la fila de la tabla con manejo seguro
                const imageElement = row.querySelector('.product-image-cell img');
                const image = imageElement && imageElement.src ? imageElement.src : 'https://via.placeholder.com/300x200';

                const nameElement = row.querySelector('.product-name-cell .product-name');
                const productName = nameElement ? nameElement.textContent : 'Producto';

                const categoryElement = row.querySelector('.product-name-cell .product-category');
                const productCategory = categoryElement ? categoryElement.textContent : 'Categoría';

                // Obtener precio y posibles descuentos
                const priceElement = row.querySelector('.product-price');
                const price = priceElement ? priceElement.textContent : '';

                const originalPriceElement = row.querySelector('.product-original-price');
                const originalPrice = originalPriceElement ? originalPriceElement.textContent : '';

                const discountElement = row.querySelector('.product-discount');
                const discount = discountElement ? discountElement.textContent : '';

                // Obtener categorías
                const mainCategoryElement = row.querySelector('td:nth-child(4) .category-text');
                const mainCategory = mainCategoryElement ? mainCategoryElement.textContent : '';

                const subCategoryElement = row.querySelector('td:nth-child(5) .category-text');
                const subCategory = subCategoryElement ? subCategoryElement.textContent : '';

                // Obtener condición y estado
                const conditionElement = row.querySelector('.condition-badge');
                const condition = conditionElement ? conditionElement.textContent : '';
                const conditionClass = conditionElement ? conditionElement.className.split(' ')[1] || '' : '';

                const statusElement = row.querySelector('.status-badge');
                const status = statusElement ? statusElement.textContent : '';
                const statusClass = statusElement ? statusElement.className.split(' ')[1] || '' : '';

                // Crear estructura HTML de la tarjeta
                card.innerHTML = `
                    <div class="card-image">
                        <img src="${image}" alt="${productName}">
                        <div class="card-badges">
                            ${condition ? `<span class="condition-badge ${conditionClass}">${condition}</span>` : ''}
                            ${status ? `<span class="status-badge ${statusClass}">${status}</span>` : ''}
                        </div>
                        ${discount ? `<div class="discount-badge">${discount}</div>` : ''}
                    </div>
                    <div class="card-body">
                        <h3 class="card-title">${productName}</h3>
                        <p class="card-category">${productCategory}</p>
                        <div class="card-price">
                            <span class="current-price">${price}</span>
                            ${originalPrice ? `<span class="original-price">${originalPrice}</span>` : ''}
                        </div>
                        <div class="card-tags">
                            ${mainCategory ? `<span class="card-tag">${mainCategory}</span>` : ''}
                            ${subCategory ? `<span class="card-tag">${subCategory}</span>` : ''}
                        </div>
                    </div>
                    <div class="card-actions">
                        <button class="card-btn edit-btn">
                            <i class="fas fa-edit"></i> Editar
                        </button>
                        <button class="card-btn delete-btn">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;

                // Agregar eventos a los botones de manera directa
                const editBtn = card.querySelector('.edit-btn');
                if (editBtn) {
                    editBtn.addEventListener('click', function() {
                        console.log('Botón editar pulsado en tarjeta');
                        // Llamar al botón editar en la tabla correspondiente
                        const tableEditBtn = row.querySelector('.edit-btn');
                        if (tableEditBtn) {
                            tableEditBtn.click();
                        } else {
                            handleEditProduct(row);
                        }
                    });
                }

                const deleteBtn = card.querySelector('.delete-btn');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', function() {
                        console.log('Botón eliminar pulsado en tarjeta');
                        // Llamar al botón eliminar en la tabla correspondiente
                        const tableDeleteBtn = row.querySelector('.delete-btn');
                        if (tableDeleteBtn) {
                            tableDeleteBtn.click();
                        } else {
                            handleDeleteProduct(row);
                        }
                    });
                }

                // Agregar la tarjeta al contenedor
                productCardsContainer.appendChild(card);

            } catch (error) {
                console.error(`Error al procesar la fila ${index}:`, error);
            }
        });

        console.log(`Se crearon ${productRows.length} tarjetas de productos`);
    }

    // Respaldo para editar producto si falla el click en el botón de la tabla
    function handleEditProduct(tableRow) {
        console.log("Usando función de respaldo para editar producto");
        // Extraer datos del producto de la fila
        const productNameElement = tableRow.querySelector('.product-name');
        const productName = productNameElement ? productNameElement.textContent : '';

        const productCategoryElement = tableRow.querySelector('.product-category');
        const productCategory = productCategoryElement ? productCategoryElement.textContent : '';

        // Cambiar el título del formulario para indicar que es una edición
        const sectionTitle = document.querySelector('#editProductSection .section-title');
        if (sectionTitle) {
            sectionTitle.innerHTML = '<i class="fas fa-edit"></i> Editar Producto';
        }

        // Cargar datos en el formulario si los elementos existen
        const productNameInput = document.getElementById('productName');
        if (productNameInput) {
            productNameInput.value = productName;
        }

        const productDescriptionInput = document.getElementById('productDescription');
        if (productDescriptionInput) {
            productDescriptionInput.value = 'Descripción detallada del producto ' + productName;
        }

        const productShortDescriptionInput = document.getElementById('productShortDescription');
        if (productShortDescriptionInput) {
            productShortDescriptionInput.value = productCategory;
        }

        // Mostrar el formulario de edición
        showEditProductSection();
    }

    // Respaldo para eliminar producto si falla el click en el botón de la tabla
    function handleDeleteProduct(tableRow) {
        console.log("Usando función de respaldo para eliminar producto");
        const productNameElement = tableRow.querySelector('.product-name');
        const productName = productNameElement ? productNameElement.textContent : 'este producto';

        // Mostrar confirmación
        if (confirm(`¿Estás seguro de que deseas eliminar ${productName}?`)) {
            alert(`El producto ${productName} ha sido eliminado.`);
            // Aquí iría la lógica real para eliminar el producto
        }
    }

    // Actualizar vista según ancho de pantalla
    function updateResponsiveView() {
        console.log(`Actualizando vista responsiva. Ancho de ventana: ${window.innerWidth}px`);
        const isMobile = window.innerWidth <= 768;

        // Obtener elementos
        const adminTable = document.querySelector('.admin-table');
        const productCardsContainer = document.getElementById('product-cards-container');

        if (!adminTable || !productCardsContainer) {
            console.error("No se encontró la tabla o el contenedor de tarjetas");
            return;
        }

        // Mostrar vista correspondiente según ancho de pantalla
        if (isMobile) {
            console.log("Mostrando vista móvil (tarjetas)");
            adminTable.style.display = 'none';
            productCardsContainer.style.display = 'flex';
        } else {
            console.log("Mostrando vista desktop (tabla)");
            adminTable.style.display = 'table';
            productCardsContainer.style.display = 'none';
        }
    }

    // Inicializar tarjetas responsivas
    function initResponsiveProductCards() {
        console.log("Inicializando tarjetas responsivas");
        createProductCards();
        updateResponsiveView();
    }

    // SIMPLIFICACIÓN DE LA INTERACTIVIDAD MÓVIL
    function enhanceButtonInteractivity() {
        console.log("Mejorando interactividad para botones");

        // Selectores de botones a mejorar
        const buttonSelectors = [
            '.btn', '.action-btn', '.card-btn', '.nav-link',
            '.tab-btn', '#aside-toggle', '.pagination-item'
        ];

        // Aplicar a todos los botones existentes
        document.querySelectorAll(buttonSelectors.join(',')).forEach(button => {
            // Eliminar eventos existentes para evitar duplicados
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Añadir eventos directos
            newButton.addEventListener('click', function(e) {
                // Solo prevenir comportamiento por defecto cuando es necesario
                if (this.tagName === 'A' && this.getAttribute('href') === '#') {
                    e.preventDefault();
                }

                console.log(`Botón clickeado: ${this.textContent.trim() || this.className}`);
            });
        });

        // IMPORTANTE: Configurar evento de toggle del sidebar explícitamente
        if (asideToggle) {
            const newToggleBtn = asideToggle.cloneNode(true);
            asideToggle.parentNode.replaceChild(newToggleBtn, asideToggle);

            newToggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSidebar();
                console.log('Toggle del sidebar activado');
            });
        }
    }

    // FUNCIÓN PARA ACTUALIZAR BREADCRUMBS
    function updateBreadcrumbs(section) {
        const breadcrumbsContainer = document.querySelector('.breadcrumbs');
        if (!breadcrumbsContainer) return; // Si no hay breadcrumbs, no hacer nada

        // Limpiar breadcrumbs actuales
        breadcrumbsContainer.innerHTML = '';

        // Crear enlace a inicio
        const homeLink = document.createElement('a');
        homeLink.href = '#';
        homeLink.textContent = 'Inicio';
        breadcrumbsContainer.appendChild(homeLink);

        // Agregar separador
        const separator = document.createElement('span');
        separator.className = 'separator';
        separator.textContent = '/';
        breadcrumbsContainer.appendChild(separator);

        // Agregar sección actual
        const currentSection = document.createElement('span');
        currentSection.className = 'current';

        // Determinar el texto según la sección
        switch(section) {
            case 'products':
                currentSection.textContent = 'Productos';
                break;
            case 'stats':
                currentSection.textContent = 'Estadísticas';
                break;
            case 'categories':
                currentSection.textContent = 'Categorías';
                break;
            case 'store':
                currentSection.textContent = 'Mi Tienda';
                break;
            case 'edit-product':
                currentSection.textContent = 'Editar Producto';
                break;
            default:
                currentSection.textContent = 'Dashboard';
        }

        breadcrumbsContainer.appendChild(currentSection);

        // Agregar evento al enlace de inicio
        homeLink.addEventListener('click', function(e) {
            e.preventDefault();
            showProductsSection();
        });
    }

    // INICIALIZAR GRÁFICOS
    function initCharts() {
        try {
            // Comprobar si los elementos del canvas existen
            const visitsMonthlyChartCtx = document.getElementById('visitsMonthlyChart');
            const devicesChartCtx = document.getElementById('devicesChart');
            const topProductsChartCtx = document.getElementById('topProductsChart');
            const categoriesChartCtx = document.getElementById('categoriesChart');

            if (!visitsMonthlyChartCtx || !devicesChartCtx || !topProductsChartCtx || !categoriesChartCtx) {
                console.warn('No se encontraron todos los elementos de canvas para los gráficos');
                return;
            }

            // Inicializar los gráficos
            if (typeof Chart !== 'undefined') {
                // Datos para el gráfico de visitas mensuales
                new Chart(visitsMonthlyChartCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio'],
                        datasets: [{
                            label: 'Visitas 2023',
                            data: [1245, 1350, 1520, 1254, 1380, 1420],
                            borderColor: '#6a1b9a',
                            backgroundColor: 'rgba(106, 27, 154, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Visitas 2022',
                            data: [980, 1050, 1200, 1100, 1150, 1250],
                            borderColor: '#9e9e9e',
                            backgroundColor: 'rgba(158, 158, 158, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Inicializar otros gráficos de manera similar
                console.log('Gráficos inicializados correctamente');
            } else {
                console.error('La biblioteca Chart.js no está cargada correctamente');
            }
        } catch (error) {
            console.error('Error al inicializar los gráficos:', error);
        }
    }

    // Función para mostrar la sección de productos
    function showProductsSection() {
        try {
            // Ocultar todas las secciones excepto productos
            if (productsSection) productsSection.style.display = 'block';
            if (editProductSection) editProductSection.style.display = 'none';
            if (storeInfoSection) storeInfoSection.style.display = 'none';
            if (statsSection) statsSection.style.display = 'none';
            if (categoriesSection) categoriesSection.style.display = 'none';

            // Actualizar pestañas activas
            if (productsTab) productsTab.classList.add('active');
            if (editProductTab) editProductTab.classList.remove('active');
            if (storeTab) storeTab.classList.remove('active');

            // Actualizar enlaces del menú lateral
            if (dashboardLink) dashboardLink.classList.add('active');
            if (statsLink) statsLink.classList.remove('active');
            if (productsNavLink) productsNavLink.classList.remove('active');
            if (categoriesLink) categoriesLink.classList.remove('active');

            // Actualizar breadcrumbs
            updateBreadcrumbs('products');

            // Actualizar vista responsiva
            updateResponsiveView();

            console.log('Sección de productos mostrada correctamente');
        } catch (error) {
            console.error('Error al mostrar la sección de productos:', error);
        }
    }

    // Función para mostrar la sección de edición de producto
    function showEditProductSection() {
        try {
            // Ocultar todas las secciones excepto edición de producto
            if (productsSection) productsSection.style.display = 'none';
            if (editProductSection) editProductSection.style.display = 'block';
            if (storeInfoSection) storeInfoSection.style.display = 'none';
            if (statsSection) statsSection.style.display = 'none';
            if (categoriesSection) categoriesSection.style.display = 'none';

            // Actualizar pestañas activas
            if (productsTab) productsTab.classList.remove('active');
            if (editProductTab) editProductTab.classList.add('active');
            if (storeTab) storeTab.classList.remove('active');

            // Actualizar enlaces del menú lateral
            if (dashboardLink) dashboardLink.classList.remove('active');
            if (statsLink) statsLink.classList.remove('active');
            if (productsNavLink) productsNavLink.classList.add('active');
            if (categoriesLink) categoriesLink.classList.remove('active');

            console.log('Sección de edición de producto mostrada correctamente');
        } catch (error) {
            console.error('Error al mostrar la sección de edición de producto:', error);
        }
    }

    // Función para mostrar la sección de información de la tienda
    function showStoreInfoSection() {
        try {
            // Ocultar todas las secciones excepto información de tienda
            if (productsSection) productsSection.style.display = 'none';
            if (editProductSection) editProductSection.style.display = 'none';
            if (storeInfoSection) storeInfoSection.style.display = 'block';
            if (statsSection) statsSection.style.display = 'none';
            if (categoriesSection) categoriesSection.style.display = 'none';

            // Actualizar pestañas activas
            if (productsTab) productsTab.classList.remove('active');
            if (editProductTab) editProductTab.classList.remove('active');
            if (storeTab) storeTab.classList.add('active');

            // Actualizar enlaces del menú lateral
            if (dashboardLink) dashboardLink.classList.remove('active');
            if (statsLink) statsLink.classList.remove('active');
            if (productsNavLink) productsNavLink.classList.remove('active');
            if (categoriesLink) categoriesLink.classList.remove('active');

            // Actualizar breadcrumbs
            updateBreadcrumbs('store');

            console.log('Sección de información de tienda mostrada correctamente');
        } catch (error) {
            console.error('Error al mostrar la sección de información de tienda:', error);
        }
    }

    // Función para mostrar la sección de estadísticas
    function showStatsSection() {
        try {
            // Ocultar todas las secciones excepto estadísticas
            if (productsSection) productsSection.style.display = 'none';
            if (editProductSection) editProductSection.style.display = 'none';
            if (storeInfoSection) storeInfoSection.style.display = 'none';
            if (statsSection) statsSection.style.display = 'block';
            if (categoriesSection) categoriesSection.style.display = 'none';

            // Actualizar pestañas activas
            if (productsTab) productsTab.classList.remove('active');
            if (editProductTab) editProductTab.classList.remove('active');
            if (storeTab) storeTab.classList.remove('active');

            // Actualizar enlaces del menú lateral
            if (dashboardLink) dashboardLink.classList.remove('active');
            if (statsLink) statsLink.classList.add('active');
            if (productsNavLink) productsNavLink.classList.remove('active');
            if (categoriesLink) categoriesLink.classList.remove('active');

            // Actualizar breadcrumbs e inicializar gráficos
            updateBreadcrumbs('stats');
            initCharts();

            console.log('Sección de estadísticas mostrada correctamente');
        } catch (error) {
            console.error('Error al mostrar la sección de estadísticas:', error);
        }
    }

    // Función para mostrar la sección de categorías
    function showCategoriesSection() {
        try {
            // Ocultar todas las secciones excepto categorías
            if (productsSection) productsSection.style.display = 'none';
            if (editProductSection) editProductSection.style.display = 'none';
            if (storeInfoSection) storeInfoSection.style.display = 'none';
            if (statsSection) statsSection.style.display = 'none';
            if (categoriesSection) categoriesSection.style.display = 'block';

            // Actualizar pestañas activas
            if (productsTab) productsTab.classList.remove('active');
            if (editProductTab) editProductTab.classList.remove('active');
            if (storeTab) storeTab.classList.remove('active');

            // Actualizar enlaces del menú lateral
            if (dashboardLink) dashboardLink.classList.remove('active');
            if (statsLink) statsLink.classList.remove('active');
            if (productsNavLink) productsNavLink.classList.remove('active');
            if (categoriesLink) categoriesLink.classList.add('active');

            // Actualizar breadcrumbs
            updateBreadcrumbs('categories');

            console.log('Sección de categorías mostrada correctamente');
        } catch (error) {
            console.error('Error al mostrar la sección de categorías:', error);
        }
    }

    // Inicializar la aplicación
    function initApp() {
        console.log("Inicializando aplicación");

        try {
            // Inicializar componentes en el orden correcto
            initSidebar();
            initResponsiveProductCards();
            enhanceButtonInteractivity();

            // Configurar eventos para las pestañas principales
            if (productsTab) {
                productsTab.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Pestaña de productos clickeada');
                    showProductsSection();
                });
            }

            if (editProductTab) {
                editProductTab.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Pestaña de edición de producto clickeada');

                    // Cambiar el título del formulario para indicar que es una adición
                    const sectionTitle = document.querySelector('#editProductSection .section-title');
                    if (sectionTitle) {
                        sectionTitle.innerHTML = '<i class="fas fa-edit"></i> Agregar Producto';
                    }

                    // Limpiar el formulario
                    const inputs = [
                        'productName', 'productDescription', 'productShortDescription',
                        'productPrice', 'productOriginalPrice', 'productStock', 'productSKU'
                    ];

                    inputs.forEach(inputId => {
                        const input = document.getElementById(inputId);
                        if (input) input.value = '';
                    });

                    showEditProductSection();
                });
            }

            if (storeTab) {
                storeTab.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Pestaña de tienda clickeada');
                    showStoreInfoSection();
                });
            }

            // Asignar eventos a los enlaces del menú lateral
            if (dashboardLink) {
                dashboardLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Enlace de dashboard clickeado');
                    showProductsSection();
                });
            }

            if (statsLink) {
                statsLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Enlace de estadísticas clickeado');
                    showStatsSection();
                });
            }

            if (productsNavLink) {
                productsNavLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Enlace de productos clickeado');
                    showProductsSection();
                });
            }

            if (categoriesLink) {
                categoriesLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Enlace de categorías clickeado');
                    showCategoriesSection();
                });
            }

            // Configurar eventos para los botones de acción en la tabla de productos
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevenir propagación
                    console.log('Botón editar pulsado en tabla');

                    // Obtener la fila del producto
                    const row = this.closest('tr');
                    if (!row) return;

                    // Actualizar los datos en el formulario de edición
                    handleEditProduct(row);
                });
            });

            // Evento para el botón Cancelar en el formulario de edición
            const cancelEditBtn = document.getElementById('cancelEditBtn');
            if (cancelEditBtn) {
                cancelEditBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Botón cancelar clickeado');
                    showProductsSection();
                });
            }

            // Evento para el botón Nuevo Producto
            const newProductBtn = document.getElementById('newProductBtn');
            if (newProductBtn) {
                newProductBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Botón nuevo producto clickeado');

                    // Limpiar formulario
                    const inputs = [
                        'productName', 'productDescription', 'productShortDescription',
                        'productPrice', 'productOriginalPrice', 'productStock', 'productSKU'
                    ];

                    inputs.forEach(inputId => {
                        const input = document.getElementById(inputId);
                        if (input) input.value = '';
                    });

                    // Cambiar título
                    const sectionTitle = document.querySelector('#editProductSection .section-title');
                    if (sectionTitle) {
                        sectionTitle.innerHTML = '<i class="fas fa-edit"></i> Agregar Producto';
                    }

                    showEditProductSection();
                });
            }

            // Configurar eventos para los dropzones
            const dropzones = document.querySelectorAll('.dropzone');
            dropzones.forEach(dropzone => {
                // Al hacer clic en la zona de arrastre
                dropzone.addEventListener('click', function() {
                    alert('Esta funcionalidad requeriría JavaScript adicional para la carga de archivos. En una implementación real, aquí se abriría un selector de archivos.');
                });

                // Al arrastrar un archivo sobre la zona
                dropzone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.borderColor = 'var(--purple-primary)';
                    this.style.backgroundColor = 'rgba(106, 27, 154, 0.05)';
                });

                // Al salir de la zona de arrastre
                dropzone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.style.borderColor = 'var(--gray-medium)';
                    this.style.backgroundColor = '';
                });

                // Al soltar un archivo en la zona
                dropzone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.borderColor = 'var(--gray-medium)';
                    this.style.backgroundColor = '';
                    alert('Esta funcionalidad requeriría JavaScript adicional para procesar los archivos arrastrados. En una implementación real, aquí se procesarían los archivos.');
                });
            });

            // Configurar eventos para la paginación
            const paginationItems = document.querySelectorAll('.pagination-item');
            paginationItems.forEach(item => {
                item.addEventListener('click', function() {
                    paginationItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Configurar eventos para las acciones de imágenes
            const imageActionBtns = document.querySelectorAll('.image-action-btn');
            imageActionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation(); // Evitar que se propague al botón padre

                    const action = this.getAttribute('title');
                    if (action === 'Establecer como principal') {
                        alert('Imagen establecida como principal');
                    } else if (action === 'Eliminar') {
                        const confirmDelete = confirm('¿Estás seguro de que deseas eliminar esta imagen?');
                        if (confirmDelete) {
                            alert('Imagen eliminada');
                        }
                    }
                });
            });

            // Mostrar la sección inicial por defecto
            showProductsSection();

            console.log('Aplicación inicializada correctamente');
        } catch (error) {
            console.error('Error al inicializar la aplicación:', error);
        }
    }

    // Iniciar la aplicación
    initApp();

    // Ajustar sidebar y vista responsiva cuando cambia el tamaño de la ventana
    window.addEventListener('resize', function() {
        initSidebar();
        updateResponsiveView();
    });
});

// Manejador para los tooltips de información
document.addEventListener('DOMContentLoaded', function() {
    // Forzar a ocultar todos los tooltips al cargar la página
    const allTooltips = document.querySelectorAll('.info-tooltip');
    allTooltips.forEach(tooltip => {
        tooltip.style.display = 'none';
        tooltip.classList.remove('active');
    });

    // Seleccionar todos los íconos de información
    const infoIcons = document.querySelectorAll('.info-icon');

    // Cerrar todos los tooltips al hacer clic en cualquier parte del documento
    document.addEventListener('click', function(e) {
        if (!e.target.classList.contains('info-icon')) {
            allTooltips.forEach(tooltip => {
                tooltip.style.display = 'none';
                tooltip.classList.remove('active');
            });
        }
    });

    // Manejar clic en los íconos de información
    infoIcons.forEach(icon => {
        icon.addEventListener('click', function(e) {
            e.stopPropagation();
            e.preventDefault();

            // Cerrar todos los otros tooltips
            allTooltips.forEach(tooltip => {
                if (tooltip !== this.parentElement.querySelector('.info-tooltip')) {
                    tooltip.style.display = 'none';
                    tooltip.classList.remove('active');
                }
            });

            // Toggle del tooltip actual
            const tooltip = this.parentElement.querySelector('.info-tooltip');
            if (tooltip.classList.contains('active')) {
                tooltip.style.display = 'none';
                tooltip.classList.remove('active');
            } else {
                tooltip.style.display = 'block';
                tooltip.classList.add('active');

                // Asegurar que el tooltip sea visible en la pantalla
                setTimeout(() => {
                    const tooltipRect = tooltip.getBoundingClientRect();
                    if (tooltipRect.top < 0) {
                        // Si el tooltip está fuera de la pantalla por arriba, ajustar posición
                        tooltip.style.bottom = 'auto';
                        tooltip.style.top = 'calc(100% + 10px)';

                        // Ajustar la flecha
                        tooltip.style.setProperty('--arrow-top', '-8px');
                        tooltip.style.setProperty('--arrow-bottom', 'auto');
                        tooltip.style.setProperty('--arrow-border', 'transparent transparent var(--gray-900) transparent');
                    }
                }, 0);
            }
        });
    });

    // Cerrar tooltips al presionar la tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            allTooltips.forEach(tooltip => {
                tooltip.style.display = 'none';
                tooltip.classList.remove('active');
            });
        }
    });

    // Asegurarse de que los tooltips estén ocultos al cambiar de pestaña
    const tabLinks = document.querySelectorAll('.nav-link, .sidebar-link');
    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            allTooltips.forEach(tooltip => {
                tooltip.style.display = 'none';
                tooltip.classList.remove('active');
            });
        });
    });

    // Forzar a ocultar todos los tooltips nuevamente después de un breve retraso
    // para asegurarnos de que se aplique después de cualquier otro script
    setTimeout(() => {
        allTooltips.forEach(tooltip => {
            tooltip.style.display = 'none';
            tooltip.classList.remove('active');
        });
    }, 500);
});

