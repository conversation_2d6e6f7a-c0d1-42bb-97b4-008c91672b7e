/* =========================================
   VARIABLES (Assuming these exist elsewhere or define them here)
   ========================================= */
:root {
    --purple-primary: #6a1b9a; /* Example */
    --purple-dark: #4a148c;    /* Example */
    --purple-light: #ab47bc;   /* Example */
    --gray-light: #e0e0e0;     /* Example */
    --gray-medium: #bdbdbd;    /* Example */
    --gray-dark: #757575;      /* Example */
    --gray-bg: #f5f5f5;        /* Example */
    --text-primary: #212121;   /* Example */
    --text-secondary: #757575; /* Example */
    --green-primary: #43a047;  /* Example */
    --green-dark: #2e7d32;     /* Example */
    --red-error: #f44336;      /* Example */
    --blue-info: #2196f3;      /* Example */
    --yellow-light: #fff9c4;   /* Example */
    --yellow-accent: #ffc107;  /* Example */
    --border-radius: 8px;      /* Example */
    --box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* Example */
}

/* =========================================
   BASE & LAYOUT
   ========================================= */
html, body {
    scroll-behavior: smooth;
}

.container {
    /* Add container styles if needed */
}

.admin-layout {
    display: flex;
}

.main-content {
    flex-grow: 1;
    transition: margin-left 0.3s ease;
    /* Add padding or other styles */
}

/* =========================================
   SIDEBAR / ASIDE
   ========================================= */
aside {
    position: relative;
    width: 250px;
    transition: width 0.3s ease;
}

aside.collapsed {
    width: 70px !important; /* Collapsed width */
}

aside.collapsed .sidebar-logo,
aside.collapsed .sidebar-subtitle,
aside.collapsed .user-info,
aside.collapsed .nav-section-title,
aside.collapsed .nav-link span {
    display: none;
}

aside.collapsed .sidebar-header,
aside.collapsed .sidebar-user {
    justify-content: center;
    padding: 15px 0;
}

aside.collapsed .nav-link {
    justify-content: center;
    padding: 15px 0;
}

aside.collapsed .nav-link i {
    margin-right: 0;
}

/* Adjust main content margin based on sidebar state */
aside:not(.collapsed) + .main-content {
    margin-left: 250px; /* Match default sidebar width */
}

aside.collapsed + .main-content {
    margin-left: 70px; /* Match collapsed sidebar width */
}

/* Sidebar Toggle Button */
#aside-toggle {
    position: absolute;
    top: 20px;
    right: -16px; /* Posicionado en el borde derecho del sidebar */
    width: 32px;
    height: 32px;
    border-radius: 50%; /* Botón circular */
    background: var(--purple-primary);
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 100;
}

#aside-toggle:hover {
    background: var(--purple-dark);
    transform: scale(1.05);
}

#aside-toggle #toggle-icon {
    font-size: 20px; /* Consistent size */
    transition: transform 0.3s ease;
}

/* Rotation handled by JS */
#aside-toggle span {
    display: none; /* Hide text label */
}

/* Navigation Links */
.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white; /* Cambiado de var(--text-secondary) a white */
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(0,0,0,0.05);
    color: var(--yellow-accent); /* Opcional: cambiar el color al pasar el mouse */
}

.nav-link.active {
    background-color: rgba(106, 27, 154, 0.1);
    color: var(--yellow-accent);
    font-weight: 600;
    border-left-color: var(--yellow-accent);
}

.nav-link i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

/* =========================================
   TABS
   ========================================= */
.main-tabs {
    display: flex;
    border-bottom: 1px solid var(--gray-light);
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: var(--purple-primary);
}

.tab-btn.active {
    color: var(--purple-primary);
    font-weight: 600; /* Changed from bold */
    border-bottom-color: var(--purple-primary); /* Consistent active indicator */
}

/* =========================================
   CONTENT SECTIONS
   ========================================= */
.content-section {
    display: none; /* Hide sections by default */
}

.content-section.active-section {
    display: block !important; /* Show active section */
    animation: fadeIn 0.3s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--gray-light);
}

.section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-actions {
    display: flex;
    gap: 10px;
}

.section-body {
    /* Add padding or other styles */
}

.section-subtitle {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--gray-light);
}

/* =========================================
   BUTTONS
   ========================================= */
.btn {
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    display: inline-flex; /* Use inline-flex for alignment */
    align-items: center;
    justify-content: center; /* Center content */
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none; /* For button-like links */
    vertical-align: middle; /* Align with text */
    pointer-events: auto !important; /* Ensure clickable */
    z-index: 10 !important; /* Ensure clickable */
    position: relative !important; /* Ensure clickable */
}

.btn-primary {
    background-color: var(--purple-primary);
    color: white;
}
.btn-primary:hover { background-color: var(--purple-dark); }

.btn-secondary {
    background-color: var(--gray-light);
    color: var(--text-primary);
}
.btn-secondary:hover { background-color: var(--gray-medium); }

.btn-success {
    background-color: var(--green-primary);
    color: white;
}
.btn-success:hover { background-color: var(--green-dark); }

.btn-danger {
    background-color: var(--red-error);
    color: white;
}
.btn-danger:hover { background-color: #d32f2f; } /* Darker red */

/* Active state for feedback */
.btn:active, .tab-btn:active, .nav-link:active {
    opacity: 0.8;
    transform: scale(0.98);
}

/* =========================================
   TABLES
   ========================================= */
.admin-table-container {
    width: 100%;
    overflow-x: auto;
    position: relative;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    /* Hide table on smaller screens if cards are shown */
}
@media screen and (max-width: 768px) {
    .admin-table {
        display: none !important;
    }
}

.admin-table th, .admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--gray-light);
    vertical-align: middle; /* Align content vertically */
}

.admin-table th {
    font-weight: 600;
    color: var(--text-primary);
    background-color: var(--gray-bg);
    white-space: nowrap; /* Prevent header text wrapping */
}

.admin-table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Specific Table Cell Styles */
.admin-table .product-image-cell { width: 80px; text-align: center; }
.admin-table .product-image-preview {
    width: 50px; /* Consistent size */
    height: 50px; /* Consistent size */
    border-radius: 4px; /* Consistent radius */
    object-fit: cover;
    display: inline-block; /* Center properly */
    vertical-align: middle;
    cursor: pointer; /* Indicar que es clickeable */
    transition: transform 0.2s ease;
}

.admin-table .product-image-preview:hover {
    transform: scale(1.1); /* Efecto de zoom al pasar el mouse */
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.admin-table .product-name-cell { max-width: 250px; }
.admin-table .product-name {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}
.admin-table .product-category {
    font-size: 12px;
    color: var(--text-secondary);
}

.admin-table .product-price {
    font-weight: 600;
    color: var(--purple-primary);
    white-space: nowrap;
}
.admin-table .original-price { /* Renamed for consistency */
    text-decoration: line-through;
    color: var(--text-secondary);
    font-size: 12px;
    margin-left: 5px;
    display: block; /* Place below current price */
}

.admin-table .low-stock { color: var(--red-error); font-weight: bold; }
.admin-table .loading-row td { text-align: center; padding: 30px; color: var(--text-secondary); }
.admin-table .no-data td { text-align: center; padding: 30px; color: var(--text-secondary); }

/* Table Actions */
.admin-table .actions { width: 100px; text-align: right; }
.admin-table .table-actions {
    display: flex;
    gap: 8px; /* Reduced gap */
    justify-content: flex-end;
}

.action-btn {
    width: 32px; /* Slightly larger */
    height: 32px; /* Slightly larger */
    border-radius: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    pointer-events: auto !important;
    z-index: 10 !important;
    position: relative !important;
}
.action-btn:active { transform: scale(0.95); opacity: 0.8; }

.action-btn.edit-btn { color: var(--blue-info); background-color: rgba(33, 150, 243, 0.1); }
.action-btn.edit-btn:hover { background-color: rgba(33, 150, 243, 0.2); }

.action-btn.delete-btn { color: var(--red-error); background-color: rgba(244, 67, 54, 0.1); }
.action-btn.delete-btn:hover { background-color: rgba(244, 67, 54, 0.2); }

.action-btn.view-btn { color: var(--purple-primary); background-color: rgba(106, 27, 154, 0.1); }
.action-btn.view-btn:hover { background-color: rgba(106, 27, 154, 0.2); }

/* Table Row Highlighting */
.updated-row {
    animation: highlightRow 3s ease;
}
@keyframes highlightRow {
    0%, 50% { background-color: rgba(40, 167, 69, 0.15); } /* Slightly more visible */
    100% { background-color: transparent; }
}

/* =========================================
   IMAGE MODAL
   ========================================= */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease;
}

.image-modal-content {
    margin: auto;
    display: block;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    max-width: 90%;
    max-height: 90vh;
    animation: zoomIn 0.3s ease;
}

#modalImage {
    display: block;
    max-width: 100%;
    max-height: 90vh;
    margin: 0 auto;
    border-radius: 4px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.close-modal {
    position: absolute;
    top: -30px;
    right: 0;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
}

.close-modal:hover {
    color: var(--gray-light);
}

@keyframes zoomIn {
    from { transform: translateY(-50%) scale(0.9); opacity: 0; }
    to { transform: translateY(-50%) scale(1); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* =========================================
   PRODUCT CARDS (Mobile View)
   ========================================= */
.productos-container {
    width: 100%;
    position: relative;
}

.product-cards-container {
    display: none; /* Hidden by default, shown via media query */
    width: 100%;
    margin-bottom: 20px;
    padding: 10px; /* Base padding */
}

@media screen and (max-width: 768px) {
    .product-cards-container {
        display: grid !important; /* Use grid for layout */
        grid-template-columns: repeat(2, 1fr); /* Always 2 columns */
        gap: 12px; /* Consistent gap */
        padding: 12px; /* Slightly more padding */
    }
}
@media screen and (max-width: 400px) {
    .product-cards-container {
        gap: 8px; /* Smaller gap on very small screens */
        padding: 8px;
    }
}

.product-card {
    width: 100%; /* Grid handles width */
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    background: white;
    display: flex;
    flex-direction: column;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}
.product-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-card .card-image {
    position: relative;
    height: 120px; /* Consistent height */
    overflow: hidden;
}
.product-card .card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    cursor: pointer; /* Indicar que es clickeable */
}
.product-card:hover .card-image img {
    transform: scale(1.05);
}

.product-card .card-badges {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 2;
}
.product-card .discount-badge {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background: var(--yellow-light);
    color: #ff8f00;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 12px;
    z-index: 2;
}

.product-card .card-body {
    padding: 10px; /* Consistent padding */
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.product-card .card-title {
    margin: 0 0 5px 0;
    font-size: 13px; /* Consistent size */
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-primary);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 34px; /* Ensure space for 2 lines */
}

.product-card .card-category {
    margin: 0;
    font-size: 11px; /* Consistent size */
    color: var(--text-secondary);
}

.product-card .card-price {
    margin-top: 8px;
    display: flex;
    align-items: center;
}
.product-card .current-price {
    font-weight: 600;
    color: var(--purple-primary);
    font-size: 13px; /* Consistent size */
}
.product-card .original-price {
    margin-left: 5px;
    text-decoration: line-through;
    color: var(--text-secondary);
    font-size: 11px; /* Smaller than current */
}

.product-card .card-tags {
    margin-top: 8px;
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}
.product-card .card-tag {
    font-size: 10px;
    background: rgba(0,0,0,0.05);
    color: var(--text-secondary);
    padding: 2px 6px;
    border-radius: 12px;
}

.product-card .card-actions {
    padding: 8px; /* Consistent padding */
    border-top: 1px solid rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center; /* Align buttons vertically */
    gap: 8px; /* Consistent gap */
}

.product-card .card-btn {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: none;
    border-radius: 4px;
    pointer-events: auto !important;
    z-index: 10 !important;
    position: relative !important;
}
.product-card .card-btn:active { transform: scale(0.95); opacity: 0.8; }

.product-card .card-btn.edit-btn {
    flex: 1; /* Take remaining space */
    padding: 6px 10px; /* Adjusted padding */
    background: rgba(33, 150, 243, 0.1);
    color: var(--blue-info);
    font-size: 11px; /* Consistent size */
    font-weight: 500;
}
.product-card .card-btn.edit-btn:hover { background: rgba(33, 150, 243, 0.2); }

.product-card .card-btn.delete-btn {
    width: 30px; /* Consistent size */
    height: 30px; /* Consistent size */
    background: rgba(244, 67, 54, 0.1);
    color: var(--red-error);
    font-size: 12px; /* Icon size */
}
.product-card .card-btn.delete-btn:hover { background: rgba(244, 67, 54, 0.2); }

/* =========================================
   FORMS
   ========================================= */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px; /* Base size */
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--gray-light);
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff; /* Default background */
}

.form-control:focus {
    outline: none;
    border-color: var(--purple-light);
    box-shadow: 0 0 0 3px rgba(106, 27, 154, 0.15); /* Slightly larger focus ring */
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-hint {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 5px;
}

/* Select Wrapper */
.select-container {
    position: relative;
}
.select-container .form-control {
    padding-right: 35px; /* Space for arrow */
    cursor: pointer;
}
.select-arrow {
    position: absolute;
    right: 12px; /* Position arrow */
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--text-secondary);
    font-size: 12px; /* Arrow size */
}
/* Add FontAwesome arrow or SVG here if needed */
.select-arrow::before {
    content: '\f078'; /* Example: FontAwesome down arrow */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

/* Checkbox/Radio Groups */
.checkbox-group, .radio-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px; /* Gap between input and label */
}
.checkbox-label, .radio-label {
    font-size: 14px;
    cursor: pointer;
    color: var(--text-primary);
}
/* Add custom checkbox/radio styles if needed */

/* =========================================
   BADGES & STATUS INDICATORS
   ========================================= */
.status-badge, .condition-badge {
    padding: 4px 10px; /* Slightly smaller padding */
    border-radius: 12px; /* Pill shape */
    font-size: 11px; /* Smaller font */
    font-weight: 600;
    text-align: center;
    display: inline-block;
    line-height: 1.4; /* Adjust line height */
    white-space: nowrap;
}

/* Status Badges */
.status-badge.status-active, .status-badge.status-activo { background-color: rgba(67, 160, 71, 0.1); color: var(--green-primary); }
.status-badge.status-draft, .status-badge.status-borrador { background-color: rgba(158, 158, 158, 0.1); color: var(--gray-dark); }
.status-badge.status-out-of-stock, .status-badge.status-agotado { background-color: rgba(244, 67, 54, 0.1); color: var(--red-error); }
.status-badge.status-unknown { background-color: rgba(158, 158, 158, 0.1); color: var(--gray-dark); } /* Fallback */

/* Condition Badges */
.condition-badge.condition-destacado { background-color: rgba(255, 193, 7, 0.1); color: #FFC107; }
.condition-badge.condition-oferta { background-color: rgba(76, 175, 80, 0.1); color: #4CAF50; }
.condition-badge.condition-liquidacion { background-color: rgba(244, 67, 54, 0.1); color: #F44336; }
.condition-badge.condition-nuevo { background-color: rgba(156, 39, 176, 0.1); color: #9C27B0; }
.condition-badge.condition-exclusivo { background-color: rgba(0, 188, 212, 0.1); color: #00BCD4; }
.condition-badge.condition-ninguno { background-color: rgba(158, 158, 158, 0.1); color: #9E9E9E; }

/* =========================================
   PAGINATION
   ========================================= */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px; /* More space */
    gap: 8px; /* Increased gap */
}

.pagination-item {
    width: 36px; /* Slightly larger */
    height: 36px; /* Slightly larger */
    display: inline-flex; /* Use inline-flex */
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    background-color: white;
    border: 1px solid var(--gray-light);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 14px; /* Match button font size */
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-item:hover {
    background-color: var(--gray-bg); /* Lighter hover */
    border-color: var(--gray-medium);
}

.pagination-item.active {
    background-color: var(--purple-primary);
    color: white;
    border-color: var(--purple-primary);
}

.pagination-item i {
    font-size: 12px; /* Adjust icon size if needed */
}

/* =========================================
   OVERLAY (Consolidated)
   ========================================= */
.overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.4) !important; /* Consistent opacity */
    z-index: 999 !important; /* Base z-index for overlays */
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.3s ease, visibility 0s 0.3s linear !important; /* Correct transition */
    pointer-events: none !important; /* Prevent interaction when hidden */
    display: block !important; /* Keep in layout */
}

.overlay.show {
    opacity: 1 !important;
    visibility: visible !important;
    transition: opacity 0.3s ease, visibility 0s linear !important;
    pointer-events: auto !important; /* Allow interaction when shown */
}

/* Specific overlays if needed (e.g., for different z-index) */
#filterOverlay { z-index: 1000 !important; }
#editOverlay { z-index: 1000 !important; } /* Same level as filter or higher if needed */

/* =========================================
   FILTER PANEL (Consolidated & Enhanced)
   ========================================= */
.filter-container {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important; /* Start hidden off-screen */
    transform: translateX(100%) !important; /* Hide using transform */
    width: 90% !important; /* Responsive width */
    max-width: 400px !important; /* Max width */
    height: 100vh !important;
    background: white !important; /* Solid background */
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15) !important;
    z-index: 1001 !important; /* Above filter overlay */
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-left: 1px solid rgba(156, 77, 204, 0.1) !important;
    display: flex !important; /* Use flex for structure */
    flex-direction: column !important;
}

.filter-container.show {
    transform: translateX(0) !important; /* Slide in */
}

.filter-header {
    padding: 1.25rem 1.5rem !important; /* Adjusted padding */
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light)) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* Softer shadow */
    flex-shrink: 0; /* Prevent shrinking */
}

.filter-header h3 {
    font-size: 1.1rem !important; /* Adjusted size */
    font-weight: 600 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.btn-close-filter {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    border: none !important; /* Removed border */
    background: rgba(255, 255, 255, 0.15) !important; /* Slightly more visible */
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}
.btn-close-filter:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: rotate(90deg) !important;
}

/* Filter Actions (Sticky Top) */
.filter-actions {
    position: sticky !important;
    top: 0 !important;
    background: white !important;
    display: flex !important;
    gap: 1rem !important;
    padding: 1rem 1.5rem !important; /* Match body padding */
    border-bottom: 1px solid var(--gray-light) !important; /* Use standard border */
    z-index: 10 !important; /* Above body content */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
    flex-shrink: 0; /* Prevent shrinking */
}

.filter-actions button { /* Style for #resetFilters, #applyFilters */
    flex: 1 !important;
    padding: 0.75rem !important; /* Adjusted padding */
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important; /* Adjusted size */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
}
#resetFilters {
    background: rgba(156, 77, 204, 0.1) !important;
    color: var(--purple-primary) !important;
}
#resetFilters:hover {
    background: rgba(156, 77, 204, 0.2) !important;
    transform: translateY(-2px) !important;
}
#applyFilters {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light)) !important;
    color: white !important;
    box-shadow: 0 4px 10px rgba(156, 77, 204, 0.25) !important; /* Adjusted shadow */
}
#applyFilters:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(156, 77, 204, 0.35) !important;
}

.filter-body {
    padding: 1.5rem !important;
    overflow-y: auto !important;
    flex-grow: 1 !important; /* Take remaining space */
}

.filter-group {
    background: white !important;
    border-radius: 8px !important; /* Standard radius */
    padding: 1.25rem !important;
    margin-bottom: 1.25rem !important;
    box-shadow: none !important; /* Remove inner shadow */
    border: 1px solid var(--gray-light) !important; /* Standard border */
    transition: border-color 0.3s ease !important;
}
.filter-group:hover {
    border-color: var(--purple-light) !important;
}
.filter-group:last-child {
    margin-bottom: 0; /* Remove margin from last group */
}

.filter-group label {
    display: block !important;
    font-weight: 600 !important;
    color: var(--purple-primary) !important;
    margin-bottom: 0.75rem !important; /* Adjusted margin */
    font-size: 0.9rem !important; /* Adjusted size */
    position: relative !important;
}
/* Removed label::after pseudo-element for cleaner look */

.filter-row {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
    /* Removed margin-bottom, handled by filter-group margin */
}
/* Removed nested .filter-group within .filter-row */

.filter-group input[type="text"],
.filter-group input[type="number"],
.filter-group select {
    width: 100% !important;
    padding: 0.75rem 1rem !important; /* Adjusted padding */
    border: 1px solid var(--gray-medium) !important; /* Standard border */
    border-radius: 5px !important; /* Standard radius */
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    background-color: #fdfdff !important; /* Slightly off-white */
    color: var(--text-primary) !important;
}
.filter-group input[type="text"]:focus,
.filter-group input[type="number"]:focus,
.filter-group select:focus {
    outline: none !important;
    border-color: var(--purple-primary) !important;
    box-shadow: 0 0 0 3px rgba(156, 77, 204, 0.15) !important; /* Standard focus */
    background-color: white !important;
}
/* Select styling (ensure .select-container/.select-arrow are used if needed) */
.filter-group select {
    appearance: none !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23757575'%3E%3Cpath fill-rule='evenodd' d='M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z' clip-rule='evenodd' /%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1em 1em !important;
    padding-right: 2.5rem !important; /* Space for arrow */
}


.filter-options {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr)) !important; /* Adjust minmax */
    gap: 0.75rem !important;
    margin-top: 0.5rem !important;
}

.filter-checkbox { /* Label wrapping the input */
    position: relative !important;
    padding: 0.65rem 0.75rem !important; /* Adjusted padding */
    background: #f8fafc !important; /* Lighter background */
    border: 1px solid var(--gray-light) !important; /* Standard border */
    border-radius: 5px !important; /* Standard radius */
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.65rem !important; /* Adjusted gap */
}
.filter-checkbox:hover {
    border-color: var(--purple-light) !important;
    background: #f1f5f9 !important; /* Slightly darker hover */
}

.filter-checkbox input[type="checkbox"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    width: 1.1rem !important; /* Adjusted size */
    height: 1.1rem !important; /* Adjusted size */
    border: 1px solid var(--gray-medium) !important;
    border-radius: 3px !important; /* Slightly rounded */
    background: white !important;
    cursor: pointer !important;
    position: relative !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0; /* Prevent shrinking */
}
.filter-checkbox input[type="checkbox"]:checked {
    background: var(--purple-primary) !important;
    border-color: var(--purple-primary) !important;
}
.filter-checkbox input[type="checkbox"]:checked::after {
    content: '✓' !important;
    position: absolute !important;
    color: white !important;
    font-size: 0.75rem !important; /* Adjusted check size */
    font-weight: bold;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.filter-checkbox span { /* Text label */
    font-size: 0.875rem !important; /* Adjusted size */
    color: var(--text-primary) !important;
    font-weight: 500 !important;
    line-height: 1.3; /* Ensure text wraps nicely */
}

/* Filter Panel Responsive */
@media screen and (max-width: 576px) {
    .filter-container {
        max-width: none !important; /* Full width */
        width: 100% !important;
    }
    .filter-row {
        grid-template-columns: 1fr !important; /* Stack rows */
    }
    .filter-options {
        grid-template-columns: 1fr !important; /* Stack options */
    }
    .filter-actions button {
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
    }
}

/* =========================================
   EDIT PANEL (Similar structure to Filter Panel)
   ========================================= */
.edit-container {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    transform: translateX(100%) !important;
    width: 90% !important;
    max-width: 500px !important; /* Slightly wider than filter */
    height: 100vh !important;
    background: white !important;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15) !important;
    z-index: 1001 !important; /* Same level as filter or higher */
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-left: 1px solid rgba(156, 77, 204, 0.1) !important;
    display: flex !important;
    flex-direction: column !important;
}

.edit-container.show {
    transform: translateX(0) !important;
}

.edit-header {
    padding: 1.25rem 1.5rem !important;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light)) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    flex-shrink: 0;
}

.edit-header h3 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.btn-close-edit { /* Same style as btn-close-filter */
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    border: none !important;
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 1002 !important; /* Above panel content */
}
.btn-close-edit:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: rotate(90deg) !important;
}

/* Edit Actions (Sticky Bottom) */
.edit-actions {
    position: sticky !important;
    bottom: 0 !important;
    background: white !important;
    display: flex !important;
    gap: 1rem !important;
    padding: 1rem 1.5rem !important;
    border-top: 1px solid var(--gray-light) !important;
    z-index: 10 !important;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05) !important;
    flex-shrink: 0;
}

.edit-actions button { /* Base style for buttons inside */
    padding: 0.75rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
}
.edit-actions button.btn-secondary { /* Cancel/Discard */
    flex: 1 !important;
    background: var(--gray-light) !important; /* Standard secondary */
    color: var(--text-primary) !important;
}
.edit-actions button.btn-secondary:hover {
    background: var(--gray-medium) !important;
    transform: translateY(-2px) !important;
}
.edit-actions button.btn-success { /* Save/Confirm */
    flex: 2 !important; /* More emphasis */
    background: linear-gradient(135deg, var(--green-primary), #66bb6a) !important; /* Use green */
    color: white !important;
    box-shadow: 0 4px 10px rgba(67, 160, 71, 0.25) !important; /* Green shadow */
}
.edit-actions button.btn-success:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 15px rgba(67, 160, 71, 0.35) !important;
}

.edit-body {
    padding: 1.5rem !important;
    overflow-y: auto !important;
    flex-grow: 1 !important;
}

.edit-section {
    background: white !important;
    border-radius: 8px !important;
    padding: 1.25rem !important;
    margin-bottom: 1.25rem !important;
    border: 1px solid var(--gray-light) !important;
    transition: border-color 0.3s ease !important;
}
.edit-section:hover {
    border-color: var(--purple-light) !important;
}
.edit-section:last-child {
    margin-bottom: 0;
}

.edit-section-title {
    margin: 0 0 1rem 0 !important; /* Adjusted margin */
    font-weight: 600 !important;
    color: var(--purple-primary) !important;
    position: relative !important;
    font-size: 1rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 1px solid var(--gray-light) !important; /* Standard border */
}

.edit-group {
    margin-bottom: 1rem !important; /* Adjusted margin */
}
.edit-group:last-child {
    margin-bottom: 0 !important;
}

.edit-group label {
    display: block !important;
    font-weight: 500 !important; /* Standard weight */
    color: var(--text-primary) !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.9rem !important;
}

.edit-control { /* Input, Select, Textarea in edit panel */
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid var(--gray-medium) !important;
    border-radius: 5px !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    background-color: #fdfdff !important;
    color: var(--text-primary) !important;
}
.edit-control:focus {
    outline: none !important;
    border-color: var(--purple-primary) !important;
    box-shadow: 0 0 0 3px rgba(156, 77, 204, 0.15) !important;
    background-color: white !important;
}
textarea.edit-control {
    min-height: 100px !important;
    resize: vertical !important;
}
/* Add select arrow styling if needed */
.edit-group select.edit-control {
    appearance: none !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23757575'%3E%3Cpath fill-rule='evenodd' d='M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z' clip-rule='evenodd' /%3E%3C/svg%3E") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 1em 1em !important;
    padding-right: 2.5rem !important;
}


.edit-row {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 1rem !important;
}

.edit-options { /* For radio buttons */
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)) !important;
    gap: 0.75rem !important;
    margin-top: 0.5rem !important;
}

.edit-radio { /* Label wrapping radio input */
    position: relative !important;
    padding: 0.65rem 0.75rem !important;
    background: #f8fafc !important;
    border: 1px solid var(--gray-light) !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.65rem !important;
}
.edit-radio:hover {
    border-color: var (--purple-light) !important;
    background: #f1f5f9 !important;
}

.edit-radio input[type="radio"] {
    appearance: none !important;
    -webkit-appearance: none !important;
    width: 1.1rem !important;
    height: 1.1rem !important;
    border: 1px solid var(--gray-medium) !important;
    border-radius: 50% !important; /* Circular */
    background: white !important;
    cursor: pointer !important;
    position: relative !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0;
}
.edit-radio input[type="radio"]:checked {
    border-color: var(--purple-primary) !important;
    background-color: white !important; /* Keep background white */
}
.edit-radio input[type="radio"]:checked::after { /* Inner dot */
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 0.5rem !important; /* Size of the dot */
    height: 0.5rem !important;
    background-color: var(--purple-primary) !important;
    border-radius: 50% !important;
}

.edit-radio span { /* Text label */
    font-size: 0.875rem !important;
    color: var(--text-primary) !important;
    font-weight: 500 !important;
    line-height: 1.3;
}

/* Image Preview */
.edit-image-preview {
    width: 100% !important;
    height: 180px !important; /* Adjusted height */
    border-radius: 8px !important; /* Standard radius */
    overflow: hidden !important;
    position: relative !important;
    background-color: var(--gray-bg) !important; /* Background color */
    border: 1px dashed var(--gray-medium) !important; /* Dashed border */
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex; /* Center content */
    align-items: center;
    justify-content: center;
}
.edit-image-preview:hover {
    border-color: var(--purple-light) !important;
    background-color: #f0f0f5 !important;
}
.edit-image-preview img {
    max-width: 100% !important; /* Ensure image fits */
    max-height: 100% !important;
    width: auto !important; /* Maintain aspect ratio */
    height: auto !important; /* Maintain aspect ratio */
    object-fit: contain !important;
    display: block; /* Remove extra space */
}
.edit-image-overlay { /* Optional overlay for actions */
    position: absolute !important;
    top: 0 !important; left: 0 !important;
    width: 100% !important; height: 100% !important;
    background: rgba(0, 0, 0, 0.4) !important; /* Darker overlay */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none; /* Allow clicking through */
}
.edit-image-preview:hover .edit-image-overlay {
    opacity: 1 !important;
}
/* Add buttons or icons inside the overlay if needed */

/* Edit Panel Responsive */
@media screen and (max-width: 576px) {
    .edit-container {
        max-width: none !important;
        width: 100% !important;
    }
    .edit-row {
        grid-template-columns: 1fr !important;
    }
    .edit-options {
        grid-template-columns: 1fr !important;
    }
    .edit-actions button {
        padding: 0.75rem !important;
        font-size: 0.875rem !important;
    }
    .edit-image-preview {
        height: 150px !important;
    }
}

/* =========================================
   MODALS (Confirm, Error, Success)
   ========================================= */
/* Base Modal Style */
.modal { /* Generic class for all modals */
    display: none; /* Hidden by default */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    z-index: 1100; /* High z-index */
    overflow-y: auto; /* Allow scrolling if content is long */
    padding: 20px; /* Padding for small screens */
}
.modal.show {
    display: flex; /* Use flex to center content */
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: auto; /* Centered */
    padding: 0; /* Reset padding */
    border-radius: 8px;
    width: 100%; /* Responsive width */
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: fadeInScale 0.3s ease-out;
}

.modal-header {
    padding: 1rem 1.5rem; /* Consistent padding */
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.modal-header h3 {
    margin: 0;
    font-size: 1.1rem; /* Consistent size */
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}
.modal-header .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}
.modal-header .modal-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 1.5rem;
    color: var(--text-primary);
    font-size: 0.95rem;
    line-height: 1.6;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--gray-light);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem; /* Consistent gap */
}
.modal-footer .btn { /* Style buttons in footer */
    padding: 6px 12px; /* Smaller buttons */
    font-size: 13px;
}

/* Specific Modal Types */
/* Confirm Modal */
.confirm-modal .modal-header h3 { color: var(--purple-primary); }
.confirm-modal .modal-header i { color: var(--purple-primary); } /* Optional icon */

/* Error Modal */
/* Error Modal */
.error-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.error-modal.show {
    display: flex;
}

.error-modal .modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    animation: fadeInScale 0.3s ease-out;
    overflow: hidden;
}

.error-modal .error-modal-header {
    background-color: #f8d7da;
    border-bottom: 1px solid #f5c6cb;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.error-modal .error-modal-header h3 {
    color: var(--red-error);
    margin: 0;
    font-size: 1.2rem;
}

.error-modal .error-modal-header i {
    color: var(--red-error);
    font-size: 1.5rem;
}

.error-modal .error-modal-body {
    padding: 20px;
}

.error-modal .error-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #f5c6cb;
    display: flex;
    justify-content: flex-end;
}

.error-modal .btn-danger {
    background-color: var(--red-error);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.error-modal .btn-danger:hover {
    background-color: #c82333;
}

.error-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.error-list li {
    padding: 8px 0 8px 25px;
    position: relative;
    color: #555;
    font-size: 0.95rem;
    border-bottom: 1px solid #f5c6cb33;
    margin-bottom: 5px;
}

.error-list li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.error-list li::before {
    content: "•";
    color: var(--red-error);
    font-size: 18px;
    position: absolute;
    left: 8px;
    top: 6px;
}

/* Success Modal */
.success-modal .modal-header { border-bottom-color: #c3e6cb; } /* Greenish border */
.success-modal .modal-header h3 { color: var(--green-primary); }
.success-modal .modal-header i { color: var(--green-primary); font-size: 1.25rem; }
.success-modal .modal-footer .btn-primary { background-color: var(--green-primary); } /* Make primary button green */
.success-modal .modal-footer .btn-primary:hover { background-color: var(--green-dark); }

/* Success Modal Styles */
.success-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.success-modal.show {
    display: flex;
}

.success-modal .modal-content {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: fadeInScale 0.3s ease-out;
}

.success-modal-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.success-modal-header i {
    color: var(--green-primary);
    font-size: 1.5rem;
}

.success-modal-header h3 {
    margin: 0;
    color: var(--green-primary);
    font-size: 1.2rem;
    font-weight: 600;
}

.success-modal-body {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.success-modal-footer {
    display: flex;
    justify-content: flex-end;
}

.success-modal-footer button {
    background: var(--green-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s ease;
}

.success-modal-footer button:hover {
    background: var(--green-dark);
}

/* =========================================
   NOTIFICATIONS & DEBUG
   ========================================= */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 18px; /* Adjusted padding */
    border-radius: 5px; /* Standard radius */
    z-index: 1200; /* Above modals */
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 280px; /* Adjusted width */
    max-width: 450px;
    background-color: #fff; /* Base background */
    box-shadow: 0 3px 10px rgba(0,0,0,0.15); /* Standard shadow */
    border-left: 4px solid; /* Left border indicates type */
    animation: slideInRight 0.3s ease-out;
}

.notification.success { border-left-color: var(--green-primary); }
.notification.error { border-left-color: var(--red-error); }
.notification.info { border-left-color: var(--blue-info); }
.notification.warning { border-left-color: var(--yellow-accent); }

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-grow: 1; /* Take available space */
    padding-right: 10px; /* Space before close button */
}
.notification-content i { /* Icon */
    font-size: 1.2em;
    flex-shrink: 0;
}
.notification.success i { color: var(--green-primary); }
.notification.error i { color: var(--red-error); }
.notification.info i { color: var(--blue-info); }
.notification.warning i { color: var(--yellow-accent); }

.notification-message {
    font-size: 0.9rem;
    color: var(--text-primary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    font-size: 1rem; /* Adjust size */
    line-height: 1;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}
.notification-close:hover { opacity: 1; }

/* Debug Panel (Optional) */
.debug-panel {
    position: fixed;
    bottom: 15px;
    right: 15px;
    width: 90%;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    border: 1px solid var(--gray-light);
    display: none; /* Hidden by default */
}
.debug-header {
    padding: 8px 12px; /* Smaller padding */
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--gray-bg);
}
.debug-header h3 { margin: 0; font-size: 13px; font-weight: 600; }
.debug-close { background: none; border: none; font-size: 18px; cursor: pointer; color: #666; }
.debug-content { padding: 12px; max-height: 250px; overflow-y: auto; }
.debug-content pre { margin: 0; white-space: pre-wrap; word-wrap: break-word; font-size: 11px; font-family: monospace; line-height: 1.4; }

/* =========================================
   ANIMATIONS
   ========================================= */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInScale {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* =========================================
   UTILITIES & HELPERS
   ========================================= */
.hidden-section { /* Use for initial hiding if needed */
    display: none !important;
}

/* Touch device adjustments */
@media (hover: none) and (pointer: coarse) {
    .btn, .action-btn, .card-btn, .nav-link, .tab-btn, #aside-toggle, .pagination-item {
        min-height: 44px; /* Minimum touch target size */
        min-width: 44px;
    }
    /* Adjust padding on touch targets if needed */
    .action-btn, .card-btn.delete-btn, .pagination-item { padding: 0; } /* Ensure icon is centered */
}
