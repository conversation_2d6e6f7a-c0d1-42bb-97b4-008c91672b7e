<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Subcategorías</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Test de Carga de Subcategorías</h1>
    
    <div class="form-group">
        <label for="categoria">Categoría:</label>
        <select id="categoria">
            <option value="">Cargando categorías...</option>
        </select>
    </div>
    
    <div class="form-group">
        <label for="subcategoria">Subcategoría:</label>
        <select id="subcategoria" disabled>
            <option value="">Seleccione una categoría primero</option>
        </select>
    </div>
    
    <button id="testBtn">Probar API de Subcategorías</button>
    
    <div id="result">Los resultados aparecerán aquí...</div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const categoriaSelect = document.getElementById('categoria');
            const subcategoriaSelect = document.getElementById('subcategoria');
            const testBtn = document.getElementById('testBtn');
            const resultDiv = document.getElementById('result');
            
            // Cargar categorías al inicio
            fetch('/projects/villarrica_click/public/API/productos/get_categorias.php', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                console.log('Datos de categorías:', data);
                
                // Limpiar opciones
                categoriaSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
                
                // Añadir categorías
                if (data.success && Array.isArray(data.categorias)) {
                    data.categorias.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria.id;
                        option.textContent = categoria.nombre;
                        categoriaSelect.appendChild(option);
                    });
                } else if (Array.isArray(data)) {
                    data.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria.id;
                        option.textContent = categoria.nombre;
                        categoriaSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error al cargar categorías:', error);
                categoriaSelect.innerHTML = '<option value="">Error al cargar categorías</option>';
            });
            
            // Event listener para cambio de categoría
            categoriaSelect.addEventListener('change', function() {
                const categoriaId = this.value;
                
                if (!categoriaId) {
                    subcategoriaSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
                    subcategoriaSelect.disabled = true;
                    return;
                }
                
                // Cargar subcategorías
                subcategoriaSelect.disabled = true;
                subcategoriaSelect.innerHTML = '<option value="">Cargando subcategorías...</option>';
                
                fetch(`/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${categoriaId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    credentials: 'include'
                })
                .then(response => {
                    console.log('Estado de la respuesta:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Respuesta raw:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error(`Error al parsear JSON: ${e.message}. Texto recibido: ${text}`);
                    }
                })
                .then(data => {
                    console.log('Datos de subcategorías:', data);
                    
                    // Limpiar opciones
                    subcategoriaSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
                    subcategoriaSelect.disabled = false;
                    
                    // Añadir subcategorías
                    if (data.success && Array.isArray(data.subcategorias)) {
                        data.subcategorias.forEach(subcategoria => {
                            const option = document.createElement('option');
                            option.value = subcategoria.id;
                            option.textContent = subcategoria.nombre;
                            subcategoriaSelect.appendChild(option);
                        });
                    } else if (Array.isArray(data)) {
                        data.forEach(subcategoria => {
                            const option = document.createElement('option');
                            option.value = subcategoria.id;
                            option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                            subcategoriaSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error al cargar subcategorías:', error);
                    subcategoriaSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
                    subcategoriaSelect.disabled = false;
                    resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
                });
            });
            
            // Event listener para botón de prueba
            testBtn.addEventListener('click', function() {
                const categoriaId = categoriaSelect.value;
                
                if (!categoriaId) {
                    resultDiv.innerHTML = '<span class="error">Por favor, seleccione una categoría primero</span>';
                    return;
                }
                
                resultDiv.innerHTML = 'Probando API de subcategorías...';
                
                // Probar API de subcategorías con el endpoint de depuración
                fetch(`/projects/villarrica_click/public/API/productos/debug_subcategorias.php?categoria_id=${categoriaId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Datos de depuración:', data);
                    resultDiv.innerHTML = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    console.error('Error en la prueba:', error);
                    resultDiv.innerHTML = `<span class="error">Error en la prueba: ${error.message}</span>`;
                });
            });
        });
    </script>
</body>
</html>
