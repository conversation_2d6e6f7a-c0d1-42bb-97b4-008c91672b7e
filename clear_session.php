<?php
// Script para limpiar sesiones y cookies
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Mostrar información de sesión actual
echo "<h2>Información de sesión antes de limpiar</h2>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session Name: " . session_name() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "Cookies: \n";
print_r($_COOKIE);
echo "</pre>";

// Iniciar sesión para poder destruirla
session_start();

// Mostrar datos de sesión
echo "<h2>Datos de sesión</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Destruir la sesión
session_unset();
session_destroy();

// Eliminar todas las cookies de sesión
if (isset($_SERVER['HTTP_COOKIE'])) {
    $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
    foreach($cookies as $cookie) {
        $parts = explode('=', $cookie);
        $name = trim($parts[0]);
        setcookie($name, '', time()-3600, '/');
        setcookie($name, '', time()-3600, '/public/');
        setcookie($name, '', time()-3600, '/config/');
        setcookie($name, '', time()-3600);
    }
}

// Eliminar específicamente las cookies conocidas
$session_names = ['PHPSESSID', 'VILLARRICA_SID', 'VILLARRICA_SESSION'];
foreach ($session_names as $name) {
    setcookie($name, '', time()-3600, '/');
    setcookie($name, '', time()-3600, '/public/');
    setcookie($name, '', time()-3600, '/config/');
    setcookie($name, '', time()-3600);
}

echo "<h2>Sesión y cookies eliminadas</h2>";
echo "<p>Todas las cookies de sesión han sido eliminadas.</p>";
echo "<p><a href='/public/login.php'>Ir a la página de login</a></p>";
echo "<p><a href='/public/login.php?debug=true'>Ir a la página de login con depuración</a></p>";

// Mostrar información después de limpiar
echo "<h2>Información después de limpiar</h2>";
echo "<pre>";
echo "Cookies: \n";
print_r($_COOKIE);
echo "</pre>";
?>
