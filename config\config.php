<?php
// Inicio de config.php - Configuración unificada

// Incluir el sistema de logging
require_once __DIR__ . '/logger.php';

// Depuración de entorno
logInfo("Inicializando configuración", [
    'script_name' => $_SERVER['SCRIPT_NAME'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'http_host' => $_SERVER['HTTP_HOST']
]);

// Eliminar cualquier output buffering para evitar problemas
while (ob_get_level()) ob_end_clean();

// Establecer la ruta base correcta según el entorno
$isProduction = $_SERVER['HTTP_HOST'] === '**************';
$basePath = $isProduction ? '' : '/villarrica_click';
$cookieDomain = $isProduction ? '' : '';

// En producción, podría configurarse así si el dominio es un FQDN:
// $cookieDomain = $isProduction ? '**************' : '';

// Obtener el path base para cookies
$cookiePath = '/';
$urlParts = parse_url("http://{$_SERVER['HTTP_HOST']}{$basePath}");
if (isset($urlParts['path']) && !empty($urlParts['path'])) {
    $cookiePath = rtrim($urlParts['path'], '/') . '/';
}

logInfo("Configuración de rutas", [
    'entorno' => $isProduction ? 'producción' : 'desarrollo',
    'base_url' => $basePath,
    'domain' => $_SERVER['HTTP_HOST'],
    'cookie_path' => $cookiePath
]);

// Definir las constantes
define('BASE_URL', $basePath);
define('DOMAIN', $_SERVER['HTTP_HOST']);
define('COOKIE_PATH', $cookiePath);
define('COOKIE_DOMAIN', $cookieDomain);
define('SESSION_NAME', 'VILLARRICA_SID');
define('SESSION_LIFETIME', 86400);  // 24 horas en segundos

logInfo("Constantes definidas", [
    'BASE_URL' => BASE_URL,
    'DOMAIN' => DOMAIN,
    'COOKIE_PATH' => COOKIE_PATH,
    'COOKIE_DOMAIN' => COOKIE_DOMAIN
]);

// Configuración inicial de sesión si aún no hay una activa
if (session_status() !== PHP_SESSION_ACTIVE) {
    // Configurar rutas de cookies y sesión con configuraciones de seguridad mejoradas
    ini_set('session.cookie_path', BASE_URL);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);

    // Configuraciones de seguridad mejoradas
    ini_set('session.use_strict_mode', 1);
    ini_set('session.use_cookies', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.cookie_path', COOKIE_PATH);
    ini_set('session.cookie_domain', COOKIE_DOMAIN);
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);

    // Configuraciones adicionales de seguridad
    ini_set('session.entropy_length', 32);
    ini_set('session.hash_function', 'sha256');
    ini_set('session.hash_bits_per_character', 5);
    ini_set('session.sid_length', 48);
    ini_set('session.sid_bits_per_character', 5);
}

// Conexión a la base de datos - Usar variables de entorno para mayor seguridad
$db_host = $_ENV['DB_HOST'] ?? getenv('DB_HOST') ?: 'localhost';
$db_user = $_ENV['DB_USER'] ?? getenv('DB_USER') ?: 'pcornejo';
$db_pass = $_ENV['DB_PASS'] ?? getenv('DB_PASS') ?: 'Pcornejo@2025';
$db_name = $_ENV['DB_NAME'] ?? getenv('DB_NAME') ?: 'aunclick_prueba';

// Definir constantes para la conexión a la base de datos
define('DB_HOST', $db_host);
define('DB_USER', $db_user);
define('DB_PASS', $db_pass);
define('DB_NAME', $db_name);

// Crear conexión
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Verificar conexión
if ($conn->connect_error) {
    logError("Error de conexión a la base de datos", [
        'error' => $conn->connect_error,
        'host' => $db_host,
        'database' => $db_name
    ]);
    die("Conexión fallida: " . $conn->connect_error);
}

$conn->set_charset("utf8");
logInfo("Conexión a base de datos establecida correctamente", [
    'host' => $db_host,
    'database' => $db_name
]);

// Función para obtener una nueva conexión a la base de datos
function getDirectConnection() {
    global $db_host, $db_user, $db_pass, $db_name;

    $connection = new mysqli($db_host, $db_user, $db_pass, $db_name);

    if ($connection->connect_error) {
        logError("Error al crear nueva conexión directa", [
            'error' => $connection->connect_error,
            'host' => $db_host,
            'database' => $db_name
        ]);
        return null;
    }

    $connection->set_charset("utf8");
    return $connection;
}
logInfo("Configuración completada");
// Fin de config.php