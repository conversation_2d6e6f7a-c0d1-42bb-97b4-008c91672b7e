/**
 * Script para conectar el botón toggle del header con el sidebar
 * Versión mejorada para emular el comportamiento del ejemplo compartido
 */
(function() {
    // Ejecutar inmediatamente para evitar problemas de carga
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(initToggleFix, 1);
    } else {
        document.addEventListener('DOMContentLoaded', initToggleFix);
    }

    function initToggleFix() {
        // Crear un namespace aislado para evitar conflictos
        const ToggleFix = {
            // Referencias a elementos DOM
            sidebar: null,
            headerToggleBtn: null,

            // Inicializar el módulo
            init: function() {
                this.onDOMReady();
            },

            // Cuando el DOM esté listo
            onDOMReady: function() {
                console.log('ToggleFix: Inicializando...');

                // Seleccionar elementos con verificación de existencia
                this.sidebar = document.getElementById('sidebar');
                this.headerToggleBtn = document.getElementById('header-aside-toggle');

                // Verificar que los elementos existen antes de continuar
                if (!this.sidebar) {
                    console.error('ToggleFix: Sidebar no encontrado');
                    return;
                }

                if (!this.headerToggleBtn) {
                    console.error('ToggleFix: Botón toggle no encontrado');
                    // Intentar crear el botón si no existe
                    this.createToggleButton();
                    return;
                }

                // Cargar el estado del sidebar desde localStorage
                const savedState = localStorage.getItem('sidebarCollapsed');
                if (savedState === 'true' && window.innerWidth > 992) {
                    this.sidebar.classList.add('collapsed');
                } else {
                    // Por defecto, mostrar el sidebar expandido
                    this.sidebar.classList.remove('collapsed');
                }

                // Configurar eventos
                this.setupEvents();

                // Configurar observer
                this.setupObserver();

                console.log('ToggleFix: Inicializado correctamente');
            },

            // Crear el botón toggle si no existe
            createToggleButton: function() {
                console.log('ToggleFix: Creando botón toggle...');

                // Crear el botón
                const toggleBtn = document.createElement('button');
                toggleBtn.id = 'header-aside-toggle';
                toggleBtn.className = 'header-toggle-btn sidemenu-toggle';
                toggleBtn.setAttribute('aria-label', 'Toggle sidebar');
                toggleBtn.setAttribute('data-bs-toggle', 'sidebar');
                toggleBtn.setAttribute('href', 'javascript:void(0);');
                toggleBtn.style.cssText = 'display: flex !important; visibility: visible !important;';

                // Crear el icono animado
                const arrow = document.createElement('div');
                arrow.className = 'animated-arrow';
                arrow.style.cssText = 'position: relative; width: 18px; height: 18px;';

                // Crear las tres barras
                for (let i = 0; i < 3; i++) {
                    const span = document.createElement('span');
                    span.style.cssText = 'display: block; position: absolute; height: 2px; width: 100%; background: white; border-radius: 9px; opacity: 1; left: 0; transform: rotate(0deg); transition: .25s ease-in-out;';
                    span.style.top = (i * 8) + 'px';
                    arrow.appendChild(span);
                }

                // Añadir el icono al botón
                toggleBtn.appendChild(arrow);

                // Añadir el botón al header
                const header = document.querySelector('.modern-header');
                if (header) {
                    header.insertBefore(toggleBtn, header.firstChild);
                    this.headerToggleBtn = toggleBtn;

                    // Configurar eventos
                    this.setupEvents();
                    console.log('ToggleFix: Botón toggle creado correctamente');
                } else {
                    console.error('ToggleFix: No se encontró el header para añadir el botón toggle');
                }
            },

            // Configurar eventos
            setupEvents: function() {
                // Evento para el botón toggle en el header
                this.headerToggleBtn.addEventListener('click', this.toggleSidebar.bind(this));

                // Manejar clics fuera del sidebar para cerrarlo en modo responsive
                document.addEventListener('click', this.handleOutsideClick.bind(this));

                // Manejar cambios de tamaño de ventana
                window.addEventListener('resize', this.handleResize.bind(this));
            },

            // Función para alternar el sidebar
            toggleSidebar: function(e) {
                // Prevenir comportamiento por defecto
                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                if (window.innerWidth > 992) {
                    // Desktop
                    this.sidebar.classList.toggle('collapsed');
                    const isCollapsed = this.sidebar.classList.contains('collapsed');
                    localStorage.setItem('sidebarCollapsed', isCollapsed);
                } else {
                    // Mobile
                    this.sidebar.classList.toggle('expanded');
                }

                // Ajustar el margen del contenido principal
                this.adjustMainContentMargin();
            },

            // Ajustar el margen del contenido principal
            adjustMainContentMargin: function() {
                const mainContent = document.querySelector('.main-content');
                if (!mainContent) return;

                if (window.innerWidth > 992) {
                    // En desktop
                    if (this.sidebar.classList.contains('collapsed')) {
                        mainContent.style.marginLeft = '70px';
                        mainContent.style.width = 'calc(100% - 70px)';
                    } else {
                        mainContent.style.marginLeft = '250px';
                        mainContent.style.width = 'calc(100% - 250px)';
                    }
                } else {
                    // En móvil
                    mainContent.style.marginLeft = '70px';
                    mainContent.style.width = 'calc(100% - 70px)';
                }
            },

            // Configurar observer para cambios en el sidebar
            setupObserver: function() {
                try {
                    const observer = new MutationObserver(() => {
                        this.adjustMainContentMargin();
                    });
                    observer.observe(this.sidebar, { attributes: true });
                } catch (error) {
                    console.error('ToggleFix: Error al configurar el observer:', error);
                }
            },

            // Manejar clics fuera del sidebar
            handleOutsideClick: function(event) {
                if (!this.sidebar || !this.headerToggleBtn) return;

                // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
                if (window.innerWidth <= 992 && this.sidebar.classList.contains('expanded')) {
                    // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
                    if (!this.sidebar.contains(event.target) &&
                        event.target !== this.headerToggleBtn &&
                        !this.headerToggleBtn.contains(event.target)) {
                        // Cerrar el sidebar
                        this.sidebar.classList.remove('expanded');
                    }
                }
            },

            // Manejar cambios de tamaño de ventana
            handleResize: function() {
                this.adjustMainContentMargin();
            }
        };

        // Iniciar el módulo
        ToggleFix.init();
    }
})();