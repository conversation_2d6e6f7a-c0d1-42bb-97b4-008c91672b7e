// Función simple para inicializar los gráficos
function initializeCharts() {
    try {
        // Verificar si los canvas existen
        const chartsToInit = [
            { id: 'visitsMonthlyChart', type: 'line' },
            { id: 'devicesChart', type: 'doughnut' },
            { id: 'topProductsChart', type: 'bar' },
            { id: 'categoriesChart', type: 'radar' }
        ];

        chartsToInit.forEach(chart => {
            const canvas = document.getElementById(chart.id);
            if (canvas) {
                console.log(`Inicializando gráfico: ${chart.id}`);
                // Solo crear un gráfico mínimo para pruebas
                new Chart(canvas.getContext('2d'), {
                    type: chart.type,
                    data: {
                        labels: ['Ene', 'Feb', 'Mar'],
                        datasets: [ {
                            label: 'Datos',
                            data: [300, 450, 320],
                            backgroundColor: 'rgba(106, 27, 154, 0.2)',
                            borderColor: 'rgba(106, 27, 154, 1)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        });
    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
    }
}

// Exportar función
export {
    initializeCharts
};