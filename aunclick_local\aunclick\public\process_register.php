<?php
// Configurar un archivo de log específico para este proceso
ini_set('log_errors', 1);
ini_set('error_log', '../logs/register_errors.log');

// Función para registrar mensajes de log con timestamp
function log_message($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[$timestamp] [$level] $message");
}

// Registrar inicio del script
log_message("Iniciando process_register.php", "START");

// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Habilitar la visualización de errores de PHP
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Configurar un manejador de errores personalizado
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $error_type = match($errno) {
        E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR => 'FATAL ERROR',
        E_WARNING, E_CORE_WARNING, E_COMPILE_WARNING, E_USER_WARNING => 'WARNING',
        E_NOTICE, E_USER_NOTICE => 'NOTICE',
        E_DEPRECATED, E_USER_DEPRECATED => 'DEPRECATED',
        default => 'UNKNOWN'
    };

    log_message("PHP $error_type: $errstr in $errfile on line $errline", "ERROR");

    // Para errores fatales, terminar la ejecución
    if ($errno == E_ERROR || $errno == E_CORE_ERROR || $errno == E_COMPILE_ERROR || $errno == E_USER_ERROR) {
        exit(1);
    }

    return true; // Permitir que el manejador de errores estándar de PHP continúe
});

// Configurar un manejador de excepciones no capturadas
set_exception_handler(function($exception) {
    $error_message = "Excepción no capturada: " . $exception->getMessage() .
                     "\nStack trace: " . $exception->getTraceAsString();
    log_message($error_message, "EXCEPTION");

    // Redirigir a una página de error con el mensaje detallado
    header("Location: register.php?error=" . urlencode($exception->getMessage()) . "&debug=1");
    exit();
});

// Registrar datos POST recibidos (sin información sensible)
$post_data = $_POST;
if (isset($post_data['password'])) $post_data['password'] = '********';
if (isset($post_data['confirm_password'])) $post_data['confirm_password'] = '********';
log_message("Datos POST recibidos: " . json_encode($post_data, JSON_UNESCAPED_UNICODE));

// Incluir la configuración de la base de datos
log_message("Cargando configuración de base de datos", "INFO");
try {
    require_once '../config/config.php';
    log_message("Configuración de base de datos cargada correctamente", "INFO");
} catch (Throwable $e) {
    log_message("Error al cargar configuración de base de datos: " . $e->getMessage(), "ERROR");
    throw $e;
}

// Verificar conexión a la base de datos
if (!isset($conn) || $conn->connect_error) {
    log_message("Error de conexión a la base de datos: " . ($conn->connect_error ?? "Variable \$conn no definida"), "ERROR");
    header("Location: register.php?error=" . urlencode("Error de conexión a la base de datos. Por favor, inténtelo de nuevo."));
    exit();
}

log_message("Conexión a la base de datos verificada", "INFO");

// Verificar si hay errores de PHP
if (error_get_last() !== null) {
    log_message("Error de PHP detectado: " . print_r(error_get_last(), true), "ERROR");
    // Puedes agregar aquí más acciones, como redirigir a una página de error
}

// Verificar si el formulario fue enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    log_message("Procesando solicitud POST", "INFO");
    try {
        // Recoger y sanitizar datos del paso 1: Información Personal
        log_message("Procesando datos de información personal", "INFO");
        $nombres = trim($_POST['nombres'] ?? '');
        $apellidos = trim($_POST['apellidos'] ?? '');
        $rut = trim($_POST['rut'] ?? '');
        $fecha_nacimiento = trim($_POST['fechaNacimiento'] ?? '');
        $sexo = $_POST['sexo'] ?? '';
        $telefono = trim($_POST['telefono'] ?? '');
        $region = trim($_POST['region'] ?? '');
        $comuna = trim($_POST['comuna'] ?? '');
        $direccion = trim($_POST['direccion'] ?? '');

        // Verificar campos obligatorios del paso 1
        if (empty($nombres) || empty($apellidos) || empty($rut) || empty($fecha_nacimiento) ||
            empty($sexo) || empty($telefono) || empty($region) || empty($comuna) || empty($direccion)) {
            log_message("Faltan campos obligatorios en el paso 1", "WARNING");
            throw new Exception("Faltan campos obligatorios en la información personal");
        }

        // Recoger y sanitizar datos del paso 2: Datos de Cuenta
        log_message("Procesando datos de cuenta", "INFO");
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $backup_email = !empty($_POST['backup_email']) ? trim($_POST['backup_email']) : null;

        // Verificar si se proporcionó una contraseña
        if (empty($_POST['password'])) {
            log_message("Contraseña no proporcionada", "WARNING");
            throw new Exception("La contraseña es obligatoria");
        }

        $password = password_hash($_POST['password'], PASSWORD_DEFAULT); // Encriptar contraseña
        $local_fisico = $_POST['local_fisico'] ?? '';

        // Verificar campos obligatorios del paso 2
        if (empty($username) || empty($email) || empty($local_fisico)) {
            log_message("Faltan campos obligatorios en el paso 2", "WARNING");
            throw new Exception("Faltan campos obligatorios en los datos de cuenta");
        }

        // Recoger y sanitizar datos del paso 3: Datos del Negocio
        log_message("Procesando datos del negocio", "INFO");
        $nombre_negocio = trim($_POST['nombre_negocio'] ?? '');
        $telefono_negocio = !empty($_POST['telefono_negocio']) ? trim($_POST['telefono_negocio']) : null;
        $whatsapp_negocio = !empty($_POST['whatsapp_negocio']) ? trim($_POST['whatsapp_negocio']) : null;
        $descripcion_negocio = trim($_POST['descripcion_negocio'] ?? '');
        $tipo_negocio = $_POST['tipo_negocio'] ?? '';

        // Verificar campos obligatorios del paso 3
        if (empty($nombre_negocio) || empty($descripcion_negocio) || empty($tipo_negocio)) {
            log_message("Faltan campos obligatorios en el paso 3", "WARNING");
            throw new Exception("Faltan campos obligatorios en los datos del negocio");
        }

        // Categorías según tipo de negocio
        log_message("Procesando categorías según tipo de negocio: $tipo_negocio", "INFO");
        $categorias_venta = null;
        $categorias_servicios = null;
        $categorias_arriendo = null;

        if ($tipo_negocio === 'venta' && !empty($_POST['categorias_venta'])) {
            $categorias_venta = is_array($_POST['categorias_venta']) ?
                                implode(',', $_POST['categorias_venta']) :
                                $_POST['categorias_venta'];
            log_message("Categorías de venta seleccionadas: $categorias_venta", "INFO");
        } elseif ($tipo_negocio === 'servicios' && !empty($_POST['categorias_servicios'])) {
            $categorias_servicios = is_array($_POST['categorias_servicios']) ?
                                    implode(',', $_POST['categorias_servicios']) :
                                    $_POST['categorias_servicios'];
            log_message("Categorías de servicios seleccionadas: $categorias_servicios", "INFO");
        } elseif ($tipo_negocio === 'arriendo' && !empty($_POST['categorias_arriendo'])) {
            $categorias_arriendo = is_array($_POST['categorias_arriendo']) ?
                                   implode(',', $_POST['categorias_arriendo']) :
                                   $_POST['categorias_arriendo'];
            log_message("Categorías de arriendo seleccionadas: $categorias_arriendo", "INFO");
        } else {
            log_message("No se seleccionaron categorías para el tipo de negocio: $tipo_negocio", "WARNING");
        }

        // Recoger y sanitizar datos del paso 4: Plan
        log_message("Procesando datos del plan", "INFO");
        $subscription = $_POST['subscription'] ?? '';

        // Verificar campos obligatorios del paso 4
        if (empty($subscription)) {
            log_message("Falta seleccionar un plan de suscripción", "WARNING");
            throw new Exception("Debe seleccionar un plan de suscripción");
        }

        // Recoger y sanitizar datos del paso 5: Pago (si aplica)
        log_message("Procesando datos de pago", "INFO");
        $documento = isset($_POST['documento']) ? $_POST['documento'] : null;
        $empresa = isset($_POST['empresa']) ? trim($_POST['empresa']) : null;
        $rut_empresa = isset($_POST['rut_empresa']) ? trim($_POST['rut_empresa']) : null;
        $direccion_empresa = isset($_POST['direccion_empresa']) ? trim($_POST['direccion_empresa']) : null;
        $giro_empresa = isset($_POST['giro_empresa']) ? trim($_POST['giro_empresa']) : null;
        $telefono_empresa = isset($_POST['telefono_empresa']) ? trim($_POST['telefono_empresa']) : null;
        $correo_empresa = isset($_POST['correo_empresa']) ? trim($_POST['correo_empresa']) : null;

        // Preparar la consulta SQL
        log_message("Preparando consulta SQL para inserción", "INFO");
        $sql = "INSERT INTO tb_register (
                    nombres, apellidos, rut, fecha_nacimiento, sexo, telefono, region, comuna, direccion,
                    username, email, backup_email, password, local_fisico, nombre_negocio,
                    telefono_negocio, whatsapp_negocio, descripcion_negocio, tipo_negocio,
                    categorias_venta, categorias_servicios, categorias_arriendo, subscription,
                    documento, empresa, rut_empresa, direccion_empresa, giro_empresa,
                    telefono_empresa, correo_empresa
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, ?, ?, ?, ?,
                    ?, ?
                )";

        // Preparar la sentencia
        log_message("Preparando statement", "INFO");
        $stmt = $conn->prepare($sql);

        if (!$stmt) {
            log_message("Error al preparar la consulta: " . $conn->error, "ERROR");
            throw new Exception("Error al preparar la consulta: " . $conn->error);
        }

        log_message("Vinculando parámetros", "INFO");
        $bind_result = $stmt->bind_param(
            "sssssssssssssssssssssssssssss",
            $nombres, $apellidos, $rut, $fecha_nacimiento, $sexo, $telefono, $region, $comuna, $direccion,
            $username, $email, $backup_email, $password, $local_fisico, $nombre_negocio,
            $telefono_negocio, $whatsapp_negocio, $descripcion_negocio, $tipo_negocio,
            $categorias_venta, $categorias_servicios, $categorias_arriendo, $subscription,
            $documento, $empresa, $rut_empresa, $direccion_empresa, $giro_empresa,
            $telefono_empresa, $correo_empresa
        );

        if (!$bind_result) {
            log_message("Error al vincular parámetros: " . $stmt->error, "ERROR");
            throw new Exception("Error al vincular parámetros: " . $stmt->error);
        }

        // Ejecutar la consulta
        log_message("Ejecutando consulta SQL", "INFO");
        if ($stmt->execute()) {
            // Registro exitoso
            log_message("Registro exitoso. ID insertado: " . $stmt->insert_id, "SUCCESS");
            header("Location: registro_exitoso.php");
            exit();
        } else {
            log_message("Error al ejecutar la consulta: " . $stmt->error, "ERROR");
            throw new Exception("Error al registrar: " . $stmt->error);
        }

    } catch (Exception $e) {
        // Manejar errores
        $error_message = "Excepción capturada: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString();
        log_message($error_message, "ERROR");

        // Guardar el error en un archivo temporal para depuración
        file_put_contents('../logs/last_error.txt', $error_message);

        // Redirigir con mensaje detallado
        header("Location: register.php?error=" . urlencode($e->getMessage()) . "&debug=1");
        exit();
    }
} else {
    // Si no es POST, redirigir al formulario
    log_message("Solicitud no es POST. Redirigiendo al formulario", "WARNING");
    header("Location: register.php");
    exit();
}

// Registrar finalización del script
log_message("Finalizando process_register.php", "END");
?>

