<?php
// Configuración de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Incluir configuraciones necesarias
require_once '../../../config/config.php';
require_once '../../../config/SessionManager.php';

// Log para depuración
error_log("get_products.php - Inicio de ejecución");
error_log("get_products.php - Método: " . $_SERVER['REQUEST_METHOD']);

// Inicializar el manejador de sesiones solo si no hay sesión activa
$sessionManager = SessionManager::getInstance();
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

// Verificar si hay una sesión activa y obtener el user_id
$user_id = $_SESSION['user_id'] ?? null;
$user_role = $_SESSION['role'] ?? null;

error_log("get_products.php - Session ID: " . session_id());
error_log("get_products.php - User ID from session: " . $user_id);
error_log("get_products.php - User Role from session: " . $user_role);

try {
    // Verificación extra para asegurar que el usuario es válido
    if (empty($user_id)) {
        error_log("get_products.php - ERROR: user_id está vacío!");
        throw new Exception('ID de usuario inválido o no encontrado');
    }

    // Construir la consulta SQL base
    $sql = "SELECT p.*, 
                   tc.nombre as tipo_categoria_nombre,
                   c.nombre as categoria_nombre,
                   COALESCE(s.nombre, 'Sin subcategoría') as subcategoria_nombre,
                   n.nombre as negocio_nombre
            FROM tb_productos p
            LEFT JOIN tb_tipo_categoria tc ON p.id_tipo_categoria = tc.id
            LEFT JOIN tb_categorias c ON p.categoria_id = c.id
            LEFT JOIN tb_subcategorias s ON p.subcategoria_id = s.id
            LEFT JOIN tb_negocios n ON p.negocio_id = n.id
            WHERE p.usuario_id = ?
            ORDER BY p.id DESC";

    error_log("get_products.php - SQL Query: " . $sql);
    
    // Preparar y ejecutar la consulta
    if ($stmt = $conn->prepare($sql)) {
        $stmt->bind_param("i", $user_id);
        
        if (!$stmt->execute()) {
            throw new Exception("Error ejecutando la consulta: " . $stmt->error);
        }
        
        $result = $stmt->get_result();
        
        error_log("get_products.php - Ejecución de consulta completada");
        error_log("get_products.php - Número de filas obtenidas: " . $result->num_rows);

        $productos = [];
        while ($row = $result->fetch_assoc()) {
            $productos[] = $row;
        }
        
        $stmt->close();
        
        error_log("get_products.php - Productos encontrados: " . count($productos));
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'productos' => $productos,
            'total' => count($productos)
        ]);
        
    } else {
        throw new Exception("Error preparando la consulta: " . $conn->error);
    }
    
} catch (Exception $e) {
    error_log("Error en get_products.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener productos: ' . $e->getMessage()
    ]);
}