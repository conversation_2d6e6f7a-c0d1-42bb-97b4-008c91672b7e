/**
 * step4-handler.js
 * Script para manejar el paso 4 del formulario de registro (selección de plan)
 */

// Función para manejar el clic en el botón del paso 4
function handleStep4ButtonClick(event) {
    event.preventDefault();
    console.log('Botón del paso 4 clickeado (función global)');

    // Verificar si hay un plan seleccionado
    const planSeleccionado = document.querySelector('input[name="subscription"]:checked');
    if (!planSeleccionado) {
        alert('Por favor, seleccione un plan antes de continuar.');
        return false;
    }

    // Determinar si es productos o servicios
    const tipo = this.id.includes('servicios') ? 'servicios' : 'productos';
    handleStep4Submit(tipo);
}

// Ejecutar cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando manejador del paso 4');

    // Configurar los botones inmediatamente
    setupStep4Buttons();

    // Y también después de un retraso para asegurarnos
    setTimeout(setupStep4Buttons, 500);
    setTimeout(setupStep4Buttons, 1000);

    /**
     * Configura los botones "Siguiente" del paso 4
     */
    function setupStep4Buttons() {
        console.log('Configurando botones del paso 4...');

        // Botón para productos
        const step4ProductosButton = document.getElementById('step4-next-save');
        if (step4ProductosButton) {
            console.log('Botón del paso 4 para productos encontrado:', step4ProductosButton);

            // Asignar directamente el manejador de eventos (sin clonar)
            step4ProductosButton.onclick = handleStep4ButtonClick;

            // También agregar un event listener como respaldo
            step4ProductosButton.addEventListener('click', handleStep4ButtonClick);

            console.log('Eventos asignados al botón de productos');
        } else {
            console.error('No se encontró el botón del paso 4 para productos');
        }

        // Botón para servicios
        const step4ServiciosButton = document.getElementById('step4-next-save-servicios');
        if (step4ServiciosButton) {
            console.log('Botón del paso 4 para servicios encontrado:', step4ServiciosButton);

            // Asignar directamente el manejador de eventos (sin clonar)
            step4ServiciosButton.onclick = handleStep4ButtonClick;

            // También agregar un event listener como respaldo
            step4ServiciosButton.addEventListener('click', handleStep4ButtonClick);

            console.log('Eventos asignados al botón de servicios');
        } else {
            console.error('No se encontró el botón del paso 4 para servicios');
        }

        // Verificar si los botones tienen eventos asignados
        console.log('Verificando eventos del botón de productos:',
            document.getElementById('step4-next-save') ?
            (document.getElementById('step4-next-save').onclick ? 'Tiene onclick' : 'No tiene onclick') :
            'No existe');

        console.log('Verificando eventos del botón de servicios:',
            document.getElementById('step4-next-save-servicios') ?
            (document.getElementById('step4-next-save-servicios').onclick ? 'Tiene onclick' : 'No tiene onclick') :
            'No existe');
    }

    /**
     * Maneja el envío del paso 4
     * @param {string} tipo - Tipo de negocio ('productos' o 'servicios')
     */
    function handleStep4Submit(tipo) {
        console.log(`Manejando envío del paso 4 para ${tipo}`);

        // Verificar si se ha seleccionado un plan
        const planSeleccionado = document.querySelector('input[name="subscription"]:checked');
        console.log('Plan seleccionado:', planSeleccionado ? planSeleccionado.value : 'ninguno');

        if (!planSeleccionado) {
            // Mostrar mensaje de error si no hay plan seleccionado
            alert('Por favor, seleccione un plan antes de continuar.');
            return false;
        }

        console.log('Preparando para enviar datos al servidor...');

        // Verificar que AjaxHandler esté disponible
        if (!window.AjaxHandler || typeof window.AjaxHandler.submitStep4 !== 'function') {
            console.error('AjaxHandler no está disponible o no tiene la función submitStep4');
            alert('Error: No se puede enviar el formulario. Por favor, recargue la página e intente nuevamente.');
            return false;
        }

        // Crear objeto FormData para enviar los datos
        const formData = new FormData();
        formData.append('plan_suscripcion', planSeleccionado.value);

        // Mostrar indicador de carga
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
        loadingIndicator.style.position = 'fixed';
        loadingIndicator.style.top = '0';
        loadingIndicator.style.left = '0';
        loadingIndicator.style.width = '100%';
        loadingIndicator.style.height = '100%';
        loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
        loadingIndicator.style.display = 'flex';
        loadingIndicator.style.flexDirection = 'column';
        loadingIndicator.style.justifyContent = 'center';
        loadingIndicator.style.alignItems = 'center';
        loadingIndicator.style.zIndex = '9999';
        loadingIndicator.style.color = 'white';
        document.body.appendChild(loadingIndicator);

        // Usar el manejador AJAX para enviar los datos
        window.AjaxHandler.submitStep4(formData,
            // Callback de éxito
            function(response) {
                console.log('Éxito! Datos guardados correctamente:', response);

                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                // Verificar si es plan gratuito
                if (planSeleccionado.value === 'gratuita') {
                    console.log('Plan gratuito seleccionado, mostrando popup de bienvenida');
                    // Mostrar popup de bienvenida para plan gratuito
                    const welcomePopup = document.getElementById('welcome-popup-overlay');
                    if (welcomePopup) {
                        welcomePopup.style.display = 'flex';
                        document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo

                        // Configurar botón de finalizar
                        const finishButton = document.getElementById('welcome-finish-btn');
                        if (finishButton) {
                            finishButton.onclick = function() {
                                welcomePopup.style.display = 'none';
                                document.body.style.overflow = ''; // Restaurar scroll
                                // Redirigir a la página de inicio o dashboard
                                window.location.href = 'index.php';
                            };
                        }

                        // Configurar botón de cerrar
                        const closeButton = welcomePopup.querySelector('.welcome-popup-close');
                        if (closeButton) {
                            closeButton.onclick = function() {
                                welcomePopup.style.display = 'none';
                                document.body.style.overflow = ''; // Restaurar scroll
                                // Redirigir a la página de inicio o dashboard
                                window.location.href = 'index.php';
                            };
                        }
                    } else {
                        console.error('No se encontró el popup de bienvenida (welcome-popup-overlay)');
                        // Intentar con el otro ID de popup
                        const altWelcomePopup = document.getElementById('welcome-popup');
                        if (altWelcomePopup) {
                            altWelcomePopup.style.display = 'flex';
                            document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo

                            // Configurar botón de finalizar
                            const finishButton = document.getElementById('welcome-finish');
                            if (finishButton) {
                                finishButton.onclick = function() {
                                    altWelcomePopup.style.display = 'none';
                                    document.body.style.overflow = ''; // Restaurar scroll
                                    // Redirigir a la página de inicio o dashboard
                                    window.location.href = 'index.php';
                                };
                            }
                        } else {
                            console.error('No se encontró ningún popup de bienvenida');
                            // Redirigir a la página de inicio o dashboard
                            alert('Gracias por registrarte con el plan gratuito.');
                            window.location.href = 'index.php';
                        }
                    }
                } else {
                    console.log('Plan normal o premium seleccionado, avanzando al paso 5');
                    // Avanzar al paso 5 para planes normal y premium
                    const formSteps = document.querySelectorAll('.form-step');
                    const steps = document.querySelectorAll('.step');

                    // Ocultar todos los pasos
                    formSteps.forEach(step => {
                        step.style.display = 'none';
                        step.classList.remove('active');
                    });

                    steps.forEach(step => {
                        step.classList.remove('active');
                    });

                    // Mostrar el paso 5
                    if (formSteps[4]) {
                        formSteps[4].style.display = 'block';
                        formSteps[4].classList.add('active');
                    }

                    if (steps[4]) {
                        steps[4].classList.add('active');
                    }
                }
            },
            // Callback de error
            function(errorMessage) {
                console.error('Error:', errorMessage);

                // Eliminar indicador de carga
                document.body.removeChild(loadingIndicator);

                // Mostrar mensaje de error
                if (window.showErrorPopup) {
                    window.showErrorPopup(
                        'Error en la respuesta',
                        errorMessage || 'Hubo un error al procesar la respuesta del servidor.',
                        []
                    );
                } else {
                    alert('Error: ' + errorMessage);
                }
            }
        );

        return false;
    }
});
