/*
 * register.css - Estilos para el formulario de registro
 * Villarrica a un CLICK
 */

/* Estilos generales - Reset y configuración básica */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 10px;
}

/* Panel principal de registro */
.register-panel {
    width: 100%;
    max-width: 420px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 18px;
}

/* Encabezado del formulario de registro */
.register-header {
    text-align: center;
    margin-bottom: 8px;
}

.register-header h1 {
    color: #6a1b9a;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 3px;
}

.register-header p {
    color: #666;
    font-size: 13px;
}

/* Pasos del registro (indicador de progreso) */
.register-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    position: relative;
}

.register-steps::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.step {
    width: 22%;
    position: relative;
    z-index: 2;
    text-align: center;
}

.step-number {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 3px;
    font-weight: 600;
    font-size: 11px;
}

.step.active .step-number {
    background-color: #6a1b9a;
}

.step-title {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

.step.active .step-title {
    color: #6a1b9a;
    font-weight: 600;
}

/* Elementos del formulario */
.form-group {
    margin-bottom: 10px; /* Reducido para mayor compacidad */
}

/* Estilos para el texto abreviado/completo de Fecha de Nacimiento */
.texto-abreviado {
    display: none;
}

.texto-completo {
    display: inline-block;
}

.form-group label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    color: #333;
    font-size: 12px;
}

.form-control {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #6a1b9a;
    outline: none;
    box-shadow: 0 0 0 2px rgba(106, 27, 154, 0.2);
}

/* Filas y columnas del formulario */
.form-row {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
}

.form-col {
    flex: 1;
}

/* Alineación de campos de teléfono y WhatsApp */
.aligned-inputs {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
    justify-content: space-between;
}

.aligned-inputs .form-col {
    display: flex;
    flex-direction: column;
    max-width: 49%;
    width: 49%;
}

.aligned-inputs .form-group {
    width: 100%;
    margin-bottom: 0;
}

.aligned-inputs label {
    margin-bottom: 6px;
    font-size: 12px;
    font-weight: 600;
    color: #444;
}

.optional-label {
    font-weight: normal;
    font-size: 11px;
    color: #777;
    font-style: italic;
}

.aligned-inputs .phone-input {
    display: flex;
    align-items: center;
    height: 36px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.aligned-inputs .phone-prefix {
    min-width: 35px;
    width: 35px;
    text-align: center;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-right: 1px solid #ddd;
    font-size: 10px;
    font-weight: 600;
    color: #555;
    padding: 0;
}

.aligned-inputs .phone-number {
    border: none;
    height: 36px;
    border-radius: 0;
    font-size: 11px;
    padding: 0 4px;
    width: calc(100% - 35px);
}

.aligned-inputs .phone-number:focus {
    box-shadow: none;
}

/* Estilos para el campo de teléfono */
.phone-input {
    display: flex;
    align-items: center;
}

.phone-prefix {
    background-color: #f5f5f5;
    padding: 8px 6px;
    border: 1px solid #ddd;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333;
    font-weight: 500;
    font-size: 13px;
}

.phone-number {
    flex: 1;
    border-radius: 0 4px 4px 0;
}

/* Botones y acciones del formulario */
.form-actions {
    margin-top: 24px;
    margin-bottom: 24px;
    text-align: center;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-size: 13px;
}

.btn-next {
    background-color: #6a1b9a;
    color: white;
    width: 120px;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-size: 13px;
}

.btn-next:hover {
    background-color: #5c1786;
    transform: translateY(-1px);
}

/* Enlaces de inicio de sesión y privacidad */
.login-link {
    text-align: center;
    margin-top: 6px;
    margin-bottom: 6px;
    color: #666;
    font-size: 12px;
}

.login-link a {
    color: #6a1b9a;
    font-weight: 600;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}

.privacy-link {
    text-align: center;
    margin-top: 8px;
    margin-bottom: 12px;
    font-size: 12px;
}

.privacy-link a {
    color: #6a1b9a;
    text-decoration: underline;
    font-weight: bold;
    cursor: pointer;
}

.privacy-link a:hover {
    color: #5c1786;
}

/* Estilos para elementos select */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 25px;
}

/* Estilos para botones de radio */
.radio-group {
    display: flex;
    gap: 12px;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
}

.radio-option input {
    margin-right: 4px;
}

/* Estilos para fecha de nacimiento */
.date-input-container {
    display: flex;
    align-items: center;
}

#fechaNacimiento {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Manejo de pasos del formulario */
.form-step {
    display: none;
}

.form-step.active {
    display: block;
}

/* Campos con iconos */
.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

.help-icon {
    color: #ffd700;
    margin-left: 10px;
    cursor: pointer;
    font-size: 18px;
}

/* Campos de contraseña */
.password-field {
    position: relative;
    width: 100%;
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.form-control {
    width: 100%;
    padding-right: 35px;
}

/* Estilos para campos con error */
.form-control.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* Estilos para el mensaje de error del paso 4 */
.error-message {
    color: #dc3545;
    background-color: #fff8f8;
    border: 1px solid #dc3545;
    border-radius: 4px;
    padding: 10px;
    margin: 15px 0;
    font-size: 14px;
    text-align: center;
}

/* Estilo para el botón con error */
.btn-error {
    animation: shake 0.5s;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.radio-option.error {
    color: #dc3545;
}

.form-group select.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* Estilo para el borde rojo sin cambiar el fondo */
.error-border {
    border: 2px solid #dc3545 !important;
}

/* Botones de navegación entre pasos */
.form-actions-double {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    margin-bottom: 24px;
}

.btn-prev {
    background-color: #e0e0e0;
    color: #6a1b9a;
    border: 1px solid #6a1b9a;
}

.btn-prev:hover {
    background-color: #d5d5d5;
}

/* Estilos para placeholders */
.form-group input::placeholder {
    font-size: 12px;
}

.form-label-small {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Estilos para el paso 4: Planes de suscripción */
.subscription-subtitle {
    text-align: center;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.subscription-plans {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.subscription-plan {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.subscription-plan:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.subscription-plan.selected-plan {
    border: 2px solid #6a1b9a;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.2);
}

.plan-header {
    background-color: #6a1b9a;
    color: white;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.plan-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.plan-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.plan-price {
    font-size: 24px;
    font-weight: 700;
    margin-top: 5px;
}

.price-period {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
}

.plan-details {
    padding: 15px;
    background-color: white;
}

.plan-section {
    margin-bottom: 12px; /* Reducido de 15px a 12px */
}

.section-title {
    color: #6a1b9a;
    font-size: 15px; /* Reducido de 16px a 15px */
    margin-bottom: 8px; /* Reducido de 10px a 8px */
    border-bottom: 1px solid #f0e6f5;
    padding-bottom: 4px; /* Reducido de 5px a 4px */
}

.feature-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 0; /* Reducido de 5px a 4px */
    font-size: 13px; /* Reducido de 14px a 13px */
}

.feature-name {
    color: #333;
}

.feature-value {
    color: #666;
    font-weight: 500;
}

.feature-value.positive {
    color: #4caf50;
    font-weight: 600;
}

.feature-value.negative {
    color: #ff0000; /* Cambiado a rojo puro para mayor visibilidad */
    font-weight: 700; /* Aumentado el peso de la fuente para mayor visibilidad */
}

/* Botón para comparar planes */
.compare-plans-container {
    text-align: center;
    margin: 20px 0;
}

.compare-plans-btn {
    background-color: #f0e6f5;
    color: #6a1b9a;
    border: 1px solid #d9c2e5;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.compare-plans-btn:hover {
    background-color: #e6d8ed;
    border-color: #c9a6d9;
}

/* Estilos para el botón de comparar planes */
.btn-comparar-planes {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    background-color: #f0e6f5;
    color: #6a1b9a;
    border: 1px solid #d9c2e5;
    border-radius: 8px;
    padding: 12px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(106, 27, 154, 0.1);
    max-width: 350px;
    margin: 0 auto;
}

.btn-comparar-planes:hover {
    background-color: #e6d8ed;
    box-shadow: 0 4px 8px rgba(106, 27, 154, 0.2);
    transform: translateY(-2px);
}

.compare-icon {
    font-size: 20px;
    margin-bottom: 5px;
}

.compare-text {
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 3px;
}

.compare-subtext {
    font-size: 12px;
    opacity: 0.8;
}

/* Ocultar en pantallas pequeñas */
@media screen and (max-width: 768px) {
    .desktop-only {
        display: none !important;
    }
}

/* Selección de plan */
.plan-selection {
    margin-top: 20px;
    padding: 15px 15px 15px 10px; /* Padding reducido a la izquierda */
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.selection-title {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.plan-options {
    display: flex;
    justify-content: center; /* Centrado */
    flex-wrap: wrap;
    gap: 15px; /* Aumentado el espacio entre botones */
}

.plan-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: none; /* Eliminado el borde */
    border-radius: 8px;
    padding: 5px; /* Reducido el padding */
    width: 30%;
    min-width: 80px; /* Reducido el ancho mínimo */
    transition: all 0.3s ease;
    background-color: transparent; /* Cambiado a transparente */
}

.plan-option.selected .plan-option-button {
    /* Estilo actualizado para el botón del plan seleccionado */
    background-color: #5c1786;
    box-shadow: 0 2px 4px rgba(106, 27, 154, 0.3);
}

.plan-option-content {
    text-align: center;
    margin-bottom: 0; /* Cambiado de 10px a 0 ya que ahora está vacío */
}

.plan-option-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.plan-option-price {
    display: block;
    color: #666;
    font-size: 14px;
}

.plan-option-button {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 5px; /* Padding aumentado para altura */
    cursor: pointer;
    font-weight: 600;
    font-size: 10px; /* Texto más pequeño */
    transition: all 0.3s ease;
    width: 60px; /* Ancho mucho más reducido */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: auto;
    min-height: 40px; /* Altura mínima para acomodar dos líneas */
}

/* Estilos para el nombre del plan */
.plan-name {
    display: block;
    margin-bottom: 2px;
    font-weight: 600;
}

/* Estilos para el precio del plan */
.plan-price {
    display: block;
    font-size: 8px;
    font-weight: 400;
    color: #ffffff;
}

.plan-option-button:hover {
    background-color: #5c1786;
}

/* Estilos para las opciones de tipo de negocio */
.business-type-options {
    display: flex;
    justify-content: flex-start; /* Alineado a la izquierda */
    margin-bottom: 20px;
}

.servicios-arriendos-section {
    margin-top: 20px;
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

.servicios-arriendos-option {
    margin-bottom: 15px;
}

/* Texto más pequeño específicamente para Servicios/Arriendos */
.servicios-arriendos-option label {
    font-size: 11px; /* Texto aún más pequeño */
}

.business-type-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px 5px 5px 0; /* Sin padding a la izquierda */
    border-radius: 0; /* Eliminado el borde redondeado */
    transition: none; /* Eliminada la transición */
    margin: 0; /* Sin margen */
    background-color: transparent; /* Fondo transparente */
}

.business-type-option:hover {
    background-color: transparent; /* Eliminado el fondo al pasar el cursor */
}

.business-type-option.selected-type {
    background-color: transparent; /* Eliminado el fondo */
    border: none; /* Eliminado el borde */
}

.business-type-option input[type="radio"] {
    margin-right: 2px; /* Margen mínimo */
    margin-left: 5px; /* Margen a la izquierda para mover a la derecha */
    width: 12px;
    height: 12px;
    accent-color: #6a1b9a; /* Color del radio button */
}

.business-type-option label {
    font-weight: 500;
    cursor: pointer;
    font-size: 12px; /* Texto más pequeño */
}

/* Estilos para los contenedores de planes */
.plan-options-container {
    margin-bottom: 15px;
}

/* Nuevo estilo para la disposición horizontal */
.plan-selection-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    width: 100%; /* Asegurar que use todo el ancho disponible */
    padding-left: 0; /* Eliminar padding izquierdo */
}

.plan-selection-row .business-type-options {
    flex: 0 0 auto;
    margin-bottom: 0;
    padding-left: 0; /* Eliminar padding izquierdo */
    margin-left: 10px; /* Margen positivo para mover a la derecha */
}

.plan-selection-row .plan-options-container {
    flex: 1;
    margin-bottom: 0;
    margin-left: 10px; /* Margen izquierdo reducido */
}

.plan-selection-row .plan-options {
    justify-content: flex-end;
    gap: 4px; /* Espacio mínimo entre botones */
    display: flex;
    flex-wrap: nowrap; /* Evitar que los botones se envuelvan */
}

.plan-selection-row .plan-option {
    padding: 0; /* Eliminar padding para que los botones estén más juntos */
    width: auto; /* Ancho automático */
    min-width: auto; /* Sin ancho mínimo */
}

/* Estilos para mensajes de confirmación */
.confirmation-message p {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.confirmation-message p:first-child {
    font-weight: bold;
    color: #6a1b9a;
    font-size: 18px;
}

/* Estilos para el selector de categorías */
.category-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
}

.category-selector span {
    font-weight: 500;
    color: #6a1b9a;
    font-size: 13px;
}

/* Estilo para el selector de categorías seleccionado */
.category-selector.selected span {
    font-weight: 600;
}

.select-categories-btn {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.select-categories-btn:hover {
    background-color: #5c1786;
}

.selected-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
    min-height: 30px;
}

.selected-category-tag {
    background-color: #f0e6f5;
    color: #6a1b9a;
    border: 1px solid #d9c2e5;
    border-radius: 15px;
    padding: 3px 10px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    margin-bottom: 5px;
    position: relative;
    padding-right: 24px;
}

.selected-category-tag .remove-tag {
    position: absolute;
    right: 8px;
    width: 14px;
    height: 14px;
    background-color: #6a1b9a;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    cursor: pointer;
    line-height: 1;
}

.selected-category-tag .remove-tag:hover {
    background-color: #5c1786;
}

/* Estilos para el pop-up de categorías */
.categories-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.categories-popup {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 420px; /* Mismo ancho que el formulario */
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: popupFadeIn 0.3s ease;
}

.categories-popup-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.categories-popup-header h3 {
    color: #6a1b9a;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.categories-popup-close {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background-color: #e53935; /* Cambiado a rojo */
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
}

.categories-popup-close:hover {
    background-color: #c62828; /* Rojo más oscuro al hacer hover */
}

.categories-popup-content {
    padding: 20px;
    overflow-y: auto;
    flex-grow: 1;
}

.categories-popup-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.categories-popup-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    text-align: right;
}

.categories-popup-confirm {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.categories-popup-confirm:hover {
    background-color: #5c1786;
}

.category-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.category-checkbox:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.category-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #6a1b9a;
}

.category-checkbox span {
    font-size: 13px;
    color: #333;
}

/* Estilos para el contador de palabras */
.label-with-counter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.word-counter {
    font-size: 12px;
    color: #666;
}

/* Estilos para las opciones de radio */
.radio-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-options-centered {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 10px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.radio-option input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #6a1b9a;
}

.radio-label {
    font-size: 14px;
    color: #333;
}

/* Estilos para el contenedor de Local Físico en línea */
.local-fisico-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    margin-bottom: 15px;
}

.local-fisico-label {
    font-weight: 500;
    color: #333;
    font-size: 13px;
}

.local-fisico-options {
    display: flex;
    gap: 12px;
    margin-right: 20px;
}

.radio-option-small {
    display: flex;
    align-items: center;
    gap: 3px;
    cursor: pointer;
}

.radio-option-small input[type="radio"] {
    width: 12px;
    height: 12px;
    accent-color: #6a1b9a;
}

.radio-label-small {
    font-size: 13px;
    color: #333;
}

.local-fisico-options.error {
    padding: 4px 8px;
    border: 1px solid #dc3545;
    border-radius: 4px;
    background-color: #fff8f8;
}

/* Estilos para el paso 3 */
#step3 {
    padding-top: 5px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.confirmation-message {
    text-align: center;
    padding: 20px;
    margin: 20px 0;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

/* Estilos para botones de auto-llenado */
.auto-fill-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.btn-auto-fill {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-auto-fill:hover {
    background-color: #45a049;
}

/* Estilos para validación de contraseña */
.password-requirements {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

.requirement {
    margin-bottom: 3px;
}

.requirement .icon {
    display: inline-block;
    width: 16px;
    color: #ccc;
}

.requirement.valid .icon {
    color: #4CAF50;
}

.requirement.invalid .icon {
    color: #dc3545;
}

.password-match-message {
    margin-top: 5px;
    font-size: 12px;
    color: #dc3545;
}

/* Estilos para el popup de requisitos de contraseña */
.password-label {
    display: flex;
    align-items: center;
}

.info-icon {
    margin-left: 8px;
    color: #ffc107;
    cursor: pointer;
    font-size: 16px;
}

.password-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.password-popup-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 350px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.password-popup-header {
    background-color: #6a1b9a;
    color: white;
    padding: 12px 15px;
    text-align: center;
}

.password-popup-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.password-popup-body {
    padding: 20px;
}

.password-requirements-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.password-requirements-list li {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.password-requirements-list i {
    color: #4CAF50;
    margin-right: 10px;
}

.password-popup-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid #eee;
}

.btn-close-popup {
    background-color: #6a1b9a;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-close-popup:hover {
    background-color: #5c1786;
}

/* Estilos para los popups de planes */
.plan-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.plan-popup {
    background-color: white;
    border-radius: 8px;
    max-width: 360px; /* Reducido aún más de 400px a 360px para hacerlo más angosto */
    width: 80%; /* Reducido aún más de 85% a 80% */
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.plan-popup-header {
    background-color: #6a1b9a;
    color: white;
    padding: 12px 15px; /* Reducido de 15px 20px a 12px 15px */
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.plan-popup-header h3 {
    margin: 0;
    font-size: 16px; /* Reducido de 18px a 16px */
    font-weight: 600;
}

.plan-popup-close {
    background: none;
    border: none;
    color: #e53935; /* Cambiado a rojo */
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    font-weight: bold;
}

.plan-popup-content {
    padding: 15px 12px; /* Reducido el padding horizontal de 15px a 12px */
}

/* Estilos para el popup de comparación de planes */
.plans-comparison-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.plans-comparison-popup {
    background-color: white;
    border-radius: 8px;
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.comparison-popup-header {
    background-color: #6a1b9a;
    color: white;
    padding: 15px 20px;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.comparison-popup-header h3 {
    margin: 0 0 5px;
    font-size: 20px;
    font-weight: 600;
}

.comparison-popup-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.comparison-popup-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: #e53935; /* Cambiado a rojo */
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
    font-weight: bold;
}

.comparison-popup-content {
    padding: 20px;
}

.comparison-plans {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.comparison-plan {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
    max-width: 280px;
}

.comparison-plan-header {
    background-color: #f0e6f5;
    padding: 15px;
    text-align: center;
    border-bottom: 1px solid #e0e0e0;
}

.comparison-plan-header h4 {
    margin: 10px 0;
    color: #6a1b9a;
    font-size: 16px;
}

.comparison-plan-details {
    padding: 15px;
}

.comparison-section {
    margin-bottom: 20px;
}

.comparison-section h5 {
    color: #6a1b9a;
    font-size: 14px;
    margin-bottom: 10px;
    border-bottom: 1px solid #f0e6f5;
    padding-bottom: 5px;
}

.comparison-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    font-size: 13px;
}

.comparison-popup-footer {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #e0e0e0;
}

.comparison-popup-close-btn {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.comparison-popup-close-btn:hover {
    background-color: #5c1786;
}

/* Navegación móvil entre planes */
.mobile-plan-navigation {
    display: none;
    justify-content: center;
    margin-bottom: 15px;
}

.mobile-plan-navigation-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #e0e0e0;
    margin: 0 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.mobile-plan-navigation-dot.active {
    background-color: #6a1b9a;
}

/* Responsive para el popup de comparación */
@media screen and (max-width: 768px) {
    .comparison-plans {
        flex-direction: column;
        align-items: center;
    }

    .comparison-plan {
        width: 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }

    .mobile-plan-navigation {
        display: flex;
    }

    /* Cambio en la disposición de los planes en modo responsive */
    .plan-selection-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .plan-selection-row .business-type-options {
        margin-bottom: 10px;
        width: 100%;
    }

    .plan-selection-row .plan-options-container {
        width: 100%;
        margin-left: 0;
    }

    .plan-selection-row .plan-options {
        justify-content: center;
    }

    /* Cambiar el texto de "Fecha de Nacimiento" a "F.Nacim." en modo responsive */
    .texto-abreviado {
        display: inline-block;
        font-size: 11px;
    }

    .texto-completo {
        display: none;
    }

    /* Ajustar el ancho de los campos en modo responsive */
    #step1 .form-row:nth-child(2) .form-col:first-child {
        flex: 1.2;
        min-width: 48%;
    }

    #step1 .form-row:nth-child(2) .form-col:last-child {
        flex: 0.8;
        min-width: 48%;
    }

    /* Hacer que el campo de fecha tenga el mismo ancho que el campo de teléfono */
    #fechaNacimiento {
        width: 100%;
        box-sizing: border-box;
        max-width: 100%;
    }

    /* Ajustar el ancho del contenedor de fecha para que coincida con el teléfono */
    .date-input-container {
        width: 100%;
        max-width: 100%;
    }

    /* Ajustar el ancho del campo de teléfono para referencia */
    #step1 .form-row:nth-child(3) .form-col:last-child {
        flex: 1;
    }

    /* Ajustar el ancho de los campos para que sean uniformes */
    #step1 .form-row:nth-child(2) {
        display: flex;
        justify-content: space-between;
    }

    #step1 .form-row:nth-child(2) .form-col {
        width: 48%;
    }

    #step1 .form-row:nth-child(3) {
        display: flex;
        justify-content: space-between;
    }

    #step1 .form-row:nth-child(3) .form-col {
        width: 48%;
    }
}

/* Estilos para la pregunta de preferencia de plan */
.plan-preference-container {
    margin: 20px 0 12px; /* Reducido de 25px 0 15px */
    text-align: center;
}

.plan-preference-title {
    font-size: 14px; /* Reducido de 16px */
    color: #333;
    margin-bottom: 12px; /* Reducido de 15px */
    font-weight: 600;
}

.plan-preference-options {
    display: flex;
    justify-content: center;
    gap: 12px; /* Reducido de 15px */
    margin-bottom: 15px; /* Reducido de 20px */
}

.plan-preference-option {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 7px 15px; /* Reducido de 10px 20px */
    font-size: 12px; /* Reducido de 14px */
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px; /* Reducido de 100px */
}

.plan-preference-option:hover {
    background-color: #e9e9e9;
}

.plan-preference-option.selected {
    background-color: #6a1b9a;
    color: white;
    border-color: #6a1b9a;
}

/* Estilos para las opciones de documento */
.documento-preference-container {
    margin: 5px 0 15px; /* Margen reducido arriba */
    text-align: center;
}

.documento-option.selected {
    background-color: #6a1b9a;
    color: white;
    border-color: #6a1b9a;
}

/* Estilo para el indicador de completado */
.documento-option.completed::after {
    content: "✓";
    display: inline-block;
    color: #4CAF50;
    font-weight: bold;
    margin-left: 5px;
}

/* Estilos para los popups de factura y confirmación */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.factura-popup, .confirmacion-popup {
    background-color: white;
    border-radius: 8px;
    max-width: 360px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.factura-popup-header, .confirmacion-popup-header {
    background-color: #6a1b9a;
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.factura-popup-header h3, .confirmacion-popup-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.factura-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.factura-popup-content, .confirmacion-popup-content {
    padding: 15px 12px;
}

.confirmacion-popup-content p {
    text-align: center;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.4;
}

.confirmacion-popup-content p:last-of-type {
    margin-bottom: 20px;
    font-weight: 500;
}

.factura-popup-footer, .confirmacion-popup-footer {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 15px;
}

.btn-aceptar-factura, .btn-aceptar-confirmacion, .btn-modificar-factura {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-aceptar-factura:hover, .btn-aceptar-confirmacion:hover, .btn-modificar-factura:hover {
    background-color: #5c1786;
}

.btn-modificar-factura {
    background-color: #4CAF50;
}

.btn-modificar-factura:hover {
    background-color: #45a049;
}

/* Estilos para el popup de error de fecha */
.error-popup {
    background-color: white;
    border-radius: 8px;
    max-width: 360px;
    width: 80%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.error-popup-header {
    background-color: #e53935; /* Color rojo para indicar error */
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.error-popup-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.error-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.error-popup-content {
    padding: 15px 12px;
    text-align: center;
}

.error-message-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.error-message-container p {
    margin: 0;
    font-size: 14px;
    color: #333;
    padding: 5px 0;
}

.error-message-container p:first-child {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Estilos para campos con error */
input.error, select.error, textarea.error {
    border: 2px solid #e53935 !important;
    background-color: rgba(229, 57, 53, 0.05);
}

/* Estilo para el campo de fecha con error */
input.fecha-error {
    border: 2px solid #e53935 !important;
    background-color: rgba(229, 57, 53, 0.05);
}

/* Estilo para el campo de correo con error */
input.email-error {
    border: 2px solid #e53935 !important;
    background-color: rgba(229, 57, 53, 0.05);
}

/* Estilo para opciones de radio con error */
.local-fisico-options.error {
    border: 2px solid #e53935 !important;
    border-radius: 4px;
    padding: 5px;
    background-color: rgba(229, 57, 53, 0.05);
}

/* Estilo para opciones de plan con error */
.plan-preference-option.error, .documento-option.error, .business-type-option.error {
    border: 2px solid #e53935 !important;
    background-color: rgba(229, 57, 53, 0.05);
    animation: pulse 1s;
}

/* Estilo para opciones con solo borde rojo */
.plan-preference-option.error-border, .documento-option.error-border, .business-type-option.error-border {
    border: 2px solid #e53935 !important;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(229, 57, 53, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(229, 57, 53, 0); }
    100% { box-shadow: 0 0 0 0 rgba(229, 57, 53, 0); }
}

/* Estilos para el popup de bienvenida */
.bienvenida-popup, .confirmacion-popup {
    background-color: white;
    border-radius: 8px;
    max-width: 360px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.bienvenida-popup-header, .confirmacion-popup-header {
    background-color: #6a1b9a;
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.bienvenida-popup-header h3, .confirmacion-popup-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.bienvenida-popup-close, .confirmacion-paso1-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.bienvenida-popup-content, .confirmacion-popup-content {
    padding: 15px 12px;
    text-align: center;
}

.bienvenida-popup-content p, .confirmacion-popup-content p {
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.4;
}

.bienvenida-popup-footer, .confirmacion-popup-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid #eee;
}

.btn-cerrar-bienvenida, .btn-aceptar-confirmacion-paso1 {
    background-color: #6a1b9a;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-cerrar-bienvenida:hover, .btn-aceptar-confirmacion-paso1:hover {
    background-color: #5c1786;
}

/* Estilos para el indicador de carga */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-indicator p {
    color: white;
    margin-top: 10px;
    font-size: 16px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #6a1b9a;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para la aceptación simple de términos */
.simple-terms-container {
    margin: 20px 0;
    text-align: center;
    width: 100%;
}

.simple-terms-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.simple-terms-item label {
    font-size: 13px;
    color: #333;
    text-align: center;
    cursor: pointer;
}

.simple-terms-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #6a1b9a;
    cursor: pointer;
}

.simple-terms-item.error-border {
    border: 1px solid #dc3545;
    border-radius: 4px;
}

/* Estilos para popups de términos y políticas eliminados */
