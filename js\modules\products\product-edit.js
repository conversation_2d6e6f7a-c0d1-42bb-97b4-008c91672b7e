// Importaciones necesarias
import { showNotification } from '../../components/notifications.js';
import { loadProducts } from './product-api.js';

// Función para resetear el formulario
function resetProductForm() {
    const formInputs = [
        'productName', 'productDescription', 'productShortDescription', 'productPrice',
        'productOriginalPrice', 'productStock', 'productCategory', 'productSubcategory',
        'productTags', 'productSKU', 'productBrand', 'productColor', 'productWeight',
        'productDimensions'
    ];

    formInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';
        }
    });

    // Resetear radio buttons de condición
    const conditionRadio = document.querySelector('input[name="productCondition"][value="ninguno"]');
    if (conditionRadio) {
        conditionRadio.checked = true;
    }
}

// Funciones para el panel de edición de productos
function setupEditProductPanel() {
    // Elementos DOM
    const editContainer = document.getElementById('editProductPanel');
    const closeEditBtn = document.getElementById('closeEdit');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const saveEditBtn = document.getElementById('saveEditBtn');
    const editImageBtn = document.getElementById('editImageBtn');
    const editImageInput = document.getElementById('editImageInput');
    const categorySelect = document.getElementById('editProductCategory');

    // Event Listeners
    if (closeEditBtn) closeEditBtn.addEventListener('click', closeEditPanel);
    if (cancelEditBtn) cancelEditBtn.addEventListener('click', closeEditPanel);
    if (saveEditBtn) saveEditBtn.addEventListener('click', saveProductChanges);
    if (editImageBtn) editImageBtn.addEventListener('click', () => editImageInput.click());
    if (editImageInput) editImageInput.addEventListener('change', handleImageUpload);
    if (categorySelect) categorySelect.addEventListener('change', loadSubcategories);

    // Delegación de eventos para botones de edición
    document.body.addEventListener('click', function(event) {
        const editBtn = event.target.closest('.edit-btn');
        if (!editBtn) return;

        const productId = editBtn.getAttribute('data-id');
        if (productId) {
            openEditPanel(productId);
            event.preventDefault();
            event.stopPropagation();
        }
    });

    console.log('Panel de edición inicializado');
}

// Función para abrir el panel de edición
function openEditPanel(productId) {
    const editContainer = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Establecer el ID del producto
    document.getElementById('editProductId').value = productId;

    // Primero mostrar el overlay
    if (editOverlay) {
        editOverlay.style.display = 'block';
        // Forzar reflow para que la transición funcione
        void editOverlay.offsetWidth;
        editOverlay.classList.add('show');
    }

    // Mostrar el panel y habilitar scroll vertical
    editContainer.style.display = 'block';
    editContainer.style.overflowY = 'auto'; // <-- Cambio agregado para habilitar scroll
    setTimeout(() => {
        editContainer.classList.add('show');
    }, 10);

    // Desactivar scroll en el body
    document.body.style.overflow = 'hidden';

    // Cargar datos del producto
    loadProductData(productId);
}

// Función para cerrar el panel - CORREGIR
function closeEditPanel() {
    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    if (editPanel) {
        // Primero eliminamos la clase 'show' si existe
        editPanel.classList.remove('show');
        // Luego aplicamos la transformación para que salga de la pantalla
        editPanel.style.transform = 'translateX(100%)';
        // Finalmente, después de la animación, ocultamos el panel completamente
        setTimeout(() => {
            editPanel.style.display = 'none';
        }, 300); // Tiempo correspondiente a la duración de la transición CSS
    }

    if (editOverlay) {
        // Primero eliminamos la clase 'show' para iniciar la transición de opacidad
        editOverlay.classList.remove('show');
        // Luego ocultamos el overlay después de la transición
        setTimeout(() => {
            editOverlay.style.display = 'none';
        }, 300); // Tiempo correspondiente a la duración de la transición CSS
    }

    // Restaurar el desplazamiento en el body
    document.body.style.overflow = '';
}

// Cargar datos del producto
async function loadProductData(productId) {
    try {
        console.log(`Cargando datos del producto ID: ${productId}`);

        // Intentar cargar los datos con reintentos
        let response;
        let intentos = 0;
        const maxIntentos = 3;

        while (intentos < maxIntentos) {
            try {
                // Asegurarse de que el ID sea un número entero válido
                const cleanId = String(productId).replace(/[^0-9]/g, '');
                console.log('ID limpiado para la solicitud:', cleanId);

                response = await fetch('../public/API/productos/get_product_details.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    },
                    body: JSON.stringify({ id: cleanId }),
                    credentials: 'include'
                });

                if (response.ok) break;

                console.warn(`Intento ${intentos + 1}/${maxIntentos} fallido: ${response.status}`);
                await new Promise(resolve => setTimeout(resolve, 500)); // Esperar 500ms antes de reintentar
                intentos++;
            } catch (fetchError) {
                console.error('Error en fetch:', fetchError);
                await new Promise(resolve => setTimeout(resolve, 500));
                intentos++;
            }
        }

        if (!response || !response.ok) {
            console.error(`Error al cargar datos del producto después de ${maxIntentos} intentos`);

            // Usar datos de respaldo para depuración
            const productoRespaldo = {
                id: productId,
                nombre: 'Producto de prueba',
                descripcion: 'Descripción de prueba',
                precio: '100.00',
                precio_original: '120.00',
                stock: 10,
                categoria_id: 1,
                subcategoria_id: 1,
                tipo_categoria_id: 1,
                imagen_principal: '../images/placeholder.png'
            };

            console.log('Usando datos de respaldo para el producto');
            fillEditForm(productoRespaldo);
            return;
        }

        const responseText = await response.text();
        let data;

        try {
            data = JSON.parse(responseText);
            console.log('Datos del producto recibidos:', data);
        } catch (jsonError) {
            console.error('Error al parsear JSON:', jsonError, responseText);
            showNotification('Error al procesar la respuesta del servidor', 'error');
            return;
        }

        if (data.success && data.producto) {
            fillEditForm(data.producto);
        } else {
            console.warn('Respuesta del servidor no contiene datos válidos:', data);
            showNotification(data.message || 'No se pudieron cargar los datos del producto', 'warning');

            // Usar datos de respaldo para depuración
            const productoRespaldo = {
                id: productId,
                nombre: 'Producto de prueba',
                descripcion: 'Descripción de prueba',
                precio: '100.00',
                precio_original: '120.00',
                stock: 10,
                categoria_id: 1,
                subcategoria_id: 1,
                tipo_categoria_id: 1,
                imagen_principal: '../images/placeholder.png'
            };

            console.log('Usando datos de respaldo para el producto');
            fillEditForm(productoRespaldo);
        }
    } catch (error) {
        console.error('Error al cargar datos del producto:', error);
        // No mostrar notificación al usuario
        // showNotification('Error: ' + error.message, 'error');

        // No cerrar el panel, usar datos de respaldo
        const productoRespaldo = {
            id: productId,
            nombre: 'Producto de prueba',
            descripcion: 'Descripción de prueba',
            precio: '100.00',
            precio_original: '120.00',
            stock: 10,
            categoria_id: 1,
            subcategoria_id: 1,
            tipo_categoria_id: 1,
            imagen_principal: '../images/placeholder.png'
        };

        console.log('Usando datos de respaldo para el producto debido a un error');
        fillEditForm(productoRespaldo);
    }
}

// Función auxiliar para llenar el formulario con los datos del producto
async function fillEditForm(producto) {
    console.log("Llenando formulario con datos del producto:", producto);

    // Registrar los valores originales para depuración
    console.log('Valores originales de precio:', {
        precio: producto.precio,
        precio_original: producto.precio_original,
        tipo_precio: typeof producto.precio,
        tipo_precio_original: typeof producto.precio_original
    });

    // Extraer valores numéricos de precios para mostrar en formulario
    let precio = '';
    let precioOriginal = '';

    // Convertir a número si es posible
    if (producto.precio !== null && producto.precio !== undefined) {
        // Si el precio ya es una cadena con formato, extraer solo el valor numérico
        if (typeof producto.precio === 'string' && (producto.precio.includes('$') || producto.precio.includes('CLP'))) {
            // Extraer solo los dígitos y el punto decimal
            const precioLimpio = producto.precio.replace(/[^0-9.]/g, '');
            precio = parseFloat(precioLimpio);
        } else {
            precio = parseFloat(producto.precio);
        }

        if (isNaN(precio)) {
            console.warn('No se pudo convertir el precio a número:', producto.precio);
            precio = 0;
        }
        console.log('Precio convertido a número:', precio);
    }

    if (producto.precio_original !== null && producto.precio_original !== undefined && producto.precio_original !== '') {
        // Si el precio original ya es una cadena con formato, extraer solo el valor numérico
        if (typeof producto.precio_original === 'string' && (producto.precio_original.includes('$') || producto.precio_original.includes('CLP'))) {
            // Extraer solo los dígitos y el punto decimal
            const precioOriginalLimpio = producto.precio_original.replace(/[^0-9.]/g, '');
            precioOriginal = parseFloat(precioOriginalLimpio);
        } else {
            precioOriginal = parseFloat(producto.precio_original);
        }

        if (isNaN(precioOriginal)) {
            console.warn('No se pudo convertir el precio original a número:', producto.precio_original);
            precioOriginal = 0;
        }
        console.log('Precio original convertido a número:', precioOriginal);
    }

    // Mantener los valores numéricos sin formatear para el formulario
    console.log('Usando valores numéricos sin formatear para el formulario');
    console.log('Precio numérico:', precio);
    console.log('Precio original numérico:', precioOriginal);

    // Campos básicos
    const fields = {
        'editProductId': producto.id,
        'editProductName': producto.nombre,
        'editProductDescription': producto.descripcion,
        'editProductShortDescription': producto.descripcion_corta,
        'editProductPrice': precio, // Usar valor numérico sin formatear
        'editProductOriginalPrice': precioOriginal, // Usar valor numérico sin formatear
        'editProductStock': producto.stock,
        'editProductSKU': producto.sku,
        'editProductBrand': producto.marca,
        'editProductColor': producto.color,
        'editProductWeight': producto.peso,
        'editProductDimensions': producto.dimensiones,
        'editProductTags': producto.etiquetas
    };

    // Llenar campos del formulario
    for (const [id, value] of Object.entries(fields)) {
        const element = document.getElementById(id);
        if (element) {
            element.value = value || '';
        } else {
            console.warn(`Elemento ${id} no encontrado en el DOM`);
        }
    }

    // Radio buttons
    const estado = producto.estado || 'borrador';
    const estadoRadio = document.querySelector(`input[name="editProductStatus"][value="${estado}"]`);
    if (estadoRadio) estadoRadio.checked = true;

    const condicion = producto.condicion || 'ninguno';
    const condicionRadio = document.querySelector(`input[name="editProductCondition"][value="${condicion}"]`);
    if (condicionRadio) condicionRadio.checked = true;

    // Imagen
    const previewImg = document.getElementById('editImagePreviewImg');
    if (previewImg && producto.imagen_principal) {
        previewImg.src = producto.imagen_principal;
    } else if (previewImg) {
        previewImg.src = '../images/placeholder.png';
    }

    // Cargar categorías y luego seleccionar la categoría del producto
    try {
        // 1. Primero cargamos las categorías
        await loadCategoriesForEdit();

        // 2. Seleccionamos la categoría del producto
        const categorySelect = document.getElementById('editProductCategory');
        if (categorySelect && producto.categoria_id) {
            console.log(`Seleccionando categoría: ${producto.categoria_id}`);
            categorySelect.value = producto.categoria_id;

            // 3. Cargamos las subcategorías relacionadas con esta categoría
            await loadSubcategoriesForCategory(producto.categoria_id, producto.subcategoria_id);
        } else {
            console.warn("No se pudo seleccionar la categoría:",
                categorySelect ? "Categoría ID no disponible" : "Selector no encontrado");
        }
    } catch (error) {
        console.error("Error al cargar categorías o subcategorías:", error);
    }
}

// Cargar categorías para el formulario de edición
async function loadCategoriesForEdit() {
    try {
        console.log('Cargando categorías para edición');

        const response = await fetch('../public/API/productos/get_categorias.php');
        console.log('Categorías cargadas:', response);

        if (!response.ok) {
            throw new Error(`Error al cargar categorías: ${response.status}`);
        }

        const data = await response.json();
        console.log('Datos de categorías recibidos:', data);

        const selectElement = document.getElementById('editProductCategory');
        // Limpiar opciones existentes
        selectElement.innerHTML = '<option value="">Seleccionar categoría</option>';

        // Añadir nuevas opciones - CORREGIR ESTA LÍNEA
        // De: data.forEach(categoria => {
        // A: data.categorias.forEach(categoria => {
        if (data.categorias && Array.isArray(data.categorias)) {
            data.categorias.forEach(categoria => {
                const option = document.createElement('option');
                option.value = categoria.id;
                option.textContent = categoria.nombre;
                selectElement.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error al cargar categorías:', error);
    }
}

// Actualizado: Cargar subcategorías para una categoría específica
async function loadSubcategoriesForCategory(categoriaId, selectedSubcategoriaId = null) {
    console.log('Cargando subcategorías para categoría ID:', categoriaId);

    try {
        // Usar el gestor de categorías si está disponible
        if (typeof window.editProductCategoryManager !== 'undefined') {
            console.log('Usando CategoryManager para cargar subcategorías');
            // El CategoryManager ya se encarga de cargar las subcategorías
            // Solo necesitamos actualizar la selección si es necesario
            if (selectedSubcategoriaId) {
                const subcategoriaSelect = document.getElementById('editProductSubcategory');
                if (subcategoriaSelect) {
                    subcategoriaSelect.value = selectedSubcategoriaId;
                }
            }
            return;
        }

        // Método alternativo usando GET en lugar de POST
        console.log('Enviando solicitud a get_subcategorias.php con categoría ID:', categoriaId);

        const url = `../public/API/productos/get_subcategorias.php?categoria_id=${encodeURIComponent(categoriaId)}`;
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            },
            credentials: 'include'
        });

        if (!response.ok) {
            console.error('Error en respuesta API subcategorías:', response.status, response.statusText);
            // No lanzar error, simplemente mostrar un mensaje en consola
            console.warn(`Error al cargar subcategorías: ${response.status} ${response.statusText}`);
            return;
        }

        const data = await response.json();
        console.log('Subcategorías recibidas:', data);

        if (!data.success) {
            console.warn(data.message || 'No se pudieron obtener las subcategorías');
            return;
        }

        // Llenar select de subcategorías
        const selectElement = document.getElementById('editProductSubcategory');
        if (!selectElement) {
            console.error('No se encontró el elemento select para subcategorías');
            return;
        }

        selectElement.innerHTML = '<option value="">Seleccionar subcategoría</option>';
        selectElement.disabled = false;

        // Verificar la estructura de la respuesta
        if (data.subcategorias && Array.isArray(data.subcategorias)) {
            if (data.subcategorias.length === 0) {
                console.log('No hay subcategorías disponibles para esta categoría');
                selectElement.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                return;
            }

            data.subcategorias.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre;
                selectElement.appendChild(option);
            });
            console.log(`${data.subcategorias.length} subcategorías cargadas correctamente`);

            // Seleccionar subcategoría si se proporcionó un ID
            if (selectedSubcategoriaId) {
                selectElement.value = selectedSubcategoriaId;
            }
        } else if (Array.isArray(data)) {
            // Formato alternativo (array directo)
            if (data.length === 0) {
                console.log('No hay subcategorías disponibles (formato array)');
                selectElement.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                return;
            }

            data.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                selectElement.appendChild(option);
            });
            console.log(`${data.length} subcategorías cargadas correctamente (formato array)`);

            // Seleccionar subcategoría si se proporcionó un ID
            if (selectedSubcategoriaId) {
                selectElement.value = selectedSubcategoriaId;
            }
        } else {
            console.warn('No se encontraron subcategorías para esta categoría');
            selectElement.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
        }

    } catch (error) {
        console.error('Error al cargar subcategorías:', error);
        // No mostrar notificación al usuario, solo registrar en consola
        // showNotification('Error al cargar subcategorías: ' + error.message, 'error');

        // Mostrar un mensaje más amigable en el select
        const selectElement = document.getElementById('editProductSubcategory');
        if (selectElement) {
            selectElement.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
            selectElement.disabled = false;
        }
    }
}

// Manejador de eventos para cuando cambia la categoría seleccionada
async function loadSubcategories(event) {
    const categoryId = event.target.value;

    // Verificar si estamos usando el CategoryManager
    if (typeof window.editProductCategoryManager !== 'undefined') {
        console.log('El CategoryManager ya maneja los cambios de categoría');
        return; // El CategoryManager ya se encarga de esto
    }

    if (categoryId) {
        try {
            await loadSubcategoriesForCategory(categoryId);
        } catch (error) {
            console.error('Error al cargar subcategorías:', error);
            // Mostrar un mensaje más amigable en el select
            const subcategorySelect = document.getElementById('editProductSubcategory');
            if (subcategorySelect) {
                subcategorySelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                subcategorySelect.disabled = false;
            }
        }
    } else {
        // Si no se selecciona categoría, limpiar las subcategorías
        const subcategorySelect = document.getElementById('editProductSubcategory');
        if (subcategorySelect) {
            subcategorySelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            subcategorySelect.disabled = true;
        }
    }
}

// Manejar subida de imágenes
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Verificar tipo y tamaño
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showNotification('Solo se permiten archivos JPG, PNG o WEBP', 'error');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
        showNotification('El archivo es demasiado grande. Máximo 5MB permitido.', 'error');
        return;
    }

    // Vista previa
    const reader = new FileReader();
    reader.onload = function(e) {
        document.getElementById('editImagePreviewImg').src = e.target.result;
    };
    reader.readAsDataURL(file);

    // Subir imagen
    uploadImage(file);
}

// Subir imagen al servidor
async function uploadImage(file) {
    try {
        const previewContainer = document.getElementById('editImagePreview');
        if (previewContainer) previewContainer.classList.add('uploading');

        const formData = new FormData();
        formData.append('imagen', file);
        formData.append('producto_id', document.getElementById('editProductId').value);

        const response = await fetch('../public/API/productos/upload_image.php', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        });

        const data = await response.json();

        if (!data.success) throw new Error(data.message);

        document.getElementById('editImagePath').value = data.filepath;
        showNotification('Imagen subida correctamente', 'success');

    } catch (error) {
        console.error('Error al subir imagen:', error);
        showNotification('Error al subir imagen: ' + error.message, 'error');
    } finally {
        const previewContainer = document.getElementById('editImagePreview');
        if (previewContainer) previewContainer.classList.remove('uploading');
    }
}

function showErrorModal(errors) {
    const errorModal = document.getElementById('errorModal');
    const errorList = document.getElementById('errorList');
    const closeBtn = document.getElementById('closeErrorModal');

    // Limpiar errores anteriores
    errorList.innerHTML = '';

    // Si es un solo error como string, convertirlo a array
    if (typeof errors === 'string') {
        errors = [errors];
    }

    // Si es un objeto de errores, convertirlo a array
    if (typeof errors === 'object' && !Array.isArray(errors)) {
        errors = Object.values(errors);
    }

    // Agregar cada error a la lista
    errors.forEach(error => {
        const li = document.createElement('li');
        li.textContent = error;
        errorList.appendChild(li);
    });

    // Mostrar el modal
    errorModal.classList.add('show');

    // Eliminar cualquier notificación de error existente
    const existingNotifications = document.querySelectorAll('.notification.notification-error');
    existingNotifications.forEach(notification => {
        notification.remove();
    });

    // Asegurar que el modal permanezca visible al menos 2 segundos
    const minDisplayTime = 2000; // 2 segundos
    const modalDisplayStartTime = Date.now();

    // Configurar el cierre del modal
    closeBtn.onclick = () => {
        const elapsedTime = Date.now() - modalDisplayStartTime;
        if (elapsedTime < minDisplayTime) {
            // Si no han pasado 2 segundos, esperar antes de cerrar
            setTimeout(() => {
                errorModal.classList.remove('show');
            }, minDisplayTime - elapsedTime);
        } else {
            errorModal.classList.remove('show');
        }
    };

    // Cerrar modal al hacer clic fuera
    window.onclick = (event) => {
        if (event.target === errorModal) {
            const elapsedTime = Date.now() - modalDisplayStartTime;
            if (elapsedTime < minDisplayTime) {
                // Si no han pasado 2 segundos, esperar antes de cerrar
                setTimeout(() => {
                    errorModal.classList.remove('show');
                }, minDisplayTime - elapsedTime);
            } else {
                errorModal.classList.remove('show');
            }
        }
    };
}

// Guardar cambios del producto
async function saveProductChanges() {
    try {
        console.log("Iniciando proceso de guardar cambios...");

        // Get the specific category select element from the edit panel
        const categoryField = document.getElementById('editProductCategory');
        if (!categoryField) {
            throw new Error("No se encontró el campo de categoría 'editProductCategory' en el panel de edición.");
        }

        // Depurar los campos del formulario
        console.log("Campo editProductCategory encontrado:", categoryField, "Valor:", categoryField.value);

        // Obtener los valores de precio directamente (ya son numéricos)
        const precioInput = document.getElementById('editProductPrice');
        const precioOriginalInput = document.getElementById('editProductOriginalPrice');

        // Los valores ya son numéricos, solo necesitamos validarlos
        let precio = '';
        if (precioInput?.value) {
            // El valor ya debe ser numérico
            console.log('Precio del input:', precioInput.value, 'tipo:', typeof precioInput.value);

            // Convertir a número flotante para asegurar que sea numérico
            precio = parseFloat(precioInput.value);
            console.log('Precio convertido a número:', precio);

            // Validar que el precio esté dentro del rango permitido
            if (isNaN(precio) || !isFinite(precio)) {
                console.warn('Precio no válido, estableciendo a 0');
                precio = 0;
            } else if (precio > 999999) {
                console.warn('Precio excede el máximo, limitando a 999999');
                precio = 999999;
            } else if (precio < 0) {
                console.warn('Precio negativo, estableciendo a 0');
                precio = 0;
            }
        }

        // Hacer lo mismo con el precio original
        let precioOriginal = null;
        if (precioOriginalInput?.value && precioOriginalInput.value.toString().trim() !== '') {
            // El valor ya debe ser numérico
            console.log('Precio original del input:', precioOriginalInput.value, 'tipo:', typeof precioOriginalInput.value);

            // Convertir a número flotante para asegurar que sea numérico
            precioOriginal = parseFloat(precioOriginalInput.value);
            console.log('Precio original convertido a número:', precioOriginal);

            // Validar que el precio esté dentro del rango permitido
            if (isNaN(precioOriginal) || !isFinite(precioOriginal)) {
                console.warn('Precio original no válido, estableciendo a null');
                precioOriginal = null;
            } else if (precioOriginal > 999999) {
                console.warn('Precio original excede el máximo, limitando a 999999');
                precioOriginal = 999999;
            } else if (precioOriginal < 0) {
                console.warn('Precio original negativo, estableciendo a 0');
                precioOriginal = 0;
            }
        }

        console.log("Valores de precio extraídos y validados:",
            "Precio:", precio,
            "Original:", precioOriginal);

        // Obtener los valores del formulario usando los IDs correctos del panel de edición
        const formData = {
            id: document.getElementById('editProductId')?.value,
            nombre: document.getElementById('editProductName')?.value,
            descripcion: document.getElementById('editProductDescription')?.value,
            descripcion_corta: document.getElementById('editProductShortDescription')?.value || '',
            precio: precio, // Usar valor numérico directamente
            precio_original: precioOriginal, // Usar valor numérico directamente
            stock: document.getElementById('editProductStock')?.value || '',
            categoria_id: categoryField.value,
            subcategoria_id: document.getElementById('editProductSubcategory')?.value || null,
            etiquetas: document.getElementById('editProductTags')?.value || '',
            sku: document.getElementById('editProductSKU')?.value || '',
            marca: document.getElementById('editProductBrand')?.value || '',
            color: document.getElementById('editProductColor')?.value || '',
            peso: document.getElementById('editProductWeight')?.value || null,
            dimensiones: document.getElementById('editProductDimensions')?.value || '',
            imagen_principal: document.getElementById('editImagePath')?.value || null,
            estado: document.querySelector('#editProductPanel input[name="editProductStatus"]:checked')?.value,
            condicion: document.querySelector('#editProductPanel input[name="editProductCondition"]:checked')?.value
        };

        console.log("Datos del formulario:", formData);

        // Verificar campos requeridos
        const requiredFields = [
            { id: 'editProductName', label: 'Nombre del producto' },
            { id: 'editProductDescription', label: 'Descripción' },
            { id: 'editProductPrice', label: 'Precio' },
            { id: 'editProductOriginalPrice', label: 'Precio Original' },
            { id: 'editProductStock', label: 'Stock' },
            { id: 'editProductCategory', label: 'Categoría' },
            { id: 'editProductSubcategory', label: 'Subcategoría' }
        ];

        for (const field of requiredFields) {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
                throw new Error(`El campo ${field.label} es obligatorio`);
            }
        }

        // Validar que la categoría tenga un valor
        if (!formData.categoria_id) {
            throw new Error("Debe seleccionar una categoría");
        }

        // Validar que la subcategoría tenga un valor
        if (!formData.subcategoria_id) {
            throw new Error("Debe seleccionar una subcategoría");
        }

        // Validar estado y condición
        if (!formData.estado) {
            throw new Error("Debe seleccionar un estado para el producto");
        }
        if (!formData.condicion) {
            throw new Error("Debe seleccionar una condición para el producto");
        }

        // Mostrar indicador de carga
        const saveBtn = document.getElementById('saveEditBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveBtn.disabled = true;

        // Realizar la petición al servidor
        const response = await fetch('../public/API/productos/update_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(formData),
            credentials: 'include'
        });

        const data = await response.json();

        if (!response.ok) {
            // Verificar si es un error de subcategoría obligatoria
            if (response.status === 400 && data.message && data.message.includes('subcategoria_id')) {
                throw {
                    message: 'La subcategoría es obligatoria',
                    errors: ['Debe seleccionar una subcategoría para el producto']
                };
            } else {
                throw {
                    message: 'Error al guardar el producto',
                    errors: data.errors || [data.message]
                };
            }
        }

        // Si todo sale bien, mostrar notificación de éxito
        console.log("Producto actualizado correctamente:", data);

        // Mostrar mensaje de éxito
        console.log("Mostrando modal de éxito..."); // <-- Add this line
        showSuccessModal("¡Producto Actualizado Exitosamente!", "Los cambios han sido guardados correctamente.");

        // Actualizar la tabla de productos con los datos completos del servidor
        if (data.producto) {
            console.log("Actualizando tabla con datos completos del servidor");
            refreshProductsTable(data.producto);
        } else {
            console.log("Actualizando tabla con datos del formulario");
            refreshProductsTable(formData);
        }

        // Cerrar el panel de edición después de 2.5 segundos (un poco más que el modal)
        setTimeout(() => {
            closeEditPanel();

            // Recargar la lista completa de productos solo si es necesario
            // Esto se hará automáticamente al cerrar el panel si hay cambios pendientes
        }, 2500);

    } catch (error) {
        console.error('Error al guardar producto:', error);

        // Eliminar cualquier notificación de error existente
        const existingNotifications = document.querySelectorAll('.notification.notification-error');
        existingNotifications.forEach(notification => {
            notification.remove();
        });

        // Mostrar errores en el modal
        if (error.errors) {
            showErrorModal(error.errors);
        } else {
            showErrorModal(error.message || 'Error desconocido al guardar el producto');
        }
    } finally {
        // Restaurar el botón de guardar
        const saveBtn = document.getElementById('saveEditBtn');
        if (saveBtn) {
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Guardar cambios';
            saveBtn.disabled = false;
        }
    }
}

// Función para mostrar el modal de éxito
function showSuccessModal(title, message) {
    console.log("Mostrando modal de éxito", { title, message });

    // Crear modal si no existe
    let successModal = document.getElementById('successModal');

    if (!successModal) {
        successModal = document.createElement('div');
        successModal.id = 'successModal';
        successModal.className = 'success-modal';

        // Usar la estructura correcta para el modal
        successModal.innerHTML = `
            <div class="modal-content">
                <div class="success-modal-header">
                    <i class="fas fa-check-circle"></i>
                    <h3 id="successTitle"></h3>
                </div>
                <div class="success-modal-body">
                    <p id="successMessage"></p>
                </div>
                <div class="success-modal-footer">
                    <button class="btn btn-primary" id="closeSuccessModal">
                        <i class="fas fa-times"></i> Cerrar
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(successModal);

        // Event listener para cerrar
        document.getElementById('closeSuccessModal').addEventListener('click', () => {
            successModal.classList.remove('show');
        });
    }

    // Actualizar contenido
    document.getElementById('successTitle').textContent = title;
    document.getElementById('successMessage').textContent = message;

    // Mostrar modal
    successModal.classList.add('show');

    // Auto cerrar después de 2 segundos
    setTimeout(() => {
        successModal.classList.remove('show');
    }, 2000);
}

// Función para actualizar la tabla de productos dinámicamente
function refreshProductsTable(updatedProduct) {
    const productTable = document.querySelector('.admin-table tbody');
    if (!productTable) return;

    const productRow = document.querySelector(`tr[data-product-id="${updatedProduct.id}"]`);

    if (productRow) {
        // Obtener el nombre de la categoría del select
        const categorySelect = document.getElementById('editProductCategory');
        const categoryName = categorySelect ?
            categorySelect.options[categorySelect.selectedIndex]?.textContent :
            'Sin categoría';

        // Formatear el precio con precio original si existe
        let formattedPrice = '';
        if (updatedProduct.precio) {
            // Formatear precio actual
            const currentPrice = `$${Number(updatedProduct.precio).toLocaleString('es-CL')}`;

            // Si hay precio original, mostrarlo tachado debajo del precio actual
            if (updatedProduct.precio_original && Number(updatedProduct.precio_original) > Number(updatedProduct.precio)) {
                const originalPrice = `$${Number(updatedProduct.precio_original).toLocaleString('es-CL')}`;
                formattedPrice = `<div class="price-container">
                    <span class="current-price">${currentPrice}</span>
                    <span class="original-price">${originalPrice}</span>
                </div>`;
            } else {
                formattedPrice = currentPrice;
            }
        }

        // Preparar la descripción truncada y completa para el tooltip
        const descripcionCompleta = updatedProduct.descripcion || '';
        const descripcionCorta = updatedProduct.descripcion_corta ||
            (descripcionCompleta ? descripcionCompleta.substring(0, 100) + '...' : '');

        productRow.innerHTML = `
            <td>${updatedProduct.id}</td>
            <td class="product-image-cell">
                <img src="${updatedProduct.imagen_principal || '../images/placeholder.png'}"
                     alt="${updatedProduct.nombre}"
                     class="product-image-preview">
            </td>
            <td class="product-name-cell">
                <div class="product-name">${updatedProduct.nombre}</div>
            </td>
            <td class="description-cell"
                data-tooltip="${descripcionCompleta.replace(/"/g, '&quot;')}"
                title="${descripcionCompleta.replace(/"/g, '&quot;')}">
                ${descripcionCorta}
            </td>
            <td class="product-price formatted">${formattedPrice}</td>
            <td>${updatedProduct.stock}</td>
            <td><span class="status-badge status-${updatedProduct.estado.toLowerCase()}">${updatedProduct.estado}</span></td>
            <td><span class="condition-badge condition-${updatedProduct.condicion.toLowerCase()}">${updatedProduct.condicion}</span></td>
            <td>${categoryName}</td>
            <td>${updatedProduct.subcategoria_nombre || 'Sin subcategoría'}</td>
            <td>${updatedProduct.sku || '-'}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${updatedProduct.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${updatedProduct.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        // Destacar la fila actualizada
        productRow.classList.add('updated-row');
        setTimeout(() => {
            productRow.classList.remove('updated-row');
        }, 3000);
    } else {
        console.log("No se encontró la fila del producto en la tabla para actualizar");
        // No recargar toda la tabla para evitar doble actualización
        // La tabla se actualizará cuando se cierre el panel de edición
    }
}

// Función para adjuntar listeners a los botones de acción
function attachActionButtonListeners(container) {
    container.querySelectorAll('.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id') || this.getAttribute('data-id');
            if (productId) {
                // Usar openEditPanel en lugar de openEditProduct
                openEditPanel(productId);
            } else {
                console.error('ID de producto no encontrado en el botón de editar');
            }
        });
    });

    container.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id') || this.getAttribute('data-id');
            if (productId) {
                // Mostrar modal de confirmación para eliminar
                showDeleteConfirmModal(productId);
            } else {
                console.error('ID de producto no encontrado en el botón de eliminar');
            }
        });
    });
}

// Función para mostrar el modal de confirmación de eliminación
function showDeleteConfirmModal(productId) {
    const modal = document.getElementById('deleteConfirmModal');
    const confirmBtn = document.getElementById('confirmDelete');
    const cancelBtn = document.getElementById('cancelDelete');
    const closeBtn = document.getElementById('closeDeleteModal');

    // Establecer el ID del producto a eliminar
    if (modal) {
        modal.setAttribute('data-product-id', productId);

        // Mostrar el modal
        modal.style.display = 'block';

        // Configurar botones
        if (confirmBtn) {
            // Eliminar eventos previos
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

            // Añadir nuevo evento
            newConfirmBtn.addEventListener('click', function() {
                deleteProduct(productId);
                modal.style.display = 'none';
            });
        }

        if (cancelBtn) {
            cancelBtn.onclick = function() {
                modal.style.display = 'none';
            };
        }

        if (closeBtn) {
            closeBtn.onclick = function() {
                modal.style.display = 'none';
            };
        }

        // Cerrar al hacer clic fuera
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };
    }
}

// Función para eliminar un producto
async function deleteProduct(productId) {
    try {
        // Mostrar indicador de carga
        showNotification("Eliminando producto...", "info");

        const response = await fetch('../public/API/productos/delete_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ id: productId }),
            credentials: 'include'
        });

        const data = await response.json();

        if (data.success) {
            showNotification("Producto eliminado correctamente", "success");
            // Recargar la tabla de productos
            loadProducts();
        } else {
            throw new Error(data.message || "Error al eliminar el producto");
        }
    } catch (error) {
        console.error("Error al eliminar producto:", error);
        showNotification("Error: " + error.message, "error");
    }
}

// Exportar funciones necesarias
export {
    setupEditProductPanel,
    openEditPanel,
    closeEditPanel,
    resetProductForm,
    showErrorModal,
    showSuccessModal,
    saveProductChanges,
    refreshProductsTable,
    showDeleteConfirmModal  // Exportar la nueva función
};
