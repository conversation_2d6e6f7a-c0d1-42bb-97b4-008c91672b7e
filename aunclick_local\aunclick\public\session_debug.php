<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

session_start();

require_once '../config/session_config.php';

function checkSessionSettings() {
    $settings = [
        'session.cookie_httponly' => ini_get('session.cookie_httponly'),
        'session.cookie_secure' => ini_get('session.cookie_secure'),
        'session.cookie_samesite' => ini_get('session.cookie_samesite'),
        'session.cookie_lifetime' => ini_get('session.cookie_lifetime'),
        'session.gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
        'session.name' => ini_get('session.name'),
        'session.cookie_path' => ini_get('session.cookie_path'),
        'session.cookie_domain' => ini_get('session.cookie_domain'),
        'session.use_strict_mode' => ini_get('session.use_strict_mode'),
        'session.use_cookies' => ini_get('session.use_cookies'),
        'session.use_only_cookies' => ini_get('session.use_only_cookies'),
    ];
    return $settings;
}

function getBrowserInfo() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $browser = 'Unknown';
    $version = 'Unknown';

    if (preg_match('/MSIE|Trident/i', $userAgent)) {
        $browser = 'Internet Explorer';
    } elseif (preg_match('/Firefox/i', $userAgent)) {
        $browser = 'Firefox';
    } elseif (preg_match('/Chrome/i', $userAgent)) {
        $browser = 'Chrome';
    } elseif (preg_match('/Safari/i', $userAgent)) {
        $browser = 'Safari';
    } elseif (preg_match('/Opera|OPR/i', $userAgent)) {
        $browser = 'Opera';
    } elseif (preg_match('/Edge/i', $userAgent)) {
        $browser = 'Edge';
    } elseif (preg_match('/Brave/i', $userAgent)) {
        $browser = 'Brave';
    }

    return [
        'browser' => $browser,
        'userAgent' => $userAgent,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'protocol' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'HTTPS' : 'HTTP'
    ];
}

function checkCookieSupport() {
    if (!isset($_COOKIE['test_cookie'])) {
        setcookie('test_cookie', 'test', time() + 3600, '/');
        return 'Cookie test initiated';
    }
    return 'Cookies are supported';
}

function checkSessionDirectoryPermissions() {
    $sessionPath = session_save_path();
    $results = [];
    
    // Verificar existencia y permisos del directorio
    $results['directory_exists'] = is_dir($sessionPath);
    $results['directory_writable'] = is_writable($sessionPath);
    $results['directory_permissions'] = substr(sprintf('%o', fileperms($sessionPath)), -4);
    
    return $results;
}

function checkSessionConfiguration() {
    return [
        'save_handler' => ini_get('session.save_handler'),
        'save_path' => ini_get('session.save_path'),
        'name' => ini_get('session.name'),
        'gc_maxlifetime' => ini_get('session.gc_maxlifetime'),
        'cookie_lifetime' => ini_get('session.cookie_lifetime'),
        'cookie_path' => ini_get('session.cookie_path'),
        'cookie_domain' => ini_get('session.cookie_domain'),
        'cookie_secure' => ini_get('session.cookie_secure'),
        'cookie_httponly' => ini_get('session.cookie_httponly'),
        'use_strict_mode' => ini_get('session.use_strict_mode'),
        'use_cookies' => ini_get('session.use_cookies'),
        'use_only_cookies' => ini_get('session.use_only_cookies')
    ];
}

function testSessionWrite() {
    $testKey = 'test_' . time();
    $testValue = 'test_value_' . time();
    $_SESSION[$testKey] = $testValue;
    
    return [
        'write_successful' => isset($_SESSION[$testKey]) && $_SESSION[$testKey] === $testValue,
        'session_id' => session_id(),
        'test_key' => $testKey,
        'test_value' => $testValue
    ];
}

// Recopilar información
$sessionSettings = checkSessionSettings();
$browserInfo = getBrowserInfo();
$cookieStatus = checkCookieSupport();
$sessionStatus = [
    'session_id' => session_id(),
    'session_status' => session_status(),
    'session_data' => $_SESSION,
];

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico de Sesión</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin-bottom: 20px; }
        .debug-table { width: 100%; border-collapse: collapse; }
        .debug-table th, .debug-table td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: left; 
        }
        .debug-table th { background-color: #f4f4f4; }
        .status-ok { color: green; }
        .status-warning { color: orange; }
        .status-error { color: red; }
    </style>
</head>
<body>
    <h1>Diagnóstico de Sesión</h1>

    <div class="debug-section">
        <h2>Información del Navegador</h2>
        <table class="debug-table">
            <tr><th>Navegador</th><td><?php echo $browserInfo['browser']; ?></td></tr>
            <tr><th>User Agent</th><td><?php echo $browserInfo['userAgent']; ?></td></tr>
            <tr><th>IP</th><td><?php echo $browserInfo['ip']; ?></td></tr>
            <tr><th>Protocolo</th><td><?php echo $browserInfo['protocol']; ?></td></tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>Estado de la Sesión</h2>
        <table class="debug-table">
            <tr><th>ID de Sesión</th><td><?php echo $sessionStatus['session_id']; ?></td></tr>
            <tr><th>Estado de Sesión</th><td><?php echo $sessionStatus['session_status']; ?></td></tr>
            <tr><th>Soporte de Cookies</th><td><?php echo $cookieStatus; ?></td></tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>Configuración de Sesión</h2>
        <table class="debug-table">
            <?php foreach ($sessionSettings as $key => $value): ?>
            <tr>
                <th><?php echo htmlspecialchars($key); ?></th>
                <td><?php echo htmlspecialchars($value); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <div class="debug-section">
        <h2>Datos de Sesión</h2>
        <table class="debug-table">
            <?php foreach ($_SESSION as $key => $value): ?>
            <tr>
                <th><?php echo htmlspecialchars($key); ?></th>
                <td><?php echo is_array($value) ? print_r($value, true) : htmlspecialchars($value); ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <script>
    // Verificar características del navegador
    const browserFeatures = {
        cookies: navigator.cookieEnabled,
        localStorage: !!window.localStorage,
        sessionStorage: !!window.sessionStorage,
    };

    // Crear y mostrar la tabla de características del navegador
    const featureSection = document.createElement('div');
    featureSection.className = 'debug-section';
    featureSection.innerHTML = `
        <h2>Características del Navegador</h2>
        <table class="debug-table">
            <tr><th>Cookies Habilitadas</th><td>${browserFeatures.cookies}</td></tr>
            <tr><th>LocalStorage Disponible</th><td>${browserFeatures.localStorage}</td></tr>
            <tr><th>SessionStorage Disponible</th><td>${browserFeatures.sessionStorage}</td></tr>
        </table>
    `;
    document.body.appendChild(featureSection);
    </script>

    <div class="debug-section">
        <h2>Permisos del Directorio de Sesiones</h2>
        <?php
        $dirPerms = checkSessionDirectoryPermissions();
        foreach ($dirPerms as $key => $value):
            $status = $value ? 'success' : 'error';
        ?>
            <div>
                <span class="status <?php echo $status; ?>">
                    <?php echo ucwords(str_replace('_', ' ', $key)); ?>: <?php echo $value; ?>
                </span>
            </div>
        <?php endforeach; ?>
    </div>

    <div class="debug-section">
        <h2>Configuración de Sesiones</h2>
        <table class="debug-table">
        <?php
        $config = checkSessionConfiguration();
        foreach ($config as $key => $value):
        ?>
            <tr>
                <td><?php echo $key; ?></td>
                <td><?php echo $value; ?></td>
            </tr>
        <?php endforeach; ?>
        </table>
    </div>

    <div class="debug-section">
        <h2>Prueba de Escritura de Sesión</h2>
        <?php
        $sessionManager = SessionManager::getInstance();
        $sessionManager->initializeSession();
        $writeTest = testSessionWrite();
        ?>
        <table class="debug-table">
            <tr>
                <td>Estado de Escritura</td>
                <td>
                    <span class="status <?php echo $writeTest['write_successful'] ? 'success' : 'error'; ?>">
                        <?php echo $writeTest['write_successful'] ? 'ÉXITO' : 'FALLO'; ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td>ID de Sesión</td>
                <td><?php echo $writeTest['session_id']; ?></td>
            </tr>
            <tr>
                <td>Clave de Prueba</td>
                <td><?php echo $writeTest['test_key']; ?></td>
            </tr>
            <tr>
                <td>Valor de Prueba</td>
                <td><?php echo $writeTest['test_value']; ?></td>
            </tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>Cookies Actuales</h2>
        <div class="debug-data">
        <?php print_r($_COOKIE); ?>
        </div>
    </div>

    <div class="debug-section">
        <h2>Variables de Sesión</h2>
        <div class="debug-data">
        <?php print_r($_SESSION); ?>
        </div>
    </div>

    <div class="debug-section">
        <h2>Headers HTTP</h2>
        <div class="debug-data">
        <?php 
        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) === 'HTTP_') {
                $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($key, 5)))))] = $value;
            }
        }
        print_r($headers);
        ?>
        </div>
    </div>

    <script>
    // Función para copiar datos al portapapeles
    function copyToClipboard(element) {
        const text = element.previousElementSibling.innerText;
        navigator.clipboard.writeText(text).then(() => {
            alert('Datos copiados al portapapeles');
        });
    }

    // Agregar botones de copia a todas las secciones de datos
    document.querySelectorAll('.debug-data').forEach(el => {
        const button = document.createElement('button');
        button.innerText = 'Copiar Datos';
        button.className = 'copy-button';
        button.onclick = function() { copyToClipboard(this); };
        el.insertAdjacentElement('afterend', button);
    });
    </script>
</body>
</html>

<?php
require_once '../config/session_config.php';

header('Content-Type: text/plain');

echo "=== Información de Sesión ===\n\n";

echo "Session ID: " . session_id() . "\n";
echo "Session Name: " . session_name() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "Session Save Path: " . session_save_path() . "\n\n";

echo "=== Parámetros de Cookie ===\n";
print_r(session_get_cookie_params());
echo "\n";

echo "=== Variables de Sesión ===\n";
print_r($_SESSION);
echo "\n";

echo "=== Cookies ===\n";
print_r($_COOKIE);
echo "\n";

echo "=== Headers de Respuesta ===\n";
foreach (headers_list() as $header) {
    echo $header . "\n";
}
echo "\n";

echo "=== Variables de Servidor ===\n";
$serverVars = array(
    'HTTP_HOST',
    'HTTP_USER_AGENT',
    'HTTP_COOKIE',
    'REMOTE_ADDR',
    'REQUEST_URI',
    'SCRIPT_NAME',
    'PHP_SELF'
);

foreach ($serverVars as $var) {
    echo "$var: " . (isset($_SERVER[$var]) ? $_SERVER[$var] : 'no definido') . "\n";
}

echo "\n=== Archivo de Sesión ===\n";
$sessionFile = session_save_path() . '/sess_' . session_id();
echo "Ruta: $sessionFile\n";
echo "Existe: " . (file_exists($sessionFile) ? 'Sí' : 'No') . "\n";
if (file_exists($sessionFile)) {
    echo "Tamaño: " . filesize($sessionFile) . " bytes\n";
    echo "Permisos: " . substr(sprintf('%o', fileperms($sessionFile)), -4) . "\n";
    echo "Último acceso: " . date('Y-m-d H:i:s', fileatime($sessionFile)) . "\n";
    echo "Última modificación: " . date('Y-m-d H:i:s', filemtime($sessionFile)) . "\n";
}

echo "\n=== Estado del Gestor de Sesiones ===\n";
$sessionManager = SessionManager::getInstance();
echo "Sesión válida: " . ($sessionManager->isValid() ? 'Sí' : 'No') . "\n";
echo "Usuario logueado: " . ($sessionManager->isLoggedIn() ? 'Sí' : 'No') . "\n";
if ($sessionManager->isLoggedIn()) {
    $user = $sessionManager->getCurrentUser();
    echo "Usuario actual: " . print_r($user, true) . "\n";
}