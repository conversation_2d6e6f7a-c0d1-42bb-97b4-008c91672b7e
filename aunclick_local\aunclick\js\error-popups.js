/**
 * error-popups.js
 * Script para manejar los pop-ups de error en el formulario de registro
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando manejador de pop-ups de error');

    // Exponer funciones globalmente
    window.showErrorPopup = showErrorPopup;
    window.showWelcomePopup = showWelcomePopup;

    /**
     * Muestra un popup con un mensaje de error
     * @param {string} title - Título del popup
     * @param {string} message - Mensaje a mostrar
     * @param {Array} fields - Lista de campos con error (opcional)
     */
    function showErrorPopup(title, message, fields = []) {
        // Remover cualquier popup existente
        const existingPopups = document.querySelectorAll('.error-popup-overlay');
        existingPopups.forEach(popup => {
            document.body.removeChild(popup);
        });

        // Crear el popup
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'popup-overlay error-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'error-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '20px';
        popupContent.style.borderRadius = '8px';
        popupContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        popupContent.style.maxWidth = '400px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '10px';
        closeButton.style.right = '10px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '24px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#6a1b9a'; // Color morado

        const titleElement = document.createElement('h3');
        titleElement.textContent = title;
        titleElement.style.marginTop = '0';
        titleElement.style.marginBottom = '15px';
        titleElement.style.fontSize = '18px';
        titleElement.style.color = '#6a1b9a'; // Color morado
        titleElement.style.borderBottom = '1px solid #eee';
        titleElement.style.paddingBottom = '10px';

        const messageElement = document.createElement('p');
        messageElement.textContent = message;
        messageElement.style.fontSize = '14px';
        messageElement.style.margin = '15px 0';
        messageElement.style.color = '#333';

        popupContent.appendChild(closeButton);
        popupContent.appendChild(titleElement);
        popupContent.appendChild(messageElement);

        // Si hay campos con error, mostrarlos
        if (fields && fields.length > 0) {
            const fieldsList = document.createElement('ul');
            fieldsList.style.textAlign = 'left';
            fieldsList.style.paddingLeft = '20px';
            fieldsList.style.margin = '15px 0';
            fieldsList.style.color = '#555';

            fields.forEach(field => {
                const item = document.createElement('li');
                item.textContent = field;
                item.style.fontSize = '13px';
                item.style.margin = '5px 0';
                fieldsList.appendChild(item);
            });

            popupContent.appendChild(fieldsList);
        }

        // Botón de aceptar
        const acceptButton = document.createElement('button');
        acceptButton.textContent = 'Aceptar';
        acceptButton.style.backgroundColor = '#6a1b9a';
        acceptButton.style.color = 'white';
        acceptButton.style.border = 'none';
        acceptButton.style.padding = '8px 20px';
        acceptButton.style.borderRadius = '4px';
        acceptButton.style.cursor = 'pointer';
        acceptButton.style.fontSize = '14px';
        acceptButton.style.marginTop = '10px';
        acceptButton.style.transition = 'background-color 0.2s';

        // Efecto hover
        acceptButton.onmouseover = function() {
            this.style.backgroundColor = '#8e24aa';
        };
        acceptButton.onmouseout = function() {
            this.style.backgroundColor = '#6a1b9a';
        };

        popupContent.appendChild(acceptButton);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Función para cerrar el popup
        const closePopup = () => {
            if (document.body.contains(popupOverlay)) {
                document.body.removeChild(popupOverlay);
            }
        };

        // Configurar eventos para cerrar el popup
        closeButton.onclick = closePopup;
        acceptButton.onclick = closePopup;
        popupOverlay.onclick = (event) => {
            if (event.target === popupOverlay) {
                closePopup();
            }
        };
    }

    /**
     * Muestra un popup de bienvenida
     */
    function showWelcomePopup() {
        // Crear un popup con el estilo de los otros popups
        const popupOverlay = document.createElement('div');
        popupOverlay.className = 'popup-overlay welcome-popup-overlay';
        popupOverlay.style.position = 'fixed';
        popupOverlay.style.top = '0';
        popupOverlay.style.left = '0';
        popupOverlay.style.width = '100%';
        popupOverlay.style.height = '100%';
        popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        popupOverlay.style.display = 'flex';
        popupOverlay.style.justifyContent = 'center';
        popupOverlay.style.alignItems = 'center';
        popupOverlay.style.zIndex = '9999';

        const popupContent = document.createElement('div');
        popupContent.className = 'welcome-popup-content';
        popupContent.style.backgroundColor = 'white';
        popupContent.style.padding = '20px';
        popupContent.style.borderRadius = '8px';
        popupContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        popupContent.style.maxWidth = '400px';
        popupContent.style.width = '90%';
        popupContent.style.position = 'relative';
        popupContent.style.textAlign = 'center';

        const title = document.createElement('h3');
        title.textContent = '¡Bienvenido a Villarrica a un CLICK!';
        title.style.marginTop = '0';
        title.style.marginBottom = '15px';
        title.style.fontSize = '18px';
        title.style.color = '#6a1b9a'; // Color morado
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '10px';

        const message = document.createElement('p');
        message.textContent = 'Gracias por registrarte con el plan gratuito. Tu negocio ya está disponible en nuestra plataforma.';
        message.style.fontSize = '14px';
        message.style.margin = '15px 0';
        message.style.color = '#333';

        const acceptButton = document.createElement('button');
        acceptButton.textContent = 'Aceptar';
        acceptButton.style.backgroundColor = '#6a1b9a';
        acceptButton.style.color = 'white';
        acceptButton.style.border = 'none';
        acceptButton.style.padding = '8px 20px';
        acceptButton.style.borderRadius = '4px';
        acceptButton.style.cursor = 'pointer';
        acceptButton.style.fontSize = '14px';
        acceptButton.style.marginTop = '10px';
        acceptButton.style.transition = 'background-color 0.2s';

        // Efecto hover
        acceptButton.onmouseover = function() {
            this.style.backgroundColor = '#8e24aa';
        };
        acceptButton.onmouseout = function() {
            this.style.backgroundColor = '#6a1b9a';
        };

        popupContent.appendChild(title);
        popupContent.appendChild(message);
        popupContent.appendChild(acceptButton);
        popupOverlay.appendChild(popupContent);

        document.body.appendChild(popupOverlay);

        // Función para cerrar el popup y enviar el formulario
        const closePopup = () => {
            if (document.body.contains(popupOverlay)) {
                document.body.removeChild(popupOverlay);
                document.getElementById('registerForm').submit();
            }
        };

        // Configurar eventos para cerrar el popup
        acceptButton.onclick = closePopup;
    }
});
