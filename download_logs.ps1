# Script para obtener las últimas 30 líneas de los logs de errores de Aunclick
# Nombre: get_aunclick_error_logs.ps1

# Configuración
$remoteHost = "servidor-linux"  # Nombre del host configurado en SSH
$remoteUser = "root"            # Usuario para la conexión SSH
$logFiles = @(
    "/var/log/apache2/error.log",
    "/var/log/php8.1-fpm.log",
    "/var/log/mysql/error.log"
)
$tempRemoteFile = "/tmp/consolidated_logs.txt"
$localOutputFile = ".\aunclick_error_logs.txt"

# Crear comando para extraer las últimas 30 líneas de cada archivo y consolidarlas
$remoteCommand = "echo '=== APACHE ERROR LOG ===\n' > $tempRemoteFile && "
$remoteCommand += "tail -n 30 $($logFiles[0]) >> $tempRemoteFile && "
$remoteCommand += "echo '\n\n=== PHP ERROR LOG ===\n' >> $tempRemoteFile && "
$remoteCommand += "tail -n 30 $($logFiles[1]) >> $tempRemoteFile && "
$remoteCommand += "echo '\n\n=== MYSQL ERROR LOG ===\n' >> $tempRemoteFile && "
$remoteCommand += "tail -n 30 $($logFiles[2]) >> $tempRemoteFile"

# Ejecutar el comando remoto para crear el archivo consolidado
Write-Host "Consolidando logs en el servidor remoto..."
ssh $remoteHost $remoteCommand

# Descargar el archivo consolidado usando SCP
Write-Host "Descargando archivo consolidado..."
scp "${remoteHost}:${tempRemoteFile}" $localOutputFile

# Limpiar el archivo temporal en el servidor remoto
Write-Host "Limpiando archivo temporal en el servidor..."
ssh $remoteHost "rm $tempRemoteFile"

Write-Host "Proceso completado. Los logs consolidados están en: $localOutputFile"