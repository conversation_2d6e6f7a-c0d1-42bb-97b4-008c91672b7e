/**
 * Nueva solución para el sidebar en modo responsive
 * Este script reemplaza completamente la funcionalidad del sidebar en modo responsive
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando nueva solución de sidebar...');

    // Elementos principales
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');
    const body = document.body;

    if (!sidebar || !toggleBtn) {
        console.error('No se encontró el sidebar o el botón de toggle');
        return;
    }

    // Crear overlay
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay-new';
    overlay.className = 'sidebar-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '1040';
    overlay.style.display = 'none';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.3s ease';
    document.body.appendChild(overlay);

    // Función para mostrar el sidebar
    function showSidebar() {
        console.log('Mostrando sidebar...');
        // Eliminar cualquier clase existente que pueda causar conflictos
        sidebar.classList.remove('collapsed');
        
        // Agregar clase expanded
        sidebar.classList.add('expanded');

        // Aplicar estilos directamente para garantizar que se muestre correctamente
        sidebar.style.width = '250px';
        sidebar.style.transform = 'translateX(0)';
        sidebar.style.visibility = 'visible';
        sidebar.style.opacity = '1';

        // Mostrar elementos del sidebar
        const elementsToShow = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
        elementsToShow.forEach(el => {
            el.style.display = 'block';
            el.style.opacity = '1';
            el.style.visibility = 'visible';
        });

        // Mostrar overlay
        overlay.style.display = 'block';
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);

        // Cambiar ícono
        const toggleIcon = document.getElementById('toggle-icon');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-times';
        }
    }

    // Función para ocultar el sidebar
    function hideSidebar() {
        console.log('Ocultando sidebar...');
        // Eliminar clase expanded
        sidebar.classList.remove('expanded');
        
        // Agregar clase collapsed
        sidebar.classList.add('collapsed');

        // Aplicar estilos directamente para garantizar que se oculte correctamente
        sidebar.style.width = '70px';

        // Ocultar elementos del sidebar
        const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
        elementsToHide.forEach(el => {
            el.style.display = 'none';
        });

        // Ocultar overlay
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);

        // Cambiar ícono
        const toggleIcon = document.getElementById('toggle-icon');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-bars';
        }
    }

    // Manejar clic en el botón de toggle
    toggleBtn.addEventListener('click', function(e) {
        console.log('Toggle button clicked');
        e.preventDefault();
        e.stopPropagation();

        // Detener la propagación del evento para evitar conflictos con otros manejadores
        e.stopImmediatePropagation();

        if (window.innerWidth < 992) {
            // En modo responsive
            if (sidebar.classList.contains('expanded')) {
                hideSidebar();
            } else {
                showSidebar();
            }
        } else {
            // En modo desktop
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
        }

        // Devolver false para prevenir el comportamiento por defecto
        return false;
    }, true);

    // Asegurarnos de que el evento de clic sea capturado primero
    toggleBtn.addEventListener('touchstart', function(e) {
        e.stopPropagation();
    }, true);

    // Manejar clic en el overlay
    overlay.addEventListener('click', function() {
        hideSidebar();
    });

    // Manejar cambios de tamaño de ventana
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            // En desktop, restaurar estado del sidebar según localStorage
            const savedState = localStorage.getItem('asideCollapsed');
            if (savedState === 'true') {
                sidebar.classList.add('collapsed');
                sidebar.classList.remove('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                sidebar.classList.add('expanded');
            }

            // Ocultar overlay
            overlay.style.display = 'none';
            overlay.style.opacity = '0';
        } else {
            // En móvil, si no está expandido, asegurarse de que esté colapsado
            if (!sidebar.classList.contains('expanded')) {
                hideSidebar();
            }
        }
    });

    // Inicializar estado del sidebar
    if (window.innerWidth < 992) {
        // En móvil, asegurar que el sidebar esté colapsado inicialmente
        hideSidebar();

        // Forzar estilos críticos para el sidebar
        sidebar.style.position = 'fixed';
        sidebar.style.top = '0';
        sidebar.style.left = '0';
        sidebar.style.height = '100vh';
        sidebar.style.zIndex = '9999';
        sidebar.style.transition = 'width 0.3s ease';
        sidebar.style.overflowX = 'hidden';
        sidebar.style.overflowY = 'auto';
        sidebar.style.visibility = 'visible';
        sidebar.style.opacity = '1';
        sidebar.style.backgroundColor = '#6a1b9a';
    } else {
        // En desktop, restaurar estado según localStorage
        const savedState = localStorage.getItem('asideCollapsed');
        if (savedState === 'true') {
            sidebar.classList.add('collapsed');
            sidebar.classList.remove('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            sidebar.classList.add('expanded');
        }
    }

    console.log('Nueva solución de sidebar inicializada correctamente');
});
