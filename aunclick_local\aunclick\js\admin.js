
        var visitsChart;
        var ctxVisits = document.getElementById('visitsChart').getContext('2d');

        // Configuración de colores estilo Azure
        Chart.defaults.color = '#323130';
        Chart.defaults.font.family = "'Segoe UI', sans-serif";

        // Datos para cada período
        const periodData = {
            daily: {
                chartData: [120, 150, 180, 200, 170, 160, 190],
                labels: ['Lun', 'Mar', 'Mi<PERSON>', '<PERSON><PERSON>', 'Vie', 'Sáb', 'Dom'],
                kpis: {
                    visits: {
                        value: '1,170',
                        trend: '+12.5%'
                    },
                    duration: {
                        value: '2:45',
                        trend: '****%'
                    },
                    bounceRate: {
                        value: '35%',
                        trend: '-5.1%'
                    },
                    pagesPerVisit: {
                        value: '4',
                        trend: '****%'
                    },
                    activeUsers: {
                        value: '850',
                        trend: '+15.3%'
                    },
                    conversions: {
                        value: '25%',
                        trend: '+4.2%'
                    }
                }
            },
            weekly: {
                chartData: [800, 900, 1000, 1100, 950, 1050, 1150],
                labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Sem 5', 'Sem 6', 'Sem 7'],
                kpis: {
                    visits: {
                        value: '6,950',
                        trend: '+15.2%'
                    },
                    duration: {
                        value: '3:15',
                        trend: '+10.5%'
                    },
                    bounceRate: {
                        value: '38%',
                        trend: '-3.2%'
                    },
                    pagesPerVisit: {
                        value: '5',
                        trend: '+5.1%'
                    },
                    activeUsers: {
                        value: '4,200',
                        trend: '+12.8%'
                    },
                    conversions: {
                        value: '28%',
                        trend: '+5.5%'
                    }
                }
            },
            monthly: {
                chartData: [3000, 3200, 3100, 3300, 3400, 3500, 3600],
                labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul'],
                kpis: {
                    visits: {
                        value: '23,100',
                        trend: '+18.7%'
                    },
                    duration: {
                        value: '3:45',
                        trend: '+12.8%'
                    },
                    bounceRate: {
                        value: '40%',
                        trend: '-2.5%'
                    },
                    pagesPerVisit: {
                        value: '6',
                        trend: '+7.2%'
                    },
                    activeUsers: {
                        value: '15,300',
                        trend: '+18.1%'
                    },
                    conversions: {
                        value: '30%',
                        trend: '+6.8%'
                    }
                }
            }
        };

        function updateKPIs(period) {
            const data = periodData[period].kpis;
            
            // Actualizar visitas totales
            document.querySelector('.kpi-card:nth-child(2) .indicator').textContent = data.visits.value;
            document.querySelector('.kpi-card:nth-child(2) .kpi-trend span').textContent = data.visits.trend + ' vs prev. period';
            
            // Actualizar duración promedio
            document.querySelector('.kpi-card:nth-child(3) .indicator').textContent = data.duration.value;
            document.querySelector('.kpi-card:nth-child(3) .kpi-trend span').textContent = data.duration.trend + ' vs prev. period';
            
            // Actualizar tasa de rebote
            document.querySelector('.kpi-card:nth-child(4) .indicator').textContent = data.bounceRate.value;
            document.querySelector('.kpi-card:nth-child(4) .kpi-trend span').textContent = data.bounceRate.trend + ' vs prev. period';
            
            // Actualizar páginas por visita
            document.querySelector('.kpi-card:nth-child(5) .indicator').textContent = data.pagesPerVisit.value;
            document.querySelector('.kpi-card:nth-child(5) .kpi-trend span').textContent = data.pagesPerVisit.trend + ' vs prev. period';

            // Actualizar usuarios activos
            document.querySelector('.kpi-card:nth-child(6) .indicator').textContent = data.activeUsers.value;
            document.querySelector('.kpi-card:nth-child(6) .kpi-trend span').textContent = data.activeUsers.trend + ' vs prev. period';

            // Actualizar conversiones
            document.querySelector('.kpi-card:nth-child(7) .indicator').textContent = data.conversions.value;
            document.querySelector('.kpi-card:nth-child(7) .kpi-trend span').textContent = data.conversions.trend + ' vs prev. period';

            // Actualizar clases de tendencia
            document.querySelectorAll('.kpi-trend').forEach(trend => {
                const trendValue = trend.querySelector('span').textContent;
                if (trendValue.startsWith('+')) {
                    trend.classList.remove('negative');
                    trend.classList.add('positive');
                    trend.querySelector('i').className = 'fas fa-arrow-up';
                } else {
                    trend.classList.remove('positive');
                    trend.classList.add('negative');
                    trend.querySelector('i').className = 'fas fa-arrow-down';
                }
            });
        }

        function updateChart(period) {
            // Actualizar estado activo de los botones
            document.querySelectorAll('.filters button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodInfo = periodData[period];

            if (visitsChart) {
                visitsChart.destroy();
            }

            visitsChart = new Chart(ctxVisits, {
                type: 'line',
                data: {
                    labels: periodInfo.labels,
                    datasets: [{
                        label: 'Visitas',
                        data: periodInfo.chartData,
                        backgroundColor: 'rgba(0, 120, 212, 0.1)',
                        borderColor: '#0078D4',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#0078D4',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: '#323130',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            padding: 12,
                            displayColors: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                padding: 10,
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                padding: 10
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // Actualizar KPIs
            updateKPIs(period);
        }

        // Inicializar el gráfico con datos diarios
        updateChart('daily');

        // Datos de prueba estáticos
        const productsData = [
            { image: "https://via.placeholder.com/40", name: "Producto A", daily_visits: 123, weekly_visits: 456, monthly_visits: 789 },
            { image: "https://via.placeholder.com/40", name: "Producto B", daily_visits: 234, weekly_visits: 567, monthly_visits: 890 },
            { image: "https://via.placeholder.com/40", name: "Producto C", daily_visits: 345, weekly_visits: 678, monthly_visits: 901 },
            { image: "https://via.placeholder.com/40", name: "Producto D", daily_visits: 456, weekly_visits: 789, monthly_visits: 1011 }
        ];

        // Función para renderizar la tabla de productos
        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            tbody.innerHTML = '';

            productsData.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><img src="${product.image}" alt="${product.name}" style="width:40px; height:40px;"></td>
                    <td>${product.name}</td>
                    <td>${product.daily_visits.toLocaleString()}</td>
                    <td>${product.weekly_visits.toLocaleString()}</td>
                    <td>${product.monthly_visits.toLocaleString()}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Inicializar la tabla de productos
        renderProductsTable();
        
        document.addEventListener('DOMContentLoaded', function() {
            // Datos de prueba estáticos
            const productsData = [
                { image: "https://via.placeholder.com/40", name: "Producto A", daily_visits: 123, weekly_visits: 456, monthly_visits: 789 },
                { image: "https://via.placeholder.com/40", name: "Producto B", daily_visits: 234, weekly_visits: 567, monthly_visits: 890 },
                { image: "https://via.placeholder.com/40", name: "Producto C", daily_visits: 345, weekly_visits: 678, monthly_visits: 901 },
                { image: "https://via.placeholder.com/40", name: "Producto D", daily_visits: 456, weekly_visits: 789, monthly_visits: 1011 }
            ];

            // Función para renderizar la tabla de productos
            function renderProductsTable() {
                const tbody = document.getElementById('productsTableBody');
                tbody.innerHTML = '';

                productsData.forEach(product => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><img src="${product.image}" alt="${product.name}" style="width:40px; height:40px;"></td>
                        <td>${product.name}</td>
                        <td>${product.daily_visits}</td>
                        <td>${product.weekly_visits}</td>
                        <td>${product.monthly_visits}</td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // Inicializar la tabla de productos
            renderProductsTable();
        });

        // Manejo del menú de usuario
        const userMenuTrigger = document.querySelector('.user-menu-trigger');
        const userMenuModal = document.querySelector('.user-menu-modal');

        if (userMenuTrigger && userMenuModal) {
            userMenuTrigger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Click en menú de usuario');
                userMenuModal.classList.toggle('show');
            });

            // Cerrar el menú al hacer clic fuera
            document.addEventListener('click', function(e) {
                if (!userMenuTrigger.contains(e.target) && !userMenuModal.contains(e.target)) {
                    console.log('Cerrando menú de usuario');
                    userMenuModal.classList.remove('show');
                }
            });
        }
   

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Inicializando dropdowns');
            
            // MÓDULO DE DROPDOWNS DESKTOP
            const dropdowns = document.querySelectorAll('.category-dropdown, .rentals-dropdown, .services-dropdown');
            const dropdownContents = document.querySelectorAll('.dropdown-content');
            console.log('Dropdowns encontrados:', dropdowns.length);
            console.log('Dropdown contents encontrados:', dropdownContents.length);

            // Función para cerrar todos los dropdowns excepto el activo
            function closeOtherDropdowns(exceptDropdown) {
                console.log('Cerrando otros dropdowns, excepto:', exceptDropdown);
                dropdownContents.forEach(content => {
                    if (content !== exceptDropdown) {
                        content.classList.remove('show');
                        console.log('Cerrando dropdown:', content.className);
                    }
                });
            }

            // Manejo de clicks en dropdowns
            dropdowns.forEach((dropdown, index) => {
                const dropdownContent = dropdown.querySelector('.dropdown-content');
                if (!dropdownContent) {
                    console.warn(`No se encontró .dropdown-content para el dropdown ${index}`);
                    return;
                }
                console.log(`Configurando evento click para dropdown ${index}:`, dropdown.className);
                
                dropdown.addEventListener('click', function(e) {
                    console.log(`Click en dropdown ${index}:`, dropdown.className);
                    e.preventDefault();
                    e.stopPropagation();
                    closeOtherDropdowns(dropdownContent);
                    console.log('Estado actual de show:', dropdownContent.classList.contains('show'));
                    dropdownContent.classList.toggle('show');
                    console.log('Nuevo estado de show:', dropdownContent.classList.contains('show'));
                });
            });

            // Cerrar dropdowns al hacer click fuera
            document.addEventListener('click', function(e) {
                console.log('Click fuera - Cerrando todos los dropdowns');
                closeOtherDropdowns(null);
            });

            // MÓDULO MENÚ MÓVIL
            // Controla la funcionalidad del menú hamburguesa y sus tabs en modo responsive
            const menuIcon = document.querySelector('.mobile-menu-toggle');
            const mobileMenu = document.querySelector('.mobile-menu');
            const menuTabs = document.querySelectorAll('.mobile-menu-tab');
            const menuContents = document.querySelectorAll('.mobile-menu-content');

            // Función para ajustar la posición del menú móvil
            function adjustMobileMenuPosition() {
                const header = document.querySelector('.header-top');
                const headerHeight = header.offsetHeight;
                mobileMenu.style.top = `${headerHeight}px`;
            }

            if (menuIcon && mobileMenu) {
                menuIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    adjustMobileMenuPosition(); // Ajustar posición antes de mostrar
                    mobileMenu.classList.toggle('active');
                    // Ocultar todos los contenidos al abrir/cerrar menú
                    menuContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    // Desactivar todas las pestañas
                    menuTabs.forEach(tab => {
                        tab.classList.remove('active');
                    });
                });

                menuTabs.forEach(tab => {
                    tab.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const contentId = this.getAttribute('data-tab');
                        const content = document.querySelector(`[data-content="${contentId}"]`);
                        
                        if (content.classList.contains('active')) {
                            // Si está activo, lo cerramos
                            content.classList.remove('active');
                            this.classList.remove('active');
                        } else {
                            // Si no está activo, cerramos otros y abrimos este
                            menuContents.forEach(c => c.classList.remove('active'));
                            menuTabs.forEach(t => t.classList.remove('active'));
                            content.classList.add('active');
                            this.classList.add('active');
                        }
                    });
                });

                document.addEventListener('click', function(e) {
                    if (!mobileMenu.contains(e.target) && !menuIcon.contains(e.target)) {
                        mobileMenu.classList.remove('active');
                    }
                });
            }

            // MÓDULO DE RESPONSIVE
            // Cierra automáticamente el menú móvil cuando se cambia a vista desktop
            window.addEventListener('resize', function() {
                const mobileMenu = document.querySelector('.mobile-menu');
                const menuContents = document.querySelectorAll('.mobile-menu-content');
                
                adjustMobileMenuPosition(); // Ajustar posición al redimensionar
                
                if (window.innerWidth > 768) {
                    mobileMenu.classList.remove('active');
                    menuContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    // Cerrar también todos los dropdowns desktop
                    closeOtherDropdowns(null);
                }
            });
        });
 