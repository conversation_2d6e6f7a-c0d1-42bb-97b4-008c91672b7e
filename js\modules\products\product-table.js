// Función para actualizar la tabla de productos
function updateProductsTable(products) {
    const productsTable = document.getElementById('productsTable');
    if (!productsTable) {
        console.error('La tabla de productos no se encuentra en el DOM.');
        return;
    }

    const tbody = productsTable.querySelector('tbody');

    if (!tbody) {
        console.error('El tbody no se encuentra dentro de la tabla de productos.');
        return;
    }

    if (!products || products.length === 0) {
        tbody.innerHTML = `<tr><td colspan="11" class="no-data">No hay productos disponibles</td></tr>`;
        return;
    }

    tbody.innerHTML = products.map(product => {
        // Formatear precios sin decimales y con símbolo de moneda
        let formattedPrice = '';
        if (product.precio) {
            // Formatear precio actual
            let currentPrice = '';
            if (window.priceFormatter && typeof window.priceFormatter.formatPrice === 'function') {
                currentPrice = window.priceFormatter.formatPrice(product.precio);
                console.log(`Precio formateado con priceFormatter: ${product.precio} -> ${currentPrice}`);
            } else if (window.priceFormatter && typeof window.priceFormatter.format === 'function') {
                // Usar el método alternativo si está disponible
                currentPrice = window.priceFormatter.format(product.precio);
                console.log(`Precio formateado con priceFormatter.format: ${product.precio} -> ${currentPrice}`);
            } else {
                // Formateo manual como respaldo
                const numericValue = parseFloat(String(product.precio).replace(/[^0-9.]/g, ''));
                currentPrice = `$${Math.floor(numericValue).toLocaleString('es-CL')}`;
                console.log(`Precio formateado manualmente: ${product.precio} -> ${currentPrice}`);
            }

            // Si hay precio original y es mayor que el precio actual, mostrarlo tachado debajo
            if (product.precio_original && parseFloat(String(product.precio_original).replace(/[^0-9.]/g, '')) > parseFloat(String(product.precio).replace(/[^0-9.]/g, ''))) {
                let originalPrice = '';
                if (window.priceFormatter && typeof window.priceFormatter.formatPrice === 'function') {
                    originalPrice = window.priceFormatter.formatPrice(product.precio_original);
                } else if (window.priceFormatter && typeof window.priceFormatter.format === 'function') {
                    originalPrice = window.priceFormatter.format(product.precio_original);
                } else {
                    const numericValue = parseFloat(String(product.precio_original).replace(/[^0-9.]/g, ''));
                    originalPrice = `$${Math.floor(numericValue).toLocaleString('es-CL')}`;
                }

                formattedPrice = `<div class="price-container">
                    <span class="current-price">${currentPrice}</span>
                    <span class="original-price">${originalPrice}</span>
                </div>`;
                console.log(`Precio con descuento formateado: ${currentPrice} (original: ${originalPrice})`);
            } else {
                formattedPrice = currentPrice;
            }
        }

        // Preparar la descripción truncada y completa para el tooltip
        const descripcionCompleta = product.descripcion || '';
        const descripcionCorta = product.descripcion_corta ||
            (descripcionCompleta ? descripcionCompleta.substring(0, 100) + '...' : '');

        return `
        <tr data-product-id="${product.id}">
            <td>${product.id}</td>
            <td class="product-image-cell">
                <img src="${product.imagen_principal || '../images/placeholder.png'}"
                     alt="${product.nombre}"
                     class="product-image-preview">
            </td>
            <td class="product-name-cell">
                <div class="product-name">${product.nombre}</div>
            </td>
            <td class="description-cell"
                data-tooltip="${descripcionCompleta.replace(/"/g, '&quot;')}"
                title="${descripcionCompleta.replace(/"/g, '&quot;')}">
                ${descripcionCorta}
            </td>
            <td class="product-price formatted">${formattedPrice}</td>
            <td>${product.stock}</td>
            <td><span class="status-badge status-${product.estado.toLowerCase()}">${product.estado}</span></td>
            <td><span class="condition-badge condition-${product.condicion.toLowerCase()}">${product.condicion}</span></td>
            <td>${product.categoria_nombre || ''}</td>
            <td>${product.subcategoria_nombre || 'Sin subcategoría'}</td>
            <td>${product.sku || '-'}</td>
            <td class="actions">
                <div class="action-buttons">
                    <button class="action-btn edit-btn" data-id="${product.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" data-id="${product.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `}).join('');
}

// Exportar funciones
export {
    updateProductsTable
};
