<?php
// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Incluir primero la configuración base
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Inicializar gestor de sesiones
$sessionManager = SessionManager::getInstance();

// Debug avanzado
error_log("===== DEPURACIÓN DE LOGIN =====");
error_log("PHP Version: " . phpversion());
error_log("Session Save Path: " . ini_get('session.save_path'));
error_log("Session Name: " . session_name());
error_log("Cookies: " . print_r($_COOKIE, true));
error_log("Server variables: " . print_r($_SERVER, true));

// Inicializar la sesión
$sessionManager->initializeSession();

// Verificar si el usuario ya está autenticado
if ($sessionManager->isLoggedIn()) {
    $currentUser = $sessionManager->getCurrentUser();
    error_log("Login.php - Usuario ya autenticado: " . $currentUser['username']);
    
    // Forzar escritura de sesión antes de redireccionar
    session_write_close();
    
    // Redirigir a tienda_adm.php
    $redirect_url = BASE_URL . '/public/tienda_adm.php';
    
    // Si hay una URL de retorno específica y es segura, usarla
    if (isset($_GET['return_to'])) {
        $return_to = $_GET['return_to'];
        // Verificar que es una URL relativa y pertenece a la aplicación
        if (substr($return_to, 0, 1) === '/' && 
            strpos($return_to, BASE_URL) === 0) {
            $redirect_url = $return_to;
        }
    }
    
    error_log("Login.php - Redirigiendo a: " . $redirect_url);
    header('Location: ' . $redirect_url);
    exit();
}

// Debug después de la verificación de autenticación
error_log("Login.php - Usuario no autenticado, mostrando formulario de login");

// Obtener mensaje de error si existe
$error_message = '';
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'empty':
            $error_message = 'Por favor ingrese usuario y contraseña';
            break;
        case 'user_not_found':
            $error_message = 'Usuario no encontrado';
            break;
        case 'invalid_password':
            $error_message = 'Contraseña incorrecta';
            break;
        case 'unauthorized':
            if (isset($_GET['reason']) && $_GET['reason'] === 'login_required') {
                $error_message = 'Por favor inicie sesión para acceder a esta página';
            } else {
                $error_message = 'No tiene permisos para acceder a esta página';
            }
            break;
        default:
            $error_message = 'Error al iniciar sesión';
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-container {
            background: #ffffff;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-header h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .login-header p {
            color: #666;
            font-size: 1rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            z-index: 1;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px 12px 45px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: #fff;
            color: #333;
        }
        .form-control:focus {
            outline: none;
            border-color: #764ba2;
            box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.2);
        }
        .btn-login {
            width: 100%;
            padding: 12px;
            background: #764ba2;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .btn-login:hover {
            background: #667eea;
        }
        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }
        .forgot-password a {
            color: #764ba2;
            text-decoration: none;
        }
        .forgot-password a:hover {
            text-decoration: underline;
        }
        .register-link {
            text-align: center;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            color: #666;
        }
        .register-link a {
            color: #764ba2;
            text-decoration: none;
            font-weight: bold;
        }
        .alert {
            padding: 12px;
            margin-bottom: 1rem;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
        }
        .alert-error {
            background-color: #fee;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        .alert-success {
            background-color: #efe;
            color: #2ecc71;
            border: 1px solid #2ecc71;
        }
        .social-login {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .btn-social {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            color: #fff;
            font-size: 0.9rem;
            transition: opacity 0.3s ease;
            text-decoration: none;
            min-width: 160px;
        }
        .btn-facebook {
            background-color: #3b5998;
        }
        .btn-google {
            background-color: #db4437;
        }
        .btn-social:hover {
            opacity: 0.9;
        }
        .btn-social i {
            margin-right: 0.5rem;
        }

        /* Ajustes de responsividad */
        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
            }
            .btn-social {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            .social-login {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Bienvenido</h1>
            <p>Ingresa a tu cuenta</p>
        </div>
        <?php 
        // Registrar errores de formulario
        if (isset($_GET['error'])) {
            error_log("Login.php - Error recibido: " . $_GET['error']);
        }
        
        // Mostrar mensaje de éxito después de logout
        if (isset($_GET['message']) && $_GET['message'] === 'logout_success'): ?>
            <div class="alert alert-success">
                Has cerrado sesión exitosamente.
            </div>
        <?php endif;

        if ($error_message): 
        ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>
        <form action="<?php echo BASE_URL; ?>/config/auth.php" method="POST" id="loginForm">
            <div class="form-group">
                <i class="fas fa-user"></i>
                <input type="text" 
                       class="form-control" 
                       name="username" 
                       placeholder="Usuario o Email" 
                       required>
            </div>
            <div class="form-group">
                <i class="fas fa-lock"></i>
                <input type="password" 
                       class="form-control" 
                       name="password" 
                       placeholder="Contraseña" 
                       required>
            </div>
            <button type="submit" class="btn-login">Iniciar Sesión</button>
        </form>
        <div class="social-login">
            <a href="#" class="btn-social btn-facebook"><i class="fab fa-facebook-f"></i> Facebook</a>
            <a href="#" class="btn-social btn-google"><i class="fab fa-google"></i> Google</a>
        </div>
        <div class="forgot-password">
            <a href="recover.php">¿Olvidaste tu contraseña?</a>
        </div>
        <div class="register-link">
            ¿No tienes una cuenta? <a href="register.php">Regístrate aquí</a>
        </div>
    </div>
</body>
</html>