document.addEventListener('DOMContentLoaded', function() {
    // Referencias para el sidebar
    const sidebar = document.getElementById('sidebar');
    const asideToggle = document.getElementById('aside-toggle');
    const toggleIcon = document.getElementById('toggle-icon');

    // Función para manejar el toggle del sidebar
    function toggleSidebar() {
        if (window.innerWidth < 992) {
            sidebar.classList.toggle('expanded');
        } else {
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
        }
    }

    // Inicializar sidebar
    if (asideToggle) {
        asideToggle.addEventListener('click', toggleSidebar);
    }

    // Inicializar los gráficos
    const failedRequestsChartCtx = document.getElementById('failedRequestsChart').getContext('2d');
    const failedRequestsChart = new Chart(failedRequestsChartCtx, {
        type: 'line',
        data: {
            labels: ['3:45 PM', '4:00 PM', '4:15 PM', '4:30 PM', '4:45 PM', '5:00 PM', '5:15 PM', '5:30 PM', '5:45 PM'],
            datasets: [{
                label: 'Solicitudes fallidas (Cantidad)',
                data: [5, 8, 6, 7, 5, 6, 8, 10, 9],
                borderColor: '#e91e63',
                backgroundColor: 'rgba(233, 30, 99, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: 'Solicitudes fallidas (Última hora)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    const responseTimeChartCtx = document.getElementById('responseTimeChart').getContext('2d');
    const responseTimeChart = new Chart(responseTimeChartCtx, {
        type: 'line',
        data: {
            labels: ['3:45 PM', '4:00 PM', '4:15 PM', '4:30 PM', '4:45 PM', '5:00 PM', '5:15 PM', '5:30 PM', '5:45 PM'],
            datasets: [{
                label: 'Tiempo de respuesta (ms)',
                data: [150, 200, 180, 220, 210, 190, 200, 230, 220],
                borderColor: '#2196f3',
                backgroundColor: 'rgba(33, 150, 243, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: 'Tiempo de respuesta del servidor (Última hora)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    const serverRequestsChartCtx = document.getElementById('serverRequestsChart').getContext('2d');
    const serverRequestsChart = new Chart(serverRequestsChartCtx, {
        type: 'line',
        data: {
            labels: ['3:45 PM', '4:00 PM', '4:15 PM', '4:30 PM', '4:45 PM', '5:00 PM', '5:15 PM', '5:30 PM', '5:45 PM'],
            datasets: [{
                label: 'Solicitudes (Cantidad)',
                data: [20, 30, 25, 35, 30, 28, 32, 40, 38],
                borderColor: '#3f51b5',
                backgroundColor: 'rgba(63, 81, 181, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: 'Solicitudes al servidor (Última hora)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    const availabilityChartCtx = document.getElementById('availabilityChart').getContext('2d');
    const availabilityChart = new Chart(availabilityChartCtx, {
        type: 'line',
        data: {
            labels: ['3:45 PM', '4:00 PM', '4:15 PM', '4:30 PM', '4:45 PM', '5:00 PM', '5:15 PM', '5:30 PM', '5:45 PM'],
            datasets: [{
                label: 'Disponibilidad (%)',
                data: [99.8, 99.5, 99.7, 99.9, 99.6, 99.5, 99.7, 99.8, 99.9],
                borderColor: '#4caf50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                title: {
                    display: true,
                    text: 'Disponibilidad (Última hora)'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    min: 95,
                    max: 100
                }
            }
        }
    });

    // Función para actualizar los gráficos según el filtro seleccionado
    function updateServerCharts(timeRange) {
        let timeLabel;
        let newData;
        
        switch(timeRange) {
            case '30m':
                timeLabel = 'Últimos 30 minutos';
                newData = generateRandomData(6, [3, 10], [120, 250], [15, 45], [99, 100]);
                break;
            case '1h':
                timeLabel = 'Última hora';
                newData = generateRandomData(9, [5, 15], [150, 250], [20, 50], [99, 100]);
                break;
            case '6h':
                timeLabel = 'Últimas 6 horas';
                newData = generateRandomData(12, [8, 20], [180, 300], [30, 70], [98, 100]);
                break;
            case '12h':
                timeLabel = 'Últimas 12 horas';
                newData = generateRandomData(12, [10, 25], [200, 350], [40, 90], [97, 100]);
                break;
            case '1d':
                timeLabel = 'Último día';
                newData = generateRandomData(12, [15, 30], [200, 400], [50, 120], [96, 100]);
                break;
            case '3d':
                timeLabel = 'Últimos 3 días';
                newData = generateRandomData(12, [20, 40], [250, 450], [60, 150], [95, 100]);
                break;
            case '7d':
                timeLabel = 'Últimos 7 días';
                newData = generateRandomData(14, [25, 50], [300, 500], [80, 180], [94, 100]);
                break;
            case '30d':
                timeLabel = 'Últimos 30 días';
                newData = generateRandomData(15, [30, 70], [350, 600], [100, 250], [93, 100]);
                break;
            default:
                timeLabel = 'Última hora';
                newData = generateRandomData(9, [5, 15], [150, 250], [20, 50], [99, 100]);
        }
        
        // Actualizar títulos y datos
        failedRequestsChart.options.plugins.title.text = `Solicitudes fallidas (${timeLabel})`;
        responseTimeChart.options.plugins.title.text = `Tiempo de respuesta del servidor (${timeLabel})`;
        serverRequestsChart.options.plugins.title.text = `Solicitudes al servidor (${timeLabel})`;
        availabilityChart.options.plugins.title.text = `Disponibilidad (${timeLabel})`;
        
        // Actualizar datos
        failedRequestsChart.data.datasets[0].data = newData.failedRequests;
        responseTimeChart.data.datasets[0].data = newData.responseTime;
        serverRequestsChart.data.datasets[0].data = newData.serverRequests;
        availabilityChart.data.datasets[0].data = newData.availability;
        
        // Actualizar gráficos
        failedRequestsChart.update();
        responseTimeChart.update();
        serverRequestsChart.update();
        availabilityChart.update();
    }
    
    // Función para generar datos aleatorios para simulación
    function generateRandomData(points, failedRange, responseRange, requestsRange, availabilityRange) {
        const failedRequests = Array.from({length: points}, () => Math.floor(Math.random() * (failedRange[1] - failedRange[0]) + failedRange[0]));
        const responseTime = Array.from({length: points}, () => Math.floor(Math.random() * (responseRange[1] - responseRange[0]) + responseRange[0]));
        const serverRequests = Array.from({length: points}, () => Math.floor(Math.random() * (requestsRange[1] - requestsRange[0]) + requestsRange[0]));
        const availability = Array.from({length: points}, () => (Math.random() * (availabilityRange[1] - availabilityRange[0]) + availabilityRange[0]).toFixed(1));
        
        return {
            failedRequests,
            responseTime,
            serverRequests,
            availability
        };
    }

    // Asignar eventos a los botones de filtro
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover la clase 'active' de todos los botones
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Agregar la clase 'active' al botón seleccionado
            this.classList.add('active');
            // Actualizar los gráficos
            updateServerCharts(this.getAttribute('data-time'));
        });
    });
});