<?php
/**
 * SecurityService.php
 * 
 * Servicio para gestionar aspectos de seguridad como CSRF, sanitización de datos, etc.
 */

namespace Config;

class SecurityService {
    private static $instance = null;
    
    /**
     * Constructor privado (<PERSON><PERSON><PERSON>)
     */
    private function __construct() {
        // Inicializar el servicio
    }
    
    /**
     * Obtiene la instancia única del servicio
     *
     * @return SecurityService
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Genera un token CSRF
     *
     * @return string Token generado
     */
    public function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Valida un token CSRF
     *
     * @param string $token Token a validar
     * @param bool $regenerate Si se debe regenerar el token después de la validación
     * @return bool True si el token es válido
     */
    public function validateCsrfToken($token, $regenerate = true) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        $valid = hash_equals($_SESSION['csrf_token'], $token);
        
        // Regenerar el token después de la validación (por seguridad)
        if ($valid && $regenerate) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $valid;
    }
    
    /**
     * Sanitiza datos de entrada para prevenir XSS
     *
     * @param string $input Texto a sanitizar
     * @return string Texto sanitizado
     */
    public function sanitizeInput($input) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Sanitiza datos recursivamente (arrays y strings)
     *
     * @param mixed $data Datos a sanitizar
     * @return mixed Datos sanitizados
     */
    public function sanitizeData($data) {
        if (is_array($data)) {
            $sanitized = [];
            foreach ($data as $key => $value) {
                $sanitized[$key] = $this->sanitizeData($value);
            }
            return $sanitized;
        }
        
        if (is_string($data)) {
            return $this->sanitizeInput($data);
        }
        
        return $data;
    }
    
    /**
     * Valida si una URL es segura para redirección
     *
     * @param string $url URL a validar
     * @param string $baseUrl URL base permitida
     * @return bool True si la URL es segura
     */
    public function isSecureRedirectUrl($url, $baseUrl) {
        // Si es una URL relativa, es segura
        if (substr($url, 0, 1) === '/') {
            return true;
        }
        
        // Si tiene el mismo dominio que la aplicación, es segura
        if (strpos($url, $baseUrl) === 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Establece encabezados de seguridad HTTP
     */
    public function setSecurityHeaders() {
        // Content-Security-Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com; style-src 'self' https://cdnjs.cloudflare.com 'unsafe-inline'; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data:;");
        
        // Prevenir clickjacking
        header("X-Frame-Options: DENY");
        
        // Cross-site scripting protection
        header("X-XSS-Protection: 1; mode=block");
        
        // Prevenir MIME sniffing
        header("X-Content-Type-Options: nosniff");
        
        // Referrer policy
        header("Referrer-Policy: strict-origin-when-cross-origin");
        
        // Strict-Transport-Security (HSTS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header("Strict-Transport-Security: max-age=31536000; includeSubDomains");
        }
    }
    
    /**
     * Genera una contraseña aleatoria segura
     *
     * @param int $length Longitud de la contraseña
     * @return string Contraseña generada
     */
    public function generateSecurePassword($length = 12) {
        $charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+';
        $password = '';
        $charsetLength = strlen($charset) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $charset[random_int(0, $charsetLength)];
        }
        
        return $password;
    }
    
    /**
     * Verifica si una IP está en la lista negra
     *
     * @param string $ip Dirección IP a verificar
     * @return bool True si la IP está bloqueada
     */
    public function isIpBlacklisted($ip) {
        // Implementar verificación de lista negra
        // Esto podría consultar una tabla en la base de datos o un archivo de configuración
        
        // Ejemplo sencillo
        $blacklistedIps = [
            // Lista de IPs conocidas por actividad maliciosa
            // '123.456.789.012',
        ];
        
        return in_array($ip, $blacklistedIps);
    }
    
    /**
     * Verifica si una solicitud podría ser un ataque
     *
     * @return bool True si se detectan signos de ataque
     */
    public function detectSuspiciousActivity() {
        // Verificar inyección SQL básica en parámetros GET y POST
        $patterns = [
            '/\bselect\b.*\bfrom\b/i',
            '/\binsert\b.*\binto\b/i',
            '/\bupdate\b.*\bset\b/i',
            '/\bdelete\b.*\bfrom\b/i',
            '/\bdrop\b.*\btable\b/i',
            '/\balter\b.*\btable\b/i',
            '/\bunion\b.*\bselect\b/i',
            '/--/',
            '/;/',
            '/\/\*.*\*\//',
        ];
        
        foreach ($_GET as $param) {
            if (is_string($param)) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $param)) {
                        logWarning('Posible ataque SQL detectado en GET', ['param' => $param]);
                        return true;
                    }
                }
            }
        }
        
        foreach ($_POST as $param) {
            if (is_string($param)) {
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $param)) {
                        logWarning('Posible ataque SQL detectado en POST', ['param' => $param]);
                        return true;
                    }
                }
            }
        }
        
        // Verificar XSS básico
        $xssPatterns = [
            '/<script.*?>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+=/i',
            '/<iframe.*?>.*?<\/iframe>/i',
        ];
        
        foreach ($_GET as $param) {
            if (is_string($param)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $param)) {
                        logWarning('Posible ataque XSS detectado en GET', ['param' => $param]);
                        return true;
                    }
                }
            }
        }
        
        foreach ($_POST as $param) {
            if (is_string($param)) {
                foreach ($xssPatterns as $pattern) {
                    if (preg_match($pattern, $param)) {
                        logWarning('Posible ataque XSS detectado en POST', ['param' => $param]);
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Registra una actividad sospechosa
     *
     * @param string $reason Razón de la sospecha
     * @param array $data Datos adicionales
     */
    public function logSuspiciousActivity($reason, $data = []) {
        $data['ip'] = $_SERVER['REMOTE_ADDR'];
        $data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $data['request_method'] = $_SERVER['REQUEST_METHOD'];
        $data['request_uri'] = $_SERVER['REQUEST_URI'];
        
        logWarning('Actividad sospechosa detectada: ' . $reason, $data);
        
        // Aquí se podría implementar un sistema de alerta o bloqueo automático
    }
}