/**
 * Manejador de pasos para el formulario de registro
 * Este script se encarga de mostrar y ocultar los pasos del formulario
 * según la navegación del usuario y el tipo de negocio seleccionado.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Obtener todos los pasos del formulario
    const formSteps = document.querySelectorAll('.form-step');
    const stepIndicators = document.querySelectorAll('.step');
    
    // Obtener los botones de navegación
    const prevButtons = document.querySelectorAll('.btn-prev');
    const nextButtons = document.querySelectorAll('.btn-next');
    
    // Obtener los radio buttons del tipo de negocio
    const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
    
    // Variable para almacenar el tipo de negocio seleccionado
    let tipoNegocioSeleccionado = '';
    
    /**
     * Muestra un paso específico del formulario
     * @param {string} stepId - El ID del paso a mostrar
     */
    function showFormStep(stepId) {
        console.log('Mostrando paso:', stepId);
        
        // Ocultar todos los pasos
        formSteps.forEach(step => {
            step.style.display = 'none';
            step.classList.remove('active');
        });
        
        // Mostrar el paso solicitado
        const targetStep = document.getElementById(stepId);
        if (targetStep) {
            targetStep.style.display = 'block';
            targetStep.classList.add('active');
            
            // Actualizar los indicadores de paso
            updateStepIndicators(stepId);
            
            // Desplazarse al inicio del formulario
            window.scrollTo(0, 0);
        } else {
            console.error('Paso no encontrado:', stepId);
        }
    }
    
    /**
     * Actualiza los indicadores de paso según el paso actual
     * @param {string} currentStepId - El ID del paso actual
     */
    function updateStepIndicators(currentStepId) {
        // Determinar el número de paso
        let stepNumber = 1;
        
        if (currentStepId === 'step1') {
            stepNumber = 1;
        } else if (currentStepId === 'step2') {
            stepNumber = 2;
        } else if (currentStepId === 'step3') {
            stepNumber = 3;
        } else if (currentStepId === 'step4-productos' || currentStepId === 'step4-servicios') {
            stepNumber = 4;
        } else if (currentStepId === 'step5') {
            stepNumber = 5;
        }
        
        // Actualizar los indicadores de paso
        stepIndicators.forEach((indicator, index) => {
            if (index < stepNumber) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
    }
    
    /**
     * Maneja el cambio de tipo de negocio para mostrar el paso 4 correcto
     */
    tipoNegocioRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            tipoNegocioSeleccionado = this.value;
            console.log('Tipo de negocio seleccionado:', tipoNegocioSeleccionado);
        });
    });
    
    /**
     * Maneja el evento de clic en el botón "Siguiente" del paso 3
     * para mostrar el paso 4 correcto según el tipo de negocio
     */
    const step3NextButton = document.querySelector('#step3 .btn-next');
    if (step3NextButton) {
        step3NextButton.addEventListener('click', function(e) {
            // Si es un botón de tipo submit, no hacemos nada aquí
            // ya que el formulario se enviará y PHP manejará la redirección
            if (this.type === 'submit') {
                return;
            }
            
            // Si es un botón de tipo button, manejamos la navegación en JavaScript
            e.preventDefault();
            
            // Determinar qué paso 4 mostrar según el tipo de negocio
            if (tipoNegocioSeleccionado === 'venta') {
                showFormStep('step4-productos');
            } else if (tipoNegocioSeleccionado === 'servicios' || tipoNegocioSeleccionado === 'arriendo') {
                showFormStep('step4-servicios');
            } else {
                // Si no hay tipo seleccionado, mostrar un mensaje de error
                alert('Por favor, seleccione un tipo de negocio');
            }
        });
    }
    
    /**
     * Inicializar el formulario mostrando el paso correcto según el hash de la URL
     */
    function initializeFormFromHash() {
        const hash = window.location.hash;
        if (hash) {
            const stepId = hash.substring(1); // Eliminar el # del inicio
            showFormStep(stepId);
        } else {
            // Si no hay hash, mostrar el primer paso
            showFormStep('step1');
        }
    }
    
    // Inicializar el formulario cuando se carga la página
    initializeFormFromHash();
    
    // Escuchar cambios en el hash de la URL
    window.addEventListener('hashchange', initializeFormFromHash);
});
