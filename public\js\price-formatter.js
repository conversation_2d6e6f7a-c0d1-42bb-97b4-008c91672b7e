/**
 * price-formatter.js - Formateador de precios para tienda
 *
 * Este script maneja el formateo de campos de precio en formularios y tablas,
 * asegurando que los precios se muestren sin decimales y con el símbolo de moneda.
 */

// Objeto global para exportar funciones
window.priceFormatter = window.priceFormatter || {};

/**
 * Extrae el valor numérico de un texto de precio
 * @param {string|number} value - Texto o número que contiene el precio
 * @returns {number} - Valor numérico sin formateo
 */
function extractNumericValue(value) {
    // Manejar valores nulos o indefinidos
    if (value === null || value === undefined) {
        console.warn('Valor nulo o indefinido pasado a extractNumericValue');
        return 0;
    }

    // Convertir a string para aplicar regex
    let stringValue = value.toString().trim();
    console.log(`Extrayendo valor numérico de: "${stringValue}" (tipo: ${typeof value})`);

    // Si es un número, devolverlo directamente
    if (typeof value === 'number') {
        console.log(`Valor numérico directo: ${value}`);
        return value;
    }

    // Eliminar símbolos de moneda y espacios
    stringValue = stringValue.replace(/[$\s]/g, '');

    // Reemplazar comas por puntos si hay comas como separador decimal
    if (stringValue.indexOf(',') !== -1 && stringValue.indexOf('.') === -1) {
        stringValue = stringValue.replace(',', '.');
    }

    // Eliminar puntos que sean separadores de miles (mantener solo el último punto como decimal)
    if (stringValue.indexOf('.') !== stringValue.lastIndexOf('.')) {
        // Hay múltiples puntos, eliminar todos excepto el último
        const parts = stringValue.split('.');
        const decimal = parts.pop(); // Último elemento (parte decimal)
        const integer = parts.join(''); // Unir el resto sin puntos
        stringValue = integer + '.' + decimal;
    }

    // Convertir a número y retornar
    const numericValue = parseFloat(stringValue) || 0;
    console.log(`Valor numérico extraído: ${numericValue}`);
    return numericValue;
}

/**
 * Formatea un valor numérico como precio con el símbolo de moneda
 * @param {string|number} value - Valor a formatear
 * @returns {string} - Texto formateado como precio
 */
function formatPrice(value) {
    try {
        console.log(`Formateando precio: ${value} (tipo: ${typeof value})`);

        // Extraer valor numérico
        const numericValue = extractNumericValue(value);

        // Mantener el valor sin redondear para preservar los decimales
        console.log(`Valor numérico extraído: ${numericValue}`);

        // Formatear con separador de miles sin decimales
        const formattedValue = `$${Math.floor(numericValue).toLocaleString('es-CL')}`;
        console.log(`Valor formateado final: ${formattedValue}`);

        return formattedValue;
    } catch (error) {
        console.error('Error al formatear precio:', error);
        return value;
    }
}

/**
 * Aplica formato de precio a un input
 * @param {HTMLInputElement} input - Elemento input que contiene el precio
 */
function formatPriceInput(input) {
    try {
        const originalValue = input.value;
        const formattedValue = formatPrice(originalValue);

        // Solo actualizar si es diferente
        if (originalValue !== formattedValue) {
            console.log(`Input formateado: ${originalValue} -> ${formattedValue}`);
            input.value = formattedValue;
        }
    } catch (error) {
        console.error('Error al formatear input de precio:', error);
    }
}

/**
 * Extrae el valor numérico de un input formateado como precio
 * @param {HTMLInputElement} input - Elemento input que contiene el precio formateado
 * @returns {number} - Valor numérico sin formato
 */
function extractNumericValueFromInput(input) {
    return extractNumericValue(input.value);
}

/**
 * Inicializa todos los inputs de precio en el documento
 */
function initPriceInputs() {
    // Seleccionar todos los inputs que manejan precios
    const priceInputs = document.querySelectorAll(
        'input[type="text"][id*="price"], ' +
        'input[type="text"][id*="Price"], ' +
        'input[type="number"][id*="price"], ' +
        'input[type="number"][id*="Price"], ' +
        'input[data-price="true"]'
    );

    priceInputs.forEach(input => {
        // Configurar propiedades para inputs de tipo número
        if (input.type === 'number') {
            input.step = '1'; // No permitir decimales
            input.pattern = '\\d*'; // Solo números
        }

        // Formatear valor inicial
        if (input.value) {
            formatPriceInput(input);
        }

        // Eventos para mantener formato
        input.addEventListener('change', function() {
            formatPriceInput(this);
        });

        input.addEventListener('blur', function() {
            formatPriceInput(this);
        });

        // Para inputs de tipo texto, interceptar teclas
        if (input.type === 'text') {
            input.addEventListener('keyup', function() {
                // No formatear mientras está escribiendo para no interferir
                if (this.value.length > 0 && !this.value.startsWith('$')) {
                    this.value = '$' + this.value.replace(/[^0-9]/g, '');
                }
            });
        }
    });
}

/**
 * Inicializa el formateo de precios en tablas
 * @param {string} tableSelector - Selector CSS para la tabla
 * @param {number} priceColumnIndex - Índice de la columna que contiene precios (base 0)
 */
function initTablePriceFormatting(tableSelector, priceColumnIndex) {
    function formatTablePrices() {
        const priceCells = document.querySelectorAll(`${tableSelector} tbody td:nth-child(${priceColumnIndex + 1})`);

        priceCells.forEach(cell => {
            // Solo formatear si no está ya formateado
            if (!cell.classList.contains('price-formatted')) {
                const originalText = cell.textContent.trim();
                const numericValue = extractNumericValue(originalText);

                if (numericValue || numericValue === 0) {
                    cell.innerHTML = `<span class="price-formatted">${formatPrice(numericValue)}</span>`;
                    cell.classList.add('price-formatted');
                }
            }
        });

        console.log(`Formateadas ${priceCells.length} celdas de precio en la tabla`);
    }

    // Ejecutar formato inicial
    setTimeout(formatTablePrices, 500);

    // Observar cambios en la tabla
    const targetTable = document.querySelector(tableSelector);
    if (targetTable) {
        const observer = new MutationObserver(function() {
            setTimeout(formatTablePrices, 100);
        });

        observer.observe(targetTable, {
            childList: true,
            subtree: true
        });
    }
}

// Inicialización automática cuando se carga la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando formateador de precios');

    // Inicializar campos de formulario
    initPriceInputs();

    // Inicializar tabla de productos con precios en la 5ta columna (índice 4)
    initTablePriceFormatting('#productsTable', 4);
});

// Exportar funciones para uso en otros scripts
window.priceFormatter = {
    format: formatPrice,
    formatPrice: formatPrice, // Alias para compatibilidad con otros archivos
    extractNumeric: extractNumericValue,
    extractNumericValue: extractNumericValue, // Alias para compatibilidad con otros archivos
    formatInput: formatPriceInput,
    extractFromInput: extractNumericValueFromInput,
    initInputs: initPriceInputs,
    initTable: initTablePriceFormatting
};
