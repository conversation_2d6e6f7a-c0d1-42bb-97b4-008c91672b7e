// Importar componentes y módulos necesarios
import { showNotification } from '../../components/notifications.js';
import { openEditPanel, closeEditPanel } from './product-edit.js';
import { handleDeleteProduct as apiDeleteProduct, loadProducts } from './product-api.js';
import { formatPrice } from '../../core/utils.js';

/**
 * Renderiza la tabla de productos con los datos proporcionados.
 * @param {Array} products - Lista de productos a mostrar
 */
export function renderProductTable(products) {
    const tableBody = document.querySelector('.admin-table tbody');
    if (!tableBody) {
        console.error("No se encontró el cuerpo de la tabla de productos.");
        return;
    }

    tableBody.innerHTML = ''; // Limpiar contenido existente

    if (!products || products.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="11" class="text-center">No se encontraron productos.</td></tr>';
        return;
    }

    products.forEach(product => {
        const row = tableBody.insertRow();
        row.dataset.productId = product.id;

        // Preparar la descripción truncada y completa para el tooltip
        const descripcionCompleta = product.descripcion || '';
        const descripcionCorta = product.descripcion_corta || 
            (descripcionCompleta ? descripcionCompleta.substring(0, 100) + '...' : '');

        // ID
        row.insertCell().textContent = product.id;

        // Imagen
        const imgCell = row.insertCell();
        imgCell.className = 'product-image-cell';
        const img = document.createElement('img');
        img.src = product.imagen_principal || '../images/placeholder.png';
        img.alt = product.nombre;
        img.className = 'product-image-preview';
        imgCell.appendChild(img);

        // Nombre
        const nameCell = row.insertCell();
        nameCell.className = 'product-name-cell';
        nameCell.innerHTML = `<div class="product-name">${product.nombre || 'Sin nombre'}</div>`;

        // Descripción con tooltip
        const descCell = row.insertCell();
        descCell.className = 'description-cell';
        descCell.setAttribute('data-tooltip', descripcionCompleta);
        descCell.setAttribute('title', descripcionCompleta);
        descCell.textContent = descripcionCorta;

        // Precio
        const priceCell = row.insertCell();
        priceCell.textContent = formatPrice(product.precio);
        priceCell.classList.add('price-cell');

        // Stock
        const stockCell = row.insertCell();
        stockCell.textContent = product.stock !== null ? product.stock : 'N/A';
        stockCell.classList.add('stock-cell');

        // Estado
        const estadoCell = row.insertCell();
        estadoCell.innerHTML = `<span class="status-badge status-${product.estado || 'desconocido'}">${product.estado || 'N/A'}</span>`;

        // Condición
        const condicionCell = row.insertCell();
        condicionCell.innerHTML = `<span class="condition-badge condition-${product.condicion || 'ninguno'}">${product.condicion || 'Ninguna'}</span>`;

        // Categoría - MODIFICADA PARA MOSTRAR EL NOMBRE
        const categoriaCell = row.insertCell();
        categoriaCell.textContent = product.categoria_nombre || 'Sin categoría';

        // SKU
        const skuCell = row.insertCell();
        skuCell.textContent = product.sku || '-';

        // Acciones
        const actionsCell = row.insertCell();
        actionsCell.classList.add('actions-cell');
        actionsCell.innerHTML = `
            <button class="action-btn edit-btn" data-id="${product.id}" title="Editar">
                <i class="fas fa-edit"></i>
            </button>
            <button class="action-btn delete-btn" data-id="${product.id}" title="Eliminar">
                <i class="fas fa-trash"></i>
            </button>
        `;
    });

    // Volver a configurar los botones después de renderizar
    setupTableButtons();
}

/**
 * Configura los botones de la tabla de productos
 */
function setupTableButtons() {
    const tableBody = document.querySelector('.admin-table tbody');
    if (!tableBody) return;

    console.log("Configurando event listener para botones de tabla");
    
    // Eliminar listener anterior si existe
    tableBody.removeEventListener('click', handleTableButtonClick);
    
    // Agregar nuevo listener con delegación de eventos
    tableBody.addEventListener('click', handleTableButtonClick);
}

/**
 * Maneja los clics en los botones de acción de la tabla
 * @param {Event} e - Evento de clic
 */
function handleTableButtonClick(e) {
    const target = e.target.closest('.action-btn');
    if (!target) return;
    
    if (target.classList.contains('edit-btn')) {
        const productId = target.dataset.id;
        if (productId) {
            handleEditProduct(productId);
        }
    } 
    else if (target.classList.contains('delete-btn')) {
        const productId = target.dataset.id;
        if (productId) {
            handleDeleteProduct(productId);
        }
    }
}

/**
 * Maneja la acción de editar un producto
 * @param {string|number} productId - ID del producto a editar
 */
function handleEditProduct(productId) {
    openEditPanel(productId);
}

/**
 * Maneja la acción de eliminar un producto
 * @param {string|number} productId - ID del producto a eliminar
 */
function handleDeleteProduct(productId) {
    apiDeleteProduct(productId);
}

/**
 * Actualiza la vista según el ancho de la pantalla
 */
function updateResponsiveView() {
    const isMobile = window.innerWidth <= 768;
    const adminTable = document.querySelector('.admin-table');
    const productCardsContainer = document.getElementById('product-cards-container');

    if (!adminTable || !productCardsContainer) return;

    if (isMobile) {
        adminTable.style.display = 'none';
        productCardsContainer.style.display = 'flex';
    } else {
        adminTable.style.display = 'table';
        productCardsContainer.style.display = 'none';
    }
}

// Exportar funciones necesarias
export {
    setupTableButtons,
    handleTableButtonClick,
    handleEditProduct,
    handleDeleteProduct,
    updateResponsiveView
};
