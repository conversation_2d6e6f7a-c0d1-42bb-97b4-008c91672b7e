/**
 * Solución específica para los iconos del sidebar
 * Este script se ejecuta después de sidebar-desktop-width-fix.js para asegurar que los iconos se muestren correctamente
 */

(function() {
    console.log('Aplicando solución para los iconos del sidebar...');
    
    // Función para corregir los iconos del sidebar
    function fixSidebarIcons() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) {
            console.error('No se encontró el sidebar');
            return;
        }
        
        console.log('Corrigiendo iconos del sidebar...');
        
        // Asegurar que todos los iconos tengan los estilos correctos
        const allIcons = sidebar.querySelectorAll('.nav-link i, .user-avatar i');
        allIcons.forEach(icon => {
            // Estilos base para todos los iconos
            icon.style.display = 'inline-flex';
            icon.style.alignItems = 'center';
            icon.style.justifyContent = 'center';
            icon.style.minWidth = '24px';
            icon.style.textAlign = 'center';
            icon.style.opacity = '1';
            icon.style.visibility = 'visible';
        });
        
        // Aplicar estilos específicos según el estado del sidebar
        const isCollapsed = sidebar.classList.contains('collapsed');
        const isDesktop = window.innerWidth >= 993;
        
        if (isDesktop) {
            if (isCollapsed) {
                // Sidebar contraído en desktop
                const collapsedIcons = sidebar.querySelectorAll('.nav-link i, .user-avatar i');
                collapsedIcons.forEach(icon => {
                    icon.style.marginRight = '0';
                    icon.style.fontSize = '1.3em';
                    icon.style.display = 'flex';
                    icon.style.justifyContent = 'center';
                    icon.style.width = '100%';
                });
                
                // Ajustar alineación de elementos
                const navLinks = sidebar.querySelectorAll('.nav-link, .sidebar-header, .sidebar-user');
                navLinks.forEach(el => {
                    el.style.justifyContent = 'center';
                    el.style.padding = '15px 0';
                    el.style.textAlign = 'center';
                });
                
                // Ajustar espaciado de elementos
                const navItems = sidebar.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.style.marginBottom = '15px';
                });
            } else {
                // Sidebar expandido en desktop
                const expandedIcons = sidebar.querySelectorAll('.nav-link i');
                expandedIcons.forEach(icon => {
                    icon.style.marginRight = '10px';
                    icon.style.fontSize = '1em';
                    icon.style.width = 'auto';
                });
            }
        } else {
            // En móvil
            if (!sidebar.classList.contains('expanded')) {
                // Sidebar contraído en móvil
                const mobileIcons = sidebar.querySelectorAll('.nav-link i, .user-avatar i');
                mobileIcons.forEach(icon => {
                    icon.style.marginRight = '0';
                    icon.style.fontSize = '1.2em';
                    icon.style.display = 'flex';
                    icon.style.justifyContent = 'center';
                    icon.style.width = '100%';
                });
            }
        }
        
        console.log('Iconos del sidebar corregidos');
    }
    
    // Ejecutar la función de corrección
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixSidebarIcons);
    } else {
        fixSidebarIcons();
    }
    
    // También ejecutar después de que todo haya cargado
    window.addEventListener('load', () => {
        setTimeout(fixSidebarIcons, 500);
    });
    
    // Ejecutar inmediatamente
    setTimeout(fixSidebarIcons, 0);
    
    // Ejecutar cuando cambie el tamaño de la ventana
    window.addEventListener('resize', fixSidebarIcons);
    
    // Ejecutar cuando se haga clic en el botón de toggle
    document.addEventListener('click', function(e) {
        if (e.target.closest('#aside-toggle')) {
            // Esperar a que se complete la transición
            setTimeout(fixSidebarIcons, 300);
        }
    });
})();
