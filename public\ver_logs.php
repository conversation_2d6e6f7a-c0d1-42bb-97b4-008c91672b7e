<?php
// Script para ver los últimos errores registrados

// Habilitar la visualización de errores
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Función para obtener los últimos N líneas de un archivo
function obtener_ultimas_lineas($archivo, $n = 100) {
    if (!file_exists($archivo)) {
        return "El archivo no existe: $archivo";
    }
    
    $lineas = [];
    $fp = fopen($archivo, 'r');
    
    // Leer el archivo línea por línea
    while (!feof($fp)) {
        $linea = fgets($fp);
        if ($linea !== false) {
            $lineas[] = $linea;
            
            // Mantener solo las últimas N líneas
            if (count($lineas) > $n) {
                array_shift($lineas);
            }
        }
    }
    
    fclose($fp);
    return implode('', $lineas);
}

// Función para listar archivos en un directorio
function listar_archivos($directorio) {
    if (!file_exists($directorio)) {
        return [];
    }
    
    $archivos = [];
    $dir = opendir($directorio);
    
    while (($archivo = readdir($dir)) !== false) {
        if ($archivo != '.' && $archivo != '..' && is_file($directorio . '/' . $archivo)) {
            $archivos[] = $archivo;
        }
    }
    
    closedir($dir);
    rsort($archivos); // Ordenar por nombre en orden descendente
    return $archivos;
}

// Obtener el archivo de log solicitado
$log_dir = '../logs';
$archivo_seleccionado = isset($_GET['archivo']) ? $_GET['archivo'] : null;
$archivos_log = listar_archivos($log_dir);

// Iniciar la salida HTML
echo "<!DOCTYPE html>
<html>
<head>
    <title>Visor de Logs</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .log-container { 
            background-color: #f5f5f5; 
            padding: 15px; 
            border-radius: 5px; 
            white-space: pre-wrap; 
            font-family: monospace; 
            max-height: 500px; 
            overflow-y: auto; 
        }
        .archivo-item { margin-bottom: 5px; }
        .archivo-item a { text-decoration: none; color: #0066cc; }
        .archivo-item a:hover { text-decoration: underline; }
        .seccion { margin-bottom: 30px; }
    </style>
</head>
<body>
    <h1>Visor de Logs</h1>";

// Mostrar lista de archivos de log
echo "<div class='seccion'>
    <h2>Archivos de Log Disponibles</h2>";

if (empty($archivos_log)) {
    echo "<p>No hay archivos de log disponibles.</p>";
} else {
    echo "<div>";
    foreach ($archivos_log as $archivo) {
        $clase = ($archivo == $archivo_seleccionado) ? 'seleccionado' : '';
        echo "<div class='archivo-item {$clase}'>";
        echo "<a href='?archivo={$archivo}'>{$archivo}</a>";
        echo "</div>";
    }
    echo "</div>";
}

echo "</div>";

// Mostrar contenido del archivo seleccionado
if ($archivo_seleccionado) {
    echo "<div class='seccion'>
        <h2>Contenido de {$archivo_seleccionado}</h2>";
    
    $ruta_completa = $log_dir . '/' . $archivo_seleccionado;
    $contenido = obtener_ultimas_lineas($ruta_completa);
    
    echo "<div class='log-container'>";
    echo htmlspecialchars($contenido);
    echo "</div>";
    
    echo "</div>";
}

// Mostrar último error registrado
$ultimo_error_file = $log_dir . '/last_error.txt';
if (file_exists($ultimo_error_file)) {
    echo "<div class='seccion'>
        <h2>Último Error Registrado</h2>";
    
    $contenido = file_get_contents($ultimo_error_file);
    
    echo "<div class='log-container'>";
    echo htmlspecialchars($contenido);
    echo "</div>";
    
    echo "</div>";
}

// Finalizar la salida HTML
echo "</body></html>";
?>
