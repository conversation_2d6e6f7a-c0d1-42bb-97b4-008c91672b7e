// Importar componentes necesarios
import { handleDeleteProduct } from './product-api.js';
import { openEditPanel as handleEditProduct, closeEditPanel } from './product-edit.js';

// Crear tarjetas para visualización móvil
function createProductCards() {
    console.log("Creando tarjetas de productos");
    const productCardsContainer = document.getElementById('product-cards-container');
    if (!productCardsContainer) {
        console.error("No se encontró el contenedor de tarjetas con ID 'product-cards-container'");
        return;
    }

    // Limpiar tarjetas existentes
    productCardsContainer.innerHTML = '';

    // Obtener filas de la tabla de productos
    const productRows = document.querySelectorAll('.admin-table tbody tr');
    if (!productRows || productRows.length === 0) {
        console.warn("No se encontraron filas de productos en la tabla");
        productCardsContainer.innerHTML = '<p class="no-products-message">No hay productos disponibles</p>';
        return;
    }

    // Si hay una sola fila y es de carga o error, no crear tarjetas
    if (productRows.length === 1 && 
        (productRows[0].querySelector('.loading-row') || 
         productRows[0].querySelector('.error-row'))) {
        console.log("La tabla está en estado de carga o error, no se crean tarjetas.");
        return;
    }

    console.log(`Procesando ${productRows.length} productos`);

    // Crear tarjeta para cada producto
    productRows.forEach((row, index) => {
        try {
            // Crear un elemento de tarjeta
            const card = document.createElement('div');
            card.className = 'product-card';
            card.dataset.rowIndex = index;
            
            // Obtener ID del producto si está disponible
            const productId = row.cells[0] ? row.cells[0].textContent.trim() : '';
            if (productId) {
                card.dataset.productId = productId;
            }

            // Obtener datos del producto de la fila de la tabla con verificación
            const imageEl = row.querySelector('.product-image-cell img');
            const image = imageEl && imageEl.src ? imageEl.src : '../images/placeholder.png';

            const nameEl = row.querySelector('.product-name-cell .product-name');
            const productName = nameEl ? nameEl.textContent : 'Producto';

            const categoryEl = row.querySelector('.product-name-cell .product-category');
            const productCategory = categoryEl ? categoryEl.textContent : 'Categoría';

            // Obtener precio y posibles descuentos
            const priceEl = row.querySelector('.current-price');
            const price = priceEl ? priceEl.textContent : '';

            const originalPriceEl = row.querySelector('.original-price');
            const originalPrice = originalPriceEl ? originalPriceEl.textContent : '';

            // Obtener condición y estado
            const conditionEl = row.querySelector('.condition-badge');
            const condition = conditionEl ? conditionEl.textContent : '';
            const conditionClass = conditionEl && conditionEl.className.includes(' ') ? 
                conditionEl.className.split(' ')[1] : '';

            const statusEl = row.querySelector('.status-badge');
            const status = statusEl ? statusEl.textContent : '';
            const statusClass = statusEl && statusEl.className.includes(' ') ? 
                statusEl.className.split(' ')[1] : '';

            // Crear estructura HTML de la tarjeta con validaciones
            card.innerHTML = `
                <div class="card-image">
                    <img src="${image}" alt="${productName}">
                    <div class="card-badges">
                        ${condition ? `<span class="condition-badge ${conditionClass}">${condition}</span>` : ''}
                        ${status ? `<span class="status-badge ${statusClass}">${status}</span>` : ''}
                    </div>
                    ${originalPrice ? `<div class="discount-badge">OFERTA</div>` : ''}
                </div>
                <div class="card-body">
                    <h3 class="card-title">${productName}</h3>
                    <p class="card-category">${productCategory}</p>
                    <div class="card-price">
                        <span class="current-price">${price}</span>
                        ${originalPrice ? `<span class="original-price">${originalPrice}</span>` : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button class="card-btn edit-btn" type="button" data-id="${productId}">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    <button class="card-btn delete-btn" type="button" data-id="${productId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            // Agregar eventos a los botones
            setupCardButtons(card);

            // Agregar la tarjeta al contenedor
            productCardsContainer.appendChild(card);
        } catch (error) {
            console.error(`Error al crear tarjeta para fila ${index}:`, error);
        }
    });

    console.log(`Se crearon ${productCardsContainer.children.length} tarjetas de productos`);
}

// Configurar eventos para botones de tarjeta
function setupCardButtons(card) {
    // Botón editar
    const editBtn = card.querySelector('.edit-btn');
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            const productId = this.dataset.id;
            console.log(`Botón editar pulsado en tarjeta para producto ${productId}`);
            handleEditProduct(productId);
        });
    }

    // Botón eliminar
    const deleteBtn = card.querySelector('.delete-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            const productId = this.dataset.id;
            console.log(`Botón eliminar pulsado en tarjeta para producto ${productId}`);
            handleDeleteProduct(productId);
        });
    }
}

// Exportar funciones
export {
    createProductCards,
    setupCardButtons
};