<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Iniciar o reanudar sesión
session_start();

// Configurar logging detallado para este script
logInfo("Iniciando process_step2.php", [
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
    'session_id' => session_id()
]);

// Función para enviar respuesta JSON
function sendJsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    logInfo("Enviando respuesta JSON", [
        'success' => $success,
        'message' => $message
    ]);

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Validar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError("Método no permitido", [
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
    sendJsonResponse(false, 'Método no permitido');
}

// Recuperar datos del paso 1 desde la sesión
$paso1_data = $_SESSION['paso1_data'] ?? null;

if (!$paso1_data) {
    logError("No se encontraron datos del paso 1 en la sesión", [
        'session_id' => session_id(),
        'session_data' => $_SESSION
    ]);
    sendJsonResponse(false, "No se encontraron los datos del paso 1. Por favor, vuelva a intentarlo.");
    exit;
}

// Obtener y validar los datos del paso 2
$username = isset($_POST['username']) ? trim($_POST['username']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$backup_email = isset($_POST['backup_email']) ? trim($_POST['backup_email']) : null;
$password = isset($_POST['password']) ? trim($_POST['password']) : '';
$confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
$local_fisico = isset($_POST['local_fisico']) ? ($_POST['local_fisico'] === 'Si' ? 'Si' : 'No') : 'No';

// Registrar los datos recibidos (sin incluir contraseñas)
logDebug("Datos del paso 2 recibidos", [
    'username' => $username,
    'email' => $email,
    'backup_email' => $backup_email,
    'local_fisico' => $local_fisico,
    'password_provided' => !empty($password),
    'confirm_password_provided' => !empty($confirm_password)
]);

// Validar que los campos obligatorios estén presentes
if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
    $campos_vacios = [];
    if (empty($username)) $campos_vacios[] = 'username';
    if (empty($email)) $campos_vacios[] = 'email';
    if (empty($password)) $campos_vacios[] = 'password';
    if (empty($confirm_password)) $campos_vacios[] = 'confirm_password';

    logError("Campos obligatorios faltantes en paso 2", [
        'campos_vacios' => $campos_vacios
    ]);
    sendJsonResponse(false, 'Todos los campos son obligatorios excepto el correo de respaldo');
}

// Validar que las contraseñas coincidan
if ($password !== $confirm_password) {
    logError("Las contraseñas no coinciden");
    sendJsonResponse(false, 'Las contraseñas no coinciden');
}

// Guardar los datos en la sesión para usarlos en el paso 3
$_SESSION['paso2_data'] = [
    'username' => $username,
    'email' => $email,
    'backup_email' => $backup_email,
    'password' => $password,
    'local_fisico' => $local_fisico
];

// Registrar en el log
logInfo("Datos del paso 2 guardados en sesión", [
    'session_id' => session_id(),
    'username' => $username,
    'email' => $email
]);

// Devolver respuesta exitosa
sendJsonResponse(true, 'Datos del paso 2 guardados correctamente');


