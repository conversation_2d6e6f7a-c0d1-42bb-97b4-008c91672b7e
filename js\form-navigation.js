/* =========================================
   NAVEGACIÓN DEL FORMULARIO DE REGISTRO
   ========================================= */

/**
 * Este archivo maneja toda la navegación entre pasos del formulario de registro.
 * Centraliza las funciones de avanzar y retroceder entre pasos para facilitar
 * el mantenimiento y evitar problemas de eventos duplicados.
 */

// Ejecutar cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando navegación del formulario');

    // Elementos principales
    const steps = document.querySelectorAll('.step');
    const formSteps = document.querySelectorAll('.form-step');

    console.log('Pasos encontrados:', steps.length);
    console.log('Pasos de formulario encontrados:', formSteps.length);

    // Esperar un momento para asegurarnos de que todo esté cargado
    setTimeout(function() {
        // Configurar todos los botones de navegación
        setupNavigationButtons();

        // Configurar los eventos para la selección de tipo de negocio
        setupBusinessTypeSelection();
    }, 500);

    /**
     * Configura todos los botones de navegación del formulario
     */
    function setupNavigationButtons() {
        console.log('Configurando botones de navegación');

        // Botón siguiente del paso 1
        setupButton('step1-next', function() {
            navigateToStep(2);
        });

        // Botón anterior del paso 2
        setupButton('#step2 .btn-prev', function() {
            navigateToStep(1);
        });

        // Botón siguiente del paso 2
        setupButton('step2-next', function() {
            navigateToStep(3);
        });

        // Botón anterior del paso 3
        setupButton('#step3 .btn-prev', function() {
            navigateToStep(2);
        });

        // Botón siguiente del paso 3
        setupButton('step3-next', function() {
            // Determinar qué tipo de negocio se seleccionó
            const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
            let tipoNegocioSeleccionado = '';

            tipoNegocioRadios.forEach(radio => {
                if (radio.checked) {
                    tipoNegocioSeleccionado = radio.value;
                }
            });

            console.log('Tipo de negocio seleccionado:', tipoNegocioSeleccionado);

            // Mostrar el paso 4 correspondiente según el tipo de negocio
            if (tipoNegocioSeleccionado === 'venta') {
                navigateToCustomStep('step4-productos');
            } else if (tipoNegocioSeleccionado === 'servicios' || tipoNegocioSeleccionado === 'arriendo') {
                navigateToCustomStep('step4-servicios');
            } else {
                alert('Por favor, selecciona un tipo de negocio antes de continuar.');
            }
        });

        // Botón anterior del paso 4A (productos)
        setupButton('#step4-productos .btn-prev', function() {
            navigateToStep(3);
        });

        // Botón anterior del paso 4B (servicios)
        setupButton('#step4-servicios .btn-prev', function() {
            navigateToStep(3);
        });

        // Botón siguiente del paso 4A (productos) para ir al paso 5
        // Comentado para evitar conflictos con el nuevo botón step4-next-save
        /*
        setupButton('submit-form', function() {
            const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

            if (!planSeleccionado) {
                alert('Por favor, seleccione un plan antes de continuar.');
                return;
            }

            if (planSeleccionado.value === 'gratuita') {
                // Mostrar el popup de bienvenida personalizado
                const welcomePopup = document.getElementById('welcome-popup');
                if (welcomePopup) {
                    welcomePopup.style.display = 'flex';
                    document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
                } else {
                    alert('Gracias por registrarte con el plan gratuito.');
                }
            } else {
                navigateToCustomStep('step5-pago');
            }
        });
        */

        // Botón siguiente del paso 4B (servicios) para ir al paso 5
        // Comentado para evitar conflictos con el nuevo botón step4-next-save
        /*
        setupButton('submit-form-servicios', function() {
            const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

            if (!planSeleccionado) {
                alert('Por favor, seleccione un plan antes de continuar.');
                return;
            }

            if (planSeleccionado.value === 'gratuita') {
                // Mostrar el popup de bienvenida personalizado
                const welcomePopup = document.getElementById('welcome-popup');
                if (welcomePopup) {
                    welcomePopup.style.display = 'flex';
                    document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
                } else {
                    alert('Gracias por registrarte con el plan gratuito.');
                }
            } else {
                navigateToCustomStep('step5-pago');
            }
        });
        */

        // Botón anterior del paso 5
        setupButton('#step5-pago .btn-prev', function() {
            const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
            let tipoNegocioSeleccionado = '';

            tipoNegocioRadios.forEach(radio => {
                if (radio.checked) {
                    tipoNegocioSeleccionado = radio.value;
                }
            });

            if (tipoNegocioSeleccionado === 'venta') {
                navigateToCustomStep('step4-productos');
            } else {
                navigateToCustomStep('step4-servicios');
            }
        });
    }

    /**
     * Configura los eventos para la selección de tipo de negocio
     */
    function setupBusinessTypeSelection() {
        console.log('Configurando eventos para selección de tipo de negocio');

        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');

        tipoNegocioRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('Tipo de negocio cambiado a:', this.value);
            });
        });

        // Nota: La funcionalidad de los pop-ups de planes se ha movido a plan-popups.js
    }

    /**
     * Configura un botón con un manejador de eventos
     * @param {string} selector - ID o selector CSS del botón
     * @param {Function} handler - Función a ejecutar cuando se hace clic en el botón
     */
    function setupButton(selector, handler) {
        // Determinar si es un ID o un selector CSS
        const isId = !selector.includes(' ') && !selector.includes('.') && !selector.includes('#');
        const button = isId ? document.getElementById(selector) : document.querySelector(selector);

        if (button) {
            console.log(`Configurando botón: ${selector}`);

            // Eliminar cualquier evento click previo
            const newButton = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newButton, button);
            }

            // Asegurarse de que el botón no esté deshabilitado
            newButton.disabled = false;
            newButton.removeAttribute('disabled');
            newButton.classList.remove('btn-disabled');

            // Agregar el nuevo manejador de eventos
            newButton.addEventListener('click', function(event) {
                event.preventDefault();
                console.log(`Botón clickeado: ${selector}`);
                handler();
                return false;
            });

            // Agregar también un manejador onclick directo como respaldo
            newButton.onclick = function(event) {
                event.preventDefault();
                console.log(`Botón clickeado (onclick directo): ${selector}`);
                handler();
                return false;
            };
        } else {
            // Solo mostrar error si no es un botón que sabemos que puede no existir
            if (selector !== 'submit-form' && selector !== 'submit-form-servicios') {
                console.error(`Botón no encontrado: ${selector}`);
            } else {
                console.log(`Botón opcional no encontrado: ${selector} (esto es normal si se usa otro botón)`);
            }
        }
    }

    /**
     * Navega a un paso específico
     * @param {number} stepNumber - Número del paso al que se quiere navegar
     */
    function navigateToStep(stepNumber) {
        console.log(`Navegando al paso ${stepNumber}`);

        if (stepNumber < 1 || stepNumber > formSteps.length) {
            console.error(`Número de paso inválido: ${stepNumber}`);
            return;
        }

        // Ocultar todos los pasos
        formSteps.forEach(step => {
            step.style.display = 'none';
            step.classList.remove('active');
        });

        steps.forEach(step => {
            step.classList.remove('active');
        });

        // Mostrar el paso seleccionado
        const currentStep = formSteps[stepNumber - 1];
        const currentStepIndicator = steps[stepNumber - 1];

        if (currentStep) {
            currentStep.style.display = 'block';
            currentStep.classList.add('active');
        } else {
            console.error(`No se encontró el paso ${stepNumber}`);
        }

        if (currentStepIndicator) {
            currentStepIndicator.classList.add('active');
        }

        console.log(`Paso ${stepNumber} mostrado`);
    }

    /**
     * Navega a un paso personalizado por su ID
     * @param {string} stepId - ID del paso al que se quiere navegar
     */
    function navigateToCustomStep(stepId) {
        console.log(`Navegando al paso personalizado: ${stepId}`);

        // Ocultar todos los pasos
        formSteps.forEach(step => {
            step.style.display = 'none';
            step.classList.remove('active');
        });

        steps.forEach(step => {
            step.classList.remove('active');
        });

        // Mostrar el paso específico
        const customStep = document.getElementById(stepId);
        if (customStep) {
            customStep.style.display = 'block';
            customStep.classList.add('active');

            // Activar el indicador del paso correspondiente
            if (stepId.includes('step4')) {
                if (steps.length > 3) {
                    steps[3].classList.add('active');
                }
            } else if (stepId.includes('step5')) {
                if (steps.length > 4) {
                    steps[4].classList.add('active');
                }
            }

            console.log(`Paso personalizado mostrado: ${stepId}`);
        } else {
            console.error(`Paso personalizado no encontrado: ${stepId}`);
        }
    }
});
