<?php
ob_start(); // Iniciar buffer de salida
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Debug information
error_log("Admin_products.php - Starting page load");
error_log("Admin_products.php - REQUEST_URI: " . $_SERVER['REQUEST_URI']);
error_log("Admin_products.php - HTTP_REFERER: " . ($_SERVER['HTTP_REFERER'] ?? 'No referer'));

// Incluir configuraciones
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Asegurar que tenemos una sesión válida
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

// Log de estado de sesión
error_log("Admin_products.php - Session ID: " . session_id());
error_log("Admin_products.php - Session data: " . print_r($_SESSION, true));

// Verificar autenticación
if (!$sessionManager->isLoggedIn()) {
    error_log("Admin_products.php - Usuario no autenticado");
    $currentUrl = urlencode($_SERVER['REQUEST_URI']);
    // Verificar si hay buffer para limpiar
    if (ob_get_length()) {
        ob_end_clean(); // Limpiar buffer solo si existe
    }
    header("Location: " . BASE_URL . "/public/login.php?error=unauthorized&reason=not_logged_in&return_to=" . $currentUrl);
    exit();
}

// Verificar rol de administrador o pro
if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], ['admin', 'pro'])) {
    error_log("Admin_products.php - Usuario no tiene permisos suficientes - Role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'no definido'));
    // Verificar si hay buffer para limpiar
    if (ob_get_length()) {
        ob_end_clean(); // Limpiar buffer solo si existe
    }
    header("Location: " . BASE_URL . "/public/login.php?error=unauthorized&reason=insufficient_permissions");
    exit();
}

// Actualizar tiempo de actividad
$sessionManager->updateActivity();

// Queries para cargar datos de productos
try {
    // Obtener lista de productos con sus categorías
    $sql_productos = "SELECT p.*, c.tipo_categoria, c.sub_categoria 
                     FROM tb_productos p 
                     LEFT JOIN tb_categorias c ON p.categoria_id = c.id 
                     ORDER BY p.id DESC";
    $result_productos = $conn->query($sql_productos);

    $productos = [];
    while ($row = $result_productos->fetch_assoc()) {
        $productos[] = $row;
    }
    
    error_log("Admin_products.php - Productos cargados: " . count($productos));
} catch (Exception $e) {
    error_log("Admin_products.php - Error al cargar productos: " . $e->getMessage());
}

// Se puede comenzar a enviar la salida HTML
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrar Productos</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="../css/header.css">
    <link rel="stylesheet" href="../css/admin_products.css?v=<?php echo filemtime('../css/admin_products.css'); ?>">
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <!-- Agregar depuración básica para responsive -->
  
</head>
<body>
    <?php include 'header.php'; ?>
    <div class="container" style="margin-top: 20px;">
        <div class="admin-actions">
            <button id="newProductBtn" class="new-product-btn" style="margin-top: 15px;">
                <i class="fas fa-plus"></i> Nuevo Producto
            </button>
            
            <div class="search-container">
                <div class="search-box">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchInput" placeholder="Buscar productos..." class="search-input">
                    <button id="clearSearch" class="clear-search"><i class="fas fa-times"></i></button>
                </div>
            </div>
        </div>

        <!-- Tabla de productos -->
        <div class="table-container">
            <div class="table-wrapper"> <!-- Add this wrapper -->
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 120px;">Acciones</th>
                            <th>ID</th>
                            <th>Nombre</th>
                            <th>Descripción</th>
                            <th>Precio</th>
                            <th>Imagen</th>
                            <th>Stock</th>
                            <th>Tipo</th>
                            <th>Subcategoría</th>
                            <th>Condición</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($productos as $producto): ?>
                            <tr data-id="<?php echo $producto['id']; ?>">
                                <td class="actions" data-label="Acciones">
                                    <div class="actions-container">
                                        <button onclick='openEditModal(<?php echo json_encode($producto); ?>)' title="Editar producto"><i class="fas fa-edit"></i></button>
                                        <button class="delete" onclick="deleteProduct(<?php echo $producto['id']; ?>)" title="Eliminar producto"><i class="fas fa-trash"></i></button>
                                    </div>
                                </td>
                                <td data-label="ID"><?php echo $producto['id']; ?></td>
                                <td data-label="Nombre" class="product-title"><?php echo $producto['nombre']; ?></td>
                                <td data-label="Descripción"><?php echo $producto['descripcion']; ?></td>
                                <td data-label="Precio" class="product-price"><?php echo '$ ' . number_format($producto['precio'], 0, ',', '.'); ?></td>
                                <td data-label="Imagen" class="product-image"><img src="<?php echo $producto['imagen']; ?>" alt="<?php echo $producto['nombre']; ?>" onclick="openImageModal('<?php echo $producto['imagen']; ?>')"></td>
                                <td data-label="Stock" <?php echo ($producto['stock'] < 5) ? 'class="low-stock product-stock"' : 'class="product-stock"'; ?>><?php echo $producto['stock']; ?></td>
                                <td data-label="Tipo"><?php echo $producto['tipo_categoria']; ?></td>
                                <td data-label="Subcategoría"><?php echo $producto['sub_categoria']; ?></td>
                                <td data-label="Condición"><?php echo $producto['condicion']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div> <!-- Close the wrapper -->
            <div id="noResults" class="no-results-message" style="display: none;">
                <i class="fas fa-search"></i>
                <p>No se encontraron productos que coincidan con tu búsqueda</p>
                <button id="clearSearchFromMessage" class="btn-clear-search">Limpiar búsqueda</button>
            </div>
        </div>

        <!-- Modal para Nuevo Producto -->
        <div id="newProductModal" class="product-modal">
            <div class="modal-content">
                <span class="modalClose">&times;</span>
                <h2>Nuevo Producto</h2>
                <form id="newProductForm" action="process_product.php" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="new_nombre">Nombre</label>
                        <input type="text" name="nombre" id="new_nombre" required>
                    </div>
                    <div class="form-group">
                        <label for="new_descripcion">Descripción</label>
                        <textarea name="descripcion" id="new_descripcion" rows="4" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group half-width">
                            <label for="new_precio">Precio</label>
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="text" name="precio" id="new_precio" required inputmode="numeric" min="0" 
                                       oninput="this.value = this.value.replace(/[^0-9]/g, '')" style="width: 100%;">
                            </div>
                        </div>
                        <div class="form-group half-width">
                            <label for="new_stock">Stock</label>
                            <input type="number" name="stock" id="new_stock" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="new_tipo">Tipo</label>
                        <select name="tipo" id="new_tipo" required>
                            <option value="">Seleccione un tipo</option>
                            <option value="Categoria">Categoría</option>
                            <option value="Servicios">Servicios</option>
                            <option value="Arriendo">Arriendo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="new_subcategoria">Subcategoría</label>
                        <select name="categoria_id" id="new_subcategoria" required>
                            <option value="">Primero seleccione un tipo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Condición del Producto:</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="destacado">
                                <span>Destacado</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="oferta">
                                <span>Oferta</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="liquidacion">
                                <span>Liquidación</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="nuevo">
                                <span>Nuevo</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="exclusivo">
                                <span>Exclusivo</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="ninguno" checked>
                                <span>Ninguno</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="new_imagen">Imagen</label>
                        <input type="file" name="imagen" id="new_imagen" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-save">Guardar</button>
                        <button type="button" class="btn-cancel" onclick="closeNewProductModal()">Cancelar</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal para Editar Producto -->
        <div id="editProductModal" class="product-modal">
            <div class="modal-content">
                <span class="modalClose">&times;</span>
                <h2>Editar Producto</h2>
                <form id="editProductForm" action="update_product.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="edit_id" id="edit_id">
                    <div class="form-group">
                        <label for="edit_nombre">Nombre</label>
                        <input type="text" name="nombre" id="edit_nombre" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_descripcion">Descripción</label>
                        <textarea name="descripcion" id="edit_descripcion" rows="4" required></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group half-width">
                            <label for="edit_precio">Precio producto</label>
                            <div style="display: flex; align-items: center;">                                
                                <input type="text" name="precio" id="edit_precio" value="" required style="flex: 1;">
                            </div>
                        </div>
                        <div class="form-group half-width">
                            <label for="edit_stock">Stock</label>
                            <input type="number" name="stock" id="edit_stock" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit_tipo">Tipo</label>
                        <select name="tipo" id="edit_tipo" required>
                            <option value="">Seleccione un tipo</option>
                            <option value="Categoria">Categoría</option>
                            <option value="Servicios">Servicios</option>
                            <option value="Arriendo">Arriendo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_subcategoria">Subcategoría</label>
                        <select name="categoria_id" id="edit_subcategoria" required>
                            <option value="">Primero seleccione un tipo</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Condición del Producto:</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="destacado">
                                <span>Destacado</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="oferta">
                                <span>Oferta</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="liquidacion">
                                <span>Liquidación</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="nuevo">
                                <span>Nuevo</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="exclusivo">
                                <span>Exclusivo</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="productCondition" value="ninguno" checked>
                                <span>Ninguno</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit_imagen">Nueva Imagen (opcional)</label>
                        <input type="file" name="imagen" id="edit_imagen">
                        <div id="current_image"></div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-save">Guardar Cambios</button>
                        <button type="button" class="btn-cancel" onclick="closeEditModal()">Cancelar</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal para ver imagen -->
        <div id="imageModal" class="image-modal">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img class="image-modal-content" id="imageModalContent">
        </div>

       


        <!-- Corregir la ruta al archivo JavaScript -->
        <script src="../js/admin_products.js"></script>
    </div>
</body>
</html>