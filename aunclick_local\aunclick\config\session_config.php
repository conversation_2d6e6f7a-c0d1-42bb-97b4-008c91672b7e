<?php
// First, check if config.php was already included
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/config.php';
}

// No need to include SessionManager twice
if (!class_exists('SessionManager')) {
    require_once __DIR__ . '/SessionManager.php';
}

// Intentemos asegurarnos de que no hay salida previa
if (ob_get_length()) {
    ob_clean();
}

// Error configuration (si no se ha configurado ya)
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Basic session constants - solo defina si no existen
if (!defined('SESSION_NAME')) {
    define('SESSION_NAME', 'VILLARRICA_SESSION');
}

if (!defined('SESSION_COOKIE_PATH')) {
    define('SESSION_COOKIE_PATH', COOKIE_PATH);
}

// SESSION_LIFETIME ya debería estar definido en config.php

// Session directory setup
$sessions_path = dirname(__DIR__) . '/sessions';
if (!is_dir($sessions_path)) {
    mkdir($sessions_path, 0755, true);
}

// Get server configuration
$server_name = $_SERVER['SERVER_NAME'] ?? '';
$is_ip_address = filter_var($server_name, FILTER_VALIDATE_IP) !== false;
$is_https = !empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off';
$cookie_domain = $is_ip_address ? '' : $server_name;

// Solo configure las opciones de sesión si los encabezados no han sido enviados
if (!headers_sent()) {
    // Establecer todas las configuraciones de sesión ANTES de cualquier salida
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.use_cookies', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.hash_function', 'sha256');
    ini_set('session.use_trans_sid', 0);
    ini_set('session.cache_limiter', 'nocache');
    ini_set('session.gc_probability', 1);
    ini_set('session.gc_divisor', 100);

    // Cookie parameters
    $cookie_params = [
        'lifetime' => SESSION_LIFETIME,
        'path' => SESSION_COOKIE_PATH,
        'domain' => $cookie_domain,
        'secure' => $is_https,
        'httponly' => true,
        'samesite' => 'Lax'
    ];

    // Configure session before starting
    session_name(SESSION_NAME);
    session_save_path($sessions_path);
    session_set_cookie_params($cookie_params);

    // Remove any existing session cookies
    if (isset($_COOKIE[SESSION_NAME])) {
        setcookie(SESSION_NAME, '', [
            'expires' => time() - 3600,
            'path' => SESSION_COOKIE_PATH,
            'domain' => $cookie_domain,
            'secure' => $is_https,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
        unset($_COOKIE[SESSION_NAME]);
    }

    if (isset($_COOKIE['PHPSESSID'])) {
        setcookie('PHPSESSID', '', [
            'expires' => time() - 3600,
            'path' => '/',
            'domain' => $cookie_domain,
            'secure' => $is_https,
            'httponly' => true,
            'samesite' => 'Lax'
        ]);
        unset($_COOKIE['PHPSESSID']);
    }
}

// Function to initialize session
function init_session() {
    static $initialized = false;
    
    if ($initialized) {
        return true;
    }
    
    if (session_status() === PHP_SESSION_NONE) {
        if (!headers_sent()) {
            session_start();
            
            // Initialize session data only if new or invalid
            if (!isset($_SESSION['created_at']) || !validate_session()) {
                $_SESSION = array(); // Clear any existing data
                $_SESSION['created_at'] = time();
                $_SESSION['last_activity'] = time();
                $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
                $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
                session_regenerate_id(true);
                error_log("Nueva sesión inicializada correctamente - ID: " . session_id());
            }
            
            $initialized = true;
            return true;
        } else {
            error_log("No se puede iniciar sesión, los encabezados ya han sido enviados");
            return false;
        }
    }
    
    return false;
}

// Función para validar sesión
function validate_session() {
    if (!isset($_SESSION['created_at'])) {
        error_log("Validate session failed: missing created_at");
        return false;
    }
    
    if (!isset($_SESSION['last_activity'])) {
        error_log("Validate session failed: missing last_activity");
        return false;
    }
    
    if (time() - $_SESSION['last_activity'] > 86400) {
        error_log("Validate session failed: session expired");
        return false;
    }
    
    if (!isset($_SESSION['ip']) || $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
        error_log("Validate session failed: IP mismatch");
        return false;
    }
    
    return true;
}

// Función para limpiar sesión
function clear_session() {
    $_SESSION = array();
    
    if (isset($_COOKIE[session_name()])) {
        $params = session_get_cookie_params();
        setcookie(
            session_name(),
            '',
            time() - 3600,
            $params['path'],
            $params['domain'],
            $params['secure'],
            $params['httponly']
        );
    }
    
    session_destroy();
    session_write_close();
}

// Función para verificar rol de administrador
function is_admin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// Función para verificar permisos de usuario
function check_permission($required_role) {
    if (!validate_session()) {
        return false;
    }
    
    if ($required_role === 'admin' && !is_admin()) {
        return false;
    }
    
    return true;
}

// Función para obtener información de la sesión actual
function get_session_info() {
    return [
        'id' => session_id(),
        'name' => session_name(),
        'status' => session_status(),
        'save_path' => session_save_path(),
        'cookie_params' => session_get_cookie_params(),
        'data' => isset($_SESSION) ? $_SESSION : []
    ];
}

// Función para validar ID de sesión
function isValidSessionId($session_id) {
    return preg_match('/^[a-zA-Z0-9,-]{22,256}$/', $session_id) === 1;
}

// Función para regenerar sesión de forma segura
function regenerateSessionSafely() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        $old_session_id = session_id();
        session_regenerate_id(true);
        $new_session_id = session_id();
        
        error_log("Regeneración de sesión: {$old_session_id} -> {$new_session_id}");
        return $new_session_id;
    }
    return false;
}

// Función para limpiar sesión expirada
function cleanExpiredSession() {
    if (isset($_SESSION) && isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 86400)) {
        session_unset();
        session_destroy();
        return true;
    }
    
    if (isset($_SESSION)) {
        $_SESSION['last_activity'] = time();
    }
    
    return false;
}

// Initialize session at the end if headers haven't been sent
if (!headers_sent()) {
    init_session();
    
    // Log session status for debugging
    if (session_status() === PHP_SESSION_ACTIVE) {
        error_log("Session_config.php - Sesión activa - ID: " . session_id());
        error_log("Session_config.php - Cookie parameters: " . print_r(session_get_cookie_params(), true));
        if (isset($_SESSION)) {
            error_log("Session_config.php - Session data: " . print_r($_SESSION, true));
        }
    } else {
        error_log("Session_config.php - ADVERTENCIA: La sesión no está activa");
    }
}

error_log("Session_config.php - Estado final de sesión - ID: " . session_id());
error_log("Session_config.php - Cookies actuales: " . print_r($_COOKIE, true));
if (isset($_SESSION)) {
    error_log("Session_config.php - Datos de sesión: " . print_r($_SESSION, true));
}
?>