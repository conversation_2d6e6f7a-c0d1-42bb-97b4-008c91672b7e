/**
 * JavaScript para el panel de administración de Webhooks
 */
document.addEventListener('DOMContentLoaded', function() {
    // Referencias a elementos del DOM
    const modal = document.getElementById('detailsModal');
    const detailsContent = document.getElementById('detailsContent');
    const closeModal = document.getElementsByClassName('close')[0];
    const detailButtons = document.querySelectorAll('.btn-details');
    const registerForm = document.getElementById('registerWebhookForm');
    const testForm = document.getElementById('testWebhookForm');
    const testResult = document.getElementById('testResult');
    const testResultContent = document.getElementById('testResultContent');
    const tipoEventoSelect = document.getElementById('tipoEvento');
    const datosEventoTextarea = document.getElementById('datosEvento');
    
    // Event listeners para botones de detalles
    detailButtons.forEach(button => {
        button.addEventListener('click', function() {
            const details = JSON.parse(this.getAttribute('data-details'));
            detailsContent.textContent = JSON.stringify(details, null, 2);
            modal.style.display = 'block';
        });
    });
    
    // Cerrar modal
    closeModal.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    // Cerrar modal al hacer clic fuera de él
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // Cambiar datos JSON al seleccionar tipo de evento
    if (tipoEventoSelect && datosEventoTextarea) {
        tipoEventoSelect.addEventListener('change', function() {
            const tipoSeleccionado = this.value;
            
            if (!tipoSeleccionado) return;
            
            // Obtener el JSON actual
            let jsonData;
            try {
                jsonData = JSON.parse(datosEventoTextarea.value);
            } catch (e) {
                jsonData = {};
            }
            
            // Actualizar el tipo de evento
            jsonData.tipo_evento = tipoSeleccionado;
            
            // Actualizar ejemplos según el tipo
            switch (tipoSeleccionado) {
                case 'pago_completado':
                    jsonData.id_transaccion = 'pay-' + Math.floor(Math.random() * 1000);
                    jsonData.datos_adicionales = {
                        importe: 100.50,
                        moneda: 'USD',
                        metodo_pago: 'tarjeta',
                        cliente: 'Cliente Ejemplo'
                    };
                    break;
                    
                case 'usuario_registrado':
                    jsonData.id_usuario = 'usr-' + Math.floor(Math.random() * 1000);
                    jsonData.datos_adicionales = {
                        email: '<EMAIL>',
                        nombre: 'Usuario Ejemplo',
                        fecha_registro: new Date().toISOString()
                    };
                    break;
                    
                case 'producto_actualizado':
                    jsonData.id_producto = 'prod-' + Math.floor(Math.random() * 1000);
                    jsonData.datos_adicionales = {
                        nombre: 'Producto Ejemplo',
                        precio: 49.99,
                        stock: 100,
                        categoria: 'Electrónica'
                    };
                    break;
                    
                case 'pedido_creado':
                    jsonData.id_pedido = 'ord-' + Math.floor(Math.random() * 1000);
                    jsonData.datos_adicionales = {
                        cliente: 'Cliente Ejemplo',
                        items: [
                            { id: 'prod-1', cantidad: 2, precio: 29.99 },
                            { id: 'prod-2', cantidad: 1, precio: 59.99 }
                        ],
                        total: 119.97
                    };
                    break;
                    
                case 'pedido_enviado':
                    jsonData.id_pedido = 'ord-' + Math.floor(Math.random() * 1000);
                    jsonData.datos_adicionales = {
                        tracking: 'TR' + Math.floor(Math.random() * 1000000),
                        transportista: 'Transportes Rápidos',
                        fecha_envio: new Date().toISOString(),
                        fecha_entrega_estimada: new Date(Date.now() + 3*24*60*60*1000).toISOString()
                    };
                    break;
            }
            
            // Actualizar el textarea
            datosEventoTextarea.value = JSON.stringify(jsonData, null, 4);
        });
    }
    
    // Manejar envío del formulario de registro
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Aquí se implementaría la lógica para registrar el webhook
            const formData = new FormData(this);
            const webhookUrl = formData.get('webhookUrl');
            const eventos = formData.getAll('eventos[]');
            const descripcion = formData.get('descripcion');
            
            // Validar que se hayan seleccionado eventos
            if (eventos.length === 0) {
                alert('Debes seleccionar al menos un evento');
                return;
            }
            
            // Simular registro exitoso (aquí se enviaría a un endpoint real)
            alert(`Webhook registrado exitosamente para los eventos: ${eventos.join(', ')}`);
            this.reset();
        });
    }
    
    // Manejar envío del formulario de prueba
    if (testForm) {
        testForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const url = this.testUrl.value;
            let datos;
            
            try {
                datos = JSON.parse(this.datosEvento.value);
            } catch (error) {
                alert('El JSON ingresado no es válido');
                return;
            }
            
            // Mostrar indicador de carga
            testResultContent.textContent = 'Enviando prueba...';
            testResult.classList.remove('hidden');
            
            // Simular envío (aquí se enviaría a un endpoint real)
            setTimeout(() => {
                // Simular respuesta
                const respuesta = {
                    success: true,
                    timestamp: new Date().toISOString(),
                    url: url,
                    datos_enviados: datos,
                    respuesta: {
                        status: 200,
                        message: 'Webhook recibido exitosamente'
                    }
                };
                
                // Mostrar resultado
                testResultContent.textContent = JSON.stringify(respuesta, null, 2);
            }, 1500);
        });
    }
});
