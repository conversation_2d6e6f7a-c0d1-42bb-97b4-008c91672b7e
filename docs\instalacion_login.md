# Guía de Instalación del Nuevo Sistema de Login

## Requisitos Previos

- PHP 7.4 o superior
- MySQL 5.7 o superior
- Extensiones PHP:
  - mysqli
  - session
  - openssl
  - mbstring

## Pasos para la Instalación

### 1. Actualización de la Base de Datos

Ejecutar el siguiente script SQL para agregar la tabla de tokens de autenticación:

```sql
-- Tabla para almacenar tokens de autenticación (recordarme, reset contraseña)
CREATE TABLE IF NOT EXISTS auth_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    selector VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires DATETIME NOT NULL,
    type ENUM('remember', 'reset', 'activation') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (selector),
    INDEX (user_id),
    INDEX (expires),
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

Asegurarse de que la tabla `login_attempts` existe:

```sql
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    user_id INT NULL,
    ip VARCHAR(45) NOT NULL,
    user_agent VARCHAR(255) NOT NULL,
    success TINYINT(1) NOT NULL DEFAULT 0,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (username),
    INDEX (ip),
    INDEX (user_id),
    INDEX (attempt_time)
);
```

### 2. Reemplazo de Archivos

Los siguientes archivos deben reemplazarse o agregarse:

1. `/config/AuthService.php` (nuevo)
2. `/config/SecurityService.php` (nuevo)
3. `/config/LoginHandler.php` (nuevo)
4. `/config/SessionManager.php` (reemplazar)
5. `/config/process_login.php` (nuevo)
6. `/config/process_logout.php` (nuevo)
7. `/public/login.php` (reemplazar)
8. `/public/logout.php` (reemplazar)

### 3. Configuración

Verificar y ajustar las siguientes configuraciones en `/config/config.php`:

```php
// Establecer la ruta base correcta según el entorno
$isProduction = $_SERVER['HTTP_HOST'] === '**************';
$basePath = $isProduction ? '' : '/villarrica_click';
$cookieDomain = $isProduction ? '' : '';

// Definir las constantes
define('BASE_URL', $basePath);
define('DOMAIN', $_SERVER['HTTP_HOST']);
define('COOKIE_PATH', $cookiePath);
define('COOKIE_DOMAIN', $cookieDomain);
define('SESSION_NAME', 'VILLARRICA_SID');
define('SESSION_LIFETIME', 86400);  // 24 horas en segundos
```

### 4. Verificación

Después de la instalación, verificar que:

1. El archivo `/public/login.php` se carga correctamente
2. Los usuarios pueden iniciar sesión con credenciales correctas
3. Los usuarios no pueden iniciar sesión con credenciales incorrectas
4. La sesión se mantiene al navegar entre páginas
5. El cierre de sesión funciona correctamente

### 5. Migración de Contraseñas

Si las contraseñas actuales están en texto plano, el sistema las actualizará a bcrypt automáticamente cuando los usuarios inicien sesión.

Para forzar la actualización de todas las contraseñas, ejecutar este script:

```php
<?php
require_once 'config/config.php';

$stmt = $conn->prepare("SELECT id, password FROM users WHERE password NOT LIKE '$2y$%'");
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    // Solo para la migración, asumimos que la contraseña en texto plano es el valor actual
    $plainPassword = $row['password'];
    $hash = password_hash($plainPassword, PASSWORD_BCRYPT, ['cost' => 12]);
    
    $updateStmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $updateStmt->bind_param("si", $hash, $row['id']);
    $updateStmt->execute();
    
    echo "Usuario ID {$row['id']} actualizado a bcrypt.<br>";
}

echo "Migración completada.";
?>
```

> ⚠️ **ADVERTENCIA**: Este script solo debe ejecutarse una vez durante la migración.

## Solución de Problemas

### Problemas con las Sesiones

Si las sesiones no se mantienen:

1. Verificar la configuración `SESSION_NAME` y `COOKIE_PATH`
2. Comprobar que el dominio para cookies está configurado correctamente
3. Verificar que PHP tiene permisos para escribir en la ruta de sesiones

### Problemas con HTTPS

Si utiliza HTTPS:

1. Asegurarse de que la detección de HTTPS funciona correctamente
2. Verificar que las cookies tienen el flag "Secure"

### Bloqueo de Cuenta

Si un usuario está bloqueado:

1. Puede desbloquear manualmente ejecutando:

```sql
DELETE FROM login_attempts 
WHERE username = 'nombre_usuario' AND success = 0;
```

## Configuración Avanzada

### Ajuste de Seguridad de Contraseñas

Para modificar los requisitos de contraseñas, editar en `AuthService.php`:

```php
const PASSWORD_MIN_LENGTH = 8;
```

### Tiempo de Bloqueo por Intentos Fallidos

Para ajustar el tiempo de bloqueo, modificar en `AuthService.php`:

```php
const LOCKOUT_TIME = 900;     // Tiempo de bloqueo en segundos (15 minutos)
```

### Regeneración de ID de Sesión

Para ajustar la probabilidad de regeneración aleatoria de ID de sesión, modificar en `SessionManager.php`:

```php
const SESSION_REGENERATE_PROBABILITY = 10; // 10% de probabilidad
```