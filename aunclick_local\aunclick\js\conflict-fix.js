/**
 * Script para solucionar conflictos entre los diferentes scripts que manejan el sidebar
 * Este script se ejecuta después de todos los demás scripts para asegurar que
 * no haya errores en la consola
 *
 * Versión mejorada compatible con el nuevo botón toggle animado
 */
(function() {
    // Crear un namespace aislado para evitar conflictos
    const ConflictFix = {
        // Referencias a elementos
        dummyToggle: null,
        dummyIcon: null,
        realToggle: null,
        sidebar: null,

        // Inicializar el módulo
        init: function() {
            // Ejecutar cuando el DOM esté listo
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', this.onDOMReady.bind(this));
            } else {
                // El DOM ya está cargado
                this.onDOMReady();
            }

            // También ejecutar cuando la página esté completamente cargada
            window.addEventListener('load', this.onPageLoad.bind(this));
        },

        // Cuando el DOM esté listo
        onDOMReady: function() {
            console.log('ConflictFix: Inicializando...');

            // Crear elementos dummy
            this.createDummyElements();

            // Obtener referencias a elementos reales
            this.realToggle = document.getElementById('header-aside-toggle');
            this.sidebar = document.getElementById('sidebar');

            // Redirigir eventos
            this.redirectEvents();

            // Desactivar event listeners conflictivos
            this.disableConflictingEventListeners();

            // Ajustar el margen del contenido principal
            this.adjustMainContentMargin();

            console.log('ConflictFix: Inicializado correctamente');
        },

        // Cuando la página esté completamente cargada
        onPageLoad: function() {
            // Volver a ejecutar las funciones para asegurar que todo funcione
            this.createDummyElements();
            this.redirectEvents();
            this.disableConflictingEventListeners();

            // Parchar funciones globales que puedan causar errores
            this.patchGlobalFunctions();

            // Ajustar el margen del contenido principal
            this.adjustMainContentMargin();

            console.log('ConflictFix: Configuración completada después de cargar la página');
        },

        // Crear elementos dummy para evitar errores
        createDummyElements: function() {
            // Si el elemento #aside-toggle no existe, crear uno oculto
            if (!document.getElementById('aside-toggle')) {
                this.dummyToggle = document.createElement('button');
                this.dummyToggle.id = 'aside-toggle';
                this.dummyToggle.style.display = 'none';
                this.dummyToggle.setAttribute('aria-hidden', 'true');

                // Crear el icono dentro del botón
                this.dummyIcon = document.createElement('i');
                this.dummyIcon.id = 'toggle-icon';
                this.dummyIcon.className = 'fas fa-bars';
                this.dummyToggle.appendChild(this.dummyIcon);

                // Añadir al body
                document.body.appendChild(this.dummyToggle);

                console.log('ConflictFix: Elementos dummy creados para evitar errores');
            } else {
                this.dummyToggle = document.getElementById('aside-toggle');
                this.dummyIcon = document.getElementById('toggle-icon');
            }
        },

        // Desactivar event listeners que causan errores
        disableConflictingEventListeners: function() {
            // Crear una función vacía para capturar los errores
            const noop = function() {};

            // Reemplazar funciones que puedan causar errores
            if (window.toggleSidebar && typeof window.toggleSidebar === 'function') {
                const originalToggleSidebar = window.toggleSidebar;
                window.toggleSidebar = function() {
                    try {
                        return originalToggleSidebar.apply(this, arguments);
                    } catch (e) {
                        console.warn('ConflictFix: Error capturado en toggleSidebar:', e);
                        return false;
                    }
                };
            }

            // Proteger contra errores en event listeners de document
            const originalAddEventListener = document.addEventListener;
            document.addEventListener = function(type, listener, options) {
                if (type === 'click') {
                    const safeListener = function(event) {
                        try {
                            return listener.call(this, event);
                        } catch (e) {
                            console.warn('ConflictFix: Error capturado en document click listener:', e);
                            return false;
                        }
                    };
                    return originalAddEventListener.call(this, type, safeListener, options);
                }
                return originalAddEventListener.call(this, type, listener, options);
            };
        },

        // Redirigir eventos del botón dummy al botón real
        redirectEvents: function() {
            if (this.dummyToggle && this.realToggle) {
                // Eliminar event listeners anteriores
                const newDummyToggle = this.dummyToggle.cloneNode(true);
                this.dummyToggle.parentNode.replaceChild(newDummyToggle, this.dummyToggle);
                this.dummyToggle = newDummyToggle;

                // Actualizar referencia al icono
                this.dummyIcon = this.dummyToggle.querySelector('#toggle-icon');

                // Añadir nuevo event listener
                this.dummyToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    // Simular clic en el botón real
                    this.realToggle.click();
                });

                console.log('ConflictFix: Eventos redirigidos correctamente');
            }
        },

        // Ajustar el margen del contenido principal
        adjustMainContentMargin: function() {
            if (!this.sidebar) return;

            const mainContent = document.querySelector('.main-content');
            if (!mainContent) return;

            if (window.innerWidth > 992) {
                // En desktop
                if (this.sidebar.classList.contains('collapsed')) {
                    mainContent.style.marginLeft = '70px';
                    mainContent.style.width = 'calc(100% - 70px)';
                } else {
                    mainContent.style.marginLeft = '250px';
                    mainContent.style.width = 'calc(100% - 250px)';
                }
            } else {
                // En móvil
                mainContent.style.marginLeft = '70px';
                mainContent.style.width = 'calc(100% - 70px)';
            }
        },

        // Parchar funciones globales que puedan causar errores
        patchGlobalFunctions: function() {
            // Proteger contra errores en main.js
            if (window.addEventListener) {
                const originalWindowAddEventListener = window.addEventListener;
                window.addEventListener = function(type, listener, options) {
                    if (type === 'error') {
                        const safeListener = function(event) {
                            try {
                                return listener.call(this, event);
                            } catch (e) {
                                console.warn('ConflictFix: Error capturado en window error listener:', e);
                                return false;
                            }
                        };
                        return originalWindowAddEventListener.call(this, type, safeListener, options);
                    }
                    return originalWindowAddEventListener.call(this, type, listener, options);
                };
            }
        }
    };

    // Iniciar el módulo
    ConflictFix.init();
})();
