<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Función para mostrar mensajes
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 10px 0; border-radius: 5px; ' . 
         ($isError ? 'background-color: #ffebee; color: #c62828;' : 'background-color: #e8f5e9; color: #2e7d32;') . 
         '">' . $message . '</div>';
}

// Probar conexión a la base de datos
echo '<h2>Prueba de conexión a la base de datos</h2>';

try {
    // Registrar inicio de la prueba
    logInfo("Iniciando prueba de conexión a la base de datos");
    
    // Obtener conexión a la base de datos
    $conn = getDbConnection();
    
    if (!$conn) {
        logError("No se pudo obtener conexión a la base de datos");
        showMessage('Error: No se pudo conectar a la base de datos.', true);
        exit;
    }
    
    logInfo("Conexión exitosa a la base de datos");
    showMessage('Conexión exitosa a la base de datos.');
    
    // Verificar si la tabla tb_registros existe
    $sql = "SHOW TABLES LIKE 'tb_registros'";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 0) {
        logError("La tabla tb_registros no existe");
        showMessage('Error: La tabla tb_registros no existe.', true);
        exit;
    }
    
    logInfo("La tabla tb_registros existe");
    showMessage('La tabla tb_registros existe.');
    
    // Verificar si la columna planSuscripcion existe en la tabla tb_registros
    $sql = "SHOW COLUMNS FROM tb_registros LIKE 'planSuscripcion'";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 0) {
        logError("La columna planSuscripcion no existe en la tabla tb_registros");
        showMessage('Error: La columna planSuscripcion no existe en la tabla tb_registros.', true);
        exit;
    }
    
    logInfo("La columna planSuscripcion existe en la tabla tb_registros");
    showMessage('La columna planSuscripcion existe en la tabla tb_registros.');
    
    // Mostrar la estructura de la tabla tb_registros
    $sql = "DESCRIBE tb_registros";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo '<h3>Estructura de la tabla tb_registros</h3>';
        echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">';
        echo '<tr style="background-color: #f5f5f5;"><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>';
        
        while($row = $result->fetch_assoc()) {
            echo '<tr>';
            echo '<td>' . $row["Field"] . '</td>';
            echo '<td>' . $row["Type"] . '</td>';
            echo '<td>' . $row["Null"] . '</td>';
            echo '<td>' . $row["Key"] . '</td>';
            echo '<td>' . $row["Default"] . '</td>';
            echo '<td>' . $row["Extra"] . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        
        logInfo("Se mostró la estructura de la tabla tb_registros");
    } else {
        logError("No se pudo obtener la estructura de la tabla tb_registros");
        showMessage('No se pudo obtener la estructura de la tabla tb_registros.', true);
    }
    
    // Probar una inserción simple
    echo '<h3>Prueba de inserción</h3>';
    
    // Crear datos de prueba
    $test_data = [
        'nombres' => 'Test',
        'apellidos' => 'Usuario',
        'rut' => '12345678-9',
        'fechaNacimiento' => '01/01/1990',
        'sexo' => 'Masculino',
        'telefono' => '1234567890',
        'region' => 'Test Region',
        'comuna' => 'Test Comuna',
        'direccion' => 'Test Direccion 123',
        'NombreUsuario' => 'test_user_' . time(),
        'mail' => 'test_' . time() . '@example.com',
        'mailRespaldo' => null,
        'contraseña' => password_hash('test123', PASSWORD_DEFAULT),
        'localFisico' => 'Si',
        'nombreNegocio' => 'Test Negocio',
        'telefonoNegocio' => '1234567890',
        'whatsappNegocio' => '1234567890',
        'tipoNegocio' => 'Venta',
        'descripcionNegocio' => 'Test Descripcion',
        'planSuscripcion' => 'Gratuita'
    ];
    
    // Preparar la consulta SQL
    $sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion, 
                                     NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                     nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                     planSuscripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    logInfo("Preparando consulta SQL para inserción de prueba", ['sql' => $sql]);
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        logError("Error al preparar la consulta", ['error' => $conn->error]);
        showMessage('Error al preparar la consulta: ' . $conn->error, true);
        exit;
    }
    
    logInfo("Consulta preparada correctamente");
    
    $stmt->bind_param("ssssssssssssssssssss", 
        $test_data['nombres'], $test_data['apellidos'], $test_data['rut'], 
        $test_data['fechaNacimiento'], $test_data['sexo'], $test_data['telefono'], 
        $test_data['region'], $test_data['comuna'], $test_data['direccion'],
        $test_data['NombreUsuario'], $test_data['mail'], $test_data['mailRespaldo'], $test_data['contraseña'], $test_data['localFisico'],
        $test_data['nombreNegocio'], $test_data['telefonoNegocio'], $test_data['whatsappNegocio'], 
        $test_data['tipoNegocio'], $test_data['descripcionNegocio'],
        $test_data['planSuscripcion']);
    
    logInfo("Parámetros vinculados correctamente");
    
    if ($stmt->execute()) {
        logInfo("Inserción exitosa", ['insert_id' => $conn->insert_id]);
        showMessage('Inserción exitosa. ID: ' . $conn->insert_id);
    } else {
        logError("Error al ejecutar la consulta", ['error' => $stmt->error]);
        showMessage('Error al ejecutar la consulta: ' . $stmt->error, true);
    }
    
    // Cerrar conexión
    $stmt->close();
    $conn->close();
    
    logInfo("Prueba de conexión a la base de datos finalizada");
    
} catch (Exception $e) {
    logException($e, ['context' => 'test_db_connection']);
    showMessage('Error: ' . $e->getMessage(), true);
}
?>
