/* =========================================
   LAYOUT PRINCIPAL
   ========================================= */
   .admin-layout {
    display: flex;
    min-height: 100vh;
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: all 0.3s ease;
}

/* Contenedor normal (cuando el aside está expandido) */
.container {
    padding: 20px;
    /* max-width: var(--max-width); */
    margin: 0 auto;
    transition: all 0.3s ease;
}

/* Contenedor cuando el aside está contraído */
aside.collapsed + .main-content .container {
    max-width: none;
    margin: 0;
}

/* Barra lateral */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(to bottom, var(--purple-primary), var(--purple-dark));
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-logo {
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 20px;
    color: white;
    margin-bottom: 5px;
}

.sidebar-subtitle {
    font-size: 12px;
    color: var(--yellow-accent);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Información del usuario en la barra lateral */
.sidebar-user {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

/* Avatar del usuario */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--purple-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.user-avatar i {
    font-size: 20px;
    color: white;
}

/* Información textual del usuario */
.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-role {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
}

/* Navegación en la barra lateral */
.sidebar-nav {
    padding: 20px 0;
}

/* Secciones de navegación */
.nav-section {
    margin-bottom: 15px;
}

.nav-section-title {
    padding: 10px 20px;
    font-size: 12px;
    text-transform: uppercase;
    color: rgba(255,255,255,0.5);
    letter-spacing: 1px;
}

.nav-items {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

/* Enlaces de navegación */
.nav-link {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover, .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-left-color: var(--yellow-accent);
}

.nav-link i {
    margin-right: 10px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* =========================================
   PESTAÑAS PRINCIPALES
   ========================================= */
/* Pestañas principales - Navegación entre secciones principales */
.main-tabs {
    display: flex;
    background-color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.tab-btn {
    padding: 15px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.tab-btn i {
    font-size: 16px;
    color: var(--purple-primary);
}

.tab-btn:hover {
    background-color: var(--gray-bg);
}

.tab-btn.active {
    border-bottom-color: var(--yellow-accent);
    color: var(--purple-primary);
}

.tab-btn.active i {
    color: var(--yellow-accent);
}

/* =========================================
   MIGAS DE PAN (BREADCRUMBS)
   ========================================= */
/* Migas de pan - Muestra la ruta de navegación actual */
.breadcrumbs {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumbs a {
    color: var(--text-secondary);
    text-decoration: none;
}

.breadcrumbs a:hover {
    color: var(--purple-primary);
}

.breadcrumbs .separator {
    margin: 0 10px;
    color: var(--text-secondary);
}

.breadcrumbs .current {
    color: var(--purple-primary);
    font-weight: 600;
}

/* =========================================
   CONTENEDOR DE SECCIÓN
   ========================================= */
.content-section {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.section-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-title i {
    color: var(--purple-primary);
}

/* Acciones de sección */
.section-actions {
    display: flex;
    gap: 10px;
}

.section-body {
    padding: 2px;
}

/* =========================================
   TARJETAS DE ESTADÍSTICAS
   ========================================= */
/* Contenedor de tarjetas de estadísticas - Organiza las tarjetas en una cuadrícula */
.stats-grid {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

/* Tarjeta individual de estadística */
.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;
}

/* Iconos de estadísticas */
.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.stat-icon.products {
    background-color: rgba(106, 27, 154, 0.1);
    color: var(--purple-primary);
}

.stat-icon.sales {
    background-color: rgba(67, 160, 71, 0.1);
    color: var(--green-primary);
}

.stat-icon.users {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--blue-info);
}

.stat-icon.views {
    background-color: rgba(255, 213, 79, 0.1);
    color: #ff9800;
}

/* Información de estadísticas */
.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* =========================================
   TARJETAS DE CATEGORÍAS
   ========================================= */
/* Cuadrícula de categorías - Organiza las tarjetas de categorías */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

/* Tarjeta de categoría */
.category-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.category-header {
    height: 80px;
    background: linear-gradient(to right, var(--purple-light), var(--purple-primary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 30px;
}

.category-body {
    padding: 15px;
}

.category-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.category-count {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.category-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

/* Estilos para texto de categoría fijo */
.category-text {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 500;
}

/* =========================================
   TARJETAS DE GRÁFICOS
   ========================================= */
.chart-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px !important; /* Reducido de 20px a 15px */
    margin-bottom: 15px;
}

.chart-container {
    position: relative;
    height: 200px !important; /* Reducido de 300px a 250px */
    width: 90%;
}

.chart-card h3 {
    margin-bottom: 10px !important; /* Reducido de 15px a 10px */
    font-size: 15px !important; /* Reducido de 16px a 15px */
    font-weight: 500;
}

/* =========================================
   FORMULARIO - LAYOUT
   ========================================= */
/* Cuadrícula de formulario - Organiza el formulario en dos columnas */
.form-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

/* Sección de formulario */
.form-section {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.form-section-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-light);
}

.form-section-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-section-body {
    padding: 20px;
}

/* =========================================
   DROPZONE PARA IMÁGENES
   ========================================= */
/* Dropzone - Área para arrastrar y soltar imágenes */
.dropzone {
    border: 2px dashed var(--gray-medium);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.dropzone:hover {
    border-color: var(--purple-light);
    background-color: rgba(106, 27, 154, 0.02);
}

.dropzone-icon {
    font-size: 40px;
    color: var(--gray-medium);
    margin-bottom: 10px;
}

.dropzone-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.dropzone-hint {
    font-size: 13px;
    color: var(--text-secondary);
}

/* =========================================
   GALERÍA DE IMÁGENES
   ========================================= */
/* Galería de imágenes - Muestra miniaturas de imágenes */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
}

.image-item {
    position: relative;
    border-radius: 5px;
    overflow: hidden;
    height: 100px;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.image-action-btn {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.image-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

/* =========================================
   PANEL DE FILTROS FLOTANTE
   ========================================= */
.filter-container {
    position: fixed;
    top: 0;
    right: -400px;
    width: 350px;
    height: 100vh;
    background-color: white;
    box-shadow: -5px 0 15px rgba(0,0,0,0.1);
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow: hidden;
}

.filter-container.show {
    transform: translateX(0);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(45deg, var(--purple-primary), var(--purple-light));
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-header h3 {
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(106, 27, 154, 0.1);
    position: sticky;
    top: 60px; /* Justo debajo del header */
    z-index: 5;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.filter-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 120px); /* Ajustado para considerar header y acciones */
}

/* Media queries para responsive */
@media screen and (max-width: 992px) {
    .section-body {
        padding: 0;
    }
}