<?php
// Asegurar que no haya output antes de los headers
if (ob_get_level()) ob_end_clean();

// Habilitar registro de errores en un archivo
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', $_SERVER['DOCUMENT_ROOT'] . '/projects/villarrica_click/error_log.txt');
error_log("=== Inicio de solicitud save_product.php - " . date('Y-m-d H:i:s') . " ===");

// Registrar detalles de la solicitud
error_log("Método HTTP: " . $_SERVER['REQUEST_METHOD']);
error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'No especificado'));

// Configurar headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true'); // Permitir credenciales en CORS

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../../config/config.php';
require_once '../../../config/SessionManager.php';

// Inicializar el gestor de sesiones y verificar estado de la sesión
$sessionManager = SessionManager::getInstance();

// Registrar información de la sesión para depuración
error_log("Session ID al inicio: " . session_id());
error_log("Estado de la sesión: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVA' : 'NO ACTIVA'));

// Asegurar que la sesión está activa
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
    error_log("Sesión inicializada, nuevo ID: " . session_id());
}

// Verificar autenticación
if (!$sessionManager->isLoggedIn()) {
    error_log("save_product.php - Usuario no autenticado");
    error_log("Session data: " . print_r($_SESSION, true));
    echo json_encode(['success' => false, 'message' => 'No autorizado - Sesión no válida']);
    exit;
}

// Obtener el ID del usuario de la sesión
if (!isset($_SESSION['user_id'])) {
    error_log("save_product.php - user_id no está definido en la sesión");
    error_log("Session data: " . print_r($_SESSION, true));
    echo json_encode(['success' => false, 'message' => 'No autorizado - ID de usuario no encontrado']);
    exit;
}

$user_id = $_SESSION['user_id'];
error_log("save_product.php - user_id de la sesión: " . $user_id);

try {
    // Verificar el método HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        error_log("save_product.php - Método no permitido: " . $_SERVER['REQUEST_METHOD']);
        throw new Exception('Método no permitido: ' . $_SERVER['REQUEST_METHOD']);
    }

    // Obtener y validar los datos
    $raw_input = file_get_contents('php://input');
    error_log("save_product.php - Datos de entrada: " . $raw_input);

    $data = json_decode($raw_input, true);
    if (!$data) {
        error_log("save_product.php - Error decodificando JSON: " . json_last_error_msg());
        throw new Exception('Datos inválidos: ' . json_last_error_msg());
    }

    error_log("save_product.php - Datos decodificados: " . print_r($data, true));

    // Validación extendida de campos requeridos
    $required_fields = [
        'productName' => 'Nombre del producto',
        'productDescription' => 'Descripción',
        'productPrice' => 'Precio',
        'productStock' => 'Stock',
        'productTipoCategoria' => 'Tipo de categoría',
        'productCategory' => 'Categoría',
        'negocio_id' => 'Negocio'
    ];

    $missing_fields = [];
    $invalid_fields = [];

    foreach ($required_fields as $field => $label) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            $missing_fields[] = $label;
            error_log("save_product.php - Campo faltante: {$field}");
        }
    }

    // Validaciones específicas de tipo de dato
    if (isset($data['productPrice']) && !is_numeric(str_replace(['$', '.', ','], '', $data['productPrice']))) {
        $invalid_fields[] = 'El precio debe ser un valor numérico';
    }
    if (isset($data['productStock']) && !is_numeric($data['productStock'])) {
        $invalid_fields[] = 'El stock debe ser un valor numérico';
    }

    // Si hay campos faltantes o inválidos, lanzar excepción
    if (!empty($missing_fields) || !empty($invalid_fields)) {
        $error_message = '';
        if (!empty($missing_fields)) {
            $error_message .= "Campos requeridos faltantes: " . implode(', ', $missing_fields) . ". ";
        }
        if (!empty($invalid_fields)) {
            $error_message .= "Campos inválidos: " . implode(', ', $invalid_fields);
        }
        error_log("save_product.php - Validación fallida: " . $error_message);
        throw new Exception($error_message);
    }

    // Validar el valor de condicion
    $allowed_conditions = ['destacado', 'oferta', 'liquidacion', 'nuevo', 'exclusivo', 'ninguno'];
    $condicion = strtolower($data['productCondition'] ?? 'ninguno');

    if (!in_array($condicion, $allowed_conditions)) {
        error_log("save_product.php - Valor de condición inválido: {$condicion}");
        throw new Exception('El valor de condición debe ser uno de: ' . implode(', ', $allowed_conditions));
    }

    // Limpiar y preparar los datos
    $nombre = $conn->real_escape_string($data['productName']);
    $descripcion = $conn->real_escape_string($data['productDescription']);
    $descripcion_corta = $conn->real_escape_string($data['productShortDescription'] ?? '');
    // Limpiar y convertir el precio correctamente
    $precio_raw = $data['productPrice'];
    // Mantener el punto decimal pero eliminar otros caracteres no numéricos
    $precio_limpio = preg_replace('/[^0-9.]/', '', $precio_raw);
    // Asegurar que solo hay un punto decimal (el último)
    if (substr_count($precio_limpio, '.') > 1) {
        $partes = explode('.', $precio_limpio);
        $decimal = array_pop($partes); // Última parte (decimal)
        $entero = implode('', $partes); // Unir partes enteras sin puntos
        $precio_limpio = $entero . '.' . $decimal;
    }
    $precio = floatval($precio_limpio);
    error_log("Precio procesado: {$precio_raw} -> {$precio_limpio} -> {$precio}");
    // Limpiar y convertir el precio original correctamente
    $precio_original = null;
    if (isset($data['productOriginalPrice']) && !empty($data['productOriginalPrice'])) {
        $precio_original_raw = $data['productOriginalPrice'];
        // Mantener el punto decimal pero eliminar otros caracteres no numéricos
        $precio_original_limpio = preg_replace('/[^0-9.]/', '', $precio_original_raw);
        // Asegurar que solo hay un punto decimal (el último)
        if (substr_count($precio_original_limpio, '.') > 1) {
            $partes = explode('.', $precio_original_limpio);
            $decimal = array_pop($partes); // Última parte (decimal)
            $entero = implode('', $partes); // Unir partes enteras sin puntos
            $precio_original_limpio = $entero . '.' . $decimal;
        }
        $precio_original = floatval($precio_original_limpio);
        error_log("Precio original procesado: {$precio_original_raw} -> {$precio_original_limpio} -> {$precio_original}");
    }
    $stock = intval($data['productStock']);
    $sku = $conn->real_escape_string($data['productSKU'] ?? '');
    $estado = 'borrador'; // Valor por defecto
    $id_tipo_categoria = intval($data['productTipoCategoria']); // Nuevo campo: tipo de categoría
    $categoria_id = intval($data['productCategory']);
    $subcategoria_id = isset($data['productSubcategory']) ? intval($data['productSubcategory']) : null;
    $etiquetas = $conn->real_escape_string($data['productTags'] ?? '');
    $marca = $conn->real_escape_string($data['productBrand'] ?? '');
    $color = $conn->real_escape_string($data['productColor'] ?? '');
    $peso = isset($data['productWeight']) ? floatval($data['productWeight']) : null;
    $dimensiones = $conn->real_escape_string($data['productDimensions'] ?? '');

    error_log("Datos preparados para inserción:");
    error_log(print_r([
        'nombre' => $nombre,
        'descripcion' => $descripcion,
        'precio' => $precio,
        'stock' => $stock,
        'id_tipo_categoria' => $id_tipo_categoria,
        'categoria_id' => $categoria_id,
        'subcategoria_id' => $subcategoria_id,
        'condicion' => $condicion,
        'sku' => $sku
    ], true));

    // Construir la consulta SQL
    $sql = "INSERT INTO tb_productos (
        nombre, descripcion, descripcion_corta, precio, precio_original,
        stock, sku, estado, condicion, id_tipo_categoria, categoria_id, subcategoria_id,
        etiquetas, marca, color, peso, dimensiones, negocio_id, usuario_id
    ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
    )";

    error_log("Consulta SQL preparada");

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparando la consulta: " . $conn->error);
        throw new Exception('Error preparando la consulta: ' . $conn->error);
    }

    $stmt->bind_param(
        "sssddisssiiiisssdii",
        $nombre, $descripcion, $descripcion_corta, $precio, $precio_original,
        $stock, $sku, $estado, $condicion, $id_tipo_categoria, $categoria_id, $subcategoria_id,
        $etiquetas, $marca, $color, $peso, $dimensiones, $negocio_id, $user_id
    );

    error_log("Ejecutando consulta preparada");
    if (!$stmt->execute()) {
        error_log("Error ejecutando la consulta: " . $stmt->error);
        throw new Exception('Error al guardar el producto: ' . $stmt->error);
    }

    $new_id = $stmt->insert_id;
    error_log("Producto insertado con ID: {$new_id}");

    // Procesar imagen si se proporcionó
    if (isset($_FILES['productImage'])) {
        error_log("Procesando imagen adjunta");
        $imagen_path = procesarImagen($_FILES['productImage'], $new_id);

        // Actualizar el producto con la ruta de la imagen
        $sql_update = "UPDATE tb_productos SET imagen_principal = ? WHERE id = ?";
        $stmt_update = $conn->prepare($sql_update);
        $stmt_update->bind_param("si", $imagen_path, $new_id);
        $stmt_update->execute();
        error_log("Imagen guardada en: {$imagen_path}");
    }

    $response = [
        'success' => true,
        'message' => 'Producto guardado exitosamente',
        'product_id' => $new_id
    ];
    error_log("Respuesta: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    error_log("EXCEPCIÓN: " . $e->getMessage());
    error_log("Trace: " . $e->getTraceAsString());

    http_response_code(400);
    $error_response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
    error_log("Respuesta de error: " . json_encode($error_response));
    echo json_encode($error_response);
}

function procesarImagen($file, $product_id) {
    error_log("Iniciando procesamiento de imagen para producto ID: {$product_id}");
    $upload_dir = $_SERVER['DOCUMENT_ROOT'] . '/projects/villarrica_click/images/productos/';
    error_log("Directorio para subir imágenes: {$upload_dir}");

    if (!is_dir($upload_dir)) {
        error_log("Creando directorio para imágenes");
        mkdir($upload_dir, 0755, true);
    }

    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $new_filename = 'producto_' . $product_id . '_' . uniqid() . '.' . $file_extension;
    $upload_path = $upload_dir . $new_filename;

    error_log("Nombre de archivo generado: {$new_filename}");

    if (!move_uploaded_file($file['tmp_name'], $upload_path)) {
        error_log("Error al mover archivo subido: " . print_r(error_get_last(), true));
        throw new Exception('Error al subir la imagen');
    }

    $relative_path = '/projects/villarrica_click/images/productos/' . $new_filename;
    error_log("Ruta relativa de la imagen: {$relative_path}");
    return $relative_path;
}

// Registrar fin de la solicitud
error_log("=== Fin de solicitud save_product.php - " . date('Y-m-d H:i:s') . " ===");