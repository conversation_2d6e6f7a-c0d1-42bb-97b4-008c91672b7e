/**
 * Ajustes para el contenido principal
 * Asegura que el main content se posicione correctamente respecto al sidebar
 */

/* Estilos base para el main-content */
.main-content {
  transition: all 0.3s ease;
  padding-top: 60px; /* Espacio para el header */
  min-height: 100vh;
  box-sizing: border-box;
  background: #f8f9fa;
}

/* Estilos para pantallas grandes (desktop) */
@media screen and (min-width: 993px) {
  /* Cuando el sidebar está expandido */
  #sidebar:not(.collapsed) ~ .main-content {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
  }
  
  /* Cuando el sidebar está colapsado */
  #sidebar.collapsed ~ .main-content {
    margin-left: 70px !important;
    width: calc(100% - 70px) !important;
  }
}

/* Estilos para pantallas pequeñas (mobile) */
@media screen and (max-width: 992px) {
  .main-content {
    margin-left: 70px !important;
    width: calc(100% - 70px) !important;
  }
}

/* Asegurar que el contenido dentro del main-content esté bien contenido */
.container {
  padding: 20px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Estilos para el contenido cuando se está en pantalla completa */
.fullscreen-mode .main-content {
  margin-left: 0 !important;
  width: 100% !important;
}
