// Importar componentes necesarios
import { showNotification } from '../../components/notifications.js';

// Función para cerrar el panel de filtros
function closeFilterPanel() {
    const filterContainer = document.getElementById('filterContainer');
    const filterOverlay = document.getElementById('filterOverlay');
    
    if (filterContainer) {
        filterContainer.classList.remove('show');
    }
    
    if (filterOverlay) {
        filterOverlay.classList.remove('show');
    }
    
    setTimeout(() => {
        if (filterContainer) {
            filterContainer.style.display = 'none';
        }
        if (filterOverlay) {
            filterOverlay.style.display = 'none';
        }
        document.body.style.overflow = '';
    }, 300);
}

// Inicializar los manejadores de filtros
function initFilterHandlers() {
    document.addEventListener('DOMContentLoaded', function() {
        const filterBtn = document.getElementById('filterBtn');
        const filterContainer = document.getElementById('filterContainer');
        const closeFilter = document.getElementById('closeFilter');
        const filterOverlay = document.getElementById('filterOverlay');
        const applyFilters = document.getElementById('applyFilters');
        const resetFilters = document.getElementById('resetFilters');
        
        // Abrir filtro
        filterBtn?.addEventListener('click', function() {
            if (filterContainer) {
                document.body.style.overflow = 'hidden';
                filterContainer.style.display = 'block';
                filterOverlay.style.display = 'block';
                
                // Forzar reflow
                void filterContainer.offsetWidth;
                void filterOverlay.offsetWidth;
                
                // Añadir clases show
                filterContainer.classList.add('show');
                filterOverlay.classList.add('show');
            }
        });
        
        // Cerrar filtro
        closeFilter?.addEventListener('click', closeFilterPanel);
        
        // Cerrar al hacer clic en el overlay
        filterOverlay?.addEventListener('click', closeFilterPanel);
        
        // Aplicar filtros
        applyFilters?.addEventListener('click', function() {
            const filters = {
                search: document.getElementById('filterSearch')?.value || '',
                minPrice: document.getElementById('filterMinPrice')?.value || '',
                maxPrice: document.getElementById('filterMaxPrice')?.value || '',
                minStock: document.getElementById('filterMinStock')?.value || '',
                maxStock: document.getElementById('filterMaxStock')?.value || '',
                states: Array.from(document.querySelectorAll('input[type="checkbox"][value^="activo"], input[type="checkbox"][value^="borrador"], input[type="checkbox"][value^="agotado"]'))
                    .filter(cb => cb.checked)
                    .map(cb => cb.value),
                conditions: Array.from(document.querySelectorAll('input[type="checkbox"][value^="destacado"], input[type="checkbox"][value^="oferta"], input[type="checkbox"][value^="liquidacion"], input[type="checkbox"][value^="nuevo"], input[type="checkbox"][value^="exclusivo"]'))
                    .filter(cb => cb.checked)
                    .map(cb => cb.value),
                category: document.getElementById('filterCategory')?.value || '',
                subcategory: document.getElementById('filterSubcategory')?.value || ''
            };

            console.log("Aplicando filtros:", filters);

            // Filtrar productos en la tabla
            filterTableRows(filters);
            
            // Filtrar productos en la vista de tarjetas
            filterProductCards(filters);
            
            // Cerrar el filtro
            closeFilterPanel();
            
            // Mostrar notificación
            const activeCount = countVisibleProducts();
            showNotification(`Se encontraron ${activeCount} productos con los filtros aplicados`, 'success');
        });
        
        // Resetear filtros
        resetFilters?.addEventListener('click', function() {
            // Limpiar campos de texto y número
            document.getElementById('filterSearch').value = '';
            document.getElementById('filterMinPrice').value = '';
            document.getElementById('filterMaxPrice').value = '';
            document.getElementById('filterMinStock').value = '';
            document.getElementById('filterMaxStock').value = '';
            
            // Desmarcar checkboxes
            document.querySelectorAll('.filter-checkbox input[type="checkbox"]')
                .forEach(cb => cb.checked = false);
            
            // Resetear selects
            document.getElementById('filterCategory').value = '';
            document.getElementById('filterSubcategory').value = '';
            
            // Mostrar todos los productos en la tabla
            document.querySelectorAll('.admin-table tbody tr').forEach(tr => {
                tr.style.display = '';
            });
            
            // Mostrar todos los productos en tarjetas
            document.querySelectorAll('.product-card').forEach(card => {
                card.style.display = '';
            });
            
            showNotification('Filtros restablecidos', 'success');
        });
        
        // Cargar las categorías y subcategorías para los filtros
        loadCategoriesForFilters();

        // Añadir clases activas a los checkboxes cuando se seleccionan
        document.querySelectorAll('.filter-checkbox input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const parent = this.closest('.filter-checkbox');
                if (this.checked) {
                    parent.classList.add('active');
                } else {
                    parent.classList.remove('active');
                }
            });
        });
    });
}

// Función para filtrar filas de la tabla
function filterTableRows(filters) {
    const productos = Array.from(document.querySelectorAll('.admin-table tbody tr'));
    productos.forEach(producto => {
        let visible = shouldBeVisible(producto, filters, 'table');
        producto.style.display = visible ? '' : 'none';
    });
}

// Función para filtrar tarjetas de productos
function filterProductCards(filters) {
    const cards = Array.from(document.querySelectorAll('.product-card'));
    cards.forEach(card => {
        let visible = shouldBeVisible(card, filters, 'card');
        card.style.display = visible ? '' : 'none';
    });
}

// Función para determinar si un elemento debe ser visible según los filtros
function shouldBeVisible(element, filters, type) {
    let visible = true;
    
    // Extraer los datos dependiendo de si es una fila de tabla o una tarjeta
    const datos = extractElementData(element, type);
    
    // Filtrar por búsqueda
    if (filters.search && visible) {
        const searchTerm = filters.search.toLowerCase();
        if (!datos.nombre.includes(searchTerm) && !datos.descripcion.includes(searchTerm)) {
            visible = false;
        }
    }
    
    // Filtrar por precio
    if (visible && (filters.minPrice || filters.maxPrice)) {
        const precio = parseFloat(datos.precio);
        if (filters.minPrice && precio < filters.minPrice) visible = false;
        if (filters.maxPrice && precio > filters.maxPrice) visible = false;
    }
    
    // Filtrar por stock
    if (visible && (filters.minStock || filters.maxStock)) {
        const stock = parseInt(datos.stock);
        if (filters.minStock && stock < filters.minStock) visible = false;
        if (filters.maxStock && stock > filters.maxStock) visible = false;
    }
    
    // Filtrar por estado
    if (visible && filters.states.length > 0) {
        if (!filters.states.some(s => datos.estado.includes(s))) visible = false;
    }
    
    // Filtrar por condición
    if (visible && filters.conditions.length > 0) {
        if (!filters.conditions.some(c => datos.condicion.includes(c))) visible = false;
    }
    
    // Filtrar por categoría
    if (visible && filters.category) {
        if (datos.categoria !== filters.category) visible = false;
    }
    
    // Filtrar por subcategoría
    if (visible && filters.subcategory) {
        if (datos.subcategoria !== filters.subcategory) visible = false;
    }
    
    return visible;
}

// Función para extraer datos de un elemento (fila de tabla o tarjeta)
function extractElementData(element, type) {
    if (type === 'table') {
        // Extraer datos de una fila de tabla
        return {
            nombre: (element.querySelector('.product-name')?.textContent || '').toLowerCase(),
            descripcion: (element.querySelector('td:nth-child(4)')?.textContent || '').toLowerCase(),
            precio: (element.querySelector('.product-price')?.textContent || '0').replace(/[^0-9]/g, ''),
            stock: element.querySelector('td:nth-child(6)')?.textContent || '0',
            estado: (element.querySelector('.status-badge')?.textContent || '').toLowerCase(),
            condicion: (element.querySelector('.condition-badge')?.textContent || '').toLowerCase(),
            categoria: element.querySelector('td:nth-child(9)')?.textContent || '',
            subcategoria: element.querySelector('td:nth-child(9)')?.textContent || ''
        };
    } else {
        // Extraer datos de una tarjeta
        return {
            nombre: (element.querySelector('.card-title')?.textContent || '').toLowerCase(),
            descripcion: (element.querySelector('.card-category')?.textContent || '').toLowerCase(),
            precio: (element.querySelector('.current-price')?.textContent || '0').replace(/[^0-9]/g, ''),
            stock: '0', // Supongamos que no mostramos el stock en las tarjetas
            estado: (element.querySelector('.status-badge')?.textContent || '').toLowerCase(),
            condicion: (element.querySelector('.condition-badge')?.textContent || '').toLowerCase(),
            categoria: element.querySelector('.card-category')?.textContent || '',
            subcategoria: element.querySelector('.card-category')?.textContent || ''
        };
    }
}

// Función para contar productos visibles
function countVisibleProducts() {
    // Contar elementos visibles en la tabla (para desktop)
    const visibleTableRows = document.querySelectorAll('.admin-table tbody tr:not([style*="display: none"])').length;
    
    // Contar elementos visibles en tarjetas (para móvil)
    const visibleCards = document.querySelectorAll('.product-card:not([style*="display: none"])').length;
    
    // Devolver el número correspondiente según el modo de visualización actual
    return window.innerWidth <= 768 ? visibleCards : visibleTableRows;
}

// Función para cargar las categorías y subcategorías en los filtros
function loadCategoriesForFilters() {
    const categorySelect = document.getElementById('filterCategory');
    const subcategorySelect = document.getElementById('filterSubcategory');
    
    if (!categorySelect || !subcategorySelect) return;
    
    // Obtener todas las categorías únicas de los productos mostrados
    const categoryUniqueNames = new Set();
    document.querySelectorAll('.product-name-cell .product-category').forEach(el => {
        const category = el?.textContent?.trim();
        if (category) categoryUniqueNames.add(category);
    });
    
    // Limpiar y añadir opciones al select de categorías
    categorySelect.innerHTML = '<option value="">Todas las categorías</option>';
    categoryUniqueNames.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });
    
    // Configurar evento de cambio de categoría para actualizar subcategorías
    categorySelect.addEventListener('change', function() {
        const selectedCategory = this.value;
        
        // Limpiar y añadir opciones al select de subcategorías
        subcategorySelect.innerHTML = '<option value="">Todas las subcategorías</option>';
        
        if (selectedCategory) {
            // Aquí deberías cargar las subcategorías correspondientes a la categoría seleccionada
            // como no tenemos una API específica para esto, podríamos simularlo
            // ejemplos de subcategorías
            const subcategories = ['Smartphones', 'Tablets', 'Laptops', 'Auriculares', 'Televisores'];
            
            subcategories.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory;
                option.textContent = subcategory;
                subcategorySelect.appendChild(option);
            });
        }
    });
}

// Exportar funciones
export {
    initFilterHandlers,
    closeFilterPanel,
    filterTableRows,
    filterProductCards
};