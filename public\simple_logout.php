<?php
// Primero cargar las configuraciones
require_once '../config/config.php';

// Capturar ID de sesión inicial para depuración
$initial_session_id = session_id() ?: 'no hay sesión activa';
error_log("SimpleLogout - ID de sesión inicial: " . $initial_session_id);
error_log("SimpleLogout - Cookies antes: " . print_r($_COOKIE, true));

// Iniciar sesión con las configuraciones correctas
session_name('VILLARRICA_SESSION'); // Usar el mismo nombre que en SessionManager
session_start();

error_log("SimpleLogout - Sesión iniciada, ID: " . session_id());
error_log("SimpleLogout - Contenido de sesión: " . print_r($_SESSION, true));

// Limpiar todas las variables de sesión
$_SESSION = array();

// Destruir la cookie de sesión con la ruta CORRECTA
if (isset($_COOKIE[session_name()])) {
    error_log("SimpleLogout - Eliminando cookie: " . session_name());
    setcookie(
        session_name(),
        '',
        time() - 3600,
        COOKIE_PATH,      // Usar la constante de config.php
        COOKIE_DOMAIN,    // Usar la constante de config.php
        false,
        true
    );
    unset($_COOKIE[session_name()]);
}

// Si hay otras cookies de sesión, también eliminarlas
if (isset($_COOKIE['PHPSESSID'])) {
    setcookie('PHPSESSID', '', time() - 3600, '/');
    unset($_COOKIE['PHPSESSID']);
}

// Destruir la sesión
session_destroy();

error_log("SimpleLogout - Sesión destruida");
error_log("SimpleLogout - Cookies después: " . print_r($_COOKIE, true));

// Forzar caducidad de caché
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Redireccionar usando BASE_URL
header('Location: ' . BASE_URL . '/public/login.php?message=logout_success&t=' . time());
exit();
?>