<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Función para mostrar mensajes
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 10px 0; border-radius: 5px; ' . 
         ($isError ? 'background-color: #ffebee; color: #c62828;' : 'background-color: #e8f5e9; color: #2e7d32;') . 
         '">' . $message . '</div>';
}

// Probar inserción directa
echo '<h2>Prueba de inserción directa</h2>';

try {
    // Registrar inicio de la prueba
    logInfo("Iniciando prueba de inserción directa");
    
    // Obtener conexión a la base de datos
    global $conn;
    
    // Si ya existe una conexión global, usarla
    if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
        logInfo("Usando conexión global existente");
        showMessage("Usando conexión global existente");
    } else {
        // Si no existe, obtener una nueva
        $conn = getDbConnection();
        
        if (!$conn) {
            logError("No se pudo obtener conexión a la base de datos");
            showMessage("No se pudo obtener conexión a la base de datos", true);
            exit;
        }
        
        logInfo("Nueva conexión obtenida");
        showMessage("Nueva conexión obtenida");
    }
    
    // Crear datos de prueba
    $test_data = [
        'nombres' => 'Test Direct',
        'apellidos' => 'Usuario',
        'rut' => '12345678-9',
        'fechaNacimiento' => '01/01/1990',
        'sexo' => 'Masculino',
        'telefono' => '1234567890',
        'region' => 'Test Region',
        'comuna' => 'Test Comuna',
        'direccion' => 'Test Direccion 123',
        'NombreUsuario' => 'test_direct_' . time(),
        'mail' => 'test_direct_' . time() . '@example.com',
        'mailRespaldo' => null,
        'contraseña' => password_hash('test123', PASSWORD_DEFAULT),
        'localFisico' => 'Si',
        'nombreNegocio' => 'Test Negocio',
        'telefonoNegocio' => '1234567890',
        'whatsappNegocio' => '1234567890',
        'tipoNegocio' => 'Venta',
        'descripcionNegocio' => 'Test Descripcion',
        'planSuscripcion' => 'Gratuita'
    ];
    
    // Insertar directamente sin usar prepared statements
    $sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion, 
                                     NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                     nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                     planSuscripcion)
            VALUES (
                '{$test_data['nombres']}', 
                '{$test_data['apellidos']}', 
                '{$test_data['rut']}', 
                '{$test_data['fechaNacimiento']}', 
                '{$test_data['sexo']}', 
                '{$test_data['telefono']}', 
                '{$test_data['region']}', 
                '{$test_data['comuna']}', 
                '{$test_data['direccion']}',
                '{$test_data['NombreUsuario']}', 
                '{$test_data['mail']}', 
                " . ($test_data['mailRespaldo'] ? "'{$test_data['mailRespaldo']}'" : "NULL") . ", 
                '{$test_data['contraseña']}', 
                '{$test_data['localFisico']}',
                '{$test_data['nombreNegocio']}', 
                '{$test_data['telefonoNegocio']}', 
                '{$test_data['whatsappNegocio']}', 
                '{$test_data['tipoNegocio']}', 
                '{$test_data['descripcionNegocio']}',
                '{$test_data['planSuscripcion']}'
            )";
    
    logInfo("Ejecutando consulta SQL directa", ['sql' => $sql]);
    showMessage("Ejecutando consulta SQL directa");
    
    if ($conn->query($sql) === TRUE) {
        logInfo("Inserción directa exitosa", ['insert_id' => $conn->insert_id]);
        showMessage("Inserción directa exitosa. ID: " . $conn->insert_id);
    } else {
        logError("Error en la inserción directa", ['error' => $conn->error]);
        showMessage("Error en la inserción directa: " . $conn->error, true);
    }
    
    // Probar con prepared statement
    echo '<h2>Prueba con prepared statement</h2>';
    
    // Crear datos de prueba
    $test_data2 = [
        'nombres' => 'Test Prepared',
        'apellidos' => 'Usuario',
        'rut' => '12345678-9',
        'fechaNacimiento' => '01/01/1990',
        'sexo' => 'Masculino',
        'telefono' => '1234567890',
        'region' => 'Test Region',
        'comuna' => 'Test Comuna',
        'direccion' => 'Test Direccion 123',
        'NombreUsuario' => 'test_prepared_' . time(),
        'mail' => 'test_prepared_' . time() . '@example.com',
        'mailRespaldo' => null,
        'contraseña' => password_hash('test123', PASSWORD_DEFAULT),
        'localFisico' => 'Si',
        'nombreNegocio' => 'Test Negocio',
        'telefonoNegocio' => '1234567890',
        'whatsappNegocio' => '1234567890',
        'tipoNegocio' => 'Venta',
        'descripcionNegocio' => 'Test Descripcion',
        'planSuscripcion' => 'Gratuita'
    ];
    
    // Preparar la consulta SQL
    $sql2 = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion, 
                                     NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                     nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                     planSuscripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    logInfo("Preparando consulta SQL para prepared statement", ['sql' => $sql2]);
    showMessage("Preparando consulta SQL para prepared statement");
    
    $stmt = $conn->prepare($sql2);
    
    if (!$stmt) {
        logError("Error al preparar la consulta", ['error' => $conn->error]);
        showMessage("Error al preparar la consulta: " . $conn->error, true);
        exit;
    }
    
    logInfo("Consulta preparada correctamente");
    showMessage("Consulta preparada correctamente");
    
    $stmt->bind_param("ssssssssssssssssssss", 
        $test_data2['nombres'], $test_data2['apellidos'], $test_data2['rut'], 
        $test_data2['fechaNacimiento'], $test_data2['sexo'], $test_data2['telefono'], 
        $test_data2['region'], $test_data2['comuna'], $test_data2['direccion'],
        $test_data2['NombreUsuario'], $test_data2['mail'], $test_data2['mailRespaldo'], $test_data2['contraseña'], $test_data2['localFisico'],
        $test_data2['nombreNegocio'], $test_data2['telefonoNegocio'], $test_data2['whatsappNegocio'], 
        $test_data2['tipoNegocio'], $test_data2['descripcionNegocio'],
        $test_data2['planSuscripcion']);
    
    logInfo("Parámetros vinculados correctamente");
    showMessage("Parámetros vinculados correctamente");
    
    if ($stmt->execute()) {
        logInfo("Inserción con prepared statement exitosa", ['insert_id' => $conn->insert_id]);
        showMessage("Inserción con prepared statement exitosa. ID: " . $conn->insert_id);
    } else {
        logError("Error al ejecutar la consulta", ['error' => $stmt->error]);
        showMessage("Error al ejecutar la consulta: " . $stmt->error, true);
    }
    
    // Cerrar conexión
    $stmt->close();
    
    logInfo("Prueba de inserción finalizada");
    
} catch (Exception $e) {
    logException($e, ['context' => 'test_insert']);
    showMessage("Error: " . $e->getMessage(), true);
}
?>
