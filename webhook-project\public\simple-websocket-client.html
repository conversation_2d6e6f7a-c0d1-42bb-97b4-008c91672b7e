<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente WebSocket Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0069d9;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            width: 300px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>Cliente WebSocket Simple</h1>
    
    <div id="status" class="status disconnected">Desconectado</div>
    
    <div>
        <label for="serverUrl">URL del servidor:</label>
        <input type="text" id="serverUrl" value="ws://**************:8080">
        <button id="connectBtn">Conectar</button>
        <button id="disconnectBtn" disabled>Desconectar</button>
    </div>
    
    <h2>Registro de eventos</h2>
    <div id="log" class="log"></div>
    
    <div>
        <label for="message">Mensaje:</label>
        <input type="text" id="message" placeholder="Escribe un mensaje..." disabled>
        <button id="sendBtn" disabled>Enviar</button>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusEl = document.getElementById('status');
            const logEl = document.getElementById('log');
            const serverUrlEl = document.getElementById('serverUrl');
            const messageEl = document.getElementById('message');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            
            let socket = null;
            
            function log(message, type = 'info') {
                const entry = document.createElement('div');
                entry.className = `log-entry ${type}`;
                
                const time = new Date().toLocaleTimeString();
                entry.textContent = `[${time}] ${message}`;
                
                logEl.appendChild(entry);
                logEl.scrollTop = logEl.scrollHeight;
            }
            
            function updateStatus(connected) {
                if (connected) {
                    statusEl.className = 'status connected';
                    statusEl.textContent = 'Conectado';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    messageEl.disabled = false;
                    sendBtn.disabled = false;
                } else {
                    statusEl.className = 'status disconnected';
                    statusEl.textContent = 'Desconectado';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    messageEl.disabled = true;
                    sendBtn.disabled = true;
                }
            }
            
            connectBtn.addEventListener('click', function() {
                const url = serverUrlEl.value.trim();
                
                if (!url) {
                    alert('Por favor, ingresa una URL válida');
                    return;
                }
                
                try {
                    log(`Intentando conectar a ${url}...`);
                    
                    socket = new WebSocket(url);
                    
                    socket.onopen = function() {
                        log('Conexión establecida', 'success');
                        updateStatus(true);
                    };
                    
                    socket.onmessage = function(event) {
                        log(`Mensaje recibido: ${event.data}`, 'received');
                    };
                    
                    socket.onclose = function(event) {
                        if (event.wasClean) {
                            log(`Conexión cerrada limpiamente, código=${event.code} razón=${event.reason}`);
                        } else {
                            log('Conexión interrumpida', 'error');
                        }
                        updateStatus(false);
                        socket = null;
                    };
                    
                    socket.onerror = function(error) {
                        log(`Error: ${error.message}`, 'error');
                    };
                    
                } catch (error) {
                    log(`Error al conectar: ${error.message}`, 'error');
                }
            });
            
            disconnectBtn.addEventListener('click', function() {
                if (socket) {
                    socket.close();
                    log('Desconectado manualmente');
                }
            });
            
            sendBtn.addEventListener('click', function() {
                const message = messageEl.value.trim();
                
                if (!message) return;
                
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(message);
                    log(`Mensaje enviado: ${message}`, 'sent');
                    messageEl.value = '';
                } else {
                    log('No se puede enviar: No hay conexión', 'error');
                }
            });
            
            messageEl.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendBtn.click();
                }
            });
            
            // Estado inicial
            updateStatus(false);
            log('Cliente WebSocket inicializado');
        });
    </script>
</body>
</html>
