# Sistema de Webhooks

Este proyecto implementa un sistema completo de webhooks para la integración de eventos entre aplicaciones, proporcionando una alternativa eficiente a las conexiones SSE (Server-Sent Events).

## Estructura del Proyecto

```
webhook-project/
│
├── public/              # Archivos accesibles públicamente
│   ├── css/             # Hojas de estilo
│   ├── js/              # Scripts JavaScript
│   ├── index_webhook.php # Panel de administración
│   ├── webhook.php      # Endpoint para recibir webhooks
│   └── websocket-client.html  # Cliente de prueba WebSocket (alternativa)
│
├── src/                 # Código fuente PHP
│   ├── WebhookHandler.php  # Clase para procesar webhooks
│   ├── Logger.php       # Sistema de registro de eventos
│   └── config.php       # Configuración general
│
└── logs/                # Directorio para almacenar logs
```

## Conceptos Básicos

### ¿Qué son los Webhooks?

Los webhooks son callbacks HTTP que se activan cuando ocurre un evento específico. A diferencia de las APIs tradicionales donde el cliente hace solicitudes periódicas (polling), los webhooks funcionan con un modelo "push", donde el servidor envía datos al cliente cuando ocurre un evento.

### Diferencias con SSE (Server-Sent Events)

| Característica | Webhooks | SSE |
|----------------|----------|-----|
| Dirección | Bidireccional | Unidireccional (servidor a cliente) |
| Modelo | Push (basado en eventos) | Push en tiempo real |
| Conexión | No mantiene conexiones persistentes | Mantiene conexiones persistentes |
| Consumo de recursos | Bajo | Alto |
| Escalabilidad | Alta | Media |
| Uso de memoria del servidor | Bajo | Alto por conexión mantenida |

## Configuración

1. **Servidor Web**: Configura Apache/Nginx para apuntar al directorio `public/`.

2. **Permisos**: Asegúrate de que el directorio `logs/` tenga permisos de escritura:
   ```bash
   chmod 755 logs/
   ```

3. **Configuración**: Edita el archivo `src/config.php` con tus parámetros:
   - Modifica `webhook_secret` para mejorar la seguridad
   - Actualiza `base_url` con tu dominio
   - Configura los proveedores de webhooks externos

## Uso Básico

### Recibir Webhooks

El endpoint principal está en `public/webhook.php`. Este script recibe las solicitudes POST con datos JSON y procesa los eventos según su tipo.

### Panel de Administración

Accede al panel de administración en `public/index_webhook.php` para:
- Ver webhooks recibidos
- Revisar errores
- Registrar nuevos webhooks
- Probar webhooks

### Estructura del Webhook

Los webhooks esperan recibir datos JSON con al menos los siguientes campos:

```json
{
    "tipo_evento": "nombre_del_evento",
    "datos_adicionales": {
        "campo1": "valor1",
        "campo2": "valor2"
    }
}
```

### Verificación de Seguridad

Para mejorar la seguridad, se implementa verificación de firmas HMAC:

1. El remitente genera una firma con HMAC-SHA256 del cuerpo completo del mensaje
2. Esta firma se envía en el encabezado HTTP `X-Webhook-Signature`
3. El receptor verifica la firma usando la clave secreta compartida

## WebSockets como Alternativa

Se incluye un cliente WebSocket de ejemplo (`websocket-client.html`) para demostrar otra alternativa a SSE con menor consumo de recursos que las conexiones persistentes tradicionales.

### Ventajas de WebSockets sobre SSE

- Comunicación bidireccional
- Mayor eficiencia en la transmisión de datos
- Mejor manejo de reconexiones
- Menos problemas con firewalls y proxies

### Implementación de Servidor WebSocket

Para implementar un servidor WebSocket con PHP, recomendamos usar la biblioteca Ratchet:

```bash
composer require cboden/ratchet
```

## Consideraciones de Rendimiento

- **Webhooks**: Mejor para eventos poco frecuentes o asíncronos
- **WebSockets**: Mejor para comunicación bidireccional en tiempo real
- **SSE**: Útil para actualizaciones unidireccionales frecuentes, pero con mayor consumo de recursos

## Manejo de Errores

El sistema incluye un registro detallado de errores en el directorio `logs/`. Los errores se clasifican por tipo y fecha para facilitar el seguimiento.

## Extensión del Sistema

### Añadir Nuevos Tipos de Eventos

1. Agrega el nuevo tipo de evento en `src/config.php` en el array `eventos_soportados`
2. Implementa un método para procesar el evento en `src/WebhookHandler.php`

## Licencia

Este proyecto está licenciado bajo la licencia MIT.
