<?php
/**
 * Panel de administración de Webhooks
 */

// Incluir archivos necesarios
require_once '../src/Logger.php';

// Inicializar el logger
$logger = new Logger();

// Obtener eventos recientes
$eventosWebhook = $logger->obtenerEventos('webhook_recibido');
$eventosError = $logger->obtenerEventos('error');

// Obtener configuración
$config = require_once '../src/config.php';
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administración de Webhooks</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Panel de Administración de Webhooks</h1>
        </header>
        
        <main>
            <section class="card">
                <h2>Eventos Recibidos</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Fecha/Hora</th>
                            <th>Tipo de Evento</th>
                            <th>IP Origen</th>
                            <th>Detalles</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($eventosWebhook)): ?>
                            <tr>
                                <td colspan="4">No hay eventos registrados</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($eventosWebhook as $evento): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($evento['timestamp']); ?></td>
                                    <td><?php echo htmlspecialchars($evento['datos']['tipo'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($evento['datos']['ip'] ?? 'N/A'); ?></td>
                                    <td>
                                        <button class="btn-details" data-details="<?php echo htmlspecialchars(json_encode($evento['datos'])); ?>">
                                            Ver detalles
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </section>
            
            <section class="card">
                <h2>Errores Recientes</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Fecha/Hora</th>
                            <th>Mensaje</th>
                            <th>Tipo de Evento</th>
                            <th>Detalles</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($eventosError)): ?>
                            <tr>
                                <td colspan="4">No hay errores registrados</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($eventosError as $error): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($error['timestamp']); ?></td>
                                    <td><?php echo htmlspecialchars($error['datos']['mensaje'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($error['datos']['tipo_evento'] ?? 'N/A'); ?></td>
                                    <td>
                                        <button class="btn-details" data-details="<?php echo htmlspecialchars(json_encode($error['datos'])); ?>">
                                            Ver detalles
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </section>
            
            <section class="card">
                <h2>Registrar Webhook</h2>
                <form id="registerWebhookForm">
                    <div class="form-group">
                        <label for="webhookUrl">URL de Callback:</label>
                        <input type="url" id="webhookUrl" name="webhookUrl" required>
                    </div>
                    
                    <div class="form-group">
                        <label>Eventos a recibir:</label>
                        <div class="checkbox-group">
                            <?php foreach ($config['eventos_soportados'] as $evento): ?>
                                <label>
                                    <input type="checkbox" name="eventos[]" value="<?php echo htmlspecialchars($evento); ?>">
                                    <?php echo htmlspecialchars($evento); ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="descripcion">Descripción:</label>
                        <textarea id="descripcion" name="descripcion" rows="3"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Registrar Webhook</button>
                    </div>
                </form>
            </section>
            
            <section class="card">
                <h2>Probar Webhook</h2>
                <form id="testWebhookForm">
                    <div class="form-group">
                        <label for="testUrl">URL a probar:</label>
                        <input type="url" id="testUrl" name="testUrl" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="tipoEvento">Tipo de evento:</label>
                        <select id="tipoEvento" name="tipoEvento" required>
                            <option value="">Seleccione un evento</option>
                            <?php foreach ($config['eventos_soportados'] as $evento): ?>
                                <option value="<?php echo htmlspecialchars($evento); ?>">
                                    <?php echo htmlspecialchars($evento); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="datosEvento">Datos del evento (JSON):</label>
                        <textarea id="datosEvento" name="datosEvento" rows="5" required>{
    "tipo_evento": "",
    "id_transaccion": "test-123",
    "datos_adicionales": {
        "cliente": "Ejemplo",
        "importe": 100.50
    }
}</textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Enviar Prueba</button>
                    </div>
                </form>
                
                <div id="testResult" class="result-box hidden">
                    <h3>Resultado de la prueba:</h3>
                    <pre id="testResultContent"></pre>
                </div>
            </section>
        </main>
        
        <footer>
            <p>&copy; <?php echo date('Y'); ?> Sistema de Webhooks</p>
        </footer>
    </div>
    
    <!-- Modal de detalles -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Detalles del Evento</h2>
            <pre id="detailsContent"></pre>
        </div>
    </div>
    
    <script src="js/webhook-admin.js"></script>
</body>
</html>
