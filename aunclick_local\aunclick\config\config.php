<?php
// Inicio de config.php - Configuración unificada

// Incluir el sistema de logging
require_once __DIR__ . '/logger.php';

// Depuración de entorno
logInfo("Inicializando configuración", [
    'script_name' => $_SERVER['SCRIPT_NAME'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'http_host' => $_SERVER['HTTP_HOST']
]);

// Eliminar cualquier output buffering para evitar problemas
while (ob_get_level()) ob_end_clean();

// Establecer la ruta base correcta
$basePath = '';  // Ruta base actualizada para la nueva estructura
$cookieDomain = '';

logInfo("Configuración de rutas", [
    'base_url' => $basePath,
    'domain' => $_SERVER['HTTP_HOST']
]);

// Definir las constantes
define('BASE_URL', $basePath);
define('DOMAIN', $_SERVER['HTTP_HOST']);
define('COOKIE_PATH', $basePath);
define('COOKIE_DOMAIN', $cookieDomain);
define('SESSION_LIFETIME', 86400);  // 24 horas en segundos

logInfo("Constantes definidas", [
    'BASE_URL' => BASE_URL,
    'DOMAIN' => DOMAIN,
    'COOKIE_PATH' => COOKIE_PATH,
    'COOKIE_DOMAIN' => COOKIE_DOMAIN
]);

// Configuración inicial de sesión si aún no hay una activa
if (session_status() !== PHP_SESSION_ACTIVE) {
    // Configurar rutas de cookies y sesión
    ini_set('session.cookie_path', BASE_URL);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);

    ini_set('session.use_strict_mode', 1);
    ini_set('session.use_cookies', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.cookie_path', COOKIE_PATH);
    ini_set('session.cookie_domain', COOKIE_DOMAIN);
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
}

// Conexión a la base de datos
$db_host = 'localhost';
$db_user = 'pcornejo';
$db_pass = 'Pcornejo@2025';
$db_name = 'aunclick_prueba';

// Definir constantes para la conexión a la base de datos
define('DB_HOST', $db_host);
define('DB_USER', $db_user);
define('DB_PASS', $db_pass);
define('DB_NAME', $db_name);

// Crear conexión
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Verificar conexión
if ($conn->connect_error) {
    logError("Error de conexión a la base de datos", [
        'error' => $conn->connect_error,
        'host' => $db_host,
        'database' => $db_name
    ]);
    die("Conexión fallida: " . $conn->connect_error);
}

$conn->set_charset("utf8");
logInfo("Conexión a base de datos establecida correctamente", [
    'host' => $db_host,
    'database' => $db_name
]);

// Función para obtener una nueva conexión a la base de datos
function getDirectConnection() {
    global $db_host, $db_user, $db_pass, $db_name;

    $connection = new mysqli($db_host, $db_user, $db_pass, $db_name);

    if ($connection->connect_error) {
        logError("Error al crear nueva conexión directa", [
            'error' => $connection->connect_error,
            'host' => $db_host,
            'database' => $db_name
        ]);
        return null;
    }

    $connection->set_charset("utf8");
    return $connection;
}
logInfo("Configuración completada");
// Fin de config.php