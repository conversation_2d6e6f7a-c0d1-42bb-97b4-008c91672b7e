<?php
/**
 * AuthService.php
 * 
 * Servicio para gestionar autenticación y autorización de usuarios
 * 
 * Este servicio es responsable de:
 * - Verificar credenciales de usuario
 * - Gestionar roles y permisos
 * - Proteger contra ataques de fuerza bruta
 * - Implementar autenticación multifactor (si se activa)
 */

namespace Config;

class AuthService {
    private static $instance = null;
    private $conn;
    private $logger;
    
    // Constantes de configuración
    const MAX_LOGIN_ATTEMPTS = 5; // Número máximo de intentos de login fallidos
    const LOCKOUT_TIME = 900;     // Tiempo de bloqueo en segundos (15 minutos)
    const PASSWORD_MIN_LENGTH = 8;
    const TOKEN_EXPIRY = 3600;    // 1 hora para tokens de reseteo, etc.
    
    /**
     * Constructor privado (patrón Singleton)
     */
    private function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Obtiene la instancia única del servicio
     *
     * @param \mysqli $conn Conexión a la base de datos
     * @return AuthService
     */
    public static function getInstance($conn) {
        if (self::$instance === null) {
            self::$instance = new self($conn);
        }
        return self::$instance;
    }
    
    /**
     * Verifica las credenciales del usuario
     *
     * @param string $username Nombre de usuario o email
     * @param string $password Contraseña
     * @return array|null Datos del usuario si es válido o null si no es válido
     */
    public function verifyCredentials($username, $password) {
        // Verificar intentos de inicio de sesión
        if ($this->isAccountLocked($username)) {
            logWarning('Cuenta bloqueada temporalmente por demasiados intentos fallidos', ['username' => $username]);
            throw new \Exception('account_locked');
        }

        // Buscar usuario por nombre de usuario o email
        $stmt = $this->conn->prepare("SELECT id, username, email, password, role, is_active 
                                     FROM users 
                                     WHERE (username = ? OR email = ?)");
        
        if (!$stmt) {
            logError('Error al preparar consulta de usuario', ['error' => $this->conn->error]);
            throw new \Exception('database_error');
        }
        
        $stmt->bind_param("ss", $username, $username);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar consulta de usuario', ['error' => $stmt->error]);
            throw new \Exception('database_error');
        }
        
        $result = $stmt->get_result();
        
        // Si no se encuentra el usuario
        if ($result->num_rows === 0) {
            $this->registerFailedAttempt($username);
            throw new \Exception('invalid_credentials');
        }
        
        $user = $result->fetch_assoc();
        
        // Verificar si la cuenta está activa
        if (!$user['is_active']) {
            logWarning('Intento de acceso a cuenta inactiva', ['username' => $username]);
            throw new \Exception('account_inactive');
        }
        
        // Verificar la contraseña
        $password_correct = false;
        
        // Verificar si la contraseña está en formato hash
        if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $user['password'])) {
            $password_correct = password_verify($password, $user['password']);
            
            // Verificar si se necesita actualizar el hash (cambios en algoritmo/costo)
            if ($password_correct && password_needs_rehash($user['password'], PASSWORD_BCRYPT, ['cost' => 12])) {
                $this->updatePasswordHash($user['id'], $password);
            }
        } 
        // Compatibilidad con contraseñas antiguas (texto plano)
        else {
            $password_correct = ($password === $user['password']);
            
            // Si la contraseña es correcta, actualizarla al nuevo formato
            if ($password_correct) {
                $this->updatePasswordHash($user['id'], $password);
            }
        }
        
        // Si la contraseña es incorrecta
        if (!$password_correct) {
            $this->registerFailedAttempt($username);
            throw new \Exception('invalid_credentials');
        }
        
        // Si todo es correcto, limpiar intentos fallidos y devolver datos del usuario
        $this->clearFailedAttempts($username);
        
        // Actualizar último login
        $this->updateLastLogin($user['id']);
        
        return [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role']
        ];
    }
    
    /**
     * Actualiza el hash de contraseña de un usuario
     *
     * @param int $userId ID del usuario
     * @param string $password Contraseña en texto plano
     * @return bool
     */
    private function updatePasswordHash($userId, $password) {
        $hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        
        $stmt = $this->conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        if (!$stmt) {
            logError('Error al preparar actualización de contraseña', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("si", $hash, $userId);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar actualización de contraseña', ['error' => $stmt->error]);
            return false;
        }
        
        logInfo('Contraseña actualizada a nuevo formato hash', ['user_id' => $userId]);
        return true;
    }
    
    /**
     * Verifica si una cuenta está bloqueada por demasiados intentos fallidos
     *
     * @param string $username Nombre de usuario o email
     * @return bool
     */
    public function isAccountLocked($username) {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) as attempts 
            FROM login_attempts 
            WHERE (username = ? OR ip = ?) 
            AND success = 0 
            AND attempt_time > DATE_SUB(NOW(), INTERVAL ? SECOND)
        ");
        
        if (!$stmt) {
            logError('Error al verificar bloqueo de cuenta', ['error' => $this->conn->error]);
            return false;
        }
        
        $ip = $_SERVER['REMOTE_ADDR'];
        $lockout_time = self::LOCKOUT_TIME;
        
        $stmt->bind_param("ssi", $username, $ip, $lockout_time);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar verificación de bloqueo', ['error' => $stmt->error]);
            return false;
        }
        
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['attempts'] >= self::MAX_LOGIN_ATTEMPTS;
    }
    
    /**
     * Registra un intento de login fallido
     *
     * @param string $username Nombre de usuario o email
     * @return bool
     */
    private function registerFailedAttempt($username) {
        $stmt = $this->conn->prepare("
            INSERT INTO login_attempts (username, ip, user_agent, success)
            VALUES (?, ?, ?, 0)
        ");
        
        if (!$stmt) {
            logError('Error al registrar intento fallido', ['error' => $this->conn->error]);
            return false;
        }
        
        $ip = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->bind_param("sss", $username, $ip, $user_agent);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar registro de intento fallido', ['error' => $stmt->error]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Limpia los intentos fallidos de un usuario cuando inicia sesión correctamente
     *
     * @param string $username Nombre de usuario o email
     * @return bool
     */
    private function clearFailedAttempts($username) {
        $stmt = $this->conn->prepare("
            DELETE FROM login_attempts 
            WHERE (username = ? OR ip = ?) 
            AND success = 0
        ");
        
        if (!$stmt) {
            logError('Error al limpiar intentos fallidos', ['error' => $this->conn->error]);
            return false;
        }
        
        $ip = $_SERVER['REMOTE_ADDR'];
        
        $stmt->bind_param("ss", $username, $ip);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar limpieza de intentos fallidos', ['error' => $stmt->error]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Actualiza la fecha de último login de un usuario
     *
     * @param int $userId ID del usuario
     * @return bool
     */
    private function updateLastLogin($userId) {
        $stmt = $this->conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        
        if (!$stmt) {
            logError('Error al actualizar último login', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("i", $userId);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar actualización de último login', ['error' => $stmt->error]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Registra un inicio de sesión exitoso
     *
     * @param int $userId ID del usuario
     * @param string $username Nombre de usuario
     * @return bool
     */
    public function registerSuccessfulLogin($userId, $username) {
        $stmt = $this->conn->prepare("
            INSERT INTO login_attempts (
                username, ip, user_agent, success, user_id
            ) VALUES (?, ?, ?, 1, ?)
        ");
        
        if (!$stmt) {
            logError('Error al registrar login exitoso', ['error' => $this->conn->error]);
            return false;
        }
        
        $ip = $_SERVER['REMOTE_ADDR'];
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->bind_param("sssi", $username, $ip, $user_agent, $userId);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar registro de login exitoso', ['error' => $stmt->error]);
            return false;
        }
        
        return true;
    }
    
    /**
     * Verifica si un usuario tiene un rol específico
     *
     * @param int $userId ID del usuario
     * @param string|array $roles Rol o array de roles permitidos
     * @return bool
     */
    public function hasRole($userId, $roles) {
        $stmt = $this->conn->prepare("SELECT role FROM users WHERE id = ?");
        
        if (!$stmt) {
            logError('Error al verificar rol de usuario', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("i", $userId);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar verificación de rol', ['error' => $stmt->error]);
            return false;
        }
        
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            return false;
        }
        
        $user = $result->fetch_assoc();
        
        if (is_array($roles)) {
            return in_array($user['role'], $roles);
        }
        
        return $user['role'] === $roles;
    }
    
    /**
     * Genera un token de reset de contraseña
     *
     * @param string $email Email del usuario
     * @return string|false Token generado o false si hay error
     */
    public function generatePasswordResetToken($email) {
        $stmt = $this->conn->prepare("
            SELECT id FROM users WHERE email = ?
        ");
        
        if (!$stmt) {
            logError('Error al buscar usuario para reset de contraseña', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("s", $email);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar búsqueda de usuario para reset', ['error' => $stmt->error]);
            return false;
        }
        
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            logWarning('Intento de reset de contraseña con email no encontrado', ['email' => $email]);
            return false;
        }
        
        $user = $result->fetch_assoc();
        $userId = $user['id'];
        
        // Generar token seguro
        $token = bin2hex(random_bytes(32));
        $expiryTime = date('Y-m-d H:i:s', time() + self::TOKEN_EXPIRY);
        
        // Guardar token en la base de datos
        $updateStmt = $this->conn->prepare("
            UPDATE users 
            SET reset_token = ?, reset_token_expiry = ? 
            WHERE id = ?
        ");
        
        if (!$updateStmt) {
            logError('Error al preparar actualización de token', ['error' => $this->conn->error]);
            return false;
        }
        
        $updateStmt->bind_param("ssi", $token, $expiryTime, $userId);
        
        if (!$updateStmt->execute()) {
            logError('Error al ejecutar actualización de token', ['error' => $updateStmt->error]);
            return false;
        }
        
        return $token;
    }
    
    /**
     * Verifica si un token de reset de contraseña es válido
     *
     * @param string $token Token a verificar
     * @return int|false ID del usuario si es válido o false si no es válido
     */
    public function validateResetToken($token) {
        $stmt = $this->conn->prepare("
            SELECT id 
            FROM users 
            WHERE reset_token = ? 
            AND reset_token_expiry > NOW()
        ");
        
        if (!$stmt) {
            logError('Error al validar token de reset', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("s", $token);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar validación de token', ['error' => $stmt->error]);
            return false;
        }
        
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            return false;
        }
        
        $user = $result->fetch_assoc();
        return $user['id'];
    }
    
    /**
     * Cambia la contraseña de un usuario
     *
     * @param int $userId ID del usuario
     * @param string $newPassword Nueva contraseña
     * @param bool $clearToken Si se debe limpiar el token de reset
     * @return bool
     */
    public function changePassword($userId, $newPassword, $clearToken = true) {
        // Validar longitud de la contraseña
        if (strlen($newPassword) < self::PASSWORD_MIN_LENGTH) {
            throw new \Exception('password_too_short');
        }
        
        // Generar hash de la nueva contraseña
        $hash = password_hash($newPassword, PASSWORD_BCRYPT, ['cost' => 12]);
        
        // Preparar la consulta
        $query = "UPDATE users SET password = ?";
        
        // Si se debe limpiar el token, añadir a la consulta
        if ($clearToken) {
            $query .= ", reset_token = NULL, reset_token_expiry = NULL";
        }
        
        $query .= " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        
        if (!$stmt) {
            logError('Error al preparar cambio de contraseña', ['error' => $this->conn->error]);
            return false;
        }
        
        $stmt->bind_param("si", $hash, $userId);
        
        if (!$stmt->execute()) {
            logError('Error al ejecutar cambio de contraseña', ['error' => $stmt->error]);
            return false;
        }
        
        logInfo('Contraseña cambiada exitosamente', ['user_id' => $userId]);
        return true;
    }
    
    /**
     * Verifica requisitos de seguridad para una contraseña
     * 
     * @param string $password La contraseña a verificar
     * @return array Errores encontrados o array vacío si es válida
     */
    public function validatePasswordStrength($password) {
        $errors = [];
        
        // Verificar longitud mínima
        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            $errors[] = 'La contraseña debe tener al menos ' . self::PASSWORD_MIN_LENGTH . ' caracteres';
        }
        
        // Verificar complejidad (letras mayúsculas, minúsculas y números)
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'La contraseña debe incluir al menos una letra mayúscula';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'La contraseña debe incluir al menos una letra minúscula';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'La contraseña debe incluir al menos un número';
        }
        
        // Verificar caracteres especiales
        if (!preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $password)) {
            $errors[] = 'La contraseña debe incluir al menos un carácter especial (!@#$%^&*()-_=+{};:,<.>)';
        }
        
        return $errors;
    }
}