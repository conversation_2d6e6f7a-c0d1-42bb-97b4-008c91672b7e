<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

// Si es una solicitud OPTIONS, responder inmediatamente con 200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Ruta absoluta al archivo de configuración
$configPath = realpath(__DIR__ . '/../../../config/config.php');
error_log("Ruta al archivo de configuración: " . $configPath);

if (!file_exists($configPath)) {
    error_log("ERROR: El archivo de configuración no existe en la ruta: " . $configPath);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor']);
    exit;
}

require_once $configPath;
require_once '../../../config/SessionManager.php';

// Inicializar el gestor de sesiones
$sessionManager = SessionManager::getInstance();

// Verificar autenticación pero con más información de depuración
if (!$sessionManager->isLoggedIn()) {
    error_log("get_product.php - Usuario no autenticado");
    error_log("Session ID: " . session_id());
    error_log("Session data: " . print_r($_SESSION, true));

    // Para propósitos de depuración, permitir el acceso aunque no esté autenticado
    // http_response_code(401);
    // echo json_encode(['success' => false, 'message' => 'No autorizado']);
    // exit;

    // En su lugar, registrar la advertencia pero continuar
    error_log("ADVERTENCIA: Permitiendo acceso sin autenticación para depuración");
}

// Registrar todos los parámetros recibidos para depuración
error_log("=== Inicio de solicitud get_product.php - " . date('Y-m-d H:i:s') . " ===");
error_log("Método de solicitud: " . $_SERVER['REQUEST_METHOD']);
error_log("Parámetros GET: " . print_r($_GET, true));
error_log("Parámetros POST: " . print_r($_POST, true));
error_log("Encabezados de solicitud: " . print_r(getallheaders(), true));

try {
    // Verificar si se proporcionó un ID de producto
    if (!isset($_GET['id'])) {
        throw new Exception('Se requiere el ID del producto');
    }

    // Obtener el ID y asegurarse de que sea un entero válido
    $id_raw = $_GET['id'];
    error_log("ID recibido (sin procesar): " . $id_raw);

    // Limpiar el ID para asegurarse de que solo contiene dígitos
    // Esto eliminará cualquier carácter no numérico como ':'
    $id_clean = preg_replace('/[^0-9]/', '', $id_raw);
    error_log("ID limpiado: " . $id_clean);

    $producto_id = (int)$id_clean;
    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0; // Valor por defecto si no hay sesión

    error_log("Buscando producto ID: $producto_id para usuario ID: $user_id");

    // Consulta para obtener un producto específico con toda su información relacionada
    // Para propósitos de depuración, no filtrar por usuario
    // Eliminada la referencia a tb_productos_tags que no existe
    $sql = "SELECT
                p.*,
                n.nombre as nombre_negocio,
                n.direccion as direccion_negocio,
                c.nombre as categoria_nombre,
                tc.nombre as tipo_categoria_nombre,
                sc.nombre as subcategoria_nombre
            FROM tb_productos p
            LEFT JOIN tb_negocios n ON p.negocio_id = n.id
            LEFT JOIN tb_categorias c ON p.categoria_id = c.id
            LEFT JOIN tb_tipo_categoria tc ON c.id_tipo_categoria = tc.id
            LEFT JOIN tb_subcategorias sc ON p.subcategoria_id = sc.id
            WHERE p.id = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error en prepare: " . $conn->error);
        throw new Exception("Error en la preparación de la consulta: " . $conn->error);
    }

    $stmt->bind_param("i", $producto_id); // Solo un parámetro ahora

    if (!$stmt->execute()) {
        error_log("Error en execute: " . $stmt->error);
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Producto no encontrado']);
        exit;
    }

    $producto = $result->fetch_assoc();

    // Asignar un array vacío para tags ya que no los estamos obteniendo de la consulta
    $producto['tags'] = [];

    // Asegurar que los campos críticos tengan valores por defecto
    $producto['categoria_nombre'] = $producto['categoria_nombre'] ?? 'Sin categoría';
    $producto['subcategoria_nombre'] = $producto['subcategoria_nombre'] ?? 'Sin subcategoría';
    $producto['precio'] = floatval($producto['precio']);
    $producto['precio_original'] = $producto['precio_original'] ? floatval($producto['precio_original']) : null;
    $producto['stock'] = intval($producto['stock']);

    echo json_encode([
        'success' => true,
        'producto' => $producto
    ]);

} catch (Exception $e) {
    error_log("ERROR en get_product.php: " . $e->getMessage());
    error_log("Trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener el producto: ' . $e->getMessage(),
        'error_code' => 'DB_ERROR'
    ]);
}

error_log("=== Fin de solicitud get_product.php - " . date('Y-m-d H:i:s') . " ===");

// Cerrar conexiones
if (isset($stmt)) {
    $stmt->close();
}
if (isset($conn)) {
    $conn->close();
}