/**
 * plan-popups.js
 * Script para manejar los pop-ups de planes de suscripción
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando manejador de pop-ups de planes');

    // Ocultar inmediatamente el popup de bienvenida al cargar la página
    const welcomePopup = document.getElementById('welcome-popup');
    if (welcomePopup) {
        console.log('Ocultando popup de bienvenida al inicio');
        welcomePopup.style.display = 'none';
    }

    // Inicializar los manejadores de eventos
    initPlanPopups();

    /**
     * Inicializa todos los manejadores de eventos para los pop-ups de planes
     */
    function initPlanPopups() {
        // Botones para mostrar comparación de planes
        setupComparisonButtons();

        // Botones para cerrar los pop-ups
        setupCloseButtons();

        // Eventos para los planes individuales
        setupPlanButtons();

        // Eventos para los radio buttons de tipo de negocio
        setupBusinessTypeEvents();

        // Configurar el pop-up de bienvenida
        setupWelcomePopup();

        // Configurar los campos de facturación en el paso 5
        setupFacturacionFields();
    }

    /**
     * Configura los eventos para los botones de tipo de negocio
     */
    function setupBusinessTypeEvents() {
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');

        tipoNegocioRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                const tipoNegocio = this.value;
                console.log('Tipo de negocio cambiado a:', tipoNegocio);

                // Actualizar la visibilidad de los botones de comparación según el tipo de negocio
                updateComparisonButtonsVisibility(tipoNegocio);
            });
        });
    }

    /**
     * Actualiza la visibilidad de los botones de comparación según el tipo de negocio
     * @param {string} tipoNegocio - Tipo de negocio seleccionado (venta, servicios, arriendo)
     */
    function updateComparisonButtonsVisibility(tipoNegocio) {
        // Ocultar todos los botones de comparación
        const allComparisonButtons = document.querySelectorAll('.btn-comparar-planes');
        allComparisonButtons.forEach(button => {
            button.style.display = 'none';
        });

        // Mostrar el botón correspondiente al tipo de negocio
        if (tipoNegocio === 'venta') {
            const btnProductos = document.getElementById('comparar-planes-productos');
            if (btnProductos) btnProductos.style.display = 'block';
        } else if (tipoNegocio === 'servicios' || tipoNegocio === 'arriendo') {
            const btnServicios = document.getElementById('comparar-planes-servicios');
            if (btnServicios) btnServicios.style.display = 'block';
        }
    }

    /**
     * Configura los botones para mostrar los pop-ups de comparación de planes
     */
    function setupComparisonButtons() {
        // Botón para comparar planes de productos
        const btnCompararProductos = document.getElementById('comparar-planes-productos');
        if (btnCompararProductos) {
            btnCompararProductos.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('Mostrando comparación de planes para productos');
                showComparisonPopup('productos');
            });
        }

        // Botón para comparar planes de servicios
        const btnCompararServicios = document.getElementById('comparar-planes-servicios');
        if (btnCompararServicios) {
            btnCompararServicios.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('Mostrando comparación de planes para servicios');
                showComparisonPopup('servicios');
            });
        }
    }

    /**
     * Configura los botones para cerrar los pop-ups de comparación
     */
    function setupCloseButtons() {
        // Botones de cierre para el pop-up de productos
        const closeButtonsProductos = document.querySelectorAll('#plans-comparison-overlay-productos .comparison-popup-close, #plans-comparison-overlay-productos .comparison-popup-close-btn');
        closeButtonsProductos.forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                hideComparisonPopup('productos');
            });
        });

        // Botones de cierre para el pop-up de servicios
        const closeButtonsServicios = document.querySelectorAll('#plans-comparison-overlay-servicios .comparison-popup-close, #plans-comparison-overlay-servicios .comparison-popup-close-btn');
        closeButtonsServicios.forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                hideComparisonPopup('servicios');
            });
        });

        // Cerrar al hacer clic fuera del pop-up
        const overlayProductos = document.getElementById('plans-comparison-overlay-productos');
        if (overlayProductos) {
            overlayProductos.addEventListener('click', function(event) {
                if (event.target === this) {
                    hideComparisonPopup('productos');
                }
            });
        }

        const overlayServicios = document.getElementById('plans-comparison-overlay-servicios');
        if (overlayServicios) {
            overlayServicios.addEventListener('click', function(event) {
                if (event.target === this) {
                    hideComparisonPopup('servicios');
                }
            });
        }
    }

    /**
     * Configura los eventos para los botones de planes individuales
     */
    function setupPlanButtons() {
        // Planes de productos
        const planesProductos = document.querySelectorAll('#step4-productos .subscription-plan');
        planesProductos.forEach(plan => {
            plan.addEventListener('click', function(event) {
                // Evitar que se propague el evento si se hizo clic en un botón dentro del plan
                if (event.target.tagName === 'BUTTON' || event.target.closest('button')) {
                    return;
                }

                // Seleccionar el plan y mostrar el pop-up
                selectPlan(this, 'productos');
                showPlanPopup(this.id, 'productos');
            });
        });

        // Planes de servicios
        const planesServicios = document.querySelectorAll('#step4-servicios .subscription-plan');
        planesServicios.forEach(plan => {
            plan.addEventListener('click', function(event) {
                // Evitar que se propague el evento si se hizo clic en un botón dentro del plan
                if (event.target.tagName === 'BUTTON' || event.target.closest('button')) {
                    return;
                }

                // Seleccionar el plan y mostrar el pop-up
                selectPlan(this, 'servicios');
                showPlanPopup(this.id, 'servicios');
            });
        });

        // Configurar botones para cerrar los pop-ups de planes individuales
        setupPlanPopupCloseButtons();
    }

    /**
     * Configura los botones para cerrar los pop-ups de planes individuales
     */
    function setupPlanPopupCloseButtons() {
        // Botones de cierre para todos los pop-ups de planes
        const closePlanButtons = document.querySelectorAll('.plan-popup-close, .plan-popup-close-btn');
        closePlanButtons.forEach(button => {
            button.addEventListener('click', function() {
                hidePlanPopups();
            });
        });

        // Cerrar al hacer clic fuera del pop-up
        const planPopupOverlays = document.querySelectorAll('.plan-popup-overlay');
        planPopupOverlays.forEach(overlay => {
            overlay.addEventListener('click', function(event) {
                if (event.target === this) {
                    hidePlanPopups();
                }
            });
        });
    }

    /**
     * Selecciona un plan y actualiza la interfaz
     * @param {HTMLElement} planElement - Elemento del plan seleccionado
     * @param {string} tipo - Tipo de plan ('productos' o 'servicios')
     */
    function selectPlan(planElement, tipo) {
        // Quitar la clase selected-plan de todos los planes del mismo tipo
        const planes = document.querySelectorAll(`#step4-${tipo} .subscription-plan`);
        planes.forEach(plan => {
            plan.classList.remove('selected-plan');
        });

        // Agregar la clase selected-plan al plan seleccionado
        planElement.classList.add('selected-plan');

        // Obtener el ID del plan seleccionado
        const planId = planElement.id;

        // Marcar el radio button correspondiente
        if (planId.includes('free')) {
            document.getElementById(`plan-gratuita-${tipo}`).checked = true;
        } else if (planId.includes('normal')) {
            document.getElementById(`plan-normal-${tipo}`).checked = true;
        } else if (planId.includes('premium')) {
            document.getElementById(`plan-premium-${tipo}`).checked = true;
        }
    }

    /**
     * Muestra el pop-up de un plan específico
     * @param {string} planId - ID del plan seleccionado
     * @param {string} tipo - Tipo de plan ('productos' o 'servicios')
     */
    function showPlanPopup(planId, tipo) {
        // Ocultar todos los pop-ups de planes
        hidePlanPopups();

        // Determinar qué pop-up mostrar según el ID del plan
        let popupId = '';

        if (planId.includes('free')) {
            popupId = `plan-free-popup-${tipo}`;
        } else if (planId.includes('normal')) {
            popupId = `plan-normal-popup-${tipo}`;
        } else if (planId.includes('premium')) {
            popupId = `plan-premium-popup-${tipo}`;
        }

        // Mostrar el pop-up correspondiente
        const popup = document.getElementById(popupId);
        if (popup) {
            popup.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
        } else {
            console.error(`No se encontró el pop-up para el plan ${planId}`);
        }
    }

    /**
     * Oculta todos los pop-ups de planes individuales
     */
    function hidePlanPopups() {
        const popups = document.querySelectorAll('.plan-popup-overlay');
        popups.forEach(popup => {
            popup.style.display = 'none';
        });
        document.body.style.overflow = ''; // Restaurar scroll
    }

    /**
     * Muestra el pop-up de comparación de planes
     * @param {string} tipo - Tipo de negocio ('productos' o 'servicios')
     */
    function showComparisonPopup(tipo) {
        const overlay = document.getElementById(`plans-comparison-overlay-${tipo}`);
        if (overlay) {
            overlay.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
        } else {
            console.error(`No se encontró el overlay para ${tipo}`);
        }
    }

    /**
     * Oculta el pop-up de comparación de planes
     * @param {string} tipo - Tipo de negocio ('productos' o 'servicios')
     */
    function hideComparisonPopup(tipo) {
        const overlay = document.getElementById(`plans-comparison-overlay-${tipo}`);
        if (overlay) {
            overlay.style.display = 'none';
            document.body.style.overflow = ''; // Restaurar scroll
        } else {
            console.error(`No se encontró el overlay para ${tipo}`);
        }
    }

    // Inicializar el estado según el tipo de negocio seleccionado al cargar la página
    const tipoNegocioSeleccionado = document.querySelector('input[name="tipo_negocio"]:checked');
    if (tipoNegocioSeleccionado) {
        updateComparisonButtonsVisibility(tipoNegocioSeleccionado.value);
    } else {
        // Si no hay ningún tipo de negocio seleccionado, mostrar todos los botones
        const allComparisonButtons = document.querySelectorAll('.btn-comparar-planes');
        allComparisonButtons.forEach(button => {
            button.style.display = 'block';
        });
    }

    /**
     * Configura el pop-up de bienvenida para el plan gratuito
     */
    function setupWelcomePopup() {
        // Obtener el pop-up de bienvenida (el original con recomendaciones)
        const welcomePopup = document.getElementById('welcome-popup');

        if (!welcomePopup) {
            console.error('No se encontró el pop-up de bienvenida');
            return;
        }

        // Forzar que el popup esté oculto al cargar la página
        welcomePopup.style.display = 'none';

        // Agregar un atributo para asegurarnos de que no se muestre
        welcomePopup.setAttribute('data-hidden', 'true');

        // Configurar el botón de cierre
        const closeBtn = welcomePopup.querySelector('.welcome-popup-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                welcomePopup.style.display = 'none';
                document.body.style.overflow = ''; // Restaurar scroll
            });
        }

        // Configurar el botón de finalizar
        const finishBtn = document.getElementById('welcome-finish');
        if (finishBtn) {
            finishBtn.addEventListener('click', function() {
                welcomePopup.style.display = 'none';
                document.body.style.overflow = ''; // Restaurar scroll
                document.getElementById('registerForm').submit();
            });
        }

        // Cerrar al hacer clic fuera del popup
        welcomePopup.addEventListener('click', function(event) {
            if (event.target === this) {
                this.style.display = 'none';
                document.body.style.overflow = ''; // Restaurar scroll
            }
        });

        // Modificar los botones de envío del formulario para mostrar el popup de bienvenida
        const submitFormBtn = document.getElementById('submit-form');
        if (submitFormBtn) {
            // Guardar la función original
            const originalClickHandler = submitFormBtn.onclick;

            // Reemplazar con nuestra función
            submitFormBtn.onclick = function(event) {
                const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

                if (!planSeleccionado) {
                    alert('Por favor, seleccione un plan antes de continuar.');
                    return;
                }

                // Si se seleccionó el plan gratuito, mostrar el popup de bienvenida
                if (planSeleccionado.value === 'gratuita') {
                    event.preventDefault(); // Prevenir el comportamiento predeterminado
                    welcomePopup.style.display = 'flex';
                    document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
                    return false;
                } else if (originalClickHandler) {
                    // Si no es plan gratuito, llamar al manejador original
                    return originalClickHandler.call(this, event);
                }
            };
        }

        const submitFormServiciosBtn = document.getElementById('submit-form-servicios');
        if (submitFormServiciosBtn) {
            // Guardar la función original
            const originalClickHandler = submitFormServiciosBtn.onclick;

            // Reemplazar con nuestra función
            submitFormServiciosBtn.onclick = function(event) {
                const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

                if (!planSeleccionado) {
                    alert('Por favor, seleccione un plan antes de continuar.');
                    return;
                }

                // Si se seleccionó el plan gratuito, mostrar el popup de bienvenida
                if (planSeleccionado.value === 'gratuita') {
                    event.preventDefault(); // Prevenir el comportamiento predeterminado
                    welcomePopup.style.display = 'flex';
                    document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo
                    return false;
                } else if (originalClickHandler) {
                    // Si no es plan gratuito, llamar al manejador original
                    return originalClickHandler.call(this, event);
                }
            };
        }
    }

    /**
     * Configura los campos de facturación en el paso 5
     */
    function setupFacturacionFields() {
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');
        const facturaFields = document.getElementById('factura-fields');

        if (!facturaRadio || !boletaRadio || !facturaFields) {
            console.log('No se encontraron los elementos de facturación');
            return;
        }

        console.log('Configurando campos de facturación');

        // Mostrar campos de factura cuando se selecciona "Factura"
        facturaRadio.addEventListener('change', function() {
            if (this.checked) {
                console.log('Factura seleccionada, mostrando campos');
                facturaFields.style.display = 'block';
                // Verificar campos al seleccionar factura
                checkDocumentoSelection();
            }
        });

        // Ocultar campos de factura cuando se selecciona "Boleta"
        boletaRadio.addEventListener('change', function() {
            if (this.checked) {
                console.log('Boleta seleccionada, ocultando campos');
                facturaFields.style.display = 'none';
                // Habilitar botón al seleccionar boleta
                checkDocumentoSelection();
            }
        });

        // Agregar evento de cambio a los campos de factura
        const facturaInputs = facturaFields.querySelectorAll('input');
        facturaInputs.forEach(input => {
            input.addEventListener('input', checkDocumentoSelection);
        });

        // Función para verificar la selección de documento y habilitar/deshabilitar el botón finalizar
        function checkDocumentoSelection() {
            const finalizarRegistroBtn = document.getElementById('finalizar-registro');

            if (!finalizarRegistroBtn) {
                console.error('No se encontró el botón finalizar-registro');
                return;
            }

            if (boletaRadio && boletaRadio.checked) {
                // Si seleccionó boleta, habilitar el botón
                finalizarRegistroBtn.disabled = false;
                finalizarRegistroBtn.classList.remove('btn-disabled');
            } else if (facturaRadio && facturaRadio.checked) {
                // Si seleccionó factura, verificar que todos los campos estén llenos
                if (validateFacturaFields()) {
                    finalizarRegistroBtn.disabled = false;
                    finalizarRegistroBtn.classList.remove('btn-disabled');
                } else {
                    finalizarRegistroBtn.disabled = true;
                    finalizarRegistroBtn.classList.add('btn-disabled');
                }
            } else {
                // Si no ha seleccionado ninguna opción, deshabilitar el botón
                finalizarRegistroBtn.disabled = true;
                finalizarRegistroBtn.classList.add('btn-disabled');
            }
        }

        // Función para validar los campos de factura
        function validateFacturaFields() {
            // Obtener todos los campos del formulario de factura
            const empresa = document.getElementById('empresa');
            const rutEmpresa = document.getElementById('rut_empresa');
            const direccionEmpresa = document.getElementById('direccion_empresa');
            const giroEmpresa = document.getElementById('giro_empresa');
            const telefonoEmpresa = document.getElementById('telefono_empresa');
            const correoEmpresa = document.getElementById('correo_empresa');

            // Verificar que todos los campos estén llenos
            if (empresa && rutEmpresa && direccionEmpresa && giroEmpresa && telefonoEmpresa && correoEmpresa) {
                return empresa.value.trim() !== '' &&
                       rutEmpresa.value.trim() !== '' &&
                       direccionEmpresa.value.trim() !== '' &&
                       giroEmpresa.value.trim() !== '' &&
                       telefonoEmpresa.value.trim() !== '' &&
                       correoEmpresa.value.trim() !== '';
            }

            return false;
        }

        // Verificar selección al cargar la página
        checkDocumentoSelection();
    }
});
