document.addEventListener('DOMContentLoaded', function() {
    // Vista previa de imagen
    const imageInput = document.getElementById('imagen');
    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('img');
                    preview.src = e.target.result;
                    preview.style.maxWidth = '200px';
                    preview.style.marginTop = '10px';
                    
                    const previewContainer = document.getElementById('imagePreview');
                    if (previewContainer) {
                        previewContainer.innerHTML = '';
                        previewContainer.appendChild(preview);
                    } else {
                        const container = document.createElement('div');
                        container.id = 'imagePreview';
                        container.appendChild(preview);
                        imageInput.parentNode.appendChild(container);
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Filtrado y búsqueda en la tabla de productos
    const searchInput = document.getElementById('searchProducts');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.products-table tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    }
});