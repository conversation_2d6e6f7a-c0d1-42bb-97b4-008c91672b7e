<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el sistema de logging
require_once '../config/logger.php';

echo "<h1>Actualización de la tabla tb_registros</h1>";

try {
    // Registrar inicio de la actualización
    logInfo("Iniciando actualización de la tabla tb_registros");

    // Incluir la configuración base
    require_once '../config/config.php';

    echo "<p>Conexión a la base de datos establecida correctamente.</p>";

    // Verificar si la tabla existe
    $tableCheckQuery = "SHOW TABLES LIKE 'tb_registros'";
    $tableCheckResult = $conn->query($tableCheckQuery);

    if ($tableCheckResult->num_rows > 0) {
        echo "<p style='color: green;'>✓ La tabla tb_registros existe.</p>";
        
        // Mostrar estructura actual
        echo "<h2>Estructura actual de la tabla:</h2>";
        $describeQuery = "DESCRIBE tb_registros";
        $describeResult = $conn->query($describeQuery);
        
        if ($describeResult->num_rows > 0) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";
            while ($row = $describeResult->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . (isset($row['Default']) ? htmlspecialchars($row['Default']) : 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Leer el archivo SQL
        $sqlFile = file_get_contents('update_tb_registros.sql');
        
        // Dividir el archivo en consultas individuales
        $queries = explode(';', $sqlFile);
        
        // Ejecutar cada consulta
        foreach ($queries as $query) {
            $query = trim($query);
            if (empty($query)) continue;
            
            // Saltar comentarios
            if (strpos($query, '--') === 0) continue;
            
            // Ejecutar la consulta
            if ($conn->query($query)) {
                echo "<p style='color: green;'>✓ Consulta ejecutada correctamente: " . htmlspecialchars(substr($query, 0, 100)) . "...</p>";
            } else {
                echo "<p style='color: red;'>✗ Error al ejecutar consulta: " . htmlspecialchars($conn->error) . "</p>";
                echo "<pre>" . htmlspecialchars($query) . "</pre>";
            }
        }
        
        // Mostrar estructura actualizada
        echo "<h2>Estructura actualizada de la tabla:</h2>";
        $describeQuery = "DESCRIBE tb_registros";
        $describeResult = $conn->query($describeQuery);
        
        if ($describeResult->num_rows > 0) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";
            while ($row = $describeResult->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . (isset($row['Default']) ? htmlspecialchars($row['Default']) : 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>✗ La tabla tb_registros no existe.</p>";
        
        // Crear la tabla
        echo "<h2>Creando la tabla tb_registros...</h2>";
        
        $createTableSQL = "CREATE TABLE IF NOT EXISTS tb_registros (
            `id` int NOT NULL AUTO_INCREMENT,
            `nombres` varchar(100) NOT NULL COMMENT 'Almacena dos nombres',
            `apellidos` varchar(100) NOT NULL COMMENT 'Almacena dos apellidos',
            `rut` varchar(10) NOT NULL COMMENT 'Numérico con posible K al final',
            `fechaNacimiento` varchar(10) NOT NULL COMMENT 'Formato dd/mm/aaaa',
            `sexo` enum('Masculino','Femenino','Otro') NOT NULL,
            `telefono` varchar(10) NOT NULL COMMENT 'Solo números',
            `region` varchar(50) NOT NULL,
            `comuna` varchar(50) NOT NULL,
            `direccion` varchar(150) NOT NULL COMMENT 'Letras y números',
            `NombreUsuario` varchar(50) NOT NULL COMMENT 'Letras y números',
            `mail` varchar(100) NOT NULL COMMENT 'Letras, símbolos y números',
            `mailRespaldo` varchar(100) DEFAULT NULL COMMENT 'Letras, símbolos y números',
            `contraseña` varchar(255) NOT NULL COMMENT 'Almacena contraseña encriptada',
            `localFisico` enum('Si','No') NOT NULL COMMENT 'Indica si tiene local físico',
            `fecha_registro` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci";
        
        if ($conn->query($createTableSQL)) {
            echo "<p style='color: green;'>✓ Tabla tb_registros creada correctamente.</p>";
            
            // Mostrar estructura de la tabla
            echo "<h2>Estructura de la tabla:</h2>";
            $describeQuery = "DESCRIBE tb_registros";
            $describeResult = $conn->query($describeQuery);
            
            if ($describeResult->num_rows > 0) {
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";
                while ($row = $describeResult->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                    echo "<td>" . (isset($row['Default']) ? htmlspecialchars($row['Default']) : 'NULL') . "</td>";
                    echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: red;'>✗ Error al crear la tabla: " . htmlspecialchars($conn->error) . "</p>";
        }
    }
    
    echo "<p><a href='register.php'>Volver a la página de registro</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    logException($e, "Error en update_table.php");
}

// Cerrar la conexión
$conn->close();
?>
