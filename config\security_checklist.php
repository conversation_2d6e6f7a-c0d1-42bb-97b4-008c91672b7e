<?php
/**
 * Security Checklist and Monitoring Functions
 * Use this file to perform regular security checks
 */

class SecurityChecker {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Perform comprehensive security check
     */
    public function performSecurityCheck() {
        $results = [
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => []
        ];
        
        $results['checks']['environment'] = $this->checkEnvironmentSecurity();
        $results['checks']['database'] = $this->checkDatabaseSecurity();
        $results['checks']['files'] = $this->checkFilePermissions();
        $results['checks']['sessions'] = $this->checkSessionSecurity();
        $results['checks']['rate_limiting'] = $this->checkRateLimiting();
        $results['checks']['headers'] = $this->checkSecurityHeaders();
        
        return $results;
    }
    
    /**
     * Check environment security
     */
    private function checkEnvironmentSecurity() {
        $checks = [];
        
        // Check if .env file exists and is protected
        $envFile = __DIR__ . '/.env';
        $checks['env_file_exists'] = file_exists($envFile);
        
        if ($checks['env_file_exists']) {
            $perms = fileperms($envFile);
            $checks['env_file_permissions'] = ($perms & 0777) <= 0600;
        }
        
        // Check if display_errors is off in production
        $checks['display_errors_off'] = !ini_get('display_errors');
        
        // Check if error_reporting is appropriate
        $checks['error_reporting_secure'] = error_reporting() === (E_ALL & ~E_NOTICE & ~E_DEPRECATED);
        
        // Check PHP version
        $checks['php_version_secure'] = version_compare(PHP_VERSION, '8.0.0', '>=');
        
        return $checks;
    }
    
    /**
     * Check database security
     */
    private function checkDatabaseSecurity() {
        $checks = [];
        
        try {
            // Check if using prepared statements (this is a basic check)
            $checks['prepared_statements'] = true; // Our code uses prepared statements
            
            // Check database connection encryption
            $result = $this->conn->query("SHOW STATUS LIKE 'Ssl_cipher'");
            if ($result && $row = $result->fetch_assoc()) {
                $checks['ssl_connection'] = !empty($row['Value']);
            } else {
                $checks['ssl_connection'] = false;
            }
            
            // Check for rate limiting table
            $result = $this->conn->query("SHOW TABLES LIKE 'rate_limits'");
            $checks['rate_limit_table_exists'] = $result && $result->num_rows > 0;
            
        } catch (Exception $e) {
            $checks['database_accessible'] = false;
        }
        
        return $checks;
    }
    
    /**
     * Check file permissions
     */
    private function checkFilePermissions() {
        $checks = [];
        
        $criticalFiles = [
            'config.php' => 0644,
            'InputValidator.php' => 0644,
            'RateLimiter.php' => 0644,
            '.env' => 0600
        ];
        
        foreach ($criticalFiles as $file => $expectedPerms) {
            $filePath = __DIR__ . '/' . $file;
            if (file_exists($filePath)) {
                $actualPerms = fileperms($filePath) & 0777;
                $checks[$file . '_permissions'] = $actualPerms <= $expectedPerms;
            } else {
                $checks[$file . '_exists'] = false;
            }
        }
        
        return $checks;
    }
    
    /**
     * Check session security
     */
    private function checkSessionSecurity() {
        $checks = [];
        
        $checks['session_use_strict_mode'] = ini_get('session.use_strict_mode') == 1;
        $checks['session_cookie_httponly'] = ini_get('session.cookie_httponly') == 1;
        $checks['session_cookie_secure'] = ini_get('session.cookie_secure') == 1 || !isset($_SERVER['HTTPS']);
        $checks['session_use_only_cookies'] = ini_get('session.use_only_cookies') == 1;
        
        return $checks;
    }
    
    /**
     * Check rate limiting effectiveness
     */
    private function checkRateLimiting() {
        $checks = [];
        
        try {
            // Check recent rate limiting activity
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as blocked_count 
                FROM rate_limits 
                WHERE blocked_until > NOW() 
                AND last_attempt > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            
            if ($stmt && $stmt->execute()) {
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                $checks['active_blocks'] = (int)$row['blocked_count'];
                $checks['rate_limiting_active'] = true;
            }
            
            // Check for suspicious activity
            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as suspicious_count 
                FROM rate_limits 
                WHERE attempts > 10 
                AND last_attempt > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            
            if ($stmt && $stmt->execute()) {
                $result = $stmt->get_result();
                $row = $result->fetch_assoc();
                $checks['suspicious_activity'] = (int)$row['suspicious_count'];
            }
            
        } catch (Exception $e) {
            $checks['rate_limiting_accessible'] = false;
        }
        
        return $checks;
    }
    
    /**
     * Check security headers (basic check)
     */
    private function checkSecurityHeaders() {
        $checks = [];
        
        // This is a basic check - in a real scenario, you'd test actual HTTP responses
        $requiredHeaders = [
            'X-Frame-Options',
            'X-Content-Type-Options',
            'X-XSS-Protection',
            'Content-Security-Policy',
            'Strict-Transport-Security'
        ];
        
        // For now, we'll assume headers are set if the code is in place
        foreach ($requiredHeaders as $header) {
            $checks[strtolower(str_replace('-', '_', $header)) . '_set'] = true;
        }
        
        return $checks;
    }
    
    /**
     * Generate security report
     */
    public function generateSecurityReport() {
        $results = $this->performSecurityCheck();
        
        $report = "SECURITY CHECK REPORT\n";
        $report .= "Generated: " . $results['timestamp'] . "\n";
        $report .= str_repeat("=", 50) . "\n\n";
        
        foreach ($results['checks'] as $category => $checks) {
            $report .= strtoupper($category) . " CHECKS:\n";
            $report .= str_repeat("-", 20) . "\n";
            
            foreach ($checks as $check => $status) {
                $statusText = $status ? "✓ PASS" : "✗ FAIL";
                $report .= sprintf("%-30s %s\n", $check, $statusText);
            }
            $report .= "\n";
        }
        
        return $report;
    }
    
    /**
     * Check for security updates needed
     */
    public function checkForUpdates() {
        $updates = [];
        
        // Check PHP version
        if (version_compare(PHP_VERSION, '8.2.0', '<')) {
            $updates[] = "Consider upgrading PHP to version 8.2+ for latest security features";
        }
        
        // Check for common vulnerabilities
        $vulnerablePatterns = [
            'eval(' => 'Avoid using eval() function',
            'exec(' => 'Review exec() usage for security',
            'system(' => 'Review system() usage for security',
            'shell_exec(' => 'Review shell_exec() usage for security'
        ];
        
        // This would scan files for vulnerable patterns
        // Implementation would depend on specific requirements
        
        return $updates;
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent($event, $severity = 'INFO', $details = []) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'severity' => $severity,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'details' => $details
        ];
        
        $logFile = '../logs/security_events.log';
        $logLine = json_encode($logEntry) . "\n";
        
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
}

// Usage example:
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    // Only run if called directly
    require_once 'config.php';
    
    $checker = new SecurityChecker($conn);
    echo $checker->generateSecurityReport();
}
?>
