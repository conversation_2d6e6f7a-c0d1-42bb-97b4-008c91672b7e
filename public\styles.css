/* Estilos base del header */
.main-header {
    background: linear-gradient(135deg, #1a237e 0%, #0d47a1 100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 1.25rem 1rem;
    position: relative;
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.logo h1 {
    margin: 0;
    color: #ffffff;
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.search-bar {
    display: flex;
    flex: 1;
    max-width: 600px;
    margin: 0 2rem;
    position: relative;
}

.search-bar input {
    flex: 1;
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.search-bar button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    padding: 0 1.5rem;
    background: transparent;
    color: #1a237e;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-bar button:hover {
    color: #0d47a1;
    transform: scale(1.1);
}

.user-actions {
    display: flex;
    gap: 1.5rem;
}

.user-actions a {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.3rem;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-actions a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Estilos de navegación */
.header-nav {
    margin-top: 1rem;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.nav-list a {
    text-decoration: none;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
}

/* Media queries para diseño responsive */
@media screen and (max-width: 768px) {
    .menu-toggle {
        display: block;
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 1000;
        color: #333;
    }

    .header-top {
        flex-direction: column;
        gap: 1rem;
    }

    .search-bar {
        width: 100%;
        margin: 1rem 0;
    }

    .header-nav {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        background: #fff;
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        transition: 0.3s ease;
        z-index: 999;
    }

    .header-nav.active {
        left: 0;
    }

    .nav-list {
        flex-direction: column;
        padding: 4rem 1rem 1rem;
        gap: 1rem;
    }

    .nav-list li a {
        padding: 0.8rem;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .nav-list li a:hover {
        background-color: #f5f5f5;
    }

    .nav-list li a i {
        width: 24px;
        text-align: center;
    }
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

tr:hover {
    background-color: #ddd;
}