/*
 * register1.js - Funcionalidades para el formulario de registro
 * Villarrica a un CLICK
 */

document.addEventListener('DOMContentLoaded', function() {
    // Variables globales
    let currentStep = 1;
    const steps = document.querySelectorAll('.step');
    const formSteps = document.querySelectorAll('.form-step');
    const submitBtn = document.getElementById('submit-form');
    const termsCheckbox = document.getElementById('accept_terms');
    const termsError = document.getElementById('terms-error');
    
    // Inicializar pasos
    showStep(1);
    
    // Configurar validaciones y eventos
    setupStepNavigationEvents();
    setupFormValidation();
    setupPasswordToggle();
    setupRegionComunaSelector();
    setupPlanSelectionEvents();
    setupTermsCheckbox();
    
    // Botón finalizar ahora es type="button" => manejar click
    const finalBtn = document.getElementById('submit-form');
    if (finalBtn) {
        finalBtn.addEventListener('click', (e) => submitFormWithAjax(e));
    }
    
    // Función para mostrar un paso específico
    function showStep(stepNumber) {
        // Ocultar todos los pasos
        formSteps.forEach(step => {
            step.style.display = 'none';
            step.classList.remove('active');
        });
        steps.forEach(step => step.classList.remove('active'));

        // Mostrar el paso actual
        formSteps[stepNumber - 1].style.display = 'block';
        formSteps[stepNumber - 1].classList.add('active');
        steps[stepNumber - 1].classList.add('active');
        currentStep = stepNumber;
        
        // Si estamos en el último paso, validar para habilitar/deshabilitar el botón Finalizar
        if (stepNumber === 4) {
            validateStep4();
        }
    }
    
    // Configurar eventos de navegación entre pasos
    function setupStepNavigationEvents() {
        // Botones "Siguiente" para cada paso
        document.getElementById('step1-next').addEventListener('click', function() {
            if (validateStep1()) {
                showStep(2);
            }
        });
        
        document.getElementById('step2-next').addEventListener('click', function() {
            if (validateStep2()) {
                showStep(3);
            }
        });
        
        document.getElementById('step3-next').addEventListener('click', function() {
            if (validateStep3()) {
                showStep(4);
            }
        });
        
        // Botones "Anterior"
        const prevButtons = document.querySelectorAll('.btn-prev');
        prevButtons.forEach(button => {
            button.addEventListener('click', function() {
                showStep(currentStep - 1);
            });
        });
    }
    
    // Configurar validaciones de formulario
    function setupFormValidation() {
        // Validar campos al perder foco
        document.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('blur', function() {
                if (this.hasAttribute('required') && !this.value.trim()) {
                    this.classList.add('error');
                } else {
                    this.classList.remove('error');
                }
            });
            
            // Limpiar errores al empezar a escribir
            input.addEventListener('input', function() {
                this.classList.remove('error');
            });
        });
        
        // Validar contraseñas en tiempo real
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        const passwordMatchMessage = document.getElementById('password-match-message');
        
        if (password && confirmPassword) {
            [password, confirmPassword].forEach(input => {
                input.addEventListener('input', validatePasswordMatch);
            });
        }
        
        function validatePasswordMatch() {
            if (password.value && confirmPassword.value) {
                if (password.value !== confirmPassword.value) {
                    passwordMatchMessage.textContent = 'Las contraseñas no coinciden';
                    passwordMatchMessage.style.display = 'block';
                    confirmPassword.classList.add('error');
                    return false;
                } else {
                    passwordMatchMessage.textContent = '';
                    passwordMatchMessage.style.display = 'none';
                    confirmPassword.classList.remove('error');
                    return true;
                }
            }
            return true;
        }
    }
    
    // Configurar mostrar/ocultar contraseña
    function setupPasswordToggle() {
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });
        });
    }
    
    // Configurar selector de región y comuna
    function setupRegionComunaSelector() {
        const regionSelect = document.getElementById('region');
        const comunaSelect = document.getElementById('comuna');
        
        if (regionSelect && comunaSelect) {
            // Datos de comunas por región
            const comunasPorRegion = {
                arica: ['Arica', 'Camarones', 'Putre', 'General Lagos'],
                tarapaca: ['Iquique', 'Alto Hospicio', 'Pozo Almonte', 'Camiña', 'Colchane', 'Huara', 'Pica'],
                antofagasta: ['Antofagasta', 'Mejillones', 'Sierra Gorda', 'Taltal', 'Calama', 'Ollagüe', 'San Pedro de Atacama', 'Tocopilla', 'María Elena'],
                atacama: ['Copiapó', 'Caldera', 'Tierra Amarilla', 'Chañaral', 'Diego de Almagro', 'Vallenar', 'Alto del Carmen', 'Freirina', 'Huasco'],
                coquimbo: ['La Serena', 'Coquimbo', 'Andacollo', 'La Higuera', 'Paiguano', 'Vicuña', 'Illapel', 'Canela', 'Los Vilos', 'Salamanca', 'Ovalle', 'Combarbalá', 'Monte Patria', 'Punitaqui', 'Río Hurtado'],
                valparaiso: ['Valparaíso', 'Casablanca', 'Concón', 'Juan Fernández', 'Puchuncaví', 'Quintero', 'Viña del Mar', 'Isla de Pascua', 'Los Andes', 'Calle Larga', 'Rinconada', 'San Esteban', 'La Ligua', 'Cabildo', 'Papudo', 'Petorca', 'Zapallar', 'Quillota', 'Calera', 'Hijuelas', 'La Cruz', 'Nogales', 'San Antonio', 'Algarrobo', 'Cartagena', 'El Quisco', 'El Tabo', 'Santo Domingo', 'San Felipe', 'Catemu', 'Llaillay', 'Panquehue', 'Putaendo', 'Santa María', 'Quilpué', 'Limache', 'Olmué', 'Villa Alemana'],
                metropolitana: ['Santiago', 'Cerrillos', 'Cerro Navia', 'Conchalí', 'El Bosque', 'Estación Central', 'Huechuraba', 'Independencia', 'La Cisterna', 'La Florida', 'La Granja', 'La Pintana', 'La Reina', 'Las Condes', 'Lo Barnechea', 'Lo Espejo', 'Lo Prado', 'Macul', 'Maipú', 'Ñuñoa', 'Pedro Aguirre Cerda', 'Peñalolén', 'Providencia', 'Pudahuel', 'Quilicura', 'Quinta Normal', 'Recoleta', 'Renca', 'San Joaquín', 'San Miguel', 'San Ramón', 'Vitacura', 'Puente Alto', 'Pirque', 'San José de Maipo', 'Colina', 'Lampa', 'Tiltil', 'San Bernardo', 'Buin', 'Calera de Tango', 'Paine', 'Melipilla', 'Alhué', 'Curacaví', 'María Pinto', 'San Pedro', 'Talagante', 'El Monte', 'Isla de Maipo', 'Padre Hurtado', 'Peñaflor'],
                ohiggins: ['Rancagua', 'Codegua', 'Coinco', 'Coltauco', 'Doñihue', 'Graneros', 'Las Cabras', 'Machalí', 'Malloa', 'Mostazal', 'Olivar', 'Peumo', 'Pichidegua', 'Quinta de Tilcoco', 'Rengo', 'Requínoa', 'San Vicente', 'Pichilemu', 'La Estrella', 'Litueche', 'Marchihue', 'Navidad', 'Paredones', 'San Fernando', 'Chépica', 'Chimbarongo', 'Lolol', 'Nancagua', 'Palmilla', 'Peralillo', 'Placilla', 'Pumanque', 'Santa Cruz'],
                maule: ['Talca', 'Constitución', 'Curepto', 'Empedrado', 'Maule', 'Pelarco', 'Pencahue', 'Río Claro', 'San Clemente', 'San Rafael', 'Cauquenes', 'Chanco', 'Pelluhue', 'Curicó', 'Hualañé', 'Licantén', 'Molina', 'Rauco', 'Romeral', 'Sagrada Familia', 'Teno', 'Vichuquén', 'Linares', 'Colbún', 'Longaví', 'Parral', 'Retiro', 'San Javier', 'Villa Alegre', 'Yerbas Buenas'],
                nuble: ['Chillán', 'Bulnes', 'Cobquecura', 'Coelemu', 'Coihueco', 'Chillán Viejo', 'El Carmen', 'Ninhue', 'Ñiquén', 'Pemuco', 'Pinto', 'Portezuelo', 'Quillón', 'Quirihue', 'Ránquil', 'San Carlos', 'San Fabián', 'San Ignacio', 'San Nicolás', 'Treguaco', 'Yungay'],
                biobio: ['Concepción', 'Coronel', 'Chiguayante', 'Florida', 'Hualqui', 'Lota', 'Penco', 'San Pedro de la Paz', 'Santa Juana', 'Talcahuano', 'Tomé', 'Hualpén', 'Lebu', 'Arauco', 'Cañete', 'Contulmo', 'Curanilahue', 'Los Álamos', 'Tirúa', 'Los Ángeles', 'Antuco', 'Cabrero', 'Laja', 'Mulchén', 'Nacimiento', 'Negrete', 'Quilaco', 'Quilleco', 'San Rosendo', 'Santa Bárbara', 'Tucapel', 'Yumbel', 'Alto Biobío'],
                araucania: ['Temuco', 'Carahue', 'Cunco', 'Curarrehue', 'Freire', 'Galvarino', 'Gorbea', 'Lautaro', 'Loncoche', 'Melipeuco', 'Nueva Imperial', 'Padre Las Casas', 'Perquenco', 'Pitrufquén', 'Pucón', 'Saavedra', 'Teodoro Schmidt', 'Toltén', 'Vilcún', 'Villarrica', 'Cholchol', 'Angol', 'Collipulli', 'Curacautín', 'Ercilla', 'Lonquimay', 'Los Sauces', 'Lumaco', 'Purén', 'Renaico', 'Traiguén', 'Victoria'],
                losrios: ['Valdivia', 'Corral', 'Lanco', 'Los Lagos', 'Máfil', 'Mariquina', 'Paillaco', 'Panguipulli', 'La Unión', 'Futrono', 'Lago Ranco', 'Río Bueno'],
                loslagos: ['Puerto Montt', 'Calbuco', 'Cochamó', 'Fresia', 'Frutillar', 'Los Muermos', 'Llanquihue', 'Maullín', 'Puerto Varas', 'Castro', 'Ancud', 'Chonchi', 'Curaco de Vélez', 'Dalcahue', 'Puqueldón', 'Queilén', 'Quellón', 'Quemchi', 'Quinchao', 'Osorno', 'Puerto Octay', 'Purranque', 'Puyehue', 'Río Negro', 'San Juan de la Costa', 'San Pablo', 'Chaitén', 'Futaleufú', 'Hualaihué', 'Palena'],
                aysen: ['Coihaique', 'Lago Verde', 'Aysén', 'Cisnes', 'Guaitecas', 'Cochrane', 'O\'Higgins', 'Tortel', 'Chile Chico', 'Río Ibáñez'],
                magallanes: ['Punta Arenas', 'Laguna Blanca', 'Río Verde', 'San Gregorio', 'Cabo de Hornos', 'Antártica', 'Porvenir', 'Primavera', 'Timaukel', 'Natales', 'Torres del Paine']
            };
            
            // Actualizar comunas cuando cambie la región
            regionSelect.addEventListener('change', function() {
                // Habilitar el select de comunas
                comunaSelect.disabled = false;
                
                // Obtener las comunas de la región seleccionada
                const regionValue = this.value;
                const comunas = comunasPorRegion[regionValue] || [];
                
                // Limpiar las opciones actuales
                comunaSelect.innerHTML = '';
                
                // Si no hay comunas, mostrar mensaje predeterminado
                if (comunas.length === 0) {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'No hay comunas disponibles';
                    comunaSelect.appendChild(option);
                    comunaSelect.disabled = true;
                    return;
                }
                
                // Agregar la opción predeterminada
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Seleccione una comuna...';
                comunaSelect.appendChild(defaultOption);
                
                // Agregar las comunas
                comunas.forEach(comuna => {
                    const option = document.createElement('option');
                    option.value = comuna.toLowerCase();
                    option.textContent = comuna;
                    comunaSelect.appendChild(option);
                });
            });
        }
    }
    
    // Configurar selección de plan
    function setupPlanSelectionEvents() {
        // Selección de tipo de negocio (venta, servicios, arriendo)
        const tipoNegocioRadios = document.querySelectorAll('input[name="tipo_negocio"]');
        tipoNegocioRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('hidden_tipo_negocio').value = this.value;
            });
        });
        
        // Selección de planes
        const planOptions = document.querySelectorAll('.plan-option-button');
        planOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Actualizar selección visual
                document.querySelectorAll('.plan-option-button').forEach(btn => {
                    btn.closest('.plan-option').classList.remove('selected');
                });
                this.closest('.plan-option').classList.add('selected');
                
                // Guardar tipo y plan seleccionado
                const planType = this.getAttribute('data-plan');
                const businessType = this.getAttribute('data-type');
                
                // Actualizar campo oculto con el plan seleccionado
                document.getElementById('selected_subscription').value = planType;
                
                // También actualizar el plan preferido por defecto
                document.getElementById('plan_preferido').value = planType;
                
                // Validar paso 4 para habilitar/deshabilitar botón de finalizar
                validateStep4();
            });
        });
        
        // Selección de plan preferido
        const planPreferenceOptions = document.querySelectorAll('.plan-preference-option');
        const documentoOptionsContainer = document.getElementById('documento-options-container');
        
        if (planPreferenceOptions.length > 0) {
            planPreferenceOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Actualizar selección visual
                    planPreferenceOptions.forEach(btn => {
                        btn.classList.remove('selected');
                    });
                    this.classList.add('selected');
                    
                    // Guardar el plan preferido en el campo oculto
                    const planType = this.getAttribute('data-plan');
                    document.getElementById('plan_preferido').value = planType;
                    
                    // Mostrar opciones de documento si es plan normal o premium
                    if (planType === 'normal' || planType === 'premium') {
                        if (documentoOptionsContainer) documentoOptionsContainer.style.display = 'block';
                    } else {
                        if (documentoOptionsContainer) documentoOptionsContainer.style.display = 'none';
                    }
                });
            });
        }
        
        // Opciones de "local físico"
        const localFisicoOptions = document.querySelectorAll('input[name="local_fisico"]');
        localFisicoOptions.forEach(option => {
            option.addEventListener('change', function() {
                document.getElementById('hidden_local_fisico').value = this.value;
            });
        });
    }
    
    // Configurar checkbox de términos y condiciones
    function setupTermsCheckbox() {
        if (termsCheckbox) {
            termsCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    if (termsError) termsError.style.display = 'none';
                } else {
                    if (termsError) termsError.style.display = 'block';
                }
                validateStep4();
            });
        }
    }
    
    // Validar el paso 1 (Información Personal)
    function validateStep1() {
        const step1 = document.getElementById('step1');
        const requiredInputs = step1.querySelectorAll('input[required], select[required]');
        let isValid = true;
        
        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
            } else {
                input.classList.remove('error');
            }
        });
        
        return isValid;
    }
    
    // Validar el paso 2 (Datos de Cuenta)
    function validateStep2() {
        const step2 = document.getElementById('step2');
        const requiredInputs = step2.querySelectorAll('input[required]');
        let isValid = true;
        
        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
            } else {
                input.classList.remove('error');
            }
        });
        
        // Validar que las contraseñas coincidan
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        
        if (password.value !== confirmPassword.value) {
            confirmPassword.classList.add('error');
            document.getElementById('password-match-message').textContent = 'Las contraseñas no coinciden';
            document.getElementById('password-match-message').style.display = 'block';
            isValid = false;
        }
        
        return isValid;
    }
    
    // Validar el paso 3 (Datos del Negocio)
    function validateStep3() {
        const step3 = document.getElementById('step3');
        const requiredInputs = step3.querySelectorAll('input[required]');
        let isValid = true;
        
        requiredInputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
            } else {
                input.classList.remove('error');
            }
        });
        
        // Validar que se seleccionó local físico
        const localFisicoOptions = document.querySelectorAll('input[name="local_fisico"]');
        let localFisicoSelected = false;
        
        localFisicoOptions.forEach(option => {
            if (option.checked) {
                localFisicoSelected = true;
                // Guardar en campo oculto
                document.getElementById('hidden_local_fisico').value = option.value;
            }
        });
        
        if (!localFisicoSelected) {
            document.querySelector('.local-fisico-options').classList.add('error');
            isValid = false;
        }
        
        return isValid;
    }
    
    // Validar el paso 4 (Plan de Suscripción)
    function validateStep4() {
        let isValid = true;
        
        // Verificar que se ha seleccionado un plan
        const selectedPlan = document.getElementById('selected_subscription').value;
        if (!selectedPlan) {
            isValid = false;
        }
        
        // Verificar que se aceptaron los términos y condiciones
        if (!termsCheckbox.checked) {
            if (termsError) termsError.style.display = 'block';
            isValid = false;
        } else {
            if (termsError) termsError.style.display = 'none';
        }
        
        // Habilitar o deshabilitar el botón según la validación
        submitBtn.disabled = !isValid;
        
        return isValid;
    }
    
    // Utilidad simple para mostrar toast
    function showToast(message, success = true) {
        const toast = document.createElement('div');
        toast.className = 'toast ' + (success ? 'success' : 'error');
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(()=> toast.classList.add('visible'), 10);
        setTimeout(()=> {
            toast.classList.remove('visible');
            setTimeout(()=> document.body.removeChild(toast), 300);
        }, 4000);
    }
    
    // Función para enviar el formulario con AJAX
    window.submitFormWithAjax = function(event) {
        event.preventDefault(); // Prevenir envío tradicional del formulario
        
        // Validar cada paso
        const isStep1Valid = validateStep1();
        const isStep2Valid = validateStep2();
        const isStep3Valid = validateStep3();
        const isStep4Valid = validateStep4();
        
        // Si algún paso no es válido, mostrar ese paso
        if (!isStep1Valid) {
            showStep(1);
            return false;
        } else if (!isStep2Valid) {
            showStep(2);
            return false;
        } else if (!isStep3Valid) {
            showStep(3);
            return false;
        } else if (!isStep4Valid) {
            showStep(4);
            return false;
        }
        
        // Si todo es válido, enviar con AJAX
        const form = document.getElementById('registerForm');
        const formData = new FormData(form);
        
        // Mostrar indicador de carga
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Procesando registro...</p>';
        document.body.appendChild(loadingIndicator);
        
        // Desactivar botón submit para evitar doble envío
        const submitButton = document.getElementById('submit-form');
        if (submitButton) submitButton.disabled = true;
        
        console.log('Enviando formulario vía AJAX a process_register.php');
        
        // Realizar la petición AJAX
        fetch(form.action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Error del servidor: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // Eliminar indicador de carga
            document.body.removeChild(loadingIndicator);
            
            if (data.success) {
                console.log('Registro exitoso:', data);
                
                // Mostrar mensaje de éxito
                showToast(data.message, true);
                
                // Mostrar mensaje de éxito
                // Primero, construir URL con parámetros si tenemos datos del usuario
                let redirectUrl = data.redirect || 'registro_exitoso.php';
                
                // Si tenemos datos del usuario, agregarlos a la URL
                if (data.user_id) {
                    // Verificar si ya tiene parámetros
                    redirectUrl += (redirectUrl.includes('?') ? '&' : '?') + 'user_id=' + data.user_id;
                    
                    // Agregar el nombre si está disponible
                    if (data.nombre) {
                        redirectUrl += '&nombre=' + encodeURIComponent(data.nombre);
                    }
                }
                
                // Guardar la URL para usarla después
                const finalRedirectUrl = redirectUrl;
                
                // Mostrar el popup de bienvenida si existe
                const bienvenidaPopup = document.getElementById('bienvenida-popup-overlay');
                if (bienvenidaPopup) {
                    bienvenidaPopup.style.display = 'flex';
                    
                    // Configurar eventos de los botones de cierre para redirigir
                    const closeButtons = [
                        document.querySelector('.bienvenida-popup-close'),
                        document.querySelector('.btn-cerrar-bienvenida')
                    ];
                    
                    closeButtons.forEach(button => {
                        if (button) {
                            // Reemplazar el evento existente
                            button.removeEventListener('click', null);
                            button.addEventListener('click', function() {
                                bienvenidaPopup.style.display = 'none';
                                window.location.href = finalRedirectUrl;
                            });
                        }
                    });
                    
                    // Configurar redirección automática después de un tiempo
                    setTimeout(() => {
                        window.location.href = finalRedirectUrl;
                    }, 5000);
                } else {
                    // Si no hay popup, redirigir directamente
                    window.location.href = finalRedirectUrl;
                }
            } else {
                console.error('Error en el registro:', data.message);
                
                // Reactivar botón submit
                if (submitButton) submitButton.disabled = false;
                
                // Mostrar mensaje de error según el tipo
                if (data.message.includes('nombre de usuario ya está en uso') || 
                    data.message.includes('correo electrónico ya está en uso')) {
                    // Mostrar error de duplicado
                    const duplicateErrorPopup = document.getElementById('duplicate-error-popup-overlay');
                    if (duplicateErrorPopup) {
                        const errorMsg = document.getElementById('duplicate-error-message');
                        if (errorMsg) errorMsg.textContent = data.message;
                        duplicateErrorPopup.style.display = 'flex';
                    } else {
                        showToast(data.message || 'Error desconocido', false);
                    }
                } else if (data.message.includes('nombre del negocio ya está en uso')) {
                    // Mostrar error de nombre de negocio duplicado
                    const businessNameErrorPopup = document.getElementById('business-name-error-popup-overlay');
                    if (businessNameErrorPopup) {
                        businessNameErrorPopup.style.display = 'flex';
                    } else {
                        showToast(data.message || 'Error desconocido', false);
                    }
                } else {
                    // Otro tipo de error
                    showToast(data.message || 'Error desconocido', false);
                }
            }
        })
        .catch(err => {
            console.error('Error en la petición AJAX:', err);
            
            // Eliminar indicador de carga
            if (document.body.contains(loadingIndicator)) {
                document.body.removeChild(loadingIndicator);
            }
            
            // Reactivar botón submit
            if (submitButton) submitButton.disabled = false;
            
            // Mostrar mensaje de error
            showToast(err.message, false);
        });
        
        return false; // Prevenir el envío normal del formulario
    };
    
    // Selección de tipo de documento
    const documentoOptions = document.querySelectorAll('.documento-option');
    if (documentoOptions.length > 0) {
        documentoOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Actualizar selección visual
                documentoOptions.forEach(btn => {
                    btn.classList.remove('selected');
                });
                this.classList.add('selected');
                
                // Guardar el tipo de documento en el campo oculto
                const docType = this.getAttribute('data-documento');
                document.getElementById('tipo_documento').value = docType;
                
                // Si es factura, mostrar el popup de factura
                if (docType === 'factura') {
                    const facturaPopup = document.getElementById('factura-popup-overlay');
                    if (facturaPopup) facturaPopup.style.display = 'flex';
                }
            });
        });
    }
});