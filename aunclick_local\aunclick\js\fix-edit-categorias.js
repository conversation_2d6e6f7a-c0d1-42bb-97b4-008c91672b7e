// Script para arreglar la carga de categorías y subcategorías en el formulario de edición
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando fix-edit-categorias.js');

    // Obtener referencias a los elementos select del formulario de edición
    const editTipoCategoriasSelect = document.getElementById('editProductTipoCategoria');
    const editCategoriasSelect = document.getElementById('editProductCategory');
    const editSubcategoriasSelect = document.getElementById('editProductSubcategory');

    // Verificar si los elementos existen
    if (!editCategoriasSelect) {
        console.error('No se encontró el elemento select de categorías (editProductCategory)');
        return;
    }

    if (!editSubcategoriasSelect) {
        console.error('No se encontró el elemento select de subcategorías (editProductSubcategory)');
        return;
    }

    // Función para cargar categorías
    async function cargarCategorias() {
        try {
            console.log('Cargando categorías para el formulario de edición...');

            const response = await fetch('../public/API/productos/get_categorias.php', {
                method: 'GET',
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`Error al cargar categorías: ${response.status}`);
            }

            const data = await response.json();
            console.log('Datos de categorías recibidos:', data);

            // Verificar que la respuesta tenga la estructura esperada
            if (!data.success || !data.categorias || !Array.isArray(data.categorias)) {
                throw new Error('Formato de respuesta inválido para categorías');
            }

            // Limpiar opciones existentes
            editCategoriasSelect.innerHTML = '<option value="">Seleccionar categoría</option>';

            // Añadir nuevas opciones
            data.categorias.forEach(categoria => {
                const option = document.createElement('option');
                option.value = categoria.id;
                option.textContent = categoria.nombre;
                editCategoriasSelect.appendChild(option);
            });

            console.log(`${data.categorias.length} categorías cargadas correctamente`);

        } catch (error) {
            console.error('Error al cargar categorías:', error);
            // Mostrar mensaje de error en el select
            editCategoriasSelect.innerHTML = '<option value="">Error al cargar categorías</option>';
        }
    }

    // Función para cargar subcategorías según la categoría seleccionada
    async function cargarSubcategorias(categoriaId) {
        try {
            console.log(`Cargando subcategorías para categoría ID: ${categoriaId}`);

            // Deshabilitar el select mientras carga
            editSubcategoriasSelect.disabled = true;
            editSubcategoriasSelect.innerHTML = '<option value="">Cargando subcategorías...</option>';

            if (!categoriaId) {
                editSubcategoriasSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
                editSubcategoriasSelect.disabled = true;
                return;
            }

            // Usar la URL correcta basada en la estructura del proyecto
            const url = `/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${encodeURIComponent(categoriaId)}`;
            console.log('URL de solicitud:', url);

            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            console.log('Estado de la respuesta:', response.status);

            if (!response.ok) {
                throw new Error(`Error al cargar subcategorías: ${response.status}`);
            }

            const responseText = await response.text();
            console.log('Respuesta del servidor (raw):', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Datos parseados:', data);
            } catch (e) {
                console.error('Error al parsear JSON:', e);
                throw new Error('Respuesta del servidor inválida');
            }

            // Limpiar opciones existentes
            editSubcategoriasSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            editSubcategoriasSelect.disabled = false;

            // Verificar la estructura de la respuesta
            if (data.success && Array.isArray(data.subcategorias)) {
                // Añadir nuevas opciones
                data.subcategorias.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre;
                    editSubcategoriasSelect.appendChild(option);
                });
                console.log(`${data.subcategorias.length} subcategorías cargadas correctamente`);
            } else if (Array.isArray(data)) {
                // Formato alternativo (array directo)
                data.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                    editSubcategoriasSelect.appendChild(option);
                });
                console.log(`${data.length} subcategorías cargadas (formato array)`);
            } else {
                console.error('Formato de respuesta inesperado:', data);
                editSubcategoriasSelect.innerHTML = '<option value="">Error: formato inesperado</option>';
            }

        } catch (error) {
            console.error('Error al cargar subcategorías:', error);
            editSubcategoriasSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
            editSubcategoriasSelect.disabled = false;
        }
    }

    // Configurar evento para el select de categorías
    if (editCategoriasSelect) {
        editCategoriasSelect.addEventListener('change', function() {
            const categoriaId = this.value;
            console.log('Categoría seleccionada en formulario de edición:', categoriaId);
            cargarSubcategorias(categoriaId);
        });
    }

    // Cargar las categorías al iniciar
    cargarCategorias();

    console.log('fix-edit-categorias.js inicializado correctamente');
});
