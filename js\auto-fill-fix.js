/**
 * Auto-Fill Fix - Reparación de funcionalidad del botón Auto-llenar datos
 * Villarrica a un CLICK
 * Versión compatible con navegadores antiguos
 */

// Usar window.onload para asegurarnos de que toda la página está cargada
window.onload = function() {
    // Función para el botón "Auto-llenar datos" del paso 1
    var autoFillBtn = document.getElementById('auto-fill-btn');
    if (autoFillBtn) {
        console.log('Botón Auto-llenar datos encontrado - asignando evento');
        
        // Usar onclick para mayor compatibilidad
        autoFillBtn.onclick = function() {
            console.log('Botón Auto-llenar datos clickeado');
            // Llenar información personal
            document.getElementById('nombres').value = 'Juan <PERSON>';
            document.getElementById('apellidos').value = 'Pérez Gómez';
            document.getElementById('rut').value = '12345678-9';
            document.getElementById('fechaNacimiento').value = '01/01/1990';
            
            // Seleccionar sexo
            var sexoSelect = document.querySelector('select[name="sexo"]');
            if (sexoSelect) {
                sexoSelect.value = 'masculino';
            }
            
            // Teléfono
            document.getElementById('telefono').value = '12345678';
            
            // Seleccionar región
            var regionSelect = document.getElementById('region');
            if (regionSelect) {
                regionSelect.value = 'metropolitana';
                
                // Disparar evento change para cargar comunas
                // Usar método de creación de eventos compatible con IE
                var evt;
                try {
                    // Para navegadores modernos
                    evt = new Event('change', { bubbles: true });
                } catch (e) {
                    // Para IE
                    evt = document.createEvent('HTMLEvents');
                    evt.initEvent('change', true, true);
                }
                regionSelect.dispatchEvent(evt);
                
                // Esperar un momento para que se carguen las comunas
                setTimeout(function() {
                    // Seleccionar comuna
                    var comunaSelect = document.getElementById('comuna');
                    if (comunaSelect && comunaSelect.options.length > 1) {
                        comunaSelect.value = comunaSelect.options[1].value;
                    }
                }, 100);
            }
            
            // Dirección
            document.getElementById('direccion').value = 'Calle Ejemplo 123';
            
            // Prevenir comportamiento por defecto
            return false;
        };
    } else {
        console.log('Botón Auto-llenar datos no encontrado en el paso 1');
    }

    // Función para el botón "Auto-llenar datos" del paso 2
    var autoFillStep2Btn = document.getElementById('auto-fill-step2-btn');
    if (autoFillStep2Btn) {
        console.log('Botón Auto-llenar datos paso 2 encontrado - asignando evento');
        autoFillStep2Btn.onclick = function() {
            console.log('Botón Auto-llenar datos paso 2 clickeado');
            // Llenar información de cuenta
            document.getElementById('username').value = 'usuario_test';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('backup_email').value = '<EMAIL>';
            
            // Contraseña
            var password = document.getElementById('password');
            var confirmPassword = document.getElementById('confirm_password');
            
            if (password && confirmPassword) {
                password.value = 'Test1234';
                confirmPassword.value = 'Test1234';
                
                // Disparar eventos para validación
                try {
                    // Para navegadores modernos
                    var event = new Event('input', { bubbles: true });
                    password.dispatchEvent(event);
                    confirmPassword.dispatchEvent(event);
                } catch (e) {
                    // Para IE
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('input', true, true);
                    password.dispatchEvent(event);
                    confirmPassword.dispatchEvent(event);
                }
            }
            
            return false; // Prevenir comportamiento por defecto
        };
    } else {
        console.log('Botón Auto-llenar datos no encontrado en el paso 2');
    }

    // Función para el botón "Auto-llenar datos" del paso 3
    var autoFillStep3Btn = document.getElementById('auto-fill-step3-btn');
    if (autoFillStep3Btn) {
        console.log('Botón Auto-llenar datos paso 3 encontrado - asignando evento');
        autoFillStep3Btn.onclick = function() {
            console.log('Botón Auto-llenar datos paso 3 clickeado');
            // Llenar información del negocio
            document.getElementById('nombre_negocio').value = 'Negocio Test';
            document.getElementById('descripcion_negocio').value = 'Este es un negocio de prueba para el formulario de registro.';
            
            // Seleccionar opción "Sí" para local físico
            var localFisicoOptions = document.querySelectorAll('input[name="local_fisico"]');
            if (localFisicoOptions.length > 0) {
                localFisicoOptions[0].checked = true;
                
                // Guardar en campo oculto
                var hiddenLocalFisico = document.getElementById('hidden_local_fisico');
                if (hiddenLocalFisico) hiddenLocalFisico.value = 'si';
            }
            
            // Teléfono del negocio
            document.getElementById('telefono_negocio').value = '912345678';
            
            // WhatsApp (opcional)
            document.getElementById('whatsapp_negocio').value = '87654321';
            
            return false; // Prevenir comportamiento por defecto
        };
    } else {
        console.log('Botón Auto-llenar datos no encontrado en el paso 3');
    }

    // Función para el botón "Auto-llenar datos" del paso 4
    var autoFillStep4Btn = document.getElementById('auto-fill-step4-btn');
    if (autoFillStep4Btn) {
        console.log('Botón Auto-llenar datos paso 4 encontrado - asignando evento');
        autoFillStep4Btn.onclick = function() {
            console.log('Botón Auto-llenar datos paso 4 clickeado');
            
            // Seleccionar plan gratuito para venta
            var planFreeButton = document.querySelector('.plan-option-button[data-plan="free"][data-type="venta"]');
            if (planFreeButton) {
                planFreeButton.click();
            }
            
            // Seleccionar opción de plan preferido "Gratuita"
            var planPreferenceOptions = document.querySelectorAll('.plan-preference-option');
            if (planPreferenceOptions.length > 0) {
                planPreferenceOptions[0].click();
            }
            
            // Marcar términos y condiciones
            var termsCheckbox = document.getElementById('accept_terms');
            if (termsCheckbox) {
                termsCheckbox.checked = true;
                
                // Disparar evento change para validación
                try {
                    // Para navegadores modernos
                    var event = new Event('change', { bubbles: true });
                    termsCheckbox.dispatchEvent(event);
                } catch (e) {
                    // Para IE
                    var event = document.createEvent('HTMLEvents');
                    event.initEvent('change', true, true);
                    termsCheckbox.dispatchEvent(event);
                }
            }
            
            return false; // Prevenir comportamiento por defecto
        };
    } else {
        console.log('Botón Auto-llenar datos no encontrado en el paso 4');
    }
    
    console.log('Script de reparación de botones Auto-llenar cargado correctamente');
};