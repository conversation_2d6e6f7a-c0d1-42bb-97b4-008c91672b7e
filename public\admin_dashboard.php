<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once '../config/session_config.php';
require_once '../config/config.php';

$sessionManager = SessionManager::getInstance();

// Verificar la autenticación usando SessionManager
if (!$sessionManager->validateSession()) {
    error_log("Admin.php - Sesión inválida");
    $sessionManager->destroySession();
    header('Location: /projects/villarrica_click/public/login.php?error=unauthorized');
    exit();
}

// Verificar el rol de administrador
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    error_log("Admin.php - Usuario no es administrador");
    $sessionManager->destroySession();
    header('Location: /projects/villarrica_click/public/login.php?error=unauthorized');
    exit();
}

// Obtener información del negocio
$sql_negocio = "SELECT * FROM tb_negocios LIMIT 1";
$result_negocio = $conn->query($sql_negocio);
$negocio = $result_negocio->fetch_assoc();

// Obtener lista de productos
$sql_productos = "SELECT * FROM tb_productos ORDER BY id DESC";
$result_productos = $conn->query($sql_productos);

$productos = [];
while ($row = $result_productos->fetch_assoc()) {
    $productos[] = $row;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Administrativo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --azure-blue: #0078D4;
            --azure-light-blue: #50E6FF;
            --azure-dark: #106EBE;
            --azure-gray: #323130;
            --azure-light-gray: #F3F2F1;
            --azure-accent: #2BC4C4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #FAF9F8;
            margin: 0;
            padding: 0;
            color: var(--azure-gray);
        }

        .dashboard {
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            color: var(--azure-gray);
            font-weight: 600;
        }

        .chart-card {
            grid-column: span 2;
        }

        .chart-container {
            position: relative;
            margin: auto;
            height: 300px;
            width: 100%;
        }

        .kpi-card {
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .kpi-content {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }

        .kpi-info {
            flex-grow: 1;
        }

        .indicator {
            font-size: 2rem;
            font-weight: 600;
            color: var(--azure-blue);
            margin: 0.5rem 0;
        }

        .kpi-trend {
            font-size: 0.9rem;
            color: #107C10;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .kpi-trend.negative {
            color: #A4262C;
        }

        .kpi-icon {
            background-color: rgba(0, 120, 212, 0.1);
            padding: 1rem;
            border-radius: 8px;
            color: var(--azure-blue);
        }

        .filters {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 0.5rem;
        }

        .filters button {
            padding: 0.5rem 1rem;
            border: 1px solid var(--azure-blue);
            background-color: white;
            color: var(--azure-blue);
            cursor: pointer;
            border-radius: 2px;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .filters button.active {
            background-color: var(--azure-blue);
            color: white;
        }

        .filters button:hover {
            background-color: var(--azure-light-gray);
        }

        .table-card {
            grid-column: 1 / -1;
            overflow: auto;
        }

        .table-container {
            overflow-x: auto;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .products-table th,
        .products-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--azure-light-gray);
        }

        .products-table th {
            background-color: var(--azure-light-gray);
            font-weight: 600;
            color: var(--azure-gray);
        }

        .products-table tbody tr:hover {
            background-color: rgba(0, 120, 212, 0.05);
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            .card {
                margin-bottom: 1rem;
            }
            .chart-card {
                grid-column: 1 / -1;
            }
            .table-card {
                grid-column: 1 / -1;
            }
            .filters {
                flex-direction: column;
                gap: 0.5rem;
            }
            .filters button {
                width: 100%;
            }
            .kpi-content {
                flex-direction: column;
                align-items: flex-start;
            }
            .kpi-icon {
                margin-top: 1rem;
            }
            .products-table th,
            .products-table td {
                padding: 0.5rem;
            }
        }

        @media (min-width: 769px) {
            .chart-card {
                grid-column: 1 / 2;
            }
            .table-card {
                grid-column: 2 / 3;
            }
            .dashboard {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            /* Definir 4 columnas en pantallas grandes */
            .dashboard {
                grid-template-columns: repeat(4, 1fr);
            }
            /* Las tarjetas KPI ocupan 1 columna (25% de ancho) */
            .kpi-card {
                grid-column: span 1;
            }
            /* El chart-card ocupará el 50% del ancho (2 columnas) */
            .chart-card {
                grid-column: span 2;
            }
            /* La tabla se extiende en las 4 columnas */
            .table-card {
                grid-column: span 4;
            }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <?php include 'header.php'; ?>
    <div class="dashboard">

    <div class="card">
        <button onclick="window.location.href='<?php echo BASE_URL; ?>/public/admin_products.php'" style="padding: 0.5rem 1rem; border: 1px solid var(--azure-blue); background-color: var(--azure-blue); color: white; cursor: pointer; border-radius: 2px; font-size: 0.9rem; transition: all 0.2s;">
            Administrar Productos
        </button>
    </div>
        <div class="card chart-card">
            <h2>Analytics Overview</h2>
            <div class="filters">
                <button onclick="updateChart('daily')" class="active">Diario</button>
                <button onclick="updateChart('weekly')">Semanal</button>
                <button onclick="updateChart('monthly')">Mensual</button>
            </div>
            <div class="chart-container">
                <canvas id="visitsChart"></canvas>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Visitas Totales</h2>
                    <div class="indicator">150,000</div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>12.5% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-users fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Duración Promedio</h2>
                    <div class="indicator">3:45</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>8.2% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-clock fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Tasa de Rebote</h2>
                    <div class="indicator">40%</div>
                    <div class="kpi-trend negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>5.1% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-percentage fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Páginas por Visita</h2>
                    <div class="indicator">5</div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>3.8% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-file fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Usuarios Activos</h2>
                    <div class="indicator">850</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>15.3% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-user-circle fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Conversiones</h2>
                    <div class="indicator">25%</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>4.2% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-chart-pie fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card table-card">
            <h2>Productos Más Revisados</h2>
            <div class="table-container">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th>Imagen</th>
                            <th>Producto</th>
                            <th>Visitas Diarias</th>
                            <th>Visitas Semanales</th>
                            <th>Visitas Mensuales</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 1" style="width:40px; height:40px;"></td>
                            <td>Producto 1</td>
                            <td>100</td>
                            <td>700</td>
                            <td>3000</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 2" style="width:40px; height:40px;"></td>
                            <td>Producto 2</td>
                            <td>120</td>
                            <td>800</td>
                            <td>3200</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 3" style="width:40px; height:40px;"></td>
                            <td>Producto 3</td>
                            <td>140</td>
                            <td>900</td>
                            <td>3500</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 4" style="width:40px; height:40px;"></td>
                            <td>Producto 4</td>
                            <td>160</td>
                            <td>1000</td>
                            <td>3800</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 5" style="width:40px; height:40px;"></td>
                            <td>Producto 5</td>
                            <td>180</td>
                            <td>1100</td>
                            <td>4000</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        var visitsChart;
        var ctxVisits = document.getElementById('visitsChart').getContext('2d');

        // Configuración de colores estilo Azure
        Chart.defaults.color = '#323130';
        Chart.defaults.font.family = "'Segoe UI', sans-serif";

        // Datos para cada período
        const periodData = {
            daily: {
                chartData: [120, 150, 180, 200, 170, 160, 190],
                labels: ['Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb', 'Dom'],
                kpis: {
                    visits: {
                        value: '1,170',
                        trend: '+12.5%'
                    },
                    duration: {
                        value: '2:45',
                        trend: '****%'
                    },
                    bounceRate: {
                        value: '35%',
                        trend: '-5.1%'
                    },
                    pagesPerVisit: {
                        value: '4',
                        trend: '****%'
                    },
                    activeUsers: {
                        value: '850',
                        trend: '+15.3%'
                    },
                    conversions: {
                        value: '25%',
                        trend: '+4.2%'
                    }
                }
            },
            weekly: {
                chartData: [800, 900, 1000, 1100, 950, 1050, 1150],
                labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Sem 5', 'Sem 6', 'Sem 7'],
                kpis: {
                    visits: {
                        value: '6,950',
                        trend: '+15.2%'
                    },
                    duration: {
                        value: '3:15',
                        trend: '+10.5%'
                    },
                    bounceRate: {
                        value: '38%',
                        trend: '-3.2%'
                    },
                    pagesPerVisit: {
                        value: '5',
                        trend: '+5.1%'
                    },
                    activeUsers: {
                        value: '4,200',
                        trend: '+12.8%'
                    },
                    conversions: {
                        value: '28%',
                        trend: '+5.5%'
                    }
                }
            },
            monthly: {
                chartData: [3000, 3200, 3100, 3300, 3400, 3500, 3600],
                labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul'],
                kpis: {
                    visits: {
                        value: '23,100',
                        trend: '+18.7%'
                    },
                    duration: {
                        value: '3:45',
                        trend: '+12.8%'
                    },
                    bounceRate: {
                        value: '40%',
                        trend: '-2.5%'
                    },
                    pagesPerVisit: {
                        value: '6',
                        trend: '+7.2%'
                    },
                    activeUsers: {
                        value: '15,300',
                        trend: '+18.1%'
                    },
                    conversions: {
                        value: '30%',
                        trend: '+6.8%'
                    }
                }
            }
        };

        function updateKPIs(period) {
            const data = periodData[period].kpis;
            
            // Actualizar visitas totales
            document.querySelector('.kpi-card:nth-child(2) .indicator').textContent = data.visits.value;
            document.querySelector('.kpi-card:nth-child(2) .kpi-trend span').textContent = data.visits.trend + ' vs prev. period';
            
            // Actualizar duración promedio
            document.querySelector('.kpi-card:nth-child(3) .indicator').textContent = data.duration.value;
            document.querySelector('.kpi-card:nth-child(3) .kpi-trend span').textContent = data.duration.trend + ' vs prev. period';
            
            // Actualizar tasa de rebote
            document.querySelector('.kpi-card:nth-child(4) .indicator').textContent = data.bounceRate.value;
            document.querySelector('.kpi-card:nth-child(4) .kpi-trend span').textContent = data.bounceRate.trend + ' vs prev. period';
            
            // Actualizar páginas por visita
            document.querySelector('.kpi-card:nth-child(5) .indicator').textContent = data.pagesPerVisit.value;
            document.querySelector('.kpi-card:nth-child(5) .kpi-trend span').textContent = data.pagesPerVisit.trend + ' vs prev. period';

            // Actualizar usuarios activos
            document.querySelector('.kpi-card:nth-child(6) .indicator').textContent = data.activeUsers.value;
            document.querySelector('.kpi-card:nth-child(6) .kpi-trend span').textContent = data.activeUsers.trend + ' vs prev. period';

            // Actualizar conversiones
            document.querySelector('.kpi-card:nth-child(7) .indicator').textContent = data.conversions.value;
            document.querySelector('.kpi-card:nth-child(7) .kpi-trend span').textContent = data.conversions.trend + ' vs prev. period';

            // Actualizar clases de tendencia
            document.querySelectorAll('.kpi-trend').forEach(trend => {
                const trendValue = trend.querySelector('span').textContent;
                if (trendValue.startsWith('+')) {
                    trend.classList.remove('negative');
                    trend.classList.add('positive');
                    trend.querySelector('i').className = 'fas fa-arrow-up';
                } else {
                    trend.classList.remove('positive');
                    trend.classList.add('negative');
                    trend.querySelector('i').className = 'fas fa-arrow-down';
                }
            });
        }

        function updateChart(period) {
            // Actualizar estado activo de los botones
            document.querySelectorAll('.filters button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodInfo = periodData[period];

            if (visitsChart) {
                visitsChart.destroy();
            }

            visitsChart = new Chart(ctxVisits, {
                type: 'line',
                data: {
                    labels: periodInfo.labels,
                    datasets: [{
                        label: 'Visitas',
                        data: periodInfo.chartData,
                        backgroundColor: 'rgba(0, 120, 212, 0.1)',
                        borderColor: '#0078D4',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#0078D4',
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: '#323130',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            padding: 12,
                            displayColors: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)',
                                drawBorder: false
                            },
                            ticks: {
                                padding: 10,
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                padding: 10
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // Actualizar KPIs
            updateKPIs(period);
        }

        // Inicializar el gráfico con datos diarios
        updateChart('daily');

        // Datos de prueba estáticos
        const productsData = [
            { image: "https://via.placeholder.com/40", name: "Producto A", daily_visits: 123, weekly_visits: 456, monthly_visits: 789 },
            { image: "https://via.placeholder.com/40", name: "Producto B", daily_visits: 234, weekly_visits: 567, monthly_visits: 890 },
            { image: "https://via.placeholder.com/40", name: "Producto C", daily_visits: 345, weekly_visits: 678, monthly_visits: 901 },
            { image: "https://via.placeholder.com/40", name: "Producto D", daily_visits: 456, weekly_visits: 789, monthly_visits: 1011 }
        ];

        // Función para renderizar la tabla de productos
        function renderProductsTable() {
            const tbody = document.getElementById('productsTableBody');
            tbody.innerHTML = '';

            productsData.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><img src="${product.image}" alt="${product.name}" style="width:40px; height:40px;"></td>
                    <td>${product.name}</td>
                    <td>${product.daily_visits.toLocaleString()}</td>
                    <td>${product.weekly_visits.toLocaleString()}</td>
                    <td>${product.monthly_visits.toLocaleString()}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // Inicializar la tabla de productos
        renderProductsTable();
        
        document.addEventListener('DOMContentLoaded', function() {
            // Datos de prueba estáticos
            const productsData = [
                { image: "https://via.placeholder.com/40", name: "Producto A", daily_visits: 123, weekly_visits: 456, monthly_visits: 789 },
                { image: "https://via.placeholder.com/40", name: "Producto B", daily_visits: 234, weekly_visits: 567, monthly_visits: 890 },
                { image: "https://via.placeholder.com/40", name: "Producto C", daily_visits: 345, weekly_visits: 678, monthly_visits: 901 },
                { image: "https://via.placeholder.com/40", name: "Producto D", daily_visits: 456, weekly_visits: 789, monthly_visits: 1011 }
            ];

            // Función para renderizar la tabla de productos
            function renderProductsTable() {
                const tbody = document.getElementById('productsTableBody');
                tbody.innerHTML = '';

                productsData.forEach(product => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><img src="${product.image}" alt="${product.name}" style="width:40px; height:40px;"></td>
                        <td>${product.name}</td>
                        <td>${product.daily_visits}</td>
                        <td>${product.weekly_visits}</td>
                        <td>${product.monthly_visits}</td>
                    `;
                    tbody.appendChild(row);
                });
            }

            // Inicializar la tabla de productos
            renderProductsTable();
        });
    </script>
</body>
</html>