    <!-- Script para formatear precios -->
    <script>
    // Función para formatear precios en la tabla y formularios
    document.addEventListener('DOMContentLoaded', function() {
        // Formatear campos de precio en formularios
        const priceInputs = document.querySelectorAll('.price-input');

        // Función para formatear el precio sin decimales
        function formatPriceWithoutDecimals(input) {
            if (input.value) {
                // Eliminar decimales
                input.value = Math.floor(parseInt(input.value));
            }
        }

        // Aplicar formato al cargar la página
        priceInputs.forEach(input => {
            formatPriceWithoutDecimals(input);

            // Aplicar formato al cambiar el valor
            input.addEventListener('change', function() {
                formatPriceWithoutDecimals(this);
            });

            // Aplicar formato al perder el foco
            input.addEventListener('blur', function() {
                formatPriceWithoutDecimals(this);
            });
        });

        // Formatear precios en la tabla
        function formatPricesInTable() {
            // Seleccionar la 5ta columna (Precio) en todas las filas de la tabla
            const priceCells = document.querySelectorAll('#productsTable tbody td:nth-child(5)');
            priceCells.forEach(cell => {
                if (!cell.classList.contains('formatted') && !isNaN(parseInt(cell.textContent.trim()))) {
                    const price = parseInt(cell.textContent.trim().replace(/[^0-9]/g, ''));
                    cell.innerHTML = `<span class="currency-formatted">${price.toLocaleString('es-CL')}</span>`;
                    cell.classList.add('formatted');
                }
            });
        }

        // Ejecutar formato inicial
        formatPricesInTable();

        // Observar cambios en la tabla para formatear precios en filas añadidas dinámicamente
        const observer = new MutationObserver(function(mutations) {
            formatPricesInTable();
        });

        const productsTable = document.getElementById('productsTable');
        if (productsTable) {
            observer.observe(productsTable, { childList: true, subtree: true });
        }
    });
    </script>        /* Estilos para campos de precio */
        .price-input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .price-symbol {
            position: absolute;
            left: 10px;
            font-weight: bold;
            color: #6a1b9a;
            z-index: 5;
        }

        .price-input {
            padding-left: 25px !important;
        }

        .currency-formatted {
            font-weight: bold;
            color: #4CAF50;
        }<?php
// Debug de inicio de script
error_log("tienda_adm.php - Inicio de ejecución");
error_log("tienda_adm.php - REQUEST_URI: " . $_SERVER['REQUEST_URI']);
error_log("tienda_adm.php - Session ID: " . session_id());

ob_start(); // Iniciar buffer de salida
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Incluir configuraciones
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Asegurar que tenemos una sesión válida
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

// Verificar si el user_id está definido en la sesión
if (!isset($_SESSION['user_id'])) {
    error_log("tienda_adm.php - Error: user_id no está definido en la sesión");
    header("Location: login.php?error=session_expired");
    exit();
}

$user_id = $_SESSION['user_id'];
error_log("tienda_adm.php - user_id de la sesión: " . $user_id);

// Obtener los negocios del usuario actual
$user_id = $_SESSION['user_id'];
error_log("tienda_adm.php - Consultando negocios para user_id: " . $user_id);

// Corregir la consulta SQL para usar user_id en lugar de user
$sql_negocios = "SELECT id, nombre FROM tb_negocios WHERE user_id = ?";
$stmt_negocios = $conn->prepare($sql_negocios);
$stmt_negocios->bind_param("i", $user_id);
$stmt_negocios->execute();
$result_negocios = $stmt_negocios->get_result();
$negocios = [];
while ($row = $result_negocios->fetch_assoc()) {
    $negocios[] = $row;
}

// Log de estado de sesión
error_log("Admin_products.php - Session ID: " . session_id());
error_log("Admin_products.php - Session data: " . print_r($_SESSION, true));

// Verificar autenticación
if (!$sessionManager->isLoggedIn()) {
    error_log("Admin_products.php - Usuario no autenticado");
    $currentUrl = urlencode($_SERVER['REQUEST_URI']);
    // Verificar si existe un buffer antes de limpiarlo
    if (ob_get_level() > 0) {
        ob_end_clean(); // Limpiar buffer antes de redireccionar
    }
    header("Location: " . BASE_URL . "/public/login.php?error=unauthorized&reason=not_logged_in&return_to=" . $currentUrl);
    exit();
}

// Verificar rol de administrador o pro
if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], ['admin', 'pro', 'premium'])) {
    error_log("Admin_products.php - Usuario no tiene permisos suficientes - Role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'no definido'));
    // Verificar si existe un buffer antes de limpiarlo
    if (ob_get_level() > 0) {
        ob_end_clean(); // Limpiar buffer antes de redireccionar
    }
    header("Location: " . BASE_URL . "/public/login.php?error=unauthorized&reason=insufficient_permissions");
    exit();
}

// Actualizar tiempo de actividad
// $sessionManager->updateActivity();

// Queries para cargar datos de productos
try {
    // Obtener lista de productos con sus categorías
    $sql_productos = "SELECT p.*,
                            c.nombre as categoria_nombre,
                            COALESCE(s.nombre, 'Sin subcategoría') as subcategoria_nombre
                     FROM tb_productos p
                     LEFT JOIN tb_categorias c ON p.categoria_id = c.id
                     LEFT JOIN tb_subcategorias s ON p.subcategoria_id = s.id
                     ORDER BY p.id DESC";
    $result_productos = $conn->query($sql_productos);

    if (!$result_productos) {
        throw new Exception("Error en la consulta: " . $conn->error);
    }

    $productos = [];
    while ($row = $result_productos->fetch_assoc()) {
        // Asegurar que los campos críticos tengan valores por defecto
        $row['categoria_nombre'] = $row['categoria_nombre'] ?? 'Sin categoría';
        $row['subcategoria_nombre'] = $row['subcategoria_nombre'] ?? 'Sin subcategoría';
        $productos[] = $row;
    }

    error_log("Productos cargados: " . count($productos));

    // Obtener todas las categorías para el formulario de edición/creación
    $sql_categorias = "SELECT id, nombre FROM tb_categorias ORDER BY nombre";
    $result_categorias = $conn->query($sql_categorias);
    if (!$result_categorias) {
        throw new Exception("Error al cargar categorías: " . $conn->error);
    }
    $categorias = [];
    while ($row = $result_categorias->fetch_assoc()) {
        $categorias[] = $row;
    }

    // Obtener todas las subcategorías agrupadas por categoría para el formulario
    $subcategorias_por_categoria = [];

    // Verificar si la tabla existe
    $tableCheckQuery = "SHOW TABLES LIKE 'tb_subcategorias'";
    $tableResult = $conn->query($tableCheckQuery);

    if ($tableResult && $tableResult->num_rows > 0) {
        $sql_subcategorias = "SELECT id, categoria_id, nombre FROM tb_subcategorias ORDER BY categoria_id, nombre";
        $result_subcategorias = $conn->query($sql_subcategorias);

        if ($result_subcategorias) {
            while ($row = $result_subcategorias->fetch_assoc()) {
                if (!isset($subcategorias_por_categoria[$row['categoria_id']])) {
                    $subcategorias_por_categoria[$row['categoria_id']] = [];
                }
                $subcategorias_por_categoria[$row['categoria_id']][] = $row;
            }
        } else {
            error_log("Error al consultar subcategorías: " . $conn->error);
            // Continuamos con un array vacío en lugar de lanzar una excepción
        }
    } else {
        error_log("La tabla tb_subcategorias no existe en la base de datos");
        // Continuamos con un array vacío
    }

    // Obtener tipos de categorías
    $sql_tipos = "SELECT id, nombre FROM tb_tipo_categoria ORDER BY nombre";
    $result_tipos = $conn->query($sql_tipos);
    if (!$result_tipos) {
        throw new Exception("Error al cargar tipos de categoría: " . $conn->error);
    }
    $tipos_categoria = [];
    while ($row = $result_tipos->fetch_assoc()) {
        $tipos_categoria[] = $row;
    }

} catch (Exception $e) {
    error_log("Error al cargar productos: " . $e->getMessage());
    $productos = []; // Asegurar que tengamos un array vacío si hay error
    $categorias = [];
    $subcategorias_por_categoria = [];
    $tipos_categoria = [];
}

// Se puede comenzar a enviar la salida HTML
?>

<!DOCTYPE html>
<!--
    NOTA IMPORTANTE:
    Se han eliminado la mayoría de los archivos JS y CSS relacionados con esta página.
    Solo se mantienen los esenciales para el header y sidebar.
    La funcionalidad completa de la página puede estar limitada.
-->
<html lang="es">
    <head>
        <meta charset="UTF-8">
        <!-- Modificar el viewport y agregar metas para apariencia de app -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
        <meta name="theme-color" content="#6a1b9a">
        <!-- Agregar datos de usuario para el acceso desde JavaScript -->
        <meta name="user-id" content="<?php echo htmlspecialchars($_SESSION['user_id'] ?? ''); ?>">
        <meta name="user-role" content="<?php echo htmlspecialchars($_SESSION['role'] ?? ''); ?>">
        <!-- Importación de fuentes -->
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
        <!-- Importación de iconos -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <title>Panel de Administración - Villarrica a un CLICKX</title>
        <!-- Estilos esenciales -->
        <link rel="stylesheet" href="../css/variables.css">
        <!-- Sidebar-->
        <link rel="stylesheet" href="../css/sidebar-improved.css">
        <!-- Header moderno estilo Synex -->
        <link rel="stylesheet" href="../css/header-modern.css">
        <!-- Sidebar overlay -->
        <link rel="stylesheet" href="../css/sidebar-overlay.css">
        <!-- Solución para el botón toggle -->
        <link rel="stylesheet" href="../css/toggle-fix.css">

        <!-- Estilos para tooltips de clic -->
        <style>
        /* Estilos para tooltips de clic */
        .tooltip-wrapper, .info-tooltip-container, .info-icon-container {
            position: relative;
            display: inline-flex;
            align-items: center;
            vertical-align: middle;
        }

        .tooltip-icon, .info-icon {
            margin-left: 5px;
            color: #6a1b9a;
            cursor: pointer;
            background-color: rgba(106, 27, 154, 0.1);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }

        .tooltip-icon:hover, .info-icon:hover {
            background-color: rgba(106, 27, 154, 0.2);
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
            color: #8e24aa;
        }

        .tooltip-content, .info-tooltip {
            visibility: hidden;
            position: absolute;
            width: 300px;
            background: linear-gradient(135deg, #6a1b9a, #8e24aa);
            color: #fff;
            text-align: left;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            top: auto;
            bottom: calc(100% + 15px);
            left: 50%;
            transform: translateX(-50%) translateY(10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            pointer-events: none; /* Evita que el tooltip interfiera con los clics */
            display: block !important;
        }

        .tooltip-content::after, .info-tooltip::after {
            content: "";
            position: absolute;
            top: 100%;
            bottom: auto;
            left: 50%;
            margin-left: -8px;
            border-width: 8px;
            border-style: solid;
            border-color: #8e24aa transparent transparent transparent;
        }

        /* Clase para mostrar el tooltip cuando está activo */
        .tooltip-content.active, .info-tooltip.active {
            visibility: visible;
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        /* Estilos para el contenido del tooltip */
        .tooltip-content strong, .info-tooltip strong {
            color: #fff;
            font-weight: 600;
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .tooltip-content p, .info-tooltip p {
            margin: 0 0 10px 0;
            line-height: 1.5;
            font-size: 13px;
        }

        /* Ajustes para el tooltip en el label de Estado y Condición */
        label .info-icon-container .info-tooltip,
        .tooltip-wrapper .tooltip-content,
        .info-tooltip {
            position: absolute;
            bottom: 2em;
            right: -415px;
            margin-left: 156px;
            border-width: 1px;
            border-style: solid;
            border-color: transparent;
            background: linear-gradient(135deg, #6a1b9a, #8e24aa);
            z-index: 1000;
            width: 350px;
            max-width: 90vw;
            white-space: normal;
            text-align: left;
            overflow: visible;
            /* Estilos exactos de la imagen */
            opacity: 1;
            visibility: visible;
            padding: 15px;
            border-radius: 8px;
            border-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Estilos para tooltips activos */
        .tooltip-content.active,
        .info-tooltip.active {
            visibility: visible;
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        label .info-icon-container .info-tooltip::after,
        .tooltip-wrapper .tooltip-content::after,
        .info-tooltip::after {
            left: 20px;
            transform: none;
        }

        /* Hacer el icono de tooltip más visible */
        .tooltip-icon, .info-icon {
            font-size: 18px !important;
            width: 26px !important;
            height: 26px !important;
            background-color: rgba(106, 27, 154, 0.15) !important;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15) !important;
        }

        /* Estilos para el contenedor del icono de información */
        .info-icon-container {
            position: relative;
            display: inline-flex;
            align-items: center;
        }

        /* Ajuste para evitar que el tooltip se corte */
        .edit-group, .form-group {
            position: relative;
            overflow: visible;
        }

        /* Estilos para campos de precio */
        .price-input-container {
            position: relative;
            display: flex;
            align-items: stretch;
            width: 100%;
        }

        .price-symbol {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            font-weight: bold;
            color: #6a1b9a;
            background-color: #f0e6f5;
            border: 1px solid #d1c4e9;
            border-right: none;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
        }

        .price-input {
            flex: 1;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
        }

        .currency-formatted, .price-formatted {
        font-weight: bold;
        color: #4CAF50;
        }

        /* Asegurar que no se muestren decimales en inputs de tipo number */
        input[type="number"][id*="Price"],
        input[type="number"][id*="price"],
        input[data-price="true"] {
            -moz-appearance: textfield; /* Firefox */
        }

        input[type="number"][id*="Price"]::-webkit-outer-spin-button,
        input[type="number"][id*="Price"]::-webkit-inner-spin-button,
        input[type="number"][id*="price"]::-webkit-outer-spin-button,
        input[type="number"][id*="price"]::-webkit-inner-spin-button,
        input[data-price="true"]::-webkit-outer-spin-button,
        input[data-price="true"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        </style>
</head>
<body data-user-id="<?php echo htmlspecialchars($_SESSION['user_id'] ?? ''); ?>" data-user-role="<?php echo htmlspecialchars($_SESSION['role'] ?? ''); ?>">
    <!-- Header moderno estilo Synex -->
    <header class="modern-header">
        <!-- Botón toggle para el sidebar -->
        <button id="header-aside-toggle" class="header-toggle-btn sidemenu-toggle" aria-label="Toggle sidebar" data-bs-toggle="sidebar" href="javascript:void(0);" style="display: flex !important; visibility: visible !important;">
            <div class="animated-arrow" style="position: relative; width: 18px; height: 18px;">
                <span style="display: block; position: absolute; height: 2px; width: 100%; background: white; border-radius: 9px; opacity: 1; left: 0; transform: rotate(0deg); transition: .25s ease-in-out; top: 0px;"></span>
                <span style="display: block; position: absolute; height: 2px; width: 100%; background: white; border-radius: 9px; opacity: 1; left: 0; transform: rotate(0deg); transition: .25s ease-in-out; top: 8px;"></span>
                <span style="display: block; position: absolute; height: 2px; width: 100%; background: white; border-radius: 9px; opacity: 1; left: 0; transform: rotate(0deg); transition: .25s ease-in-out; top: 16px;"></span>
            </div>
        </button>

        <!-- Botón toggle alternativo para compatibilidad con el código JS -->
        <button id="aside-toggle" class="header-toggle-btn sidemenu-toggle" aria-label="Toggle sidebar" style="display: none;">
            <span id="toggle-icon"></span>
        </button>

        <!-- Sección izquierda: Menú -->
        <div class="header-left">
        </div>

        <!-- Sección central: Búsqueda -->
        <div class="header-center">
            <div class="search-bar">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="Buscar..." aria-label="Buscar">
            </div>
        </div>

        <!-- Sección derecha: Acciones y perfil -->
        <div class="header-right">
            <!-- Selector de idioma -->
            <div id="languageSelector" class="language-selector" aria-label="Seleccionar idioma">
                <i class="fas fa-globe"></i>
                <span>ES</span>
            </div>

            <!-- Toggle de tema claro/oscuro -->
            <button id="themeToggle" class="header-action theme-toggle" aria-label="Cambiar tema">
                <i class="fas fa-moon"></i>
            </button>

            <!-- Notificaciones -->
            <div class="dropdown-container">
                <button id="notificationsBtn" class="header-action" aria-label="Notificaciones">
                    <i class="fas fa-bell"></i>
                    <span class="notification-indicator">2</span>
                </button>
                <div id="notificationsDropdown" class="dropdown-menu notifications-dropdown">
                    <div class="dropdown-header">
                        <h3>Notificaciones</h3>
                        <span class="unread-count">2 Sin leer</span>
                    </div>
                    <div class="dropdown-body">
                        <div class="notification-item unread">
                            <div class="notification-icon purple">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Pedido...</div>
                                <div class="notification-text">Pedido #12345 ha sido enviado a tu dirección</div>
                            </div>
                            <button class="notification-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-icon blue">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Descuento...</div>
                                <div class="notification-text">Descuento disponible en productos seleccionados</div>
                            </div>
                            <button class="notification-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon green">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Cuenta...</div>
                                <div class="notification-text">Tu cuenta ha sido verificada exitosamente</div>
                            </div>
                            <button class="notification-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon orange">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Pedido Realizado</div>
                                <div class="notification-text">Pedido realizado exitosamente</div>
                            </div>
                            <button class="notification-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon red">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title">Pedido Retrasado</div>
                                <div class="notification-text">Pedido retrasado lamentablemente</div>
                            </div>
                            <button class="notification-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="dropdown-footer">
                        <button class="view-all-btn">Ver Todas</button>
                    </div>
                </div>
            </div>

            <!-- Mensajes -->
            <div class="dropdown-container">
                <button id="messagesBtn" class="header-action" aria-label="Mensajes">
                    <i class="fas fa-envelope"></i>
                    <span class="notification-indicator">3</span>
                </button>
                <div id="messagesDropdown" class="dropdown-menu messages-dropdown">
                    <div class="dropdown-header">
                        <h3>Mensajes</h3>
                        <span class="unread-count">3 Sin leer</span>
                    </div>
                    <div class="dropdown-body">
                        <div class="message-item unread">
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-sender">Juan Pérez</div>
                                <div class="message-preview">Hola, quisiera consultar sobre el producto...</div>
                                <div class="message-time">Hace 5 minutos</div>
                            </div>
                        </div>
                        <div class="message-item unread">
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-sender">María González</div>
                                <div class="message-preview">¿Tienen disponibilidad para enviar a...</div>
                                <div class="message-time">Hace 30 minutos</div>
                            </div>
                        </div>
                        <div class="message-item unread">
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-sender">Carlos Rodríguez</div>
                                <div class="message-preview">Gracias por la atención, el producto...</div>
                                <div class="message-time">Hace 2 horas</div>
                            </div>
                        </div>
                        <div class="message-item">
                            <div class="message-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-sender">Ana Martínez</div>
                                <div class="message-preview">¿Cuándo tendrán nuevamente stock del...</div>
                                <div class="message-time">Ayer</div>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-footer">
                        <button class="view-all-btn">Ver Todos</button>
                    </div>
                </div>
            </div>

            <!-- Pantalla completa -->
            <button id="fullscreenToggle" class="header-action fullscreen-toggle" aria-label="Pantalla completa">
                <i class="fas fa-expand"></i>
            </button>

            <!-- Perfil de usuario -->
            <div id="userProfile" class="user-profile">
                <div class="user-info">
                    <span class="user-name">Administrador</span>
                    <span class="user-role">Web Designer</span>
                </div>
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
            </div>
        </div>
    </header>

    <!-- Añadir contenedor de notificaciones -->
    <div id="errorNotification" class="notification error" style="display: none;">
        <div class="notification-content">
            <i class="fas fa-exclamation-circle"></i>
            <span id="errorMessage"></span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <!-- Modal para ver imagen ampliada -->
    <div id="imageModal" class="modal" style="display: none;">
        <div class="modal-content image-modal-content">
            <span class="close-modal">&times;</span>
            <img id="modalImage" src="" alt="Imagen ampliada">
        </div>
    </div>

    <!-- Añadir contenedor de depuración -->
    <div id="debugPanel" class="debug-panel" style="display: none;">
        <div class="debug-header">
            <h3>Panel de Depuración</h3>
            <button class="debug-close">×</button>
        </div>
        <div class="debug-content">
            <pre id="debugContent"></pre>
        </div>
    </div>

    <div class="admin-layout">
        <!-- Barra lateral - Contiene el menú de navegación principal -->
        <aside class="sidebar" id="sidebar">
            <!-- Cabecera de la barra lateral - Logo y título -->
            <div class="sidebar-header">
                <div class="sidebar-logo">Villarricax a un CLICK</div>
                <div class="sidebar-subtitle">Panel de Administración</div>
            </div>

            <!-- Navegación principal -->
            <nav class="sidebar-nav">
                <!-- Sección General -->
                <div class="nav-section">
                    <h3 class="nav-section-title">General</h3>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link active" id="dashboardLink">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sección Catálogo -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Catálogo</h3>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="productsNavLink">
                                <i class="fas fa-box"></i>
                                <span>Productos</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="categoriesLink">
                                <i class="fas fa-tags"></i>
                                <span>Categorías</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sección Mi Tienda -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Mi Tienda</h3>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="storeNavLink">
                                <i class="fas fa-store"></i>
                                <span>Información</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sección Contenido -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Contenido</h3>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-images"></i>
                                <span>Galería</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sección Configuración -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Configuración</h3>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-user-cog"></i>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-cog"></i>
                                <span>Ajustes</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>/public/simple_logout.php" class="nav-link" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Footer eliminado -->
        </aside>

        <!-- Contenido principal - Área donde se muestra el contenido de la aplicación -->
        <main class="main-content">
            <!-- Contenedor principal - Limita el ancho y añade márgenes -->
            <div class="container">
                <!-- Selector de Negocio -->
                <div class="form-group" style="margin-bottom: 20px;">
                    <label class="form-label" for="selectNegocio">Seleccione un negocio:</label>
                    <select class="form-control" id="selectNegocio" name="negocio_id">
                        <option value="">Seleccione un negocio...</option>
                        <?php foreach ($negocios as $negocio): ?>
                        <option value="<?php echo htmlspecialchars($negocio['id']); ?>">
                            <?php echo htmlspecialchars($negocio['nombre']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                    <small class="form-text text-muted">Seleccione un negocio para administrar sus productos y configuración.</small>
                </div>

                <!-- Eliminamos las pestañas de navegación principal y reorganizamos la navegación a través del aside -->

                <!-- Listado de productos - Muestra todos los productos en una tabla -->
                <section class="content-section" id="productsSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-box"></i>
                            Listado de productos
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" type="button" id="filterBtn">
                                <i class="fas fa-filter"></i>
                                Filtrar
                            </button>
                            <button id="newProductBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Nuevo
                            </button>
                        </div>
                    </div>

                    <!-- Contenedor de filtros -->
                    <div id="filterContainer" class="filter-container" style="display: none;">
                        <div class="filter-content">
                            <div class="filter-header">
                                <h3><i class="fas fa-filter"></i> Filtros</h3>
                                <button class="btn-close-filter" id="closeFilter">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="filter-actions">
                                    <button class="btn btn-secondary" id="resetFilters">
                                        <i class="fas fa-undo"></i> Resetear
                                    </button>
                                    <button class="btn btn-primary" id="applyFilters">
                                        <i class="fas fa-check"></i> Aplicar
                                    </button>
                                </div>


                            <div class="filter-body">
                                <div class="filter-group">
                                    <label>Búsqueda</label>
                                    <input type="text" id="filterSearch" placeholder="Buscar por nombre o descripción...">
                                </div>
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Precio mínimo</label>
                                        <input type="number" id="filterMinPrice" placeholder="Mín">
                                    </div>
                                    <div class="filter-group">
                                        <label>Precio máximo</label>
                                        <input type="number" id="filterMaxPrice" placeholder="Máx">
                                    </div>
                                </div>
                                <div class="filter-row">
                                    <div class="filter-group">
                                        <label>Stock mínimo</label>
                                        <input type="number" id="filterMinStock" placeholder="Mín">
                                    </div>
                                    <div class="filter-group">
                                        <label>Stock máximo</label>
                                        <input type="number" id="filterMaxStock" placeholder="Máx">
                                    </div>
                                </div>
                                <div class="filter-group">
                                    <label>Estado</label>
                                    <div class="filter-options">
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="activo">
                                            <span>Publicado</span>
                                            <div class="info-tooltip-container">
                                                <i class="fas fa-info-circle info-icon"></i>
                                                <div class="info-tooltip">
                                                    Indica que el producto está activo, visible para los clientes y disponible para la compra (si hay stock). Es el estado estándar para un producto en venta.
                                                </div>
                                            </div>
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="borrador">
                                            <span>Borrador</span>
                                            <div class="info-tooltip-container">
                                                <i class="fas fa-info-circle info-icon"></i>
                                                <div class="info-tooltip">
                                                    El producto está guardado en el sistema pero no es visible en la tienda pública. Útil para cuando estás creando el producto y aún no está listo para ser publicado.
                                                </div>
                                            </div>
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="agotado">
                                            <span>Agotado</span>
                                            <div class="info-tooltip-container">
                                                <i class="fas fa-info-circle info-icon"></i>
                                                <div class="info-tooltip">
                                                    El producto es visible en la tienda (opcionalmente), pero se indica claramente que no hay stock disponible y no se puede comprar.
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class="filter-group">
                                    <label>Condición</label>
                                    <div class="filter-options">
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="destacado"> Destacado
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="oferta"> Oferta
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="liquidacion"> Liquidación
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="nuevo"> Nuevo
                                        </label>
                                        <label class="filter-checkbox">
                                            <input type="checkbox" value="exclusivo"> Exclusivo
                                        </label>
                                    </div>
                                </div>
                                <div class="filter-group">
                                    <label>Categoría</label>
                                    <select id="filterCategory">
                                        <option value="">Todas las categorías</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label>Subcategoría</label>
                                    <select id="filterSubcategory">
                                        <option value="">Todas las subcategorías</option>
                                    </select>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- Tabla de productos -->
                    <div class="section-body">
                        <!-- Contenedor para productos en vista móvil y desktop -->
                        <div class="productos-container">
                            <!-- Contenedor específico para tarjetas en modo móvil -->
                            <div id="product-cards-container" class="product-cards-container"></div>

                            <!-- Tabla de productos para desktop -->
                            <table class="admin-table" id="productsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Imagen</th>
                                        <th>Nombre</th>
                                        <th>Descripción</th>
                                        <th>Precio</th>
                                        <th>Stock</th>
                                        <th>Estado</th>
                                        <th>Condición</th>
                                        <th>Categoría</th>
                                        <th>Subcategoría</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="11" class="loading-row">
                                            <i class="fas fa-spinner fa-spin"></i> Cargando productos...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginación -->
                        <div class="pagination">
                            <button class="pagination-item">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-item active">1</button>
                            <button class="pagination-item">2</button>
                            <button class="pagination-item">3</button>
                            <button class="pagination-item">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Formulario de producto - Permite crear o editar productos -->
                <section class="content-section" id="editProductSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-edit"></i>
                            Agregar Producto
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" id="cancelProductBtn">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </button>
                            <button class="btn btn-success" id="saveProductBtn">
                                <i class="fas fa-save"></i>
                                Guardar Producto
                            </button>
                        </div>
                    </div>

                    <div class="section-body">
                        <div class="form-grid">
                            <!-- Columna principal del formulario -->
                            <div class="form-main">
                                <!-- Información básica del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Información Básica</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productName">Nombre del Producto *</label>
                                            <input type="text" class="form-control" id="productName" value="Smartphone Premium X12 Pro">
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="productDescription">Descripción *</label>
                                            <textarea class="form-control" id="productDescription">El Smartphone Premium X12 Pro cuenta con una pantalla AMOLED de 6.7", procesador octa-core, 8GB de RAM y 256GB de almacenamiento. Cámara principal de 108MP y batería de 5000mAh con carga rápida.</textarea>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="productShortDescription">Descripción Corta</label>
                                            <textarea class="form-control" id="productShortDescription">Smartphone de alta gama con cámara profesional y batería de larga duración.</textarea>
                                            <div class="form-hint">Esta descripción se mostrará en las vistas de lista y tarjetas.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Imágenes del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Imágenes</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <!-- Área para arrastrar y soltar imágenes -->
                                        <div class="dropzone" id="productImageDropzone">
                                            <div class="dropzone-icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <div class="dropzone-title">Arrastra y suelta imágenes aquí</div>
                                            <div class="dropzone-hint">o haz clic para seleccionar archivos (máximo 5 imágenes, formato JPG, PNG o WEBP)</div>
                                        </div>

                                        <!-- Galería de imágenes del producto -->
                                        <div class="image-gallery">
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 1">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 2">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 3">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Precios y stock del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Precios y Stock</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productPrice">Precio *</label>
                                            <div class="price-input-container">
                                            <span class="price-symbol">$</span>
                                            <input type="text"
                                            class="form-control price-input"
                                            id="productPrice"
                                            value="399990"
                                            data-price="true"
                                            onchange="this.value=Math.floor(this.value.replace(/[^0-9]/g, ''))"
                                            onblur="this.value='$'+Math.floor(this.value.replace(/[^0-9]/g, '')).toLocaleString('es-CL')">
                                            </div>
                                            <div class="form-hint">Ingresa el precio sin puntos ni símbolos.</div>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="productOriginalPrice">Precio Original</label>
                                            <div class="price-input-container">
                                            <span class="price-symbol">$</span>
                                            <input type="text"
                                            class="form-control price-input"
                                            id="productOriginalPrice"
                                            value="469990"
                                            data-price="true"
                                            onchange="this.value=Math.floor(this.value.replace(/[^0-9]/g, ''))"
                                            onblur="this.value='$'+Math.floor(this.value.replace(/[^0-9]/g, '')).toLocaleString('es-CL')">
                                            </div>
                                            <div class="form-hint">Si el producto está en oferta, ingresa el precio original.</div>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="productStock">Stock *</label>
                                            <input type="number" class="form-control" id="productStock" value="25">
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="productSKU">SKU</label>
                                            <input type="text" class="form-control" id="productSKU" value="SP-X12-PRO-BLK">
                                            <div class="form-hint">Código único para identificar el producto.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Columna lateral del formulario -->
                            <div class="form-sidebar">
                                <!-- Estado y condición del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Estado y Condición</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <!-- Estado del producto -->
                                        <div class="form-group">
                                            <div class="tooltip-wrapper">
                                                <label class="form-label">Estado</label>
                                                <i class="fas fa-info-circle tooltip-icon"></i>
                                                <div class="tooltip-content">
                                                    <strong>Publicado:</strong> El producto está activo, visible para los clientes y disponible para la compra (si hay stock).
                                                    <br><br>
                                                    <strong>Borrador:</strong> El producto está guardado en el sistema pero no es visible en la tienda pública.
                                                    <br><br>
                                                    <strong>Agotado:</strong> El producto es visible en la tienda pero se indica claramente que no hay stock disponible y no se puede comprar.
                                                </div>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="statusActive" name="productStatus" checked>
                                                <label class="radio-label" for="statusActive">Publicado</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="statusDraft" name="productStatus">
                                                <label class="radio-label" for="statusDraft">Borrador</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="statusOutOfStock" name="productStatus">
                                                <label class="radio-label" for="statusOutOfStock">Agotado</label>
                                            </div>
                                        </div>

                                        <!-- Condición del producto -->
                                        <div class="form-group">
                                            <div class="tooltip-wrapper">
                                                <label class="form-label">Condición</label>
                                                <i class="fas fa-info-circle tooltip-icon"></i>
                                                <div class="tooltip-content">
                                                    <strong>Destacado:</strong> Para productos que quieres resaltar, quizás en la página principal o en una sección especial.
                                                    <br><br>
                                                    <strong>Oferta:</strong> Indica que el producto tiene un precio rebajado temporalmente. Usualmente se muestra el precio original tachado junto al nuevo precio.
                                                    <br><br>
                                                    <strong>Liquidación:</strong> Similar a "Oferta", pero generalmente implica un descuento mayor para deshacerse de las últimas unidades de stock.
                                                    <br><br>
                                                    <strong>Nuevo:</strong> Para señalar productos recién llegados a la tienda.
                                                    <br><br>
                                                    <strong>Exclusivo:</strong> Indica que el producto solo se encuentra en esta tienda o es una edición especial.
                                                    <br><br>
                                                    <strong>Ninguna:</strong> El estado por defecto, sin ninguna etiqueta especial.
                                                </div>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionDestacado" name="productCondition" value="destacado">
                                                <label class="radio-label" for="conditionDestacado">Productos Destacados</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionOferta" name="productCondition" value="oferta">
                                                <label class="radio-label" for="conditionOferta">Ofertas Especiales</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionLiquidacion" name="productCondition" value="liquidacion">
                                                <label class="radio-label" for="conditionLiquidacion">Liquidación</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionNuevo" name="productCondition" value="nuevo">
                                                <label class="radio-label" for="conditionNuevo">Recién Llegados</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionExclusivo" name="productCondition" value="exclusivo">
                                                <label class="radio-label" for="conditionExclusivo">Colección Exclusiva</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionNinguno" name="productCondition" value="ninguno" checked>
                                                <label class="radio-label" for="conditionNinguno">Ninguna</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Categorización del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Categorización</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label for="productTipoCategoria" class="form-label">Tipo de Categoría *</label>
                                            <select class="form-control" id="productTipoCategoria" name="productTipoCategoria" required>
                                                <option value="">Seleccionar tipo de categoría</option>
                                                <!-- Las opciones se cargarán dinámicamente -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="productCategory" class="form-label">Categoría *</label>
                                            <select class="form-control" id="productCategory" name="productCategory" required>
                                                <option value="">Seleccionar categoría</option>
                                                <!-- Las opciones se cargarán dinámicamente -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="productSubcategory" class="form-label">Subcategoría</label>
                                            <select class="form-control" id="productSubcategory" name="productSubcategory">
                                                <option value="">Seleccionar subcategoría</option>
                                                <!-- Las opciones se cargarán dinámicamente -->
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="productTags" class="form-label">Etiquetas</label>
                                            <input type="text" class="form-control" id="productTags" name="productTags"
                                                   placeholder="Etiquetas separadas por comas">
                                            <small class="form-text text-muted">Ayudan a encontrar tu producto en búsquedas</small>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </section>

                <!-- Categorías - Gestión de categorías de productos -->
                <section class="content-section" id="categoriesSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-tags"></i>
                            Categorías
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Nueva Categoría
                            </button>
                        </div>
                    </div>

                    <div class="section-body">
                        <!-- Cuadrícula de tarjetas de categorías -->
                        <div class="categories-grid">
                            <!-- Categoría: Smartphones -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Smartphones</div>
                                    <div class="category-count">32 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Categoría: Laptops -->
                            <div class="category-card">
                                <div la="category-header">
                                    <i class="fas fa-laptop"></i>
                                </div>
                                <div la="category-body">
                                    <div la="category-name">Laptops</div>
                                    <div class="category-count">18 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Categoría: Audio -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-headphones"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Audio</div>
                                    <div class="category-count">24 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Categoría: Fotografía -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Fotografía</div>
                                    <div class="category-count">15 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Categoría: TV & Video -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">TV & Video</div>
                                    <div class="category-count">12 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Categoría: Gaming -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-gamepad"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Gaming</div>
                                    <div class="category-count">23 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Información de la tienda - Configuración de la tienda -->
                <section class="content-section" id="storeInfoSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-store"></i>
                            Información de la Tienda
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" id="cancelStoreBtn">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </button>
                            <button class="btn btn-success" id="saveStoreBtn">
                                <i class="fas fa-save"></i>
                                Guardar Cambios de Tienda
                            </button>
                        </div>
                    </div>

                    <div class="section-body">
                        <div class="form-grid">
                            <!-- Columna principal del formulario de tienda -->
                            <div class="form-main">
                                <!-- Información básica de la tienda -->
                                <div class="form-section">
                                    <div la="form-section-header">
                                        <h3 class="form-section-title">Información Básica</h3>
                                    </div>
                                    <div la="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeName">Nombre de la Tienda *</label>
                                            <input type="text" class="form-control" id="storeName" value="Electrónica Digital">
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label" for="storeDescription">Descripción *</label>
                                            <textarea class="form-control" id="storeDescription">Somos especialistas en productos electrónicos de alta calidad. Ofrecemos una amplia gama de dispositivos, accesorios y servicios técnicos para satisfacer todas tus necesidades tecnológicas.</textarea>
                                        </div>

                                        <div class="form-group">
                                            <label class="form-label">Logo de la Tienda</label>
                                            <div class="dropzone" id="storeLogoDropzone">
                                                <div class="dropzone-icon">
                                                    <i class="fas fa-cloud-upload-alt"></i>
                                                </div>
                                                <div class="dropzone-title">Arrastra y suelta el logo aquí</div>
                                                <div class="dropzone-hint">o haz clic para seleccionar archivo (formato JPG, PNG o SVG)</div>
                                            </div>

                                            <div class="image-gallery" style="grid-template-columns: 1fr;">
                                                <div class="image-item">
                                                    <img src="https://via.placeholder.com/150" alt="Logo de la tienda">
                                                    <div class="image-actions">
                                                        <button class="image-action-btn" title="Eliminar">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Información de contacto de la tienda -->
                                <div class="form-section">
                                    <div la="form-section-header">
                                        <h3 la="form-section-title">Información de Contacto</h3>
                                    </div>
                                    <div la="form-section-body">
                                        <div la="form-group">
                                            <label la="form-label" for="storeAddress">Dirección *</label>
                                            <input type="text" class="form-control" id="storeAddress" value="Av. Pedro de Valdivia 123">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeCity">Ciudad *</label>
                                            <input type="text" class="form-control" id="storeCity" value="Villarrica">
                                        </div>

                                        <div la="form-group">
                                            <label la="form-label" for="storeRegion">Región *</label>
                                            <input type="text" class="form-control" id="storeRegion" value="Araucanía">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storePhone">Teléfono *</label>
                                            <input type="text" class="form-control" id="storePhone" value="+56 9 1234 5678">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeEmail">Email *</label>
                                            <input type="email" class="form-control" id="storeEmail" value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>

                                <!-- Redes sociales de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Redes Sociales</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label la="form-label" for="storeFacebook">Facebook</label>
                                            <input type="text" class="form-control" id="storeFacebook" value="https://facebook.com/electronicadigital">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeTwitter">Twitter</label>
                                            <input type="text" class="form-control" id="storeTwitter" value="">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeInstagram">Instagram</label>
                                            <input type="text" class="form-control" id="storeInstagram" value="">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeYoutube">YouTube</label>
                                            <input type="text" class="form-control" id="storeYoutube" value="">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeWhatsapp">WhatsApp</label>
                                            <input type="text" class="form-control" id="storeWhatsapp" value="+56 9 1234 5678">
                                        </div>
                                    </div>
                                </div>

                                <!-- Horarios de atención de la tienda -->
                                <div class="form-section">
                                    <div la="form-section-header">
                                        <h3 la="form-section-title">Horarios de Atención</h3>
                                    </div>
                                    <div la="form-section-body">
                                        <div class="form-group">
                                            <label la="form-label" for="storeHours">Horario de Atención *</label>
                                            <input type="text" class="form-control" id="storeHours" value="Lun-Sáb: 9:00 - 19:00">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label">Días de Atención</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayMonday" checked>
                                                <label la="checkbox-label" for="dayMonday">Lunes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayTuesday" checked>
                                                <label la="checkbox-label" for="dayTuesday">Martes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayWednesday" checked>
                                                <label la="checkbox-label" for="dayWednesday">Miércoles</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayThursday" checked>
                                                <label la="checkbox-label" for="dayThursday">Jueves</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayFriday" checked>
                                                <label la="checkbox-label" for="dayFriday">Viernes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="daySaturday" checked>
                                                <label la="checkbox-label" for="daySaturday">Sábado</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="daySunday">
                                                <label la="checkbox-label" for="daySunday">Domingo</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Columna lateral del formulario de tienda -->
                            <div class="form-sidebar">
                                <!-- Ubicación en el mapa de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Ubicación en el Mapa</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label la="form-label" for="storeLatitude">Latitud</label>
                                            <input type="text" class="form-control" id="storeLatitude" value="-39.2746">
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label" for="storeLongitude">Longitud</label>
                                            <input type="text" class="form-control" id="storeLongitude" value="-72.2254">
                                        </div>

                                        <div class="form-group">
                                            <div style="background-color: #e9e9e9; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-top: 10px;">
                                                <span style="color: #666;">Vista previa del mapa</span>
                                            </div>
                                            <div class="form-hint">Haz clic en el mapa para seleccionar la ubicación exacta de tu tienda.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Características de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Características</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label la="form-label">Servicios Ofrecidos</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceDelivery" checked>
                                                <label la="checkbox-label" for="serviceDelivery">Envío a domicilio</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="servicePickup" checked>
                                                <label la="checkbox-label" for="servicePickup">Retiro en tienda</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceInstallation" checked>
                                                <label la="checkbox-label" for="serviceInstallation">Servicio de instalación</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceRepair" checked>
                                                <label la="checkbox-label" for="serviceRepair">Servicio técnico</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceWarranty" checked>
                                                <label la="checkbox-label" for="serviceWarranty">Garantía extendida</label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label la="form-label">Métodos de Pago</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentCash" checked>
                                                <label la="checkbox-label" for="paymentCash">Efectivo</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentCard" checked>
                                                <label la="checkbox-label" for="paymentCard">Tarjeta de crédito/débito</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentTransfer" checked>
                                                <label la="checkbox-label" for="paymentTransfer">Transferencia bancaria</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentOnline">
                                                <label la="checkbox-label" for="paymentOnline">Pago online</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Sección de Dashboard (antes estadísticas) - Muestra métricas y gráficos -->
                <section class="content-section" id="statsSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </h2>
                        <div class="section-actions">
                            <div class="date-filter">
                                <select id="dashboardPeriod" class="form-control">
                                    <option value="today">Hoy</option>
                                    <option value="yesterday">Ayer</option>
                                    <option value="week" selected>Esta semana</option>
                                    <option value="month">Este mes</option>
                                    <option value="quarter">Este trimestre</option>
                                    <option value="year">Este año</option>
                                </select>
                            </div>
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Exportar
                            </button>
                            <button class="btn btn-primary" id="refreshDashboard">
                                <i class="fas fa-sync-alt"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>

                    <div class="section-body">
                        <!-- Tarjetas de estadísticas - Resumen de métricas clave -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon products">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">124</div>
                                    <div class="stat-label">Productos</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 12% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon sales" style="background-color: rgba(76, 175, 80, 0.1); color: #4CAF50;">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">$2.4M</div>
                                    <div class="stat-label">Ventas Totales</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 8.5% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon users">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">287</div>
                                    <div class="stat-label">Visitantes únicos</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 5.2% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon views">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">1,254</div>
                                    <div class="stat-label">Vistas de productos</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 15.8% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon" style="background-color: rgba(255, 87, 34, 0.1); color: #FF5722;">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">48</div>
                                    <div class="stat-label">Compartidos</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 22% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon" style="background-color: rgba(0, 188, 212, 0.1); color: #00BCD4;">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">$19,850</div>
                                    <div class="stat-label">Ticket Promedio</div>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i> 3.2% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon" style="background-color: rgba(156, 39, 176, 0.1); color: #9C27B0;">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">4.2%</div>
                                    <div class="stat-label">Tasa de Conversión</div>
                                    <div class="stat-trend positive">
                                        <i class="fas fa-arrow-up"></i> 0.5% vs mes anterior
                                    </div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon" style="background-color: rgba(255, 193, 7, 0.1); color: #FFC107;">
                                    <i class="fas fa-undo"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">2.8%</div>
                                    <div class="stat-label">Tasa de Devoluciones</div>
                                    <div class="stat-trend negative">
                                        <i class="fas fa-arrow-down"></i> 0.3% vs mes anterior
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Gráficos de estadísticas - Primera fila -->
                        <div class="charts-container" style="margin-top: 30px; display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
                            <!-- Gráfico de ventas mensuales (ocupa 2/3 del ancho) -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <div class="chart-container" style="position: relative; height: 350px;">
                                    <canvas id="visitsMonthlyChart"></canvas>
                                </div>
                            </div>

                            <!-- Gráfico de dispositivos (ocupa 1/3 del ancho) -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Dispositivos de Acceso</h3>
                                <div class="chart-container" style="position: relative; height: 350px;">
                                    <canvas id="devicesChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Gráficos de estadísticas - Segunda fila -->
                        <div class="charts-container" style="margin-top: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- Gráfico de productos más vendidos -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Productos Más Vendidos</h3>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="topProductsChart"></canvas>
                                </div>
                            </div>

                            <!-- Gráfico de interés por categoría -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Interés por Categoría</h3>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="categoriesChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Gráficos de estadísticas - Tercera fila -->
                        <div class="charts-container" style="margin-top: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- Gráfico de tendencia de stock -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="stockTrendChart"></canvas>
                                </div>
                            </div>

                            <!-- Gráfico de rendimiento por hora -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="hourlyPerformanceChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Tabla de resumen de actividad -->
                        <div class="summary-table" style="margin-top: 30px; background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                            <div class="summary-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h3 style="font-size: 16px; font-weight: 600; margin: 0;">Resumen de Ventas Mensuales</h3>
                                <div class="summary-actions">
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </button>
                                </div>
                            </div>

                            <!-- Contenedor con estilo inline adicional -->
                            <div class="admin-table-container" style="max-width: 100%; overflow-x: auto;">
                                <!-- Tabla con width: max-content para que termine exactamente en la última columna -->
                                <table class="admin-table" style="width: max-content; min-width: 100%;">
                                    <thead>
                                        <tr>
                                            <th>Mes</th>
                                            <th>Ventas Totales</th>
                                            <th>Productos Vendidos</th>
                                            <th>Ticket Promedio</th>
                                            <th>Tasa de Conversión</th>
                                            <th>Devoluciones</th>
                                            <th>Margen Bruto</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Enero</td>
                                        <td>$1,245,000</td>
                                        <td>245</td>
                                        <td>$18,500</td>
                                        <td>3.8%</td>
                                        <td>2.5%</td>
                                        <td>32.5%</td>
                                    </tr>
                                    <tr>
                                        <td>Febrero</td>
                                        <td>$1,480,000</td>
                                        <td>285</td>
                                        <td>$19,200</td>
                                        <td>4.1%</td>
                                        <td>2.2%</td>
                                        <td>33.8%</td>
                                    </tr>
                                    <tr>
                                        <td>Marzo</td>
                                        <td>$1,320,000</td>
                                        <td>260</td>
                                        <td>$18,800</td>
                                        <td>3.9%</td>
                                        <td>2.7%</td>
                                        <td>31.5%</td>
                                    </tr>
                                    <tr>
                                        <td>Abril</td>
                                        <td>$1,650,000</td>
                                        <td>310</td>
                                        <td>$19,500</td>
                                        <td>4.3%</td>
                                        <td>2.4%</td>
                                        <td>34.2%</td>
                                    </tr>
                                    <tr>
                                        <td>Mayo</td>
                                        <td>$1,820,000</td>
                                        <td>345</td>
                                        <td>$19,800</td>
                                        <td>4.5%</td>
                                        <td>2.3%</td>
                                        <td>35.1%</td>
                                    </tr>
                                    <tr>
                                        <td>Junio</td>
                                        <td>$2,100,000</td>
                                        <td>380</td>
                                        <td>$20,200</td>
                                        <td>4.8%</td>
                                        <td>2.1%</td>
                                        <td>36.5%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Script para arreglar los tooltips eliminado - ahora se maneja a través del componente tooltips.js -->

    <!-- El manejo de tooltips ahora se realiza a través del módulo tooltips.js -->

    <!-- Modal de confirmación para eliminar producto -->
    <div class="confirm-modal" id="deleteConfirmModal" style="display: none;">
        <div class="confirm-modal-content">
            <div class="confirm-modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Confirmar eliminación</h3>
                <button class="modal-close" id="closeDeleteModal">&times;</button>
            </div>
            <div class="confirm-modal-body">
                <p>¿Estás seguro que deseas eliminar este producto?</p>
                <p>Esta acción no se puede deshacer.</p>
            </div>
            <div class="confirm-modal-footer">
                <button class="btn btn-secondary" id="cancelDelete">Cancelar</button>
                <button class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash"></i> Eliminar
                </button>
            </div>
        </div>
    </div>


    <!-- Panel lateral para editarx productos -->
    <div class="edit-container" id="editProductPanel">
        <div class="edit-content">
            <div class="edit-header">
                <h3><i class="fas fa-edit"></i> Editar Producto</h3>
                <button class="btn-close-edit" id="closeEdit">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="edit-body">
                <!-- Información básica del producto -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Información Básica</h4>
                    <input type="hidden" id="editProductId">

                    <div class="edit-group">
                        <label for="editProductName">Nombre del Producto *</label>
                        <input type="text" id="editProductName" class="edit-control" placeholder="Nombre del producto" required>
                    </div>

                    <div class="edit-group">
                        <label for="editProductDescription">Descripción *</label>
                        <textarea id="editProductDescription" class="edit-control" placeholder="Descripción detallada del producto" required></textarea>
                    </div>

                    <div class="edit-group">
                        <label for="editProductShortDescription">Descripción Corta</label>
                        <textarea id="editProductShortDescription" class="edit-control" placeholder="Breve descripción para vistas en miniatura"></textarea>
                    </div>
                </div>

                <!-- Precios y stock -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Precios y Stock</h4>

                    <div class="edit-row">
                        <div class="edit-group">
                            <label for="editProductPrice">Precio *</label>
                            <div class="price-input-container">
                                <div class="price-symbol">$</div>
                                <input type="text" id="editProductPrice" class="edit-control price-input" placeholder="0" required>
                            </div>
                        </div>

                        <div class="edit-group">
                            <label for="editProductOriginalPrice">Precio Original *</label>
                            <div class="price-input-container">
                                <div class="price-symbol">$</div>
                                <input type="text" id="editProductOriginalPrice" class="edit-control price-input" placeholder="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="edit-row">
                        <div class="edit-group">
                            <label for="editProductStock">Stock *</label>
                            <input type="number" id="editProductStock" class="edit-control" placeholder="0" required>
                        </div>

                        <div class="edit-group">
                            <label for="editProductSKU">SKU</label>
                            <input type="text" id="editProductSKU" class="edit-control" placeholder="ABC-123">
                        </div>
                    </div>
                </div>

                <!-- Estado y condición -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Estado y Condición</h4>

                    <div class="edit-group">
                        <label>Estado
                            <span class="info-icon-container">
                                <i class="fas fa-info-circle info-icon"></i>
                                <div class="info-tooltip">
                                    <p><strong>Publicado:</strong> El producto está activo, visible para los clientes y disponible para la compra (si hay stock). Es el estado estándar para un producto en venta.</p>
                                    <p><strong>Borrador:</strong> El producto está guardado en el sistema pero no es visible en la tienda pública. Útil para cuando estás creando el producto y aún no está listo para ser publicado.</p>
                                    <p><strong>Agotado:</strong> El producto es visible en la tienda (opcionalmente), pero se indica claramente que no hay stock disponible y no se puede comprar.</p>
                                </div>
                            </span>
                        </label>
                        <div class="edit-options">
                            <label class="edit-radio">
                                <input type="radio" name="editProductStatus" value="publicado">
                                <span>Publicado</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductStatus" value="borrador">
                                <span>Borrador</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductStatus" value="agotado">
                                <span>Agotado</span>
                            </label>
                        </div>
                    </div>

                    <div class="edit-group">
                        <div class="info-icon-container">
                            <label>Condición</label>
                            <i class="fas fa-info-circle info-icon"></i>
                            <div class="info-tooltip" style="margin-left:191px;">
                                <strong>Destacado:</strong> Para productos que quieres resaltar, quizás en la página principal o en una sección especial.
                                <br><br>
                                <strong>Oferta:</strong> Indica que el producto tiene un precio rebajado temporalmente. Usualmente se muestra el precio original tachado junto al nuevo precio.
                                <br><br>
                                <strong>Liquidación:</strong> Similar a "Oferta", pero generalmente implica un descuento mayor para deshacerse de las últimas unidades de stock.
                                <br><br>
                                <strong>Nuevo:</strong> Para señalar productos recién llegados a la tienda.
                                <br><br>
                                <strong>Exclusivo:</strong> Indica que el producto solo se encuentra en esta tienda o es una edición especial.
                                <br><br>
                                <strong>Ninguna:</strong> El estado por defecto, sin ninguna etiqueta especial.
                            </div>
                        </div>
                        <div class="edit-options">
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="destacado">
                                <span>Destacado</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="oferta">
                                <span>Oferta</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="liquidacion">
                                <span>Liquidación</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="nuevo">
                                <span>Nuevo</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="exclusivo">
                                <span>Exclusivo</span>
                            </label>
                            <label class="edit-radio">
                                <input type="radio" name="editProductCondition" value="ninguno">
                                <span>Ninguna</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Categorización -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Categorización</h4>

                    <div class="edit-group">
                        <label for="editProductTipoCategoria">Tipo de Categoría *</label>
                        <select id="editProductTipoCategoria" class="edit-control" required>
                            <option value="">Seleccionar tipo de categoría</option>
                        </select>
                    </div>

                    <div class="edit-group">
                        <label for="editProductCategory">Categoría *</label>
                        <select id="editProductCategory" class="edit-control" required>
                            <option value="">Seleccionar categoría</option>
                        </select>
                    </div>

                    <div class="edit-group">
                        <label for="editProductSubcategory">Subcategoría *</label>
                        <select id="editProductSubcategory" class="edit-control" required>
                            <option value="">Seleccionar subcategoría</option>
                        </select>
                    </div>

                    <div class="edit-group">
                        <label for="editProductTags">Etiquetas</label>
                        <input type="text" id="editProductTags" class="edit-control" placeholder="Separadas por comas">
                    </div>
                </div>

                <!-- Atributos adicionales -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Atributos</h4>

                    <div class="edit-row">
                        <div class="edit-group">
                            <label for="editProductBrand">Marca</label>
                            <input type="text" id="editProductBrand" class="edit-control" placeholder="Marca del producto">
                        </div>

                        <div class="edit-group">
                            <label for="editProductColor">Color</label>
                            <input type="text" id="editProductColor" class="edit-control" placeholder="Color principal">
                        </div>
                    </div>

                    <div class="edit-row">
                        <div class="edit-group">
                            <label for="editProductWeight">Peso (g)</label>
                            <input type="number" id="editProductWeight" class="edit-control" placeholder="0">
                        </div>

                        <div class="edit-group">
                            <label for="editProductDimensions">Dimensiones (cm)</label>
                            <input type="text" id="editProductDimensions" class="edit-control" placeholder="Alto x Ancho x Profundidad">
                        </div>
                    </div>
                </div>

                <!-- Imagen del producto -->
                <div class="edit-section">
                    <h4 class="edit-section-title">Imagen Principal</h4>

                    <div class="edit-group">
                        <div class="edit-image-preview" id="editImagePreview">
                            <img src="../images/placeholder.png" alt="Vista previa de imagen" id="editImagePreviewImg">
                            <div class="edit-image-overlay">
                                <button type="button" class="btn btn-secondary" id="editImageBtn">
                                    <i class="fas fa-upload"></i> Cambiar imagen
                                </button>
                            </div>
                        </div>
                        <input type="file" id="editImageInput" style="display: none" accept="image/*">
                        <input type="hidden" id="editImagePath">
                    </div>
                </div>
            </div>

            <div class="edit-actions">
                <button class="btn btn-secondary" id="cancelEditBtn">
                    <i class="fas fa-times"></i> Cancelar
                </button>
                <button class="btn btn-success" id="saveEditBtn">
                    <i class="fas fa-save"></i> Guardar cambios
                </button>
            </div>
        </div>
    </div>

    <!-- Overlay para el panel de filtros -->
    <div class="filter-overlay" id="filterOverlay" style="display: none;"></div>

    <!-- Overlay para edición (corregido, solo uno) -->
    <div id="editOverlay" class="overlay overlay-reset"></div>

    <!-- Scripts esenciales -->
    <script src="../js/sidebar-navigation.js"></script>

    <!-- Script para el nuevo sidebar mejorado -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Seleccionar elementos
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.getElementById('aside-toggle');
            const toggleIcon = document.getElementById('toggle-icon');

            // Establecer estado inicial desde localStorage
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                sidebar.classList.remove('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                if (window.innerWidth <= 992) {
                    sidebar.classList.add('expanded');
                }
            }

            // Función para alternar el sidebar
            function toggleSidebar() {
                if (window.innerWidth > 992) {
                    // Desktop
                    sidebar.classList.toggle('collapsed');
                    const isNowCollapsed = sidebar.classList.contains('collapsed');
                    localStorage.setItem('sidebarCollapsed', isNowCollapsed);
                    // Cambiar icono
                    toggleIcon.className = isNowCollapsed ? 'fas fa-bars' : 'fas fa-times';
                } else {
                    // Mobile
                    sidebar.classList.toggle('expanded');
                    // Cambiar icono
                    toggleIcon.className = sidebar.classList.contains('expanded') ? 'fas fa-times' : 'fas fa-bars';
                }
            }

            // Evento para el botón toggle
            toggleButton.addEventListener('click', toggleSidebar);

            // Evento de redimensionamiento de ventana
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992) {
                    // Cambiar a modo desktop
                    sidebar.classList.remove('expanded');
                    // Restaurar estado guardado
                    if (localStorage.getItem('sidebarCollapsed') === 'true') {
                        sidebar.classList.add('collapsed');
                    } else {
                        sidebar.classList.remove('collapsed');
                    }
                } else {
                    // Cambiar a modo mobile
                    sidebar.classList.remove('collapsed');
                }
            });
        });
    </script>

    <!-- Scripts eliminados -->

    <!-- Modal de Error -->
    <div id="errorModal" class="error-modal">
        <div class="modal-content">
            <div class="error-modal-header">
                <i class="fas fa-exclamation-circle"></i>
                <h3>Error al Editar Producto</h3>
            </div>
            <div class="error-modal-body">
                <p>Se encontraron los siguientes errores:</p>
                <ul class="error-list" id="errorList">
                    <!-- Los errores se insertarán aquí dinámicamente -->
                </ul>
            </div>
            <div class="error-modal-footer">
                <button class="btn btn-danger" id="closeErrorModal">
                    <i class="fas fa-times"></i> Cerrar
                </button>
            </div>
        </div>
    </div>

    <!-- Script para el header moderno -->
    <script src="../js/header-modern.js"></script>
    <!-- Script para arreglar el toggle -->
    <script src="../js/toggle-fix.js"></script>

    <!-- Script básico para formatear precios -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Función simple para formatear precios
        function formatPrice(price) {
            return '$' + parseInt(price).toLocaleString('es-CL');
        }

        // Exponer la función para uso global
        window.formatPrice = formatPrice;
    });
    </script>

</body>
</html>





