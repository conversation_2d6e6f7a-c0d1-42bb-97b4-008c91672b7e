export class ErrorHandler {
    static showError(message, error = null) {
        // Mostrar notificación de error
        const notification = document.getElementById('errorNotification');
        const messageElement = document.getElementById('errorMessage');
        
        if (notification && messageElement) {
            messageElement.textContent = message;
            notification.style.display = 'flex';
            
            // Auto-ocultar después de 5 segundos
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Mostrar detalles en el panel de depuración
        this.showDebugInfo(message, error);

        // Registrar en consola
        console.error('Error:', message);
        if (error) {
            console.error('Detalles:', error);
        }
    }

    static showDebugInfo(message, error = null) {
        const debugPanel = document.getElementById('debugPanel');
        const debugContent = document.getElementById('debugContent');
        
        if (debugPanel && debugContent) {
            let debugInfo = `Error: ${message}\n`;
            if (error) {
                debugInfo += `Stack: ${error.stack}\n`;
                if (error.response) {
                    debugInfo += `Response: ${JSON.stringify(error.response, null, 2)}\n`;
                }
            }
            debugContent.textContent = debugInfo;
            debugPanel.style.display = 'block';
        }
    }

    static init() {
        // Configurar botones de cierre
        document.querySelectorAll('.notification-close').forEach(button => {
            button.onclick = (e) => {
                e.target.closest('.notification').style.display = 'none';
            };
        });

        document.querySelectorAll('.debug-close').forEach(button => {
            button.onclick = (e) => {
                e.target.closest('.debug-panel').style.display = 'none';
            };
        });
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    ErrorHandler.init();
});
