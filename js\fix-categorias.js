// Script para arreglar la carga de tipos de categoría, categorías y subcategorías
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando fix-categorias.js');

    // Obtener referencias a los elementos select
    const tipoCategoriasSelect = document.getElementById('productTipoCategoria');
    const categoriasSelect = document.getElementById('productCategory');
    const subcategoriasSelect = document.getElementById('productSubcategory') ||
                               document.getElementById('subcategoria') ||
                               document.querySelector('[name="subcategoria"]');

    // Deshabilitar subcategorías al inicio
    if (subcategoriasSelect) {
        console.log('Deshabilitando select de subcategorías al inicio');
        subcategoriasSelect.disabled = true;
        subcategoriasSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
    }

    // Verificar si los elementos existen
    if (!tipoCategoriasSelect) {
        console.error('No se encontró el elemento select de tipos de categoría (productTipoCategoria)');
        return;
    }

    if (!categoriasSelect) {
        console.error('No se encontró el elemento select de categorías (productCategory)');
        return;
    }

    if (!subcategoriasSelect) {
        console.error('No se encontró el elemento select de subcategorías (productSubcategory)');
        return;
    }

    // Función para cargar los tipos de categoría
    async function cargarTiposCategorias() {
        try {
            console.log('Cargando tipos de categorías...');

            const response = await fetch('/projects/villarrica_click/public/API/productos/get_tipos_categoria.php', {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`Error al cargar tipos de categoría: ${response.status}`);
            }

            const data = await response.json();
            console.log('Datos de tipos de categoría recibidos:', data);

            // Verificar que la respuesta tenga la estructura esperada
            if (!data.success || !data.tipos || !Array.isArray(data.tipos)) {
                throw new Error('Formato de respuesta inválido para tipos de categoría');
            }

            // Limpiar opciones existentes
            tipoCategoriasSelect.innerHTML = '<option value="">Seleccionar tipo de categoría</option>';

            // Añadir nuevas opciones
            data.tipos.forEach(tipo => {
                const option = document.createElement('option');
                option.value = tipo.id;
                option.textContent = tipo.nombre;
                tipoCategoriasSelect.appendChild(option);
            });

            console.log(`${data.tipos.length} tipos de categoría cargados correctamente`);

        } catch (error) {
            console.error('Error al cargar tipos de categoría:', error);
            // Mostrar mensaje de error en el select
            tipoCategoriasSelect.innerHTML = '<option value="">Error al cargar tipos</option>';
        }
    }

    // Función para cargar categorías según el tipo seleccionado
    async function cargarCategoriasPorTipo(tipoId) {
        try {
            console.log(`Iniciando carga de categorías para tipo ID: ${tipoId}`);

            // Limpiar y deshabilitar los selects mientras se cargan los datos
            categoriasSelect.innerHTML = '<option value="">Cargando categorías...</option>';
            categoriasSelect.disabled = true;
            subcategoriasSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            subcategoriasSelect.disabled = true;

            if (!tipoId) {
                console.log('No se seleccionó ningún tipo de categoría');
                categoriasSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
                categoriasSelect.disabled = false;
                return;
            }

            const response = await fetch('/projects/villarrica_click/public/API/productos/get_categorias_by_tipo.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({ tipo_id: tipoId }),
                credentials: 'include'
            });

            console.log('Estado de la respuesta:', response.status);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Texto de error:', errorText);
                throw new Error(`Error al cargar categorías: ${response.status}`);
            }

            const data = await response.json();
            console.log('Datos de categorías recibidos:', data);

            // Habilitar el select y limpiar opciones existentes
            categoriasSelect.disabled = false;
            categoriasSelect.innerHTML = '<option value="">Seleccionar categoría</option>';

            // Verificar que data.categorias existe y es un array
            if (data.categorias && Array.isArray(data.categorias)) {
                data.categorias.forEach(categoria => {
                    const option = document.createElement('option');
                    option.value = categoria.id;
                    option.textContent = categoria.nombre;
                    categoriasSelect.appendChild(option);
                });
                console.log(`${data.categorias.length} categorías cargadas`);
            } else {
                console.log('No se encontraron categorías para este tipo');
            }

        } catch (error) {
            console.error('Error al cargar categorías:', error);
            categoriasSelect.disabled = false;
            categoriasSelect.innerHTML = '<option value="">Error al cargar categorías</option>';
        }
    }

    // Función para cargar subcategorías según la categoría seleccionada
    async function cargarSubcategoriasPorCategoria(categoriaId) {
        console.group('Carga de Subcategorías');
        console.log(`Iniciando carga de subcategorías para categoría ID: ${categoriaId}`);

        try {
            // Log del estado inicial
            console.log('Estado inicial:', {
                categoriaId,
                elementoSelect: document.getElementById('subcategoria'),
                url: `/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${categoriaId}`
            });

            // Deshabilitar el select mientras carga
            // Usamos la variable global subcategoriasSelect definida al inicio, no creamos una nueva
            if (subcategoriasSelect) {
                console.log('Select encontrado, deshabilitando temporalmente');
                subcategoriasSelect.disabled = true;
                subcategoriasSelect.innerHTML = '<option value="">Cargando...</option>';
            } else {
                console.warn('Select de subcategorías no encontrado en el DOM');
            }

            // Verificar que categoriaId sea válido
            if (!categoriaId || isNaN(parseInt(categoriaId))) {
                console.error('ID de categoría inválido:', categoriaId);
                throw new Error('ID de categoría inválido');
            }

            console.log('Iniciando fetch...');
            const response = await fetch(`/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${categoriaId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                credentials: 'include'
            });

            console.log('Estado de la respuesta:', response.status);
            const responseText = await response.text();
            console.log('Respuesta del servidor (raw):', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Datos parseados:', data);
            } catch (e) {
                console.error('Error al parsear JSON:', e);
                throw new Error('Respuesta del servidor inválida');
            }

            if (!data || !Array.isArray(data.subcategorias)) {
                console.error('Estructura de datos inválida:', data);
                throw new Error('Formato de respuesta inválido');
            }

            console.log(`Subcategorías recibidas: ${data.subcategorias.length}`);

            if (subcategoriasSelect) {
                console.log('Actualizando opciones del select...');
                subcategoriasSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
                data.subcategorias.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre;
                    subcategoriasSelect.appendChild(option);
                });
                subcategoriasSelect.disabled = false;
                console.log('Select actualizado exitosamente');
            }

        } catch (error) {
            console.error('Error en cargarSubcategoriasPorCategoria:', error);
            console.error('Stack:', error.stack);

            const subcategoriasSelect = document.getElementById('subcategoria');
            if (subcategoriasSelect) {
                subcategoriasSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
                subcategoriasSelect.disabled = true;
            }

            if (typeof showNotification === 'function') {
                showNotification('Error al cargar subcategorías: ' + error.message, 'error');
            }
        } finally {
            console.groupEnd();
        }
    }

    // Configurar evento para el select de tipos de categoría
    tipoCategoriasSelect.addEventListener('change', function() {
        const tipoId = this.value;
        console.log('Tipo de categoría seleccionado:', tipoId);
        if (tipoId) {
            cargarCategoriasPorTipo(tipoId);
        } else {
            // Resetear los selects de categoría y subcategoría
            categoriasSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
            categoriasSelect.disabled = true;
            subcategoriasSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            subcategoriasSelect.disabled = true;
        }
    });

    // Event listener para el cambio de categoría
    document.getElementById('categoria')?.addEventListener('change', function(e) {
        const categoriaId = this.value;
        console.log('Categoría seleccionada:', categoriaId);

        if (categoriaId) {
            cargarSubcategoriasPorCategoria(categoriaId);
        } else {
            const subcategoriasSelect = document.getElementById('subcategoria');
            if (subcategoriasSelect) {
                subcategoriasSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
                subcategoriasSelect.disabled = true;
            }
        }
    });

    // Cargar los tipos de categoría al iniciar
    cargarTiposCategorias();

    console.log('fix-categorias.js inicializado correctamente');
});

// Función para manejar el cambio de categoría
function handleCategoriaChange(event) {
    const categoriaId = event.target.value;
    const subcategoriaSelect = document.getElementById('editProductSubcategory') || document.querySelector('[name="subcategoria"]');

    console.log('Cambio de categoría detectado:', categoriaId);

    if (!subcategoriaSelect) {
        console.error('No se encontró el elemento select de subcategoría');
        return;
    }

    if (!categoriaId) {
        // Si no hay categoría seleccionada, deshabilitar y limpiar subcategorías
        subcategoriaSelect.disabled = true;
        subcategoriaSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
        return;
    }

    // Habilitar el select y mostrar mensaje de carga
    subcategoriaSelect.disabled = false;
    subcategoriaSelect.innerHTML = '<option value="">Cargando subcategorías...</option>';

    // Cargar subcategorías
    fetch(`/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${categoriaId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
        },
        credentials: 'include'
    })
        .then(response => {
            console.log('Estado de la respuesta:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Respuesta raw:', text);
            return JSON.parse(text);
        })
        .then(data => {
            console.log('Datos de subcategorías recibidos:', data);

            // Limpiar y rellenar el select
            subcategoriaSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';

            if (data.success && Array.isArray(data.subcategorias)) {
                data.subcategorias.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre;
                    subcategoriaSelect.appendChild(option);
                });
            }

            // Asegurarse de que el select esté habilitado
            subcategoriaSelect.disabled = false;
        })
        .catch(error => {
            console.error('Error al cargar subcategorías:', error);
            subcategoriaSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
            subcategoriaSelect.disabled = false;
        });
}

// Agregar logging al event listener principal
document.addEventListener('DOMContentLoaded', function() {
    console.group('Inicialización de Categorías');

    const categoriaSelect = document.getElementById('editProductCategory') ||
                          document.getElementById('categoria') ||
                          document.querySelector('[name="categoria"]');

    console.log('Búsqueda de select de categoría:', {
        encontrado: !!categoriaSelect,
        elemento: categoriaSelect
    });

    if (categoriaSelect) {
        console.log('Agregando event listener a select de categoría');
        categoriaSelect.addEventListener('change', function(e) {
            console.log('Evento change disparado:', {
                valor: this.value,
                evento: e
            });
            handleCategoriaChange(e);
        });

        if (categoriaSelect.value) {
            console.log('Categoría ya seleccionada, cargando subcategorías:', categoriaSelect.value);
            handleCategoriaChange({ target: categoriaSelect });
        }
    } else {
        console.error('No se encontró el elemento select de categoría');
    }

    console.groupEnd();
});

// Event listener para el cambio de categoría
const categoriaSelect = document.getElementById('categoria') ||
                          document.getElementById('productCategory') ||
                          document.querySelector('[name="categoria"]');

// Obtener referencia al select de subcategorías
const subcategoriasSelect = document.getElementById('productSubcategory') ||
                           document.getElementById('subcategoria') ||
                           document.querySelector('[name="subcategoria"]');

if (categoriaSelect) {
    console.log('Configurando event listener para categoría');
    categoriaSelect.addEventListener('change', function(e) {
        const categoriaId = this.value;
        console.log('Categoría seleccionada:', categoriaId);

        if (categoriaId) {
            console.log(`Iniciando carga de subcategorías para categoría ID: ${categoriaId}`);
            fetch(`/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${categoriaId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                credentials: 'include'
            })
                .then(response => {
                    console.log('Respuesta recibida:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Respuesta raw:', text);
                    return JSON.parse(text);
                })
                .then(data => {
                    console.log('Datos procesados:', data);

                    if (subcategoriasSelect) {
                        subcategoriasSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
                        subcategoriasSelect.disabled = false;

                        if (data.success && Array.isArray(data.subcategorias)) {
                            data.subcategorias.forEach(subcategoria => {
                                const option = document.createElement('option');
                                option.value = subcategoria.id;
                                option.textContent = subcategoria.nombre;
                                subcategoriasSelect.appendChild(option);
                            });
                            console.log(`${data.subcategorias.length} subcategorías cargadas`);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error al cargar subcategorías:', error);
                    if (subcategoriasSelect) {
                        subcategoriasSelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
                        subcategoriasSelect.disabled = true;
                    }
                });
        } else {
            if (subcategoriasSelect) {
                subcategoriasSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
                subcategoriasSelect.disabled = true;
            }
        }
    });
} else {
    console.error('No se encontró el select de categoría');
}



