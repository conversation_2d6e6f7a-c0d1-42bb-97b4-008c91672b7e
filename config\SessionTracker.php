<?php
/**
 * SessionTracker
 * 
 * Clase para gestionar el tracking de sesiones en base de datos
 */
class SessionTracker {
    private $conn;
    private static $instance = null;
    private $session_id;
    private $page_entry_time;
    private $current_url;
    
    /**
     * Constructor privado para patr<PERSON>
     */
    private function __construct($conn) {
        $this->conn = $conn;
        $this->session_id = session_id();
        $this->page_entry_time = microtime(true);
        $this->current_url = $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Obtiene la instancia única de SessionTracker
     */
    public static function getInstance($conn) {
        if (self::$instance === null) {
            self::$instance = new self($conn);
        }
        return self::$instance;
    }
    
    /**
     * Obtiene información del navegador y sistema operativo
     */
    private function getBrowserInfo() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'];
        
        // Detectar navegador
        $browser = 'Desconocido';
        $browser_version = '';
        
        if (preg_match('/MSIE|Trident/i', $user_agent)) {
            $browser = 'Internet Explorer';
            preg_match('/MSIE\s*([0-9\.]+)/i', $user_agent, $matches);
            if (!empty($matches[1])) {
                $browser_version = $matches[1];
            } else {
                preg_match('/rv:([0-9\.]+)/i', $user_agent, $matches);
                if (!empty($matches[1])) {
                    $browser_version = $matches[1];
                }
            }
        } elseif (preg_match('/Edg/i', $user_agent)) {
            $browser = 'Microsoft Edge';
            preg_match('/Edg\/([0-9\.]+)/i', $user_agent, $matches);
            $browser_version = !empty($matches[1]) ? $matches[1] : '';
        } elseif (preg_match('/Firefox/i', $user_agent)) {
            $browser = 'Firefox';
            preg_match('/Firefox\/([0-9\.]+)/i', $user_agent, $matches);
            $browser_version = !empty($matches[1]) ? $matches[1] : '';
        } elseif (preg_match('/Chrome/i', $user_agent)) {
            $browser = 'Chrome';
            preg_match('/Chrome\/([0-9\.]+)/i', $user_agent, $matches);
            $browser_version = !empty($matches[1]) ? $matches[1] : '';
        } elseif (preg_match('/Safari/i', $user_agent)) {
            $browser = 'Safari';
            preg_match('/Version\/([0-9\.]+)/i', $user_agent, $matches);
            $browser_version = !empty($matches[1]) ? $matches[1] : '';
        } elseif (preg_match('/Opera|OPR/i', $user_agent)) {
            $browser = 'Opera';
            preg_match('/(?:Opera|OPR)\/([0-9\.]+)/i', $user_agent, $matches);
            $browser_version = !empty($matches[1]) ? $matches[1] : '';
        }
        
        // Detectar sistema operativo
        $os = 'Desconocido';
        if (preg_match('/Windows/i', $user_agent)) {
            $os = 'Windows';
            if (preg_match('/Windows NT 10.0/i', $user_agent)) {
                $os .= ' 10';
            } elseif (preg_match('/Windows NT 6.3/i', $user_agent)) {
                $os .= ' 8.1';
            } elseif (preg_match('/Windows NT 6.2/i', $user_agent)) {
                $os .= ' 8';
            } elseif (preg_match('/Windows NT 6.1/i', $user_agent)) {
                $os .= ' 7';
            }
        } elseif (preg_match('/Macintosh|Mac OS X/i', $user_agent)) {
            $os = 'Mac OS X';
            preg_match('/Mac OS X 10[._]([0-9]+)/i', $user_agent, $matches);
            if (!empty($matches[1])) {
                $os .= ' 10.' . $matches[1];
            }
        } elseif (preg_match('/Linux/i', $user_agent)) {
            $os = 'Linux';
            if (preg_match('/Android/i', $user_agent)) {
                $os = 'Android';
                preg_match('/Android\s*([0-9\.]+)/i', $user_agent, $matches);
                if (!empty($matches[1])) {
                    $os .= ' ' . $matches[1];
                }
            }
        } elseif (preg_match('/iOS|iPhone|iPad|iPod/i', $user_agent)) {
            $os = 'iOS';
            preg_match('/OS\s*([0-9_]+)/i', $user_agent, $matches);
            if (!empty($matches[1])) {
                $os .= ' ' . str_replace('_', '.', $matches[1]);
            }
        }
        
        // Detectar si es móvil
        $is_mobile = false;
        if (preg_match('/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i', $user_agent)) {
            $is_mobile = true;
        }
        
        // Detectar tipo de dispositivo
        $device_type = 'Desktop';
        if ($is_mobile) {
            if (preg_match('/(tablet|ipad|playbook|silk)|(android(?!.*mobile))/i', $user_agent)) {
                $device_type = 'Tablet';
            } else {
                $device_type = 'Mobile';
            }
        }
        
        return [
            'browser' => $browser,
            'browser_version' => $browser_version,
            'os' => $os,
            'is_mobile' => $is_mobile,
            'device_type' => $device_type
        ];
    }
    
    /**
     * Obtiene información de geolocalización (simplificada)
     */
    private function getGeoInfo() {
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // En un entorno real, utilizaríamos un servicio como MaxMind GeoIP o ipinfo.io
        // Para este ejemplo, utilizamos datos simples
        // En producción, implementar una API de geolocalización real
        
        // Por ahora, devolvemos datos genéricos
        return [
            'country' => 'Unknown',
            'city' => 'Unknown'
        ];
    }
    
    /**
     * Registra una nueva sesión de usuario en la base de datos
     */
    public function registerSession($user_id, $username) {
        if (empty($this->session_id)) {
            error_log("No hay ID de sesión disponible");
            return false;
        }
        
        $browser_info = $this->getBrowserInfo();
        $geo_info = $this->getGeoInfo();
        $screen_resolution = isset($_COOKIE['screen_resolution']) ? $_COOKIE['screen_resolution'] : null;
        
        try {
            // Verificar si la sesión ya está registrada
            $check_stmt = $this->conn->prepare("SELECT session_id FROM user_sessions WHERE session_id = ?");
            $check_stmt->bind_param("s", $this->session_id);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($result->num_rows > 0) {
                // La sesión ya existe, actualizamos la actividad
                $update_stmt = $this->conn->prepare("
                    UPDATE user_sessions 
                    SET last_activity = NOW(), 
                        is_active = TRUE,
                        pages_visited = pages_visited + 1
                    WHERE session_id = ?
                ");
                $update_stmt->bind_param("s", $this->session_id);
                $update_stmt->execute();
                error_log("Sesión actualizada: " . $this->session_id);
            } else {
                // Registrar nueva sesión
                $stmt = $this->conn->prepare("
                    INSERT INTO user_sessions (
                        session_id, user_id, username, ip_address, user_agent,
                        created_at, last_activity, entry_page, device_type,
                        browser, os, country, city, is_mobile, referrer_url,
                        screen_resolution
                    ) VALUES (
                        ?, ?, ?, ?, ?, 
                        NOW(), NOW(), ?, ?,
                        ?, ?, ?, ?, ?, ?,
                        ?
                    )
                ");
                
                $ip_address = $_SERVER['REMOTE_ADDR'];
                $user_agent = $_SERVER['HTTP_USER_AGENT'];
                $entry_page = $_SERVER['REQUEST_URI'];
                $referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;
                
                $stmt->bind_param("sississssssiss",
                    $this->session_id,
                    $user_id,
                    $username,
                    $ip_address,
                    $user_agent,
                    $entry_page,
                    $browser_info['device_type'],
                    $browser_info['browser'],
                    $browser_info['os'],
                    $geo_info['country'],
                    $geo_info['city'],
                    $browser_info['is_mobile'],
                    $referrer,
                    $screen_resolution
                );
                
                $stmt->execute();
                error_log("Nueva sesión registrada: " . $this->session_id . " para usuario: " . $username);
            }
            
            // Registrar la vista de página actual
            $this->trackPageView();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error al registrar sesión: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Registra una nueva vista de página
     */
    public function trackPageView() {
        if (empty($this->session_id)) {
            return false;
        }
        
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO session_page_views (
                    session_id, page_url, timestamp
                ) VALUES (
                    ?, ?, NOW()
                )
            ");
            
            $stmt->bind_param("ss",
                $this->session_id,
                $this->current_url
            );
            
            $stmt->execute();
            $this->page_entry_time = microtime(true);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error al registrar vista de página: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Actualiza el tiempo pasado en la página anterior
     */
    public function updateTimeSpent($previous_url) {
        if (empty($this->session_id)) {
            return false;
        }
        
        $time_spent = round(microtime(true) - $this->page_entry_time);
        
        try {
            $stmt = $this->conn->prepare("
                UPDATE session_page_views 
                SET time_spent = ?
                WHERE session_id = ? 
                AND page_url = ?
                ORDER BY timestamp DESC
                LIMIT 1
            ");
            
            $stmt->bind_param("iss",
                $time_spent,
                $this->session_id,
                $previous_url
            );
            
            $stmt->execute();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error al actualizar tiempo en página: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Registra el cierre de sesión
     */
    public function logoutSession() {
        if (empty($this->session_id)) {
            return false;
        }
        
        try {
            $stmt = $this->conn->prepare("
                UPDATE user_sessions 
                SET logged_out_at = NOW(),
                    is_active = FALSE,
                    exit_page = ?
                WHERE session_id = ?
            ");
            
            $stmt->bind_param("ss",
                $_SERVER['REQUEST_URI'],
                $this->session_id
            );
            
            $stmt->execute();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error al registrar cierre de sesión: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtiene estadísticas de sesiones para un usuario específico
     */
    public function getUserSessionStats($user_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(*) as total_sessions,
                    SUM(pages_visited) as total_pages,
                    AVG(pages_visited) as avg_pages_per_session,
                    MAX(pages_visited) as max_pages_in_session,
                    AVG(TIMESTAMPDIFF(SECOND, created_at, IFNULL(logged_out_at, last_activity))) as avg_session_duration
                FROM user_sessions
                WHERE user_id = ?
            ");
            
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            
            return $stmt->get_result()->fetch_assoc();
            
        } catch (Exception $e) {
            error_log("Error al obtener estadísticas: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obtiene las estadísticas generales del sitio
     */
    public function getSiteStats($days = 30) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    COUNT(DISTINCT user_id) as total_users,
                    COUNT(*) as total_sessions,
                    SUM(pages_visited) as total_pageviews,
                    AVG(pages_visited) as avg_pages_per_session,
                    COUNT(DISTINCT DATE(created_at)) as active_days,
                    SUM(CASE WHEN device_type = 'Mobile' THEN 1 ELSE 0 END) / COUNT(*) * 100 as mobile_percentage,
                    SUM(CASE WHEN device_type = 'Tablet' THEN 1 ELSE 0 END) / COUNT(*) * 100 as tablet_percentage,
                    SUM(CASE WHEN device_type = 'Desktop' THEN 1 ELSE 0 END) / COUNT(*) * 100 as desktop_percentage
                FROM user_sessions
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            $stmt->bind_param("i", $days);
            $stmt->execute();
            
            return $stmt->get_result()->fetch_assoc();
            
        } catch (Exception $e) {
            error_log("Error al obtener estadísticas: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obtiene las páginas más visitadas
     */
    public function getMostVisitedPages($limit = 10) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    page_url,
                    COUNT(*) as visit_count,
                    AVG(time_spent) as avg_time_spent
                FROM session_page_views
                GROUP BY page_url
                ORDER BY visit_count DESC
                LIMIT ?
            ");
            
            $stmt->bind_param("i", $limit);
            $stmt->execute();
            
            return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error al obtener páginas más visitadas: " . $e->getMessage());
            return [];
        }
    }
}