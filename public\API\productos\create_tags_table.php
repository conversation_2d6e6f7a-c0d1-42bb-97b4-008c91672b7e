<?php
// Script para crear la tabla tb_productos_tags si no existe
header('Content-Type: text/html; charset=utf-8');

// Cargar configuración
require_once '../../config/config.php';

try {
    // Verificar si la tabla ya existe
    $checkTable = $conn->query("SHOW TABLES LIKE 'tb_productos_tags'");
    
    if ($checkTable->num_rows > 0) {
        echo "<p>La tabla tb_productos_tags ya existe.</p>";
    } else {
        // Crear la tabla tb_productos_tags
        $sql = "CREATE TABLE tb_productos_tags (
            id INT AUTO_INCREMENT PRIMARY KEY,
            producto_id INT NOT NULL,
            tag_id INT NOT NULL,
            FOREIGN KEY (producto_id) REFERENCES tb_productos(id) ON DELETE CASCADE,
            UNIQUE KEY unique_producto_tag (producto_id, tag_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p>Tabla tb_productos_tags creada correctamente.</p>";
            
            // Verificar si la tabla tb_tags existe
            $checkTagsTable = $conn->query("SHOW TABLES LIKE 'tb_tags'");
            
            if ($checkTagsTable->num_rows == 0) {
                // Crear la tabla tb_tags si no existe
                $sqlTags = "CREATE TABLE tb_tags (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    nombre VARCHAR(50) NOT NULL,
                    UNIQUE KEY unique_tag_name (nombre)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                
                if ($conn->query($sqlTags) === TRUE) {
                    echo "<p>Tabla tb_tags creada correctamente.</p>";
                } else {
                    echo "<p>Error al crear la tabla tb_tags: " . $conn->error . "</p>";
                }
            } else {
                echo "<p>La tabla tb_tags ya existe.</p>";
            }
        } else {
            echo "<p>Error al crear la tabla tb_productos_tags: " . $conn->error . "</p>";
        }
    }
    
    echo "<p><a href='../test_product_api.html'>Volver a la página de prueba</a></p>";
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Cerrar conexión
$conn->close();
?>
