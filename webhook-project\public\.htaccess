# Configuración general
Options -Indexes
DirectoryIndex index_webhook.php

# Habilitar el motor de reescritura
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redireccionar index.php a index_webhook.php
    RewriteCond %{REQUEST_URI} ^/index\.php$ [NC]
    RewriteRule ^(.*)$ index_webhook.php [L,R=301]
    
    # Configuración para endpoint webhook
    RewriteRule ^webhook$ webhook.php [L]
</IfModule>

# Configuración de seguridad
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
</IfModule>

# Configuración PHP
<IfModule php_module>
    php_flag display_errors Off
    php_value max_execution_time 120
    php_value max_input_time 120
    php_value post_max_size 10M
</IfModule>
