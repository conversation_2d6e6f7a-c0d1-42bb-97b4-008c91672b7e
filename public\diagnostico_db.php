<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Función para mostrar mensajes
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 10px 0; border-radius: 5px; ' . 
         ($isError ? 'background-color: #ffebee; color: #c62828;' : 'background-color: #e8f5e9; color: #2e7d32;') . 
         '">' . $message . '</div>';
}

// Función para mostrar variables
function showVar($name, $value) {
    echo '<div style="padding: 5px; margin: 5px 0; border-radius: 3px; background-color: #f5f5f5;">';
    echo '<strong>' . $name . ':</strong> ';
    
    if (is_null($value)) {
        echo '<span style="color: #9e9e9e;">NULL</span>';
    } elseif (is_bool($value)) {
        echo '<span style="color: #2196f3;">' . ($value ? 'TRUE' : 'FALSE') . '</span>';
    } elseif (is_array($value) || is_object($value)) {
        echo '<pre>' . print_r($value, true) . '</pre>';
    } else {
        echo '<span>' . $value . '</span>';
    }
    
    echo '</div>';
}

// Mostrar información del servidor y PHP
echo '<h2>Información del servidor y PHP</h2>';
showVar('PHP Version', phpversion());
showVar('Server Software', $_SERVER['SERVER_SOFTWARE'] ?? 'N/A');
showVar('Server Name', $_SERVER['SERVER_NAME'] ?? 'N/A');
showVar('Document Root', $_SERVER['DOCUMENT_ROOT'] ?? 'N/A');
showVar('Script Filename', $_SERVER['SCRIPT_FILENAME'] ?? 'N/A');

// Mostrar información de la configuración de la base de datos
echo '<h2>Configuración de la base de datos</h2>';
showVar('db_host', $db_host ?? 'No definido');
showVar('db_user', $db_user ?? 'No definido');
showVar('db_name', $db_name ?? 'No definido');
showVar('conn (global)', isset($conn) ? 'Definido' : 'No definido');

if (isset($conn)) {
    showVar('conn instanceof mysqli', $conn instanceof mysqli);
    showVar('conn->connect_error', $conn->connect_error ?? 'No hay error');
    showVar('conn->error', $conn->error ?? 'No hay error');
}

// Probar conexión a la base de datos
echo '<h2>Prueba de conexión a la base de datos</h2>';

try {
    // Registrar inicio de la prueba
    logInfo("Iniciando diagnóstico de conexión a la base de datos");
    
    // Obtener conexión a la base de datos
    global $conn;
    
    // Si ya existe una conexión global, usarla
    if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
        logInfo("Usando conexión global existente");
        showMessage("Usando conexión global existente");
    } else {
        // Si no existe, obtener una nueva
        $conn = getDbConnection();
        
        if (!$conn) {
            logError("No se pudo obtener conexión a la base de datos");
            showMessage("No se pudo obtener conexión a la base de datos", true);
            exit;
        }
        
        logInfo("Nueva conexión obtenida");
        showMessage("Nueva conexión obtenida");
    }
    
    // Verificar si la tabla tb_registros existe
    $sql = "SHOW TABLES LIKE 'tb_registros'";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 0) {
        logError("La tabla tb_registros no existe");
        showMessage("Error: La tabla tb_registros no existe.", true);
        exit;
    }
    
    logInfo("La tabla tb_registros existe");
    showMessage("La tabla tb_registros existe.");
    
    // Verificar si la columna planSuscripcion existe en la tabla tb_registros
    $sql = "SHOW COLUMNS FROM tb_registros LIKE 'planSuscripcion'";
    $result = $conn->query($sql);
    
    if ($result->num_rows == 0) {
        logError("La columna planSuscripcion no existe en la tabla tb_registros");
        showMessage("Error: La columna planSuscripcion no existe en la tabla tb_registros.", true);
        exit;
    }
    
    logInfo("La columna planSuscripcion existe en la tabla tb_registros");
    showMessage("La columna planSuscripcion existe en la tabla tb_registros.");
    
    // Mostrar la estructura de la tabla tb_registros
    $sql = "DESCRIBE tb_registros";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo '<h3>Estructura de la tabla tb_registros</h3>';
        echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">';
        echo '<tr style="background-color: #f5f5f5;"><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>';
        
        while($row = $result->fetch_assoc()) {
            echo '<tr>';
            echo '<td>' . $row["Field"] . '</td>';
            echo '<td>' . $row["Type"] . '</td>';
            echo '<td>' . $row["Null"] . '</td>';
            echo '<td>' . $row["Key"] . '</td>';
            echo '<td>' . $row["Default"] . '</td>';
            echo '<td>' . $row["Extra"] . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
        
        logInfo("Se mostró la estructura de la tabla tb_registros");
    } else {
        logError("No se pudo obtener la estructura de la tabla tb_registros");
        showMessage("No se pudo obtener la estructura de la tabla tb_registros.", true);
    }
    
    // Probar una inserción simple
    echo '<h3>Prueba de inserción</h3>';
    
    // Crear datos de prueba
    $test_data = [
        'nombres' => 'Test Diagnostico',
        'apellidos' => 'Usuario',
        'rut' => '12345678-9',
        'fechaNacimiento' => '01/01/1990',
        'sexo' => 'Masculino',
        'telefono' => '1234567890',
        'region' => 'Test Region',
        'comuna' => 'Test Comuna',
        'direccion' => 'Test Direccion 123',
        'NombreUsuario' => 'test_diag_' . time(),
        'mail' => 'test_diag_' . time() . '@example.com',
        'mailRespaldo' => null,
        'contraseña' => password_hash('test123', PASSWORD_DEFAULT),
        'localFisico' => 'Si',
        'nombreNegocio' => 'Test Negocio',
        'telefonoNegocio' => '1234567890',
        'whatsappNegocio' => '1234567890',
        'tipoNegocio' => 'Venta',
        'descripcionNegocio' => 'Test Descripcion',
        'planSuscripcion' => 'Gratuita'
    ];
    
    // Preparar la consulta SQL
    $sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion, 
                                     NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                     nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                     planSuscripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    logInfo("Preparando consulta SQL para inserción de prueba", ['sql' => $sql]);
    
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        logError("Error al preparar la consulta", ['error' => $conn->error]);
        showMessage('Error al preparar la consulta: ' . $conn->error, true);
        exit;
    }
    
    logInfo("Consulta preparada correctamente");
    
    $stmt->bind_param("ssssssssssssssssssss", 
        $test_data['nombres'], $test_data['apellidos'], $test_data['rut'], 
        $test_data['fechaNacimiento'], $test_data['sexo'], $test_data['telefono'], 
        $test_data['region'], $test_data['comuna'], $test_data['direccion'],
        $test_data['NombreUsuario'], $test_data['mail'], $test_data['mailRespaldo'], $test_data['contraseña'], $test_data['localFisico'],
        $test_data['nombreNegocio'], $test_data['telefonoNegocio'], $test_data['whatsappNegocio'], 
        $test_data['tipoNegocio'], $test_data['descripcionNegocio'],
        $test_data['planSuscripcion']);
    
    logInfo("Parámetros vinculados correctamente");
    
    if ($stmt->execute()) {
        logInfo("Inserción exitosa", ['insert_id' => $conn->insert_id]);
        showMessage('Inserción exitosa. ID: ' . $conn->insert_id);
    } else {
        logError("Error al ejecutar la consulta", ['error' => $stmt->error]);
        showMessage('Error al ejecutar la consulta: ' . $stmt->error, true);
    }
    
    // Mostrar los últimos registros
    echo '<h3>Últimos registros en la tabla</h3>';
    
    $sql = "SELECT * FROM tb_registros ORDER BY id DESC LIMIT 5";
    $result = $conn->query($sql);
    
    if ($result->num_rows > 0) {
        echo '<table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">';
        echo '<tr style="background-color: #f5f5f5;"><th>ID</th><th>Nombres</th><th>Apellidos</th><th>Email</th><th>Negocio</th><th>Plan</th><th>Fecha</th></tr>';
        
        while($row = $result->fetch_assoc()) {
            echo '<tr>';
            echo '<td>' . $row["id"] . '</td>';
            echo '<td>' . $row["nombres"] . '</td>';
            echo '<td>' . $row["apellidos"] . '</td>';
            echo '<td>' . $row["mail"] . '</td>';
            echo '<td>' . $row["nombreNegocio"] . '</td>';
            echo '<td>' . $row["planSuscripcion"] . '</td>';
            echo '<td>' . $row["fecha_registro"] . '</td>';
            echo '</tr>';
        }
        
        echo '</table>';
    } else {
        showMessage("No hay registros en la tabla.", true);
    }
    
    // Cerrar conexión
    $stmt->close();
    
    logInfo("Diagnóstico de conexión a la base de datos finalizado");
    
} catch (Exception $e) {
    logException($e, ['context' => 'diagnostico_db']);
    showMessage('Error: ' . $e->getMessage(), true);
}

// Mostrar los últimos logs
echo '<h2>Últimos logs</h2>';

$log_directory = __DIR__ . '/../logs';
$log_file = $log_directory . '/app_' . date('Y-m-d') . '.log';

if (file_exists($log_file)) {
    $logs = file($log_file);
    $logs = array_slice($logs, -50); // Mostrar solo las últimas 50 líneas
    
    echo '<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 400px; overflow: auto;">';
    foreach ($logs as $log) {
        if (strpos($log, '[ERROR]') !== false) {
            echo '<span style="color: #c62828;">' . htmlspecialchars($log) . '</span>';
        } elseif (strpos($log, '[WARNING]') !== false) {
            echo '<span style="color: #ff8f00;">' . htmlspecialchars($log) . '</span>';
        } else {
            echo htmlspecialchars($log);
        }
    }
    echo '</pre>';
} else {
    showMessage("No se encontró el archivo de logs.", true);
}
?>
