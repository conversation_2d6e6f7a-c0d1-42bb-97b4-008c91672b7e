/**
 * Category Manager - Módulo para gestionar la carga de tipos de categoría, categorías y subcategorías
 *
 * Este módulo proporciona una solución transversal para manejar la jerarquía de categorías
 * en tres niveles (tipo de categoría → categoría → subcategoría) tanto en el formulario
 * de creación como en el de edición de productos.
 */

// Clase principal para gestionar categorías
class CategoryManager {
    /**
     * Constructor
     * @param {Object} config - Configuración del gestor de categorías
     * @param {string} config.tipoSelectId - ID del select de tipo de categoría
     * @param {string} config.categoriaSelectId - ID del select de categoría
     * @param {string} config.subcategoriaSelectId - ID del select de subcategoría
     * @param {string} config.baseUrl - URL base para las peticiones API (opcional)
     * @param {Function} config.onError - Función para manejar errores (opcional)
     */
    constructor(config) {
        // Configuración por defecto
        this.config = {
            tipoSelectId: 'productTipoCategoria',
            categoriaSelectId: 'productCategory',
            subcategoriaSelectId: 'productSubcategory',
            baseUrl: '../public/API/productos',
            onError: (message) => {
                console.error(message);
                if (typeof showNotification === 'function') {
                    showNotification(message, 'error');
                }
            },
            ...config
        };

        // Referencias a los elementos select
        this.tipoSelect = document.getElementById(this.config.tipoSelectId);
        this.categoriaSelect = document.getElementById(this.config.categoriaSelectId);
        this.subcategoriaSelect = document.getElementById(this.config.subcategoriaSelectId);

        // Validar que los elementos existen
        this.validateElements();

        // Inicializar event listeners
        this.initEventListeners();

        console.log('CategoryManager inicializado con configuración:', this.config);
    }

    /**
     * Validar que los elementos select existen
     */
    validateElements() {
        if (!this.tipoSelect && this.config.tipoSelectId) {
            console.warn(`No se encontró el elemento select de tipo de categoría con ID: ${this.config.tipoSelectId}`);
        }

        if (!this.categoriaSelect) {
            throw new Error(`No se encontró el elemento select de categoría con ID: ${this.config.categoriaSelectId}`);
        }

        if (!this.subcategoriaSelect) {
            throw new Error(`No se encontró el elemento select de subcategoría con ID: ${this.config.subcategoriaSelectId}`);
        }
    }

    /**
     * Inicializar event listeners para los selects
     */
    initEventListeners() {
        // Event listener para el select de tipo de categoría
        if (this.tipoSelect) {
            this.tipoSelect.addEventListener('change', () => {
                const tipoId = this.tipoSelect.value;
                console.log('Tipo de categoría seleccionado:', tipoId);
                this.cargarCategoriasPorTipo(tipoId);
            });
        }

        // Event listener para el select de categoría
        this.categoriaSelect.addEventListener('change', () => {
            const categoriaId = this.categoriaSelect.value;
            console.log('Categoría seleccionada:', categoriaId);
            this.cargarSubcategoriasPorCategoria(categoriaId);
        });

        console.log('Event listeners inicializados');
    }

    /**
     * Inicializar el gestor de categorías
     * @param {Object} initialValues - Valores iniciales (opcional)
     * @param {string} initialValues.tipoId - ID del tipo de categoría seleccionado
     * @param {string} initialValues.categoriaId - ID de la categoría seleccionada
     * @param {string} initialValues.subcategoriaId - ID de la subcategoría seleccionada
     */
    async init(initialValues = {}) {
        console.log('Inicializando CategoryManager con valores:', initialValues);

        try {
            // Cargar tipos de categoría si existe el select
            if (this.tipoSelect) {
                console.log('Cargando tipos de categoría...');
                await this.cargarTiposCategorias();

                // Esperar un momento para asegurar que los datos se han cargado
                await new Promise(resolve => setTimeout(resolve, 100));

                // Si hay un tipo seleccionado, lo establecemos
                if (initialValues.tipoId) {
                    console.log('Estableciendo tipo de categoría:', initialValues.tipoId);
                    this.tipoSelect.value = initialValues.tipoId;
                    await this.cargarCategoriasPorTipo(initialValues.tipoId);
                } else {
                    // Seleccionar el primer tipo disponible
                    const primerTipo = this.tipoSelect.querySelector('option:not([value=""])');
                    if (primerTipo) {
                        console.log('Seleccionando primer tipo de categoría:', primerTipo.value);
                        this.tipoSelect.value = primerTipo.value;
                        await this.cargarCategoriasPorTipo(primerTipo.value);
                    } else {
                        console.warn('No se encontraron tipos de categoría disponibles');
                        // Cargar todas las categorías como respaldo
                        await this.cargarTodasCategorias();
                    }
                }
            } else {
                // Si no hay select de tipo, cargar todas las categorías
                console.log('No hay select de tipo de categoría, cargando todas las categorías...');
                await this.cargarTodasCategorias();
            }

            // Esperar un momento para asegurar que las categorías se han cargado
            await new Promise(resolve => setTimeout(resolve, 100));

            // Establecer categoría seleccionada
            if (initialValues.categoriaId) {
                console.log('Estableciendo categoría:', initialValues.categoriaId);
                this.categoriaSelect.value = initialValues.categoriaId;
                await this.cargarSubcategoriasPorCategoria(initialValues.categoriaId);
            } else {
                // Seleccionar la primera categoría disponible
                const primeraCategoria = this.categoriaSelect.querySelector('option:not([value=""])');
                if (primeraCategoria) {
                    console.log('Seleccionando primera categoría:', primeraCategoria.value);
                    this.categoriaSelect.value = primeraCategoria.value;
                    await this.cargarSubcategoriasPorCategoria(primeraCategoria.value);
                } else {
                    console.warn('No se encontraron categorías disponibles');
                }
            }

            // Esperar un momento para asegurar que las subcategorías se han cargado
            await new Promise(resolve => setTimeout(resolve, 100));

            // Establecer subcategoría seleccionada
            if (initialValues.subcategoriaId) {
                console.log('Estableciendo subcategoría:', initialValues.subcategoriaId);
                // Verificar si la opción existe
                if (this.subcategoriaSelect.querySelector(`option[value="${initialValues.subcategoriaId}"]`)) {
                    this.subcategoriaSelect.value = initialValues.subcategoriaId;
                } else {
                    console.warn(`La subcategoría ${initialValues.subcategoriaId} no está disponible`);
                }
            }

            console.log('CategoryManager inicializado correctamente');
        } catch (error) {
            console.error('Error al inicializar CategoryManager:', error);
            this.handleError('Error al inicializar las categorías: ' + error.message);
        }
    }

    /**
     * Cargar tipos de categoría
     */
    async cargarTiposCategorias() {
        if (!this.tipoSelect) return [];

        try {
            console.log('Cargando tipos de categorías...');
            this.tipoSelect.disabled = true;
            this.tipoSelect.innerHTML = '<option value="">Cargando tipos...</option>';

            const url = `${this.config.baseUrl}/get_tipos_categoria.php`;
            console.log('URL de solicitud:', url);

            // Intentar cargar los tipos de categoría con reintentos
            let response;
            let intentos = 0;
            const maxIntentos = 3;

            while (intentos < maxIntentos) {
                try {
                    response = await fetch(url, {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });

                    if (response.ok) break;

                    console.warn(`Intento ${intentos + 1}/${maxIntentos} fallido: ${response.status}`);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Esperar 500ms antes de reintentar
                    intentos++;
                } catch (fetchError) {
                    console.error('Error en fetch:', fetchError);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    intentos++;
                }
            }

            if (!response || !response.ok) {
                console.error(`Error al cargar tipos de categoría después de ${maxIntentos} intentos`);

                // Cargar datos de respaldo (hardcoded)
                const tiposRespaldo = [
                    { id: 1, nombre: 'Comida' },
                    { id: 2, nombre: 'Bebida' },
                    { id: 3, nombre: 'Servicios' }
                ];

                // Limpiar opciones existentes
                this.tipoSelect.innerHTML = '<option value="">Seleccionar tipo de categoría</option>';
                this.tipoSelect.disabled = false;

                // Añadir opciones de respaldo
                tiposRespaldo.forEach(tipo => {
                    const option = document.createElement('option');
                    option.value = tipo.id;
                    option.textContent = tipo.nombre;
                    this.tipoSelect.appendChild(option);
                });

                console.log('Usando datos de respaldo para tipos de categoría');
                return tiposRespaldo;
            }

            const responseText = await response.text();
            let data;

            try {
                data = JSON.parse(responseText);
                console.log('Datos de tipos de categoría recibidos:', data);
            } catch (jsonError) {
                console.error('Error al parsear JSON:', jsonError, responseText);
                throw new Error('Respuesta del servidor inválida');
            }

            // Verificar que la respuesta tenga la estructura esperada
            if (!data.success || !data.tipos || !Array.isArray(data.tipos)) {
                throw new Error('Formato de respuesta inválido para tipos de categoría');
            }

            // Limpiar opciones existentes
            this.tipoSelect.innerHTML = '<option value="">Seleccionar tipo de categoría</option>';
            this.tipoSelect.disabled = false;

            // Añadir nuevas opciones
            data.tipos.forEach(tipo => {
                const option = document.createElement('option');
                option.value = tipo.id;
                option.textContent = tipo.nombre;
                this.tipoSelect.appendChild(option);
            });

            console.log(`${data.tipos.length} tipos de categoría cargados correctamente`);
            return data.tipos;

        } catch (error) {
            console.error('Error al cargar tipos de categoría:', error);
            this.tipoSelect.innerHTML = '<option value="">Error al cargar tipos</option>';
            this.tipoSelect.disabled = false;
            this.handleError('Error al cargar tipos de categoría: ' + error.message);
            return [];
        }
    }

    /**
     * Cargar todas las categorías (sin filtrar por tipo)
     */
    async cargarTodasCategorias() {
        try {
            console.log('Cargando todas las categorías...');
            this.categoriaSelect.disabled = true;
            this.categoriaSelect.innerHTML = '<option value="">Cargando categorías...</option>';

            const url = `${this.config.baseUrl}/get_categorias.php`;
            console.log('URL de solicitud:', url);

            const response = await fetch(url, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`Error al cargar categorías: ${response.status}`);
            }

            const data = await response.json();
            console.log('Datos de categorías recibidos:', data);

            // Limpiar opciones existentes
            this.categoriaSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
            this.categoriaSelect.disabled = false;

            // Añadir nuevas opciones (manejar diferentes formatos de respuesta)
            if (data.success && Array.isArray(data.categorias)) {
                data.categorias.forEach(categoria => {
                    const option = document.createElement('option');
                    option.value = categoria.id;
                    option.textContent = categoria.nombre;
                    this.categoriaSelect.appendChild(option);
                });
                console.log(`${data.categorias.length} categorías cargadas correctamente`);
                return data.categorias;
            } else if (Array.isArray(data)) {
                data.forEach(categoria => {
                    const option = document.createElement('option');
                    option.value = categoria.id;
                    option.textContent = categoria.nombre;
                    this.categoriaSelect.appendChild(option);
                });
                console.log(`${data.length} categorías cargadas correctamente`);
                return data;
            } else {
                throw new Error('Formato de respuesta inválido para categorías');
            }

        } catch (error) {
            console.error('Error al cargar categorías:', error);
            this.categoriaSelect.innerHTML = '<option value="">Error al cargar categorías</option>';
            this.categoriaSelect.disabled = false;
            this.handleError('Error al cargar categorías: ' + error.message);
            return [];
        }
    }

    /**
     * Cargar categorías según el tipo seleccionado
     * @param {string} tipoId - ID del tipo de categoría
     */
    async cargarCategoriasPorTipo(tipoId) {
        try {
            console.log(`Cargando categorías para tipo ID: ${tipoId}`);

            // Limpiar y deshabilitar los selects mientras se cargan los datos
            this.categoriaSelect.innerHTML = '<option value="">Cargando categorías...</option>';
            this.categoriaSelect.disabled = true;
            this.subcategoriaSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            this.subcategoriaSelect.disabled = true;

            if (!tipoId) {
                console.log('No se seleccionó ningún tipo de categoría');
                this.categoriaSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
                this.categoriaSelect.disabled = false;
                return [];
            }

            const url = `${this.config.baseUrl}/get_categorias_by_tipo.php?tipo_id=${encodeURIComponent(tipoId)}`;
            console.log('URL de solicitud:', url);

            // Intentar cargar las categorías con reintentos
            let response;
            let intentos = 0;
            const maxIntentos = 3;

            while (intentos < maxIntentos) {
                try {
                    response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        },
                        credentials: 'include'
                    });

                    if (response.ok) break;

                    console.warn(`Intento ${intentos + 1}/${maxIntentos} fallido: ${response.status}`);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Esperar 500ms antes de reintentar
                    intentos++;
                } catch (fetchError) {
                    console.error('Error en fetch:', fetchError);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    intentos++;
                }
            }

            if (!response || !response.ok) {
                console.error(`Error al cargar categorías después de ${maxIntentos} intentos`);

                // Cargar datos de respaldo (hardcoded)
                const categoriasRespaldo = [
                    { id: 1, nombre: 'Pizzas' },
                    { id: 2, nombre: 'Hamburguesas' },
                    { id: 3, nombre: 'Sushi' }
                ];

                // Limpiar opciones existentes
                this.categoriaSelect.innerHTML = '<option value="">Seleccionar categoría</option>';
                this.categoriaSelect.disabled = false;

                // Añadir opciones de respaldo
                categoriasRespaldo.forEach(categoria => {
                    const option = document.createElement('option');
                    option.value = categoria.id;
                    option.textContent = categoria.nombre;
                    this.categoriaSelect.appendChild(option);
                });

                console.log('Usando datos de respaldo para categorías');
                return categoriasRespaldo;
            }

            const data = await response.json();
            console.log('Datos de categorías recibidos:', data);

            // Habilitar el select y limpiar opciones existentes
            this.categoriaSelect.disabled = false;
            this.categoriaSelect.innerHTML = '<option value="">Seleccionar categoría</option>';

            // Verificar que data.categorias existe y es un array
            if (data.success && Array.isArray(data.categorias)) {
                data.categorias.forEach(categoria => {
                    const option = document.createElement('option');
                    option.value = categoria.id;
                    option.textContent = categoria.nombre;
                    this.categoriaSelect.appendChild(option);
                });
                console.log(`${data.categorias.length} categorías cargadas correctamente`);
                return data.categorias;
            } else {
                console.log('No se encontraron categorías para este tipo');
                return [];
            }

        } catch (error) {
            console.error('Error al cargar categorías por tipo:', error);
            this.categoriaSelect.disabled = false;
            this.categoriaSelect.innerHTML = '<option value="">Error al cargar categorías</option>';
            this.handleError('Error al cargar categorías: ' + error.message);
            return [];
        }
    }

    /**
     * Cargar subcategorías según la categoría seleccionada
     * @param {string} categoriaId - ID de la categoría
     */
    async cargarSubcategoriasPorCategoria(categoriaId) {
        try {
            console.log(`Cargando subcategorías para categoría ID: ${categoriaId}`);

            // Deshabilitar el select mientras carga
            this.subcategoriaSelect.disabled = true;
            this.subcategoriaSelect.innerHTML = '<option value="">Cargando subcategorías...</option>';

            if (!categoriaId) {
                this.subcategoriaSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
                this.subcategoriaSelect.disabled = true;
                return [];
            }

            const url = `${this.config.baseUrl}/get_subcategorias.php?categoria_id=${encodeURIComponent(categoriaId)}`;
            console.log('URL de solicitud:', url);

            // Intentar cargar las subcategorías con reintentos
            let response;
            let intentos = 0;
            const maxIntentos = 3;

            while (intentos < maxIntentos) {
                try {
                    response = await fetch(url, {
                        method: 'GET',
                        credentials: 'include',
                        headers: {
                            'Accept': 'application/json',
                            'Cache-Control': 'no-cache'
                        }
                    });

                    if (response.ok) break;

                    console.warn(`Intento ${intentos + 1}/${maxIntentos} fallido: ${response.status}`);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Esperar 500ms antes de reintentar
                    intentos++;
                } catch (fetchError) {
                    console.error('Error en fetch:', fetchError);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    intentos++;
                }
            }

            if (!response || !response.ok) {
                console.error(`Error al cargar subcategorías después de ${maxIntentos} intentos`);

                // Cargar datos de respaldo (hardcoded)
                const subcategoriasRespaldo = [
                    { id: 1, nombre: 'Pizzas Tradicionales' },
                    { id: 2, nombre: 'Pizzas Gourmet' },
                    { id: 3, nombre: 'Pizzas Vegetarianas' }
                ];

                // Limpiar opciones existentes
                this.subcategoriaSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
                this.subcategoriaSelect.disabled = false;

                // Añadir opciones de respaldo
                subcategoriasRespaldo.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre;
                    this.subcategoriaSelect.appendChild(option);
                });

                console.log('Usando datos de respaldo para subcategorías');
                return subcategoriasRespaldo;
            }

            const responseText = await response.text();
            console.log('Respuesta del servidor (raw):', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
                console.log('Datos parseados:', data);
            } catch (e) {
                console.error('Error al parsear JSON:', e);
                throw new Error('Respuesta del servidor inválida');
            }

            // Limpiar opciones existentes
            this.subcategoriaSelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';
            this.subcategoriaSelect.disabled = false;

            // Verificar la estructura de la respuesta y añadir opciones
            if (data.success && Array.isArray(data.subcategorias)) {
                if (data.subcategorias.length === 0) {
                    console.log('No hay subcategorías disponibles para esta categoría');
                    this.subcategoriaSelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                    return [];
                }

                data.subcategorias.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre;
                    this.subcategoriaSelect.appendChild(option);
                });
                console.log(`${data.subcategorias.length} subcategorías cargadas correctamente`);
                return data.subcategorias;
            } else if (Array.isArray(data)) {
                if (data.length === 0) {
                    console.log('No hay subcategorías disponibles para esta categoría (formato array)');
                    this.subcategoriaSelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                    return [];
                }

                data.forEach(subcategoria => {
                    const option = document.createElement('option');
                    option.value = subcategoria.id;
                    option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                    this.subcategoriaSelect.appendChild(option);
                });
                console.log(`${data.length} subcategorías cargadas correctamente (formato array)`);
                return data;
            } else {
                console.warn('No se encontraron subcategorías para esta categoría');
                this.subcategoriaSelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                return [];
            }

        } catch (error) {
            console.error('Error al cargar subcategorías:', error);
            this.subcategoriaSelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
            this.subcategoriaSelect.disabled = false;

            // Mostrar mensaje de error en consola pero no al usuario
            console.warn('Error al cargar subcategorías: ' + error.message);

            // No mostrar error al usuario, simplemente dejar el select vacío
            // this.handleError('Error al cargar subcategorías: ' + error.message);

            return [];
        }
    }

    /**
     * Manejar errores
     * @param {string} message - Mensaje de error
     */
    handleError(message) {
        if (this.config.onError && typeof this.config.onError === 'function') {
            this.config.onError(message);
        } else {
            console.error(message);
        }
    }

    /**
     * Obtener los valores seleccionados
     * @returns {Object} Valores seleccionados
     */
    getSelectedValues() {
        return {
            tipoId: this.tipoSelect ? this.tipoSelect.value : null,
            categoriaId: this.categoriaSelect.value,
            subcategoriaId: this.subcategoriaSelect.value
        };
    }

    /**
     * Establecer valores seleccionados
     * @param {Object} values - Valores a establecer
     */
    async setValues(values) {
        try {
            console.log('Estableciendo valores:', values);

            // Si hay un tipo seleccionado y tenemos el select de tipo
            if (values.tipoId && this.tipoSelect) {
                this.tipoSelect.value = values.tipoId;
                await this.cargarCategoriasPorTipo(values.tipoId);
            }

            // Si hay una categoría seleccionada
            if (values.categoriaId) {
                this.categoriaSelect.value = values.categoriaId;
                await this.cargarSubcategoriasPorCategoria(values.categoriaId);
            }

            // Si hay una subcategoría seleccionada
            if (values.subcategoriaId) {
                this.subcategoriaSelect.value = values.subcategoriaId;
            }

            console.log('Valores establecidos correctamente');
        } catch (error) {
            console.error('Error al establecer valores:', error);
            this.handleError('Error al establecer valores: ' + error.message);
        }
    }

    /**
     * Resetear los selects
     */
    reset() {
        console.log('Reseteando selects de categorías');

        if (this.tipoSelect) {
            this.tipoSelect.value = '';
        }

        this.categoriaSelect.value = '';
        this.categoriaSelect.disabled = this.tipoSelect ? true : false;

        this.subcategoriaSelect.value = '';
        this.subcategoriaSelect.disabled = true;
        this.subcategoriaSelect.innerHTML = '<option value="">Seleccione una categoría primero</option>';
    }
}

// Exportar la clase para uso en módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CategoryManager };
}

// También hacer disponible globalmente
window.CategoryManager = CategoryManager;
