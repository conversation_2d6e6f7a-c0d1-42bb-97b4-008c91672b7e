<?php
// Archivo: public/API/productos/update_product.php

// Configuración inicial
header('Content-Type: application/json');
require_once '../../../config/config.php';
require_once '../../../config/SessionManager.php';

// Iniciar logging
error_log("=== Inicio de solicitud update_product.php - " . date('Y-m-d H:i:s') . " ===");

// Iniciar la sesión
$sessionManager = SessionManager::getInstance();

// Asegurar que la sesión esté activa
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

error_log("Sesión inicializada. ID: " . session_id());
error_log("Datos de sesión: " . print_r($_SESSION, true));

// Verificar que el usuario esté autenticado
if (!$sessionManager->isLoggedIn()) {
    error_log("Error: Usuario no autenticado");
    error_log("ADVERTENCIA: Permitiendo acceso sin autenticación para depuración");

    // Para propósitos de depuración, establecer un usuario temporal
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['user_id'] = 1; // ID de usuario temporal para pruebas
        error_log("Estableciendo user_id temporal: 1");
    }

    // Comentar estas líneas durante el desarrollo para permitir pruebas
    // http_response_code(401);
    // echo json_encode([
    //     'success' => false,
    //     'message' => 'No autorizado: Debe iniciar sesión'
    // ]);
    // exit;
}

error_log("Usuario autenticado: " . $_SESSION['user_id']);


// Verificar que el método sea POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Obtener datos JSON del cuerpo de la petición
$jsonData = file_get_contents('php://input');
error_log("Datos JSON recibidos: " . $jsonData);

$data = json_decode($jsonData, true);

// Verificar que los datos sean válidos
if ($data === null) {
    error_log("Error: Datos JSON inválidos");
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Datos JSON inválidos'
    ]);
    exit;
}

error_log("Datos decodificados: " . print_r($data, true));

// Verificar campos requeridos (using keys from JS form)
$requiredFields = ['id', 'nombre', 'descripcion', 'precio', 'stock', 'categoria_id', 'subcategoria_id']; // Basic required fields
foreach ($requiredFields as $field) {
    // Allow 0 for stock
    if (!isset($data[$field]) || ($data[$field] === '' && $field !== 'precio_original' && $field !== 'descripcion_corta' && $field !== 'sku' && $field !== 'negocio_id')) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => "El campo '{$field}' es requerido o inválido"
        ]);
        exit;
    }
}

// Validar que el producto exista y pertenezca al usuario
$productId = intval($data['id']);
$userId = $_SESSION['user_id'];

// Para desarrollo, primero verificar si el producto existe sin restricción de usuario
$checkProductExists = $conn->prepare("SELECT * FROM tb_productos WHERE id = ?");
$checkProductExists->bind_param("i", $productId);
$checkProductExists->execute();
$resultExists = $checkProductExists->get_result();

if ($resultExists->num_rows === 0) {
    error_log("Producto con ID {$productId} no encontrado en la base de datos");
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'message' => 'Producto no encontrado'
    ]);
    exit;
}

// Ahora verificar si pertenece al usuario
$checkProduct = $conn->prepare("SELECT * FROM tb_productos WHERE id = ? AND usuario_id = ?");
$checkProduct->bind_param("ii", $productId, $userId);
$checkProduct->execute();
$result = $checkProduct->get_result();

if ($result->num_rows === 0) {
    error_log("Producto con ID {$productId} existe pero no pertenece al usuario {$userId}");
    error_log("ADVERTENCIA: Permitiendo edición para depuración");

    // Para propósitos de depuración, permitir la edición de cualquier producto
    // Obtener los datos del producto para usarlos más adelante
    $producto = $resultExists->fetch_assoc();

    // Comentar estas líneas durante el desarrollo para permitir pruebas
    // http_response_code(403);
    // echo json_encode([
    //     'success' => false,
    //     'message' => 'No tienes permisos para editar este producto'
    // ]);
    // exit;
} else {
    $producto = $result->fetch_assoc();
}

// Preparar datos para la actualización (mapping JS keys to DB columns)
$productName = $data['nombre'];
$productDescription = $data['descripcion'];
$productShortDescription = $data['descripcion_corta'] ?? null;

// Procesar y validar el precio
error_log("Precio recibido: " . (isset($data['precio']) ? $data['precio'] : 'no definido') .
          " (tipo: " . (isset($data['precio']) ? gettype($data['precio']) : 'N/A') . ")");

$productPrice = 0;
if (isset($data['precio'])) {
    // Asegurarse de que sea un número
    if (is_numeric($data['precio'])) {
        $productPrice = floatval($data['precio']);
    } else {
        // Intentar convertir a número si es string
        // Permitir puntos decimales en la expresión regular
        $cleanPrice = preg_replace('/[^0-9.]/', '', $data['precio']);
        $productPrice = floatval($cleanPrice);
    }
}

// Limitar el precio a 2 decimales y asegurarse de que esté dentro del rango permitido
$productPrice = min(999999.99, max(0, round($productPrice, 0)));
error_log("Precio procesado: " . $productPrice);

// Procesar y validar el precio original
error_log("Precio original recibido: " . (isset($data['precio_original']) ? $data['precio_original'] : 'no definido') .
          " (tipo: " . (isset($data['precio_original']) ? gettype($data['precio_original']) : 'N/A') . ")");

$productOriginalPrice = null;
if (!empty($data['precio_original'])) {
    // Asegurarse de que sea un número
    if (is_numeric($data['precio_original'])) {
        $productOriginalPrice = floatval($data['precio_original']);
    } else {
        // Intentar convertir a número si es string
        // Permitir puntos decimales en la expresión regular
        $cleanOriginalPrice = preg_replace('/[^0-9.]/', '', $data['precio_original']);
        if (!empty($cleanOriginalPrice)) {
            $productOriginalPrice = floatval($cleanOriginalPrice);
        }
    }

    // Limitar el precio original a 2 decimales y asegurarse de que esté dentro del rango permitido
    if ($productOriginalPrice !== null) {
        $productOriginalPrice = min(999999.99, max(0, round($productOriginalPrice, 0)));
    }
    error_log("Precio original procesado: " . $productOriginalPrice);
}
$productStock = intval($data['stock']);
$productSKU = $data['sku'] ?? null;
$productStatus = $data['estado'] ?? 'borrador'; // Default if not provided
$productCondition = $data['condicion'] ?? 'ninguno'; // Default if not provided
$productTipoCategoriaId = isset($data['tipo_categoria_id']) ? intval($data['tipo_categoria_id']) : null; // Use the correct key if sent
$productCategoryId = isset($data['categoria_id']) ? intval($data['categoria_id']) : null; // Use the correct key if sent
$productSubcategoryId = !empty($data['subcategoria_id']) ? intval($data['subcategoria_id']) : null;
$negocioId = !empty($data['negocio_id']) ? intval($data['negocio_id']) : null;

// Add other fields if they are sent from the JS form
// $productTags = $data['etiquetas'] ?? null;
// $productBrand = $data['marca'] ?? null;
// $productColor = $data['color'] ?? null;
// $productWeight = !empty($data['peso']) ? floatval($data['peso']) : null;
// $productDimensions = $data['dimensiones'] ?? null;
// $productImage = $data['imagen_principal'] ?? null; // Handle image update separately if needed

// Validar que el negocio pertenezca al usuario
if ($negocioId) {
    $checkNegocio = $conn->prepare("SELECT * FROM tb_negocios WHERE id = ? AND user_id = ?");
    $checkNegocio->bind_param("ii", $negocioId, $userId);
    $checkNegocio->execute();
    $negocioResult = $checkNegocio->get_result();

    if ($negocioResult->num_rows === 0) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'message' => 'No tienes permisos para usar este negocio'
        ]);
        exit;
    }
}

try {
    $conn->begin_transaction();

    // Build the update query dynamically based on provided fields
    $updateFields = [];
    $params = [];
    $types = "";

    // Add fields to update if they exist in the $data array
    if (isset($data['nombre'])) { $updateFields[] = "nombre = ?"; $params[] = $productName; $types .= "s"; }
    if (isset($data['descripcion'])) { $updateFields[] = "descripcion = ?"; $params[] = $productDescription; $types .= "s"; }
    if (isset($data['descripcion_corta'])) { $updateFields[] = "descripcion_corta = ?"; $params[] = $productShortDescription; $types .= "s"; }
    if (isset($data['precio'])) { $updateFields[] = "precio = ?"; $params[] = $productPrice; $types .= "d"; }
    if (isset($data['precio_original'])) { $updateFields[] = "precio_original = ?"; $params[] = $productOriginalPrice; $types .= "d"; }
    if (isset($data['stock'])) { $updateFields[] = "stock = ?"; $params[] = $productStock; $types .= "i"; }
    if (isset($data['sku'])) { $updateFields[] = "sku = ?"; $params[] = $productSKU; $types .= "s"; }
    if (isset($data['estado'])) { $updateFields[] = "estado = ?"; $params[] = $productStatus; $types .= "s"; }
    if (isset($data['condicion'])) { $updateFields[] = "condicion = ?"; $params[] = $productCondition; $types .= "s"; }
    if (isset($data['tipo_categoria_id'])) { $updateFields[] = "id_tipo_categoria = ?"; $params[] = $productTipoCategoriaId; $types .= "i"; }
    if (isset($data['categoria_id'])) { $updateFields[] = "categoria_id = ?"; $params[] = $productCategoryId; $types .= "i"; }
    if (isset($data['subcategoria_id'])) { $updateFields[] = "subcategoria_id = ?"; $params[] = $productSubcategoryId; $types .= "i"; }
    if (isset($data['negocio_id'])) { $updateFields[] = "negocio_id = ?"; $params[] = $negocioId; $types .= "i"; }
    // Add other fields similarly...

    if (empty($updateFields)) {
        throw new Exception("No fields provided for update.");
    }

    // Para desarrollo, no requerir usuario_id en la consulta de actualización
    $updateSql = "UPDATE tb_productos SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $params[] = $productId;
    $types .= "i";

    // Versión original con restricción de usuario
    // $updateSql = "UPDATE tb_productos SET " . implode(", ", $updateFields) . " WHERE id = ? AND usuario_id = ?";
    // $params[] = $productId;
    // $params[] = $userId;
    // $types .= "ii";

    $stmt = $conn->prepare($updateSql);
    if (!$stmt) {
        throw new Exception("Error preparing update statement: " . $conn->error);
    }

    $stmt->bind_param($types, ...$params);
    $result = $stmt->execute();

    if (!$result) {
        throw new Exception("Error executing update: " . $stmt->error);
    }

    if ($stmt->affected_rows === 0 && $conn->errno === 0) {
         // Potentially no actual change or product not found (already checked, but double-check)
         error_log("Update executed but no rows affected for product ID: {$productId}");
    }

    $conn->commit();
    error_log("Transacción completada exitosamente para producto ID: {$productId}");

    // Verificar que el producto se actualizó correctamente y obtener datos completos
    $checkUpdated = $conn->prepare("SELECT p.*,
                                  c.nombre as categoria_nombre,
                                  s.nombre as subcategoria_nombre
                           FROM tb_productos p
                           LEFT JOIN tb_categorias c ON p.categoria_id = c.id
                           LEFT JOIN tb_subcategorias s ON p.subcategoria_id = s.id
                           WHERE p.id = ?");
    $checkUpdated->bind_param("i", $productId);
    $checkUpdated->execute();
    $updatedResult = $checkUpdated->get_result();

    if ($updatedResult->num_rows > 0) {
        $updatedProduct = $updatedResult->fetch_assoc();
        error_log("Valores actualizados en la base de datos: " .
                  "Precio: {$updatedProduct['precio']}, " .
                  "Precio original: {$updatedProduct['precio_original']}, " .
                  "Categoría: {$updatedProduct['categoria_nombre']}, " .
                  "Subcategoría: {$updatedProduct['subcategoria_nombre']}");
    }

    $response = [
        'success' => true,
        'message' => 'Producto actualizado correctamente',
        'product_id' => $productId
    ];

    // Incluir los datos completos del producto actualizado si están disponibles
    if (isset($updatedProduct)) {
        $response['producto'] = $updatedProduct;
    } else {
        // Si no se pudo obtener el producto actualizado, incluir al menos los valores actualizados
        $response['updated_values'] = [
            'precio' => $productPrice,
            'precio_original' => $productOriginalPrice
        ];
    }

    error_log("Respuesta de éxito: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    $conn->rollback();
    error_log("Error updating product ID {$productId}: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno al actualizar el producto: ' . $e->getMessage()
    ]);
}

$conn->close();
error_log("=== Fin de solicitud update_product.php - " . date('Y-m-d H:i:s') . " ===");