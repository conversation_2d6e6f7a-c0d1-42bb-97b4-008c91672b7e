<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

// Si es una solicitud OPTIONS, responder inmediatamente con 200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Ruta absoluta al archivo de configuración
    $configPath = realpath(__DIR__ . '/../../../config/config.php');
    error_log("Ruta al archivo de configuración: " . $configPath);

    if (!file_exists($configPath)) {
        error_log("ERROR: El archivo de configuración no existe en la ruta: " . $configPath);
        throw new Exception('Archivo de configuración no encontrado');
    }

    require_once $configPath;

    // Obtener y validar el tipo_id (aceptar tanto GET como POST)
    $tipo_id = null;

    // Verificar si viene por POST (JSON)
    $data = json_decode(file_get_contents('php://input'), true);
    if (isset($data['tipo_id']) && !empty($data['tipo_id'])) {
        $tipo_id = intval($data['tipo_id']);
    }
    // Verificar si viene por GET
    else if (isset($_GET['tipo_id']) && !empty($_GET['tipo_id'])) {
        $tipo_id = intval($_GET['tipo_id']);
    }

    if (!$tipo_id) {
        throw new Exception('tipo_id es requerido');
    }

    // Verificar la conexión
    if ($conn->connect_error) {
        throw new Exception("Error de conexión: " . $conn->connect_error);
    }

    // Consulta para obtener las categorías del tipo especificado
    $query = "SELECT id, nombre FROM tb_categorias WHERE id_tipo_categoria = ? ORDER BY nombre";
    $stmt = $conn->prepare($query);

    if (!$stmt) {
        throw new Exception("Error preparando la consulta: " . $conn->error);
    }

    $stmt->bind_param("i", $tipo_id);

    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando la consulta: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $categorias = [];

    while ($row = $result->fetch_assoc()) {
        $categorias[] = [
            'id' => $row['id'],
            'nombre' => $row['nombre']
        ];
    }

    // Log para depuración
    error_log("Categorías encontradas para tipo_id $tipo_id: " . count($categorias));

    echo json_encode([
        'success' => true,
        'categorias' => $categorias,
        'count' => count($categorias)
    ]);

} catch (Exception $e) {
    error_log("Error en get_categorias_by_tipo.php: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener categorías: ' . $e->getMessage()
    ]);
}

if (isset($stmt)) {
    $stmt->close();
}
if (isset($conn)) {
    $conn->close();
}
