// Inicializar el estado del sidebar según el tamaño
function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    if (!sidebar || !toggleIcon) return;

    // Siempre iniciar con el sidebar expandido por defecto
    // Establecer el valor en localStorage
    localStorage.setItem('asideCollapsed', 'false');

    if (window.innerWidth < 992) {
        // En móvil: expandir el sidebar al inicio
        sidebar.classList.add('expanded');
    } else {
        // En desktop: asegurarse de que no esté colapsado
        sidebar.classList.remove('collapsed');
    }

    // Actualizar el icono
    updateToggleIcon();
}

// Función para actualizar el icono del toggle según estado
function updateToggleIcon() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.getElementById('toggle-icon');

    if (!toggleIcon || !sidebar) return;

    const isCollapsed = sidebar.classList.contains('collapsed');
    const isMobile = window.innerWidth < 992;
    const isExpanded = sidebar.classList.contains('expanded');

    // Reglas simplificadas para el icono
    if ((isMobile && !isExpanded) || (!isMobile && isCollapsed)) {
        toggleIcon.className = 'fas fa-bars';
    } else {
        toggleIcon.className = 'fas fa-times';
    }
}

// Función para alternar el sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    if (window.innerWidth < 992) {
        // En móvil: toggleamos la clase 'expanded'
        sidebar.classList.toggle('expanded');
    } else {
        // En desktop: toggleamos la clase 'collapsed'
        sidebar.classList.toggle('collapsed');
        localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
    }

    // Actualizar ícono
    updateToggleIcon();
}

// Eventos para manejar clics fuera del sidebar
document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('aside-toggle');

        // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
        if (window.innerWidth < 992 && sidebar && sidebar.classList.contains('expanded')) {
            // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
            if (!sidebar.contains(event.target) && toggleBtn && event.target !== toggleBtn && !toggleBtn.contains(event.target)) {
                // Cerrar el sidebar
                sidebar.classList.remove('expanded');

                // Actualizar el ícono
                updateToggleIcon();
            }
        }
    });
});

// Exportar funciones
export {
    initSidebar,
    updateToggleIcon,
    toggleSidebar
};