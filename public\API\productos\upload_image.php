<?php
// Archivo para manejar la subida de imágenes y su conversión a WebP
// Este archivo es referenciado desde js/modules/products/product-edit.js y js/tienda_adm.js

// Iniciar sesión y limpiar cualquier buffer de salida
session_start();
if (ob_get_level() > 0) {
    ob_end_clean();
}
header('Content-Type: application/json');

// Función de registro para debug
function debugLog($message, $data = null) {
    // Intentar guardar en un archivo real para rastreo
    try {
        $logDir = $_SERVER['DOCUMENT_ROOT'] . '/logs';
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/webp_conversion.log';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}";

        if ($data !== null) {
            if (is_array($data) || is_object($data)) {
                $log_entry .= ": " . print_r($data, true);
            } else {
                $log_entry .= ": " . $data;
            }
        }

        @file_put_contents($logFile, $log_entry . "\n", FILE_APPEND);
    } catch (Exception $e) {
        // Si falla el log a archivo, usar memory logging como fallback
        global $debug_logs;
        if (!isset($debug_logs)) {
            $debug_logs = array();
        }
        $debug_logs[] = $message . (($data !== null) ? ": " . print_r($data, true) : "");
    }
}

// Función para convertir imágenes a WebP
function convertToWebP($source, $destination, $quality = 80) {
    debugLog("Iniciando conversión WebP", ["source" => $source, "destination" => $destination]);

    // Verificar que el archivo existe
    if (!file_exists($source)) {
        debugLog("Error: Archivo fuente no existe", $source);
        return false;
    }

    // Identificar el tipo de imagen
    $imageInfo = getimagesize($source);
    if ($imageInfo === false) {
        debugLog("Error: No se pudo obtener información de la imagen");
        return false;
    }

    debugLog("Información de imagen:", $imageInfo);

    // Crear la imagen desde el archivo original según su tipo
    $image = null;
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $image = @imagecreatefromjpeg($source);
            debugLog("Imagen JPEG detectada");
            break;
        case IMAGETYPE_PNG:
            $image = @imagecreatefrompng($source);
            // Preservar transparencia
            if ($image) {
                imagepalettetotruecolor($image);
                imagealphablending($image, true);
                imagesavealpha($image, true);
                debugLog("Imagen PNG detectada con transparencia preservada");
            }
            break;
        case IMAGETYPE_GIF:
            $image = @imagecreatefromgif($source);
            debugLog("Imagen GIF detectada");
            break;
        case IMAGETYPE_WEBP:
            // Si ya es WebP, simplemente copiar el archivo
            debugLog("Imagen ya está en formato WebP, copiando directamente");
            return copy($source, $destination);
            break;
        default:
            // Para otros tipos, intentar copiar directamente
            debugLog("Tipo de imagen no reconocido directamente: " . $imageInfo[2] . ", intentando copiar");
            return copy($source, $destination);
    }

    // Verificar si se pudo crear la imagen
    if (!$image) {
        debugLog("Error: No se pudo crear la imagen desde la fuente");
        return false;
    }

    // Guardar como WebP
    $result = @imagewebp($image, $destination, $quality);

    // Verificar resultado
    if (!$result) {
        debugLog("Error al guardar imagen como WebP");
    } else {
        debugLog("Conversión a WebP exitosa", $destination);

        // Verificar que el archivo existe y tiene tamaño
        if (file_exists($destination) && filesize($destination) > 0) {
            debugLog("Archivo WebP creado correctamente con tamaño: " . filesize($destination));
        } else {
            debugLog("Archivo WebP no existe o tiene tamaño cero");
            $result = false;
        }
    }

    // Liberar memoria
    imagedestroy($image);

    return $result;
}

// Cargar configuración
try {
    $configPath = '../../config/config.php';

    if (!file_exists($configPath)) {
        // Intentar ruta alternativa
        $altConfigPath = '/var/www/aunclick/config/config.php';

        if (file_exists($altConfigPath)) {
            require_once $altConfigPath;
        } else {
            throw new Exception('No se pudo encontrar el archivo config.php');
        }
    } else {
        require_once $configPath;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno: ' . $e->getMessage()]);
    exit();
}

// Procesar la solicitud
try {
    // Verificar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    // Verificar que se ha enviado una imagen
    if (!isset($_FILES['imagen']) || $_FILES['imagen']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No se ha enviado una imagen válida');
    }

    // Verificar ID del producto
    $producto_id = isset($_POST['producto_id']) ? intval($_POST['producto_id']) : 0;
    if ($producto_id <= 0) {
        throw new Exception('ID de producto no válido');
    }

    // Verificar tipo de archivo
    $file_extension = strtolower(pathinfo($_FILES['imagen']['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'avif'];

    if (!in_array($file_extension, $allowed_extensions)) {
        throw new Exception('Tipo de archivo no permitido. Use: ' . implode(', ', $allowed_extensions));
    }

    // Verificar tamaño
    if ($_FILES['imagen']['size'] <= 0) {
        throw new Exception('El archivo de imagen está vacío');
    }

    if ($_FILES['imagen']['size'] > 5 * 1024 * 1024) { // 5MB
        throw new Exception('El archivo es demasiado grande. Máximo 5MB permitido.');
    }

    // Crear un nombre único para la imagen
    $unique_id = uniqid();

    // Definir rutas
    $server_image_dir = $_SERVER['DOCUMENT_ROOT'] . '/images/productos/';

    // Crear el directorio si no existe
    if (!is_dir($server_image_dir)) {
        debugLog('Intentando crear directorio de imágenes', $server_image_dir);
        if (!@mkdir($server_image_dir, 0755, true)) {
            debugLog('Error al crear directorio', $server_image_dir);
            throw new Exception('No se pudo crear el directorio para guardar imágenes');
        }
    }

    // Verificar permiso de escritura en el directorio
    if (!is_writable($server_image_dir)) {
        debugLog('El directorio no tiene permisos de escritura', $server_image_dir);
        throw new Exception('El directorio de imágenes no tiene permisos de escritura');
    }

    // Guardar primero la imagen original (temporal)
    $temp_original = $server_image_dir . 'temp_' . $unique_id . '.' . $file_extension;

    debugLog('Moviendo archivo temporal a ubicación temporal', [
        'origen' => $_FILES['imagen']['tmp_name'],
        'destino' => $temp_original
    ]);

    if (!move_uploaded_file($_FILES['imagen']['tmp_name'], $temp_original)) {
        debugLog('Error al mover archivo temporal');
        throw new Exception('Error al procesar la imagen. Por favor, intente nuevamente.');
    }

    // Convertir a WebP
    $webp_filename = 'producto_' . $producto_id . '_' . $unique_id . '.webp';
    $webp_path = $server_image_dir . $webp_filename;

    debugLog('Intentando convertir a WebP', [
        'origen' => $temp_original,
        'destino' => $webp_path
    ]);

    $conversion_success = convertToWebP($temp_original, $webp_path);

    // Ruta para guardar en la BD
    if ($conversion_success) {
        // Si la conversión fue exitosa, usar la ruta WebP
        $imagen_bd = '/images/productos/' . $webp_filename;
        debugLog('Conversión exitosa, usando ruta WebP', $imagen_bd);

        // Eliminar el archivo temporal
        @unlink($temp_original);
    } else {
        // Si falló la conversión, mantener la original
        $imagen_bd = '/images/productos/' . 'temp_' . $unique_id . '.' . $file_extension;
        debugLog('Conversión fallida, usando archivo original', $imagen_bd);
    }

    // Actualizar la imagen en la base de datos
    $sql = "UPDATE tb_productos SET imagen_principal = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        throw new Exception('Error preparando la consulta: ' . $conn->error);
    }

    $stmt->bind_param("si", $imagen_bd, $producto_id);

    if (!$stmt->execute()) {
        throw new Exception('Error al actualizar la imagen del producto: ' . $stmt->error);
    }

    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Imagen subida y procesada correctamente',
        'filepath' => $imagen_bd
    ]);

} catch (Exception $e) {
    debugLog('Error en upload_image.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
