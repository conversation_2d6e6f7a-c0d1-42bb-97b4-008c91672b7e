// Función para cargar los productos en la tabla
async function loadProducts() {
    try {
        console.log("Cargando productos...");
        const tableBody = document.querySelector('.admin-table tbody');
        const cardsContainer = document.getElementById('product-cards-container');

        // Mostrar indicador de carga
        tableBody.innerHTML = `
            <tr>
                <td colspan="11" class="loading-row">
                    <i class="fas fa-spinner fa-spin"></i> Cargando productos...
                </td>
            </tr>
        `;

        // Realizar petición al servidor
        const response = await fetch('../public/API/productos/get_products.php', {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            },
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Error al cargar productos: ' + response.statusText);
        }

        const data = await response.json();
        console.log("Productos cargados:", data);

        if (!data.success) {
            throw new Error(data.message || 'Error al cargar productos');
        }

        // Limpiar contenedores
        tableBody.innerHTML = '';
        if (cardsContainer) {
            cardsContainer.innerHTML = '';
        }

        // Si no hay productos
        if (!data.productos || data.productos.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="empty-row">
                        <div class="empty-state">
                            <i class="fas fa-box-open"></i>
                            <p>No hay productos disponibles</p>
                            <button class="btn btn-primary" id="addFirstProductBtn">
                                <i class="fas fa-plus"></i> Agregar tu primer producto
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            document.getElementById('addFirstProductBtn')?.addEventListener('click', () => {
                // Cambiar a la pestaña de edición/creación
                const editProductTab = document.getElementById('editProductTab');
                if (editProductTab) {
                    editProductTab.click();
                }
            });

            return;
        }

        // Cargar productos en la tabla
        data.productos.forEach(producto => {
            // Formatear precio sin decimales y con símbolo de moneda
            let precio = '';
            if (producto.precio) {
                // Usar el formateador de precios si está disponible
                if (window.priceFormatter && typeof window.priceFormatter.formatPrice === 'function') {
                    precio = window.priceFormatter.formatPrice(producto.precio);
                    console.log(`Precio formateado con priceFormatter: ${producto.precio} -> ${precio}`);
                } else {
                    // Formateo manual como respaldo
                    precio = new Intl.NumberFormat('es-CL', {
                        style: 'currency',
                        currency: 'CLP',
                        minimumFractionDigits: 0
                    }).format(producto.precio);
                    console.log(`Precio formateado con Intl.NumberFormat: ${producto.precio} -> ${precio}`);
                }
            }

            // Determinar clase de estado
            let estadoClass = '';
            switch (producto.estado) {
                case 'publicado': estadoClass = 'status-active'; break;
                case 'borrador': estadoClass = 'status-draft'; break;
                case 'agotado': estadoClass = 'status-inactive'; break;
            }

            // Crear fila en la tabla
            const row = document.createElement('tr');
            row.setAttribute('data-product-id', producto.id);
            row.innerHTML = `
                <td>${producto.id}</td>
                <td>
                    <div class="product-image">
                        <img src="${producto.imagen_principal || '../images/placeholder.png'}" alt="${producto.nombre}">
                    </div>
                </td>
                <td>${producto.nombre}</td>
                <td class="description-cell">
                    <div class="truncate-text">${producto.descripcion}</div>
                </td>
                <td>${precio}</td>
                <td>${producto.stock}</td>
                <td><span class="status-badge ${estadoClass}">${producto.estado}</span></td>
                <td><span class="condition-badge">${producto.condicion}</span></td>
                <td>${producto.categoria_nombre || 'Sin categoría'}</td>
                <td>${producto.sku || '-'}</td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" data-product-id="${producto.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" data-product-id="${producto.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);

            // Crear tarjeta para la vista móvil
            if (cardsContainer) {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.setAttribute('data-product-id', producto.id);
                card.innerHTML = `
                    <div class="product-card-image">
                        <img src="${producto.imagen_principal || '../images/placeholder.png'}" alt="${producto.nombre}">
                    </div>
                    <div class="product-card-content">
                        <h3 class="product-card-title">${producto.nombre}</h3>
                        <div class="product-card-price">${precio}</div>
                        <div class="product-card-meta">
                            <span class="product-card-stock">Stock: ${producto.stock}</span>
                            <span class="status-badge ${estadoClass}">${producto.estado}</span>
                        </div>
                        <div class="product-card-category">${producto.categoria_nombre || 'Sin categoría'}</div>
                    </div>
                    <div class="product-card-actions">
                        <button class="card-action-btn edit-btn" data-product-id="${producto.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="card-action-btn delete-btn" data-product-id="${producto.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;

                cardsContainer.appendChild(card);
            }
        });

        // Añadir event listeners a los botones de acción
        attachActionButtonsListeners();

    } catch (error) {
        console.error("Error al cargar productos:", error);

        // Mostrar error en la tabla
        const tableBody = document.querySelector('.admin-table tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="error-row">
                        <div class="error-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Error al cargar productos: ${error.message}</p>
                            <button class="btn btn-secondary" id="retryLoadBtn">
                                <i class="fas fa-sync-alt"></i> Reintentar
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            document.getElementById('retryLoadBtn')?.addEventListener('click', () => {
                loadProducts();
            });
        }
    }
}

// Función para adjuntar listeners a los botones de acción
function attachActionButtonsListeners() {
    // Botones de edición
    document.querySelectorAll('.edit-btn, .card-action-btn.edit-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            openEditProduct(productId);
        });
    });

    // Botones de eliminación
    document.querySelectorAll('.delete-btn, .card-action-btn.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            openDeleteConfirmation(productId);
        });
    });
}