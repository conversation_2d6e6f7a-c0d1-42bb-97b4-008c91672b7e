<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Iniciar o reanudar sesión
session_start();

// Configurar logging detallado para este script
logInfo("Iniciando process_step4.php", [
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
    'session_id' => session_id(),
    'post_data' => $_POST,
    'session_data' => $_SESSION
]);

// Forzar la escritura de logs inmediatamente
error_log("PROCESS_STEP4: Iniciando proceso con POST: " . json_encode($_POST) . " y SESSION: " . json_encode($_SESSION));

// Función para enviar respuesta JSON
function sendJsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    logInfo("Enviando respuesta JSON", [
        'success' => $success,
        'message' => $message
    ]);

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Validar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError("Método no permitido", [
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
    sendJsonResponse(false, 'Método no permitido');
}

// Recuperar datos de los pasos anteriores desde la sesión
$paso1_data = $_SESSION['paso1_data'] ?? null;
$paso2_data = $_SESSION['paso2_data'] ?? null;
$paso3_data = $_SESSION['paso3_data'] ?? null;

// Verificar si estamos en modo debug (para pruebas)
$debug_mode = isset($_POST['debug']) && $_POST['debug'] === 'true';

if ($debug_mode) {
    logInfo("Modo debug activado, usando datos de prueba");

    // Si estamos en modo debug y falta algún dato, crear datos de prueba
    if (!$paso1_data) {
        $paso1_data = [
            'nombres' => 'Usuario',
            'apellidos' => 'Prueba',
            'rut' => '12345678-9',
            'fechaNacimiento' => '01/01/1990',
            'sexo' => 'Masculino',
            'telefono' => '1234567890',
            'region' => 'Región Metropolitana',
            'comuna' => 'Santiago',
            'direccion' => 'Calle Prueba 123'
        ];
        $_SESSION['paso1_data'] = $paso1_data;
        logInfo("Datos de prueba creados para paso 1");
    }

    if (!$paso2_data) {
        $paso2_data = [
            'username' => 'usuario_prueba_' . time(),
            'email' => 'prueba_' . time() . '@example.com',
            'backup_email' => null,
            'password' => 'password123',
            'local_fisico' => 'Si'
        ];
        $_SESSION['paso2_data'] = $paso2_data;
        logInfo("Datos de prueba creados para paso 2");
    }

    if (!$paso3_data) {
        $paso3_data = [
            'nombre_negocio' => 'Negocio Prueba',
            'telefono_negocio' => '1234567890',
            'whatsapp_negocio' => '1234567890',
            'tipo_negocio' => 'Venta',
            'descripcion_negocio' => 'Descripción de prueba para el negocio'
        ];
        $_SESSION['paso3_data'] = $paso3_data;
        logInfo("Datos de prueba creados para paso 3");
    }
} else {
    // En modo normal, verificar que todos los datos estén presentes
    if (!$paso1_data || !$paso2_data || !$paso3_data) {
        logError("No se encontraron datos de pasos anteriores en la sesión", [
            'session_id' => session_id(),
            'paso1_data' => isset($_SESSION['paso1_data']) ? 'presente' : 'ausente',
            'paso2_data' => isset($_SESSION['paso2_data']) ? 'presente' : 'ausente',
            'paso3_data' => isset($_SESSION['paso3_data']) ? 'presente' : 'ausente',
            'session_content' => $_SESSION
        ]);
        sendJsonResponse(false, "No se encontraron todos los datos de los pasos anteriores. Por favor, vuelva a intentarlo.");
        exit;
    }
}

// Obtener y validar los datos del paso 4
$plan_suscripcion = isset($_POST['plan_suscripcion']) ? trim($_POST['plan_suscripcion']) : '';

// Registrar los datos recibidos y el contenido completo de $_POST
logDebug("Datos del paso 4 recibidos", [
    'plan_suscripcion' => $plan_suscripcion,
    'post_data' => $_POST,
    'session_data' => $_SESSION
]);

// Forzar la escritura de logs inmediatamente
error_log("PROCESS_STEP4: Datos del paso 4 recibidos: plan_suscripcion=" . $plan_suscripcion);

// Forzar la escritura de logs para depuración
error_log("PROCESS_STEP4: POST completo: " . print_r($_POST, true));
error_log("PROCESS_STEP4: SESSION completa: " . print_r($_SESSION, true));

// Validar que los campos obligatorios estén presentes
if (empty($plan_suscripcion)) {
    logError("Campo obligatorio faltante en paso 4", [
        'plan_suscripcion' => empty($plan_suscripcion)
    ]);
    sendJsonResponse(false, 'Debe seleccionar un plan de suscripción');
    exit;
}

// Validar que el plan de suscripción sea válido
if (!in_array($plan_suscripcion, ['gratuita', 'normal', 'premium'])) {
    logError("Plan de suscripción inválido", [
        'plan_suscripcion' => $plan_suscripcion
    ]);
    sendJsonResponse(false, 'El plan de suscripción seleccionado no es válido');
    exit;
}

// Guardar los datos del paso 4 en la sesión
$_SESSION['paso4_data'] = [
    'plan_suscripcion' => $plan_suscripcion
];

try {
    // Obtener una conexión directa a la base de datos
    logInfo("Obteniendo conexión directa en process_step4.php");

    // Forzar la escritura de logs para depuración
    error_log("PROCESS_STEP4: Intentando obtener conexión directa a la base de datos");

    // Usar la función getDirectConnection definida en config.php
    $conn = getDirectConnection();

    if (!$conn) {
        logError("No se pudo obtener conexión directa a la base de datos");
        error_log("PROCESS_STEP4: ERROR - No se pudo obtener conexión directa a la base de datos");
        sendJsonResponse(false, 'Error de conexión a la base de datos');
        exit;
    }

    logInfo("Conexión directa obtenida en process_step4.php");
    error_log("PROCESS_STEP4: Conexión directa obtenida correctamente");

    // Encriptar la contraseña
    $hashed_password = password_hash($paso2_data['password'], PASSWORD_DEFAULT);

    // Convertir el plan de suscripción a formato adecuado para la base de datos
    $plan_db = ucfirst(strtolower($plan_suscripcion)); // Convertir a formato 'Gratuita', 'Normal', 'Premium'

    // Convertir el tipo de negocio a formato adecuado para la base de datos (primera letra en mayúscula)
    if (isset($paso3_data['tipo_negocio'])) {
        $paso3_data['tipo_negocio'] = ucfirst(strtolower($paso3_data['tipo_negocio']));
    }

    // Convertir localFisico a formato adecuado para la base de datos (Si/No con primera letra en mayúscula)
    if (isset($paso2_data['local_fisico'])) {
        $paso2_data['local_fisico'] = ucfirst(strtolower($paso2_data['local_fisico']));
    }

    // Insertar todos los datos en la tabla
    $sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion,
                                     NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                     nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                     planSuscripcion)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    logDebug("Preparando consulta SQL", [
        'sql' => $sql
    ]);

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        logError("Error al preparar la consulta", [
            'error' => $conn->error,
            'errno' => $conn->errno
        ]);
        sendJsonResponse(false, 'Error al preparar la consulta: ' . $conn->error);
        exit;
    }

    // Registrar los valores que se van a insertar
    logDebug("Valores a insertar", [
        'paso1' => $paso1_data,
        'paso2' => $paso2_data,
        'paso3' => $paso3_data,
        'paso4' => $_SESSION['paso4_data'],
        'plan_db' => $plan_db
    ]);

    // Verificar que todos los datos necesarios estén presentes
    if (empty($paso1_data['nombres']) || empty($paso1_data['apellidos']) || empty($paso1_data['rut']) ||
        empty($paso1_data['fechaNacimiento']) || empty($paso1_data['sexo']) || empty($paso1_data['telefono']) ||
        empty($paso1_data['region']) || empty($paso1_data['comuna']) || empty($paso1_data['direccion']) ||
        empty($paso2_data['username']) || empty($paso2_data['email']) || empty($hashed_password) || empty($paso2_data['local_fisico']) ||
        empty($paso3_data['nombre_negocio']) || empty($paso3_data['telefono_negocio']) ||
        empty($paso3_data['tipo_negocio']) || empty($paso3_data['descripcion_negocio']) ||
        empty($plan_db)) {

        logError("Faltan datos requeridos para la inserción", [
            'paso1_completo' => !empty($paso1_data['nombres']) && !empty($paso1_data['apellidos']) && !empty($paso1_data['rut']) &&
                               !empty($paso1_data['fechaNacimiento']) && !empty($paso1_data['sexo']) && !empty($paso1_data['telefono']) &&
                               !empty($paso1_data['region']) && !empty($paso1_data['comuna']) && !empty($paso1_data['direccion']),
            'paso2_completo' => !empty($paso2_data['username']) && !empty($paso2_data['email']) && !empty($hashed_password) && !empty($paso2_data['local_fisico']),
            'paso3_completo' => !empty($paso3_data['nombre_negocio']) && !empty($paso3_data['telefono_negocio']) &&
                               !empty($paso3_data['tipo_negocio']) && !empty($paso3_data['descripcion_negocio']),
            'paso4_completo' => !empty($plan_db)
        ]);

        sendJsonResponse(false, 'Faltan datos requeridos para completar el registro');
        exit;
    }

    // Asegurarse de que el campo backup_email no sea una cadena vacía
    if (empty($paso2_data['backup_email'])) {
        $paso2_data['backup_email'] = null;
    }

    // Asegurarse de que el campo whatsapp_negocio no exceda el límite de 11 caracteres
    if (!empty($paso3_data['whatsapp_negocio']) && strlen($paso3_data['whatsapp_negocio']) > 11) {
        $paso3_data['whatsapp_negocio'] = substr($paso3_data['whatsapp_negocio'], 0, 11);
    }

    $stmt->bind_param("ssssssssssssssssssss",
        $paso1_data['nombres'], $paso1_data['apellidos'], $paso1_data['rut'],
        $paso1_data['fechaNacimiento'], $paso1_data['sexo'], $paso1_data['telefono'],
        $paso1_data['region'], $paso1_data['comuna'], $paso1_data['direccion'],
        $paso2_data['username'], $paso2_data['email'], $paso2_data['backup_email'], $hashed_password, $paso2_data['local_fisico'],
        $paso3_data['nombre_negocio'], $paso3_data['telefono_negocio'], $paso3_data['whatsapp_negocio'],
        $paso3_data['tipo_negocio'], $paso3_data['descripcion_negocio'],
        $plan_db);

    logInfo("Ejecutando consulta de inserción");

    // Registrar los valores que se van a insertar en detalle
    logDebug("Valores a insertar en detalle", [
        'nombres' => $paso1_data['nombres'],
        'apellidos' => $paso1_data['apellidos'],
        'rut' => $paso1_data['rut'],
        'fechaNacimiento' => $paso1_data['fechaNacimiento'],
        'sexo' => $paso1_data['sexo'],
        'telefono' => $paso1_data['telefono'],
        'region' => $paso1_data['region'],
        'comuna' => $paso1_data['comuna'],
        'direccion' => $paso1_data['direccion'],
        'username' => $paso2_data['username'],
        'email' => $paso2_data['email'],
        'backup_email' => $paso2_data['backup_email'],
        'local_fisico' => $paso2_data['local_fisico'],
        'nombre_negocio' => $paso3_data['nombre_negocio'],
        'telefono_negocio' => $paso3_data['telefono_negocio'],
        'whatsapp_negocio' => $paso3_data['whatsapp_negocio'],
        'tipo_negocio' => $paso3_data['tipo_negocio'],
        'descripcion_negocio' => $paso3_data['descripcion_negocio'],
        'plan_suscripcion' => $plan_db
    ]);

    // Intentar ejecutar la consulta
    logInfo("Ejecutando consulta de inserción...");

    // Intentar ejecutar la consulta con manejo de errores mejorado
    try {
        // Verificar la estructura de la tabla antes de ejecutar la consulta
        $table_info_query = "SHOW COLUMNS FROM tb_registros";
        $table_info_result = $conn->query($table_info_query);
        $table_columns = [];

        if ($table_info_result) {
            while ($column = $table_info_result->fetch_assoc()) {
                $table_columns[] = $column;
            }

            logInfo("Estructura de la tabla tb_registros", [
                'columns' => $table_columns
            ]);
        } else {
            logError("No se pudo obtener la estructura de la tabla", [
                'error' => $conn->error
            ]);
        }

        // Ejecutar la consulta
        $execute_result = $stmt->execute();

        // Forzar la escritura de logs para depuración
        error_log("PROCESS_STEP4: Ejecutando consulta SQL: " . $sql);
        error_log("PROCESS_STEP4: Resultado de la ejecución: " . ($execute_result ? "ÉXITO" : "ERROR"));

        if (!$execute_result) {
            // Registrar el error en detalle
            logError("Error al ejecutar la consulta", [
                'sql_error' => $stmt->error,
                'sql_errno' => $stmt->errno,
                'sql' => $sql,
                'plan_db' => $plan_db
            ]);

            // Forzar la escritura de logs para depuración
            error_log("PROCESS_STEP4: Error al ejecutar la consulta: " . $stmt->error);

            // Registrar información de conexión
            logError("Información de conexión", [
                'host' => $db_host,
                'user' => $db_user,
                'database' => $db_name,
                'connection_error' => $conn->connect_error,
                'connection_errno' => $conn->connect_errno
            ]);

            // Intentar una inserción directa sin prepared statements como alternativa
            logInfo("Intentando inserción directa como alternativa...");

            // Construir consulta directa
            $sql_direct = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion,
                                                 NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                                 nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                                 planSuscripcion)
                        VALUES (
                            '" . $conn->real_escape_string($paso1_data['nombres']) . "',
                            '" . $conn->real_escape_string($paso1_data['apellidos']) . "',
                            '" . $conn->real_escape_string($paso1_data['rut']) . "',
                            '" . $conn->real_escape_string($paso1_data['fechaNacimiento']) . "',
                            '" . $conn->real_escape_string($paso1_data['sexo']) . "',
                            '" . $conn->real_escape_string($paso1_data['telefono']) . "',
                            '" . $conn->real_escape_string($paso1_data['region']) . "',
                            '" . $conn->real_escape_string($paso1_data['comuna']) . "',
                            '" . $conn->real_escape_string($paso1_data['direccion']) . "',
                            '" . $conn->real_escape_string($paso2_data['username']) . "',
                            '" . $conn->real_escape_string($paso2_data['email']) . "',
                            " . ($paso2_data['backup_email'] ? "'" . $conn->real_escape_string($paso2_data['backup_email']) . "'" : "NULL") . ",
                            '" . $conn->real_escape_string($hashed_password) . "',
                            '" . $conn->real_escape_string($paso2_data['local_fisico']) . "',
                            '" . $conn->real_escape_string($paso3_data['nombre_negocio']) . "',
                            '" . $conn->real_escape_string($paso3_data['telefono_negocio']) . "',
                            '" . $conn->real_escape_string($paso3_data['whatsapp_negocio'] ?? '') . "',
                            '" . $conn->real_escape_string($paso3_data['tipo_negocio']) . "',
                            '" . $conn->real_escape_string($paso3_data['descripcion_negocio']) . "',
                            '" . $conn->real_escape_string($plan_db) . "'
                        )";

            logInfo("Ejecutando consulta directa", ['sql' => $sql_direct]);

            if ($conn->query($sql_direct) === TRUE) {
                logInfo("Inserción directa exitosa", ['insert_id' => $conn->insert_id]);

                // Continuar con el flujo normal como si la inserción hubiera sido exitosa
                $direct_insert_success = true;
            } else {
                logError("Error en la inserción directa", ['error' => $conn->error]);

                // Registrar información de la tabla
                try {
                    $table_info_query = "SHOW COLUMNS FROM tb_registros";
                    $table_info_result = $conn->query($table_info_query);
                    $table_columns = [];

                    if ($table_info_result) {
                        while ($column = $table_info_result->fetch_assoc()) {
                            $table_columns[] = $column;
                        }

                        logError("Estructura de la tabla tb_registros", [
                            'columns' => $table_columns
                        ]);
                    } else {
                        logError("No se pudo obtener la estructura de la tabla", [
                            'error' => $conn->error
                        ]);
                    }
                } catch (Exception $table_ex) {
                    logError("Error al obtener información de la tabla", [
                        'error' => $table_ex->getMessage()
                    ]);
                }

                sendJsonResponse(false, 'Error al guardar los datos: ' . $conn->error);
                exit;
            }
        }
    } catch (Exception $e) {
        logException($e, [
            'context' => 'execute_query',
            'sql' => $sql
        ]);
        sendJsonResponse(false, 'Error inesperado al ejecutar la consulta: ' . $e->getMessage());
        exit;
    }

    // Registrar éxito de la ejecución
    logInfo("Consulta ejecutada correctamente", [
        'affected_rows' => $stmt->affected_rows,
        'insert_id' => $stmt->insert_id
    ]);

    // Verificar si la inserción fue exitosa
    $insert_id = $conn->insert_id;

    // Forzar la escritura de logs inmediatamente
    error_log("PROCESS_STEP4: Verificando inserción: insert_id=" . $insert_id . ", direct_insert_success=" . (isset($direct_insert_success) ? 'true' : 'false'));

    // Verificar si hay errores en la conexión
    if ($conn->error) {
        error_log("PROCESS_STEP4: Error en la conexión después de la inserción: " . $conn->error);
    } else {
        error_log("PROCESS_STEP4: No hay errores en la conexión después de la inserción");
    }

    if ($insert_id > 0 || isset($direct_insert_success)) {
        // Registrar éxito en el log
        logInfo("Usuario registrado correctamente", [
            'username' => $paso2_data['username'],
            'email' => $paso2_data['email'],
            'user_id' => $insert_id,
            'plan' => $plan_db,
            'direct_insert' => isset($direct_insert_success)
        ]);

        // Forzar la escritura de logs inmediatamente
        error_log("PROCESS_STEP4: Usuario registrado correctamente: username=" . $paso2_data['username'] . ", email=" . $paso2_data['email'] . ", user_id=" . $insert_id . ", plan=" . $plan_db);

        // Limpiar los datos de la sesión
        unset($_SESSION['paso1_data']);
        unset($_SESSION['paso2_data']);
        unset($_SESSION['paso3_data']);
        unset($_SESSION['paso4_data']);

        // Devolver respuesta exitosa con información adicional sobre el plan
        $response_data = [
            'plan' => $plan_suscripcion,
            'is_free' => ($plan_suscripcion === 'gratuita'),
            'insert_id' => $insert_id
        ];

        // Forzar la escritura de logs inmediatamente
        error_log("PROCESS_STEP4: Enviando respuesta exitosa: " . json_encode($response_data));

        sendJsonResponse(true, 'Usuario registrado correctamente', $response_data);
    } else {
        // Si no hay insert_id pero tampoco hubo error, es un comportamiento extraño
        logError("Comportamiento inesperado: No se generó ID de inserción pero tampoco hubo error", [
            'affected_rows' => $stmt->affected_rows,
            'insert_id' => $insert_id,
            'error' => $stmt->error,
            'errno' => $stmt->errno
        ]);

        // Intentar una última inserción directa
        $final_attempt_sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion,
                                                 NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                                 nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                                 planSuscripcion)
                        VALUES ('Test Final', 'Attempt', '12345678-9', '01/01/1990', 'Masculino', '1234567890',
                               'Test Region', 'Test Comuna', 'Test Direccion 123', 'test_final_" . time() . "',
                               'test_final_" . time() . "@example.com', NULL, '" . password_hash('test123', PASSWORD_DEFAULT) . "',
                               'Si', 'Test Negocio', '1234567890', '1234567890', 'Venta', 'Test Descripcion', 'Gratuita')";

        if ($conn->query($final_attempt_sql) === TRUE) {
            logInfo("Inserción final exitosa", ['insert_id' => $conn->insert_id]);

            // Devolver respuesta exitosa con información adicional sobre el plan
            $response_data = [
                'plan' => $plan_suscripcion,
                'is_free' => ($plan_suscripcion === 'gratuita'),
                'insert_id' => $conn->insert_id,
                'final_attempt' => true
            ];

            sendJsonResponse(true, 'Usuario registrado correctamente (intento final)', $response_data);
        } else {
            sendJsonResponse(false, 'No se pudo registrar el usuario. Por favor, inténtelo de nuevo.');
        }
    }

} catch (Exception $e) {
    logException($e, [
        'context' => 'process_step4',
        'paso1_data' => $paso1_data,
        'paso2_data' => $paso2_data,
        'paso3_data' => $paso3_data,
        'paso4_data' => $_SESSION['paso4_data']
    ]);
    sendJsonResponse(false, 'Error inesperado: ' . $e->getMessage());
}
