/**
 * Auto-Fill Fix con script inline - Reparación de funcionalidad del botón Auto-llenar datos
 * Villarrica a un CLICK
 */

// Asignar evento onclick directamente al botón "Auto-llenar datos" del paso 1
document.querySelector('.btn-auto-fill').onclick = function() {
  console.log('Botón Auto-llenar clickeado');
  
  // Llenar campos del formulario con datos de prueba
  document.getElementById('nombres').value = 'Juan <PERSON>';
  document.getElementById('apellidos').value = '<PERSON>';
  document.getElementById('rut').value = '12345678-9';
  document.getElementById('fechaNacimiento').value = '01/01/1990';
  
  // Seleccionar sexo
  const sexoSelect = document.querySelector('select[name="sexo"]');
  if (sexoSelect) sexoSelect.value = 'masculino';
  
  // Teléfono
  document.getElementById('telefono').value = '12345678';
  
  // Seleccionar región
  const regionSelect = document.getElementById('region');
  if (regionSelect) {
    regionSelect.value = 'metropolitana';
    
    // Disparar evento change para cargar comunas
    const event = new Event('change', { bubbles: true });
    regionSelect.dispatchEvent(event);
    
    // Esperar un momento para que se carguen las comunas
    setTimeout(() => {
      // Seleccionar comuna
      const comunaSelect = document.getElementById('comuna');
      if (comunaSelect && comunaSelect.options.length > 1) {
        comunaSelect.value = comunaSelect.options[1].value;
      }
    }, 100);
  }
  
  // Dirección
  document.getElementById('direccion').value = 'Calle Ejemplo 123';
  
  return false; // Prevenir comportamiento por defecto
};