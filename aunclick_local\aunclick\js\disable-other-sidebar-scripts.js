/**
 * Script para desactivar otros scripts de sidebar que puedan causar conflictos
 * Se ejecuta inmediatamente antes de cargar el nuevo script de sidebar
 */

(function() {
    console.log('Desactivando otros scripts de sidebar...');
    
    // Lista de funciones y eventos a buscar y desactivar
    const functionsToDisable = [
        'toggleSidebar',
        'showSidebar',
        'hideSidebar',
        'expandSidebar',
        'collapseSidebar',
        'showOverlay',
        'hideOverlay',
        'initSidebarEvents',
        'checkInitialSidebarState',
        'createSidebarOverlay',
        'addOverlayClickEvent',
        'applySidebarFix'
    ];
    
    // Función para desactivar funciones existentes
    function disableFunctions() {
        // Verificar las funciones existentes en window
        functionsToDisable.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
                console.log(`Desactivando función: ${funcName}`);
                // Reemplazar la función con una función vacía
                window[funcName] = function() {
                    console.log(`Función desactivada: ${funcName}`);
                    return false;
                };
            }
        });
    }
    
    // Función para detener propagación de eventos del botón de toggle
    function stopToggleButtonEvents() {
        const toggleBtn = document.getElementById('aside-toggle');
        if (toggleBtn) {
            console.log('Limpiando eventos del botón de toggle...');
            
            // Clonar el botón para eliminar todos los event listeners
            const newToggleBtn = toggleBtn.cloneNode(true);
            toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
            
            console.log('Botón de toggle clonado para eliminar eventos');
        }
    }
    
    // Función para detener mutation observers existentes
    function stopMutationObservers() {
        // No podemos acceder directamente a los MutationObserver, pero podemos detener su propagación
        console.log('Intentando detener MutationObservers existentes...');
        
        // Crear una copia del método disconnect original
        const originalDisconnect = window.MutationObserver.prototype.disconnect;
        
        // Reemplazar temporalmente el método observe para encontrar observers activos
        window.MutationObserver.prototype.observe = function() {
            console.log('MutationObserver intentando observar. Desconectando...');
            if (typeof this.disconnect === 'function') {
                this.disconnect();
            }
            return false;
        };
        
        // Restaurar el método original después de un breve periodo
        setTimeout(() => {
            window.MutationObserver.prototype.observe = window.MutationObserver.prototype.__observe || window.MutationObserver.prototype.observe;
        }, 100);
    }
    
    // Función para eliminar overlays existentes
    function removeExistingOverlays() {
        const overlays = [
            document.getElementById('sidebar-overlay'),
            document.getElementById('sidebar-overlay-fix'),
            document.getElementById('emergency-overlay')
        ];
        
        overlays.forEach(overlay => {
            if (overlay) {
                console.log(`Eliminando overlay: ${overlay.id}`);
                overlay.parentNode.removeChild(overlay);
            }
        });
    }
    
    // Función principal para desactivar todos los conflictos
    function disableAllConflicts() {
        disableFunctions();
        stopToggleButtonEvents();
        stopMutationObservers();
        removeExistingOverlays();
    }
    
    // Ejecutar al cargar
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', disableAllConflicts);
    } else {
        disableAllConflicts();
    }
    
    // Ejecutar después de que todo haya cargado
    window.addEventListener('load', () => {
        setTimeout(disableAllConflicts, 500);
    });
    
    // Ejecutar inmediatamente
    disableAllConflicts();
    
    console.log('Script de desactivación ejecutado');
})();
