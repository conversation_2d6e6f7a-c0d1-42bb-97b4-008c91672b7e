<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Información del servidor y la solicitud
$debug_info = [
    'timestamp' => date('Y-m-d H:i:s'),
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'query_string' => $_SERVER['QUERY_STRING'] ?? '',
    'get_params' => $_GET,
    'post_params' => $_POST,
    'server_software' => $_SERVER['SERVER_SOFTWARE'],
    'php_version' => PHP_VERSION,
    'headers_received' => getallheaders(),
    'session_status' => session_status()
];

// Intentar conectar a la base de datos
try {
    require_once '../../../config/config.php';
    
    $debug_info['db_connection'] = 'Conexión establecida correctamente';
    
    // Probar una consulta simple
    $categoria_id = isset($_GET['categoria_id']) ? intval($_GET['categoria_id']) : 1;
    $debug_info['categoria_id_test'] = $categoria_id;
    
    // Consulta directa
    $query = "SELECT id, nombre FROM tb_subcategorias WHERE categoria_id = $categoria_id LIMIT 10";
    $debug_info['query'] = $query;
    
    $result = $conn->query($query);
    if ($result) {
        $subcategorias = [];
        while ($row = $result->fetch_assoc()) {
            $subcategorias[] = $row;
        }
        $debug_info['query_result'] = [
            'success' => true,
            'count' => count($subcategorias),
            'data' => $subcategorias
        ];
    } else {
        $debug_info['query_result'] = [
            'success' => false,
            'error' => $conn->error
        ];
    }
    
    // Consulta preparada
    $debug_info['prepared_statement'] = [];
    $stmt = $conn->prepare("SELECT id, nombre FROM tb_subcategorias WHERE categoria_id = ?");
    if ($stmt) {
        $debug_info['prepared_statement']['prepare'] = 'OK';
        
        $stmt->bind_param("i", $categoria_id);
        $debug_info['prepared_statement']['bind_param'] = 'OK';
        
        if ($stmt->execute()) {
            $debug_info['prepared_statement']['execute'] = 'OK';
            
            $result = $stmt->get_result();
            $subcategorias = [];
            while ($row = $result->fetch_assoc()) {
                $subcategorias[] = $row;
            }
            
            $debug_info['prepared_statement']['result'] = [
                'success' => true,
                'count' => count($subcategorias),
                'data' => $subcategorias
            ];
        } else {
            $debug_info['prepared_statement']['execute'] = [
                'success' => false,
                'error' => $stmt->error
            ];
        }
        
        $stmt->close();
    } else {
        $debug_info['prepared_statement'] = [
            'success' => false,
            'error' => $conn->error
        ];
    }
    
    // Información de las tablas
    $tables_result = $conn->query("SHOW TABLES");
    if ($tables_result) {
        $tables = [];
        while ($row = $tables_result->fetch_row()) {
            $tables[] = $row[0];
        }
        $debug_info['tables'] = $tables;
    }
    
    // Estructura de la tabla tb_subcategorias
    $structure_result = $conn->query("DESCRIBE tb_subcategorias");
    if ($structure_result) {
        $structure = [];
        while ($row = $structure_result->fetch_assoc()) {
            $structure[] = $row;
        }
        $debug_info['tb_subcategorias_structure'] = $structure;
    }
    
    // Cerrar conexión
    $conn->close();
    
} catch (Exception $e) {
    $debug_info['db_connection'] = [
        'success' => false,
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ];
}

// Devolver información de depuración
echo json_encode($debug_info, JSON_PRETTY_PRINT);
