// Script para manejar la interactividad del Dashboard

document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard script cargado');
    
    // Obtener referencias a elementos del DOM
    const refreshDashboardBtn = document.getElementById('refreshDashboard');
    const dashboardPeriodSelect = document.getElementById('dashboardPeriod');
    
    // Manejar el evento de clic en el botón de actualizar
    if (refreshDashboardBtn) {
        refreshDashboardBtn.addEventListener('click', function() {
            console.log('Actualizando dashboard...');
            
            // Mostrar un indicador de carga
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            this.disabled = true;
            
            // Simular una carga (en una aplicación real, aquí se cargarían datos del servidor)
            setTimeout(() => {
                // Actualizar los gráficos
                if (typeof initializeCharts === 'function') {
                    initializeCharts();
                }
                
                // Actualizar las tarjetas de estadísticas con datos aleatorios
                updateStatCards();
                
                // Restaurar el botón
                this.innerHTML = '<i class="fas fa-sync-alt"></i> Actualizar';
                this.disabled = false;
                
                // Mostrar notificación
                showNotification('Dashboard actualizado correctamente', 'success');
            }, 1500);
        });
    }
    
    // Manejar el cambio en el selector de período
    if (dashboardPeriodSelect) {
        dashboardPeriodSelect.addEventListener('change', function() {
            const selectedPeriod = this.value;
            console.log(`Período seleccionado: ${selectedPeriod}`);
            
            // Actualizar los datos según el período seleccionado
            updateDashboardData(selectedPeriod);
        });
    }
    
    // Función para actualizar los datos del dashboard según el período
    function updateDashboardData(period) {
        // Mostrar un indicador de carga
        if (refreshDashboardBtn) {
            refreshDashboardBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            refreshDashboardBtn.disabled = true;
        }
        
        // Simular una carga (en una aplicación real, aquí se cargarían datos del servidor)
        setTimeout(() => {
            // Actualizar los gráficos
            if (typeof initializeCharts === 'function') {
                initializeCharts();
            }
            
            // Actualizar las tarjetas de estadísticas con datos aleatorios
            updateStatCards();
            
            // Restaurar el botón
            if (refreshDashboardBtn) {
                refreshDashboardBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Actualizar';
                refreshDashboardBtn.disabled = false;
            }
            
            // Mostrar notificación
            showNotification(`Dashboard actualizado para el período: ${getPeriodName(period)}`, 'success');
        }, 1500);
    }
    
    // Función para obtener el nombre del período
    function getPeriodName(period) {
        const periodNames = {
            'today': 'Hoy',
            'yesterday': 'Ayer',
            'week': 'Esta semana',
            'month': 'Este mes',
            'quarter': 'Este trimestre',
            'year': 'Este año'
        };
        
        return periodNames[period] || period;
    }
    
    // Función para actualizar las tarjetas de estadísticas con datos aleatorios
    function updateStatCards() {
        // Obtener todas las tarjetas de estadísticas
        const statCards = document.querySelectorAll('.stat-card');
        
        // Actualizar cada tarjeta con datos aleatorios
        statCards.forEach(card => {
            const statValue = card.querySelector('.stat-value');
            const statTrend = card.querySelector('.stat-trend');
            
            if (statValue) {
                // Obtener el valor actual
                let currentValue = statValue.textContent;
                
                // Determinar si es un valor numérico o monetario
                if (currentValue.includes('$')) {
                    // Es un valor monetario
                    if (currentValue.includes('M')) {
                        // Es un valor en millones
                        const baseValue = parseFloat(currentValue.replace('$', '').replace('M', ''));
                        const newValue = (baseValue + (Math.random() * 0.5 - 0.25)).toFixed(1);
                        statValue.textContent = `$${newValue}M`;
                    } else {
                        // Es un valor normal
                        const baseValue = parseInt(currentValue.replace('$', '').replace(/,/g, ''));
                        const newValue = baseValue + Math.floor(Math.random() * 1000 - 500);
                        statValue.textContent = `$${newValue.toLocaleString()}`;
                    }
                } else if (currentValue.includes('%')) {
                    // Es un porcentaje
                    const baseValue = parseFloat(currentValue.replace('%', ''));
                    const newValue = (baseValue + (Math.random() * 0.6 - 0.3)).toFixed(1);
                    statValue.textContent = `${newValue}%`;
                } else {
                    // Es un valor numérico
                    const baseValue = parseInt(currentValue.replace(/,/g, ''));
                    const newValue = baseValue + Math.floor(Math.random() * 20 - 10);
                    statValue.textContent = newValue.toLocaleString();
                }
            }
            
            if (statTrend) {
                // Actualizar la tendencia
                const trendValue = Math.floor(Math.random() * 25) + 1;
                const isPositive = Math.random() > 0.3; // 70% de probabilidad de ser positivo
                
                if (isPositive) {
                    statTrend.className = 'stat-trend positive';
                    statTrend.innerHTML = `<i class="fas fa-arrow-up"></i> ${trendValue}% vs mes anterior`;
                } else {
                    statTrend.className = 'stat-trend negative';
                    statTrend.innerHTML = `<i class="fas fa-arrow-down"></i> ${trendValue}% vs mes anterior`;
                }
            }
        });
    }
    
    // Inicializar el dashboard
    function initDashboard() {
        console.log('Inicializando dashboard...');
        
        // Verificar si los gráficos ya están inicializados
        if (typeof Chart !== 'undefined' && typeof initializeCharts === 'function') {
            initializeCharts();
        } else {
            console.warn('Chart.js o la función initializeCharts no están disponibles');
        }
        
        // Añadir animación a las tarjetas de estadísticas
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.animation = `fadeInUp 0.5s ease forwards ${index * 0.1}s`;
        });
    }
    
    // Inicializar el dashboard
    initDashboard();
});
