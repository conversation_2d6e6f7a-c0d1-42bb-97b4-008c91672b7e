/**
 * Solución definitiva para el sidebar en modo responsive
 * Este script se ejecuta al final para garantizar que el sidebar funcione correctamente
 */

(function() {
    console.log('Aplicando solución definitiva para el sidebar...');
    
    // Función principal para corregir el sidebar
    function fixSidebar() {
        // 0. Obtener el estado guardado del sidebar en desktop
        const savedState = localStorage.getItem('asideCollapsed');
        const isDesktop = window.innerWidth >= 993;
        
        // 1. Eliminar botones toggle duplicados
        const allToggleButtons = document.querySelectorAll('#aside-toggle, #emergency-toggle-btn');
        
        // Mantener solo el primer botón toggle y eliminar los demás
        if (allToggleButtons.length > 1) {
            console.log(`Encontrados ${allToggleButtons.length} botones toggle. Eliminando duplicados...`);
            for (let i = 1; i < allToggleButtons.length; i++) {
                if (allToggleButtons[i] && allToggleButtons[i].parentNode) {
                    allToggleButtons[i].parentNode.removeChild(allToggleButtons[i]);
                }
            }
        }
        
        // 2. Asegurarse de que el botón toggle esté visible y bien posicionado
        const toggleBtn = document.getElementById('aside-toggle');
        if (toggleBtn) {
            toggleBtn.style.display = 'flex';
            toggleBtn.style.alignItems = 'center';
            toggleBtn.style.justifyContent = 'center';
            toggleBtn.style.position = 'absolute';
            toggleBtn.style.top = '10px';
            toggleBtn.style.right = '-15px';
            toggleBtn.style.width = '30px';
            toggleBtn.style.height = '30px';
            toggleBtn.style.backgroundColor = '#6a1b9a';
            toggleBtn.style.color = 'white';
            toggleBtn.style.borderRadius = '50%';
            toggleBtn.style.cursor = 'pointer';
            toggleBtn.style.zIndex = '10000';
            toggleBtn.style.border = 'none';
            toggleBtn.style.boxShadow = '0 0 5px rgba(0,0,0,0.3)';
            
            // Limpiar eventos existentes y añadir el nuevo
            const newToggleBtn = toggleBtn.cloneNode(true);
            if (toggleBtn.parentNode) {
                toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);
            }
            
            // Añadir nuevo evento de clic
            newToggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const sidebar = document.getElementById('sidebar');
                if (!sidebar) return;
                
                console.log('Toggle button clicked. Current sidebar state:', sidebar.classList.contains('expanded') ? 'expanded' : 'collapsed');
                
                const mainContent = document.querySelector('.main-content');
                const isDesktop = window.innerWidth >= 993;
                
                if (isDesktop) {
                    // En desktop
                    if (sidebar.classList.contains('collapsed')) {
                        // Expandir sidebar
                        sidebar.classList.remove('collapsed');
                        sidebar.style.width = '250px';
                        
                        // Mostrar elementos
                        const elementsToShow = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                        elementsToShow.forEach(el => {
                            el.style.display = 'block';
                        });
                        
                        // Ajustar contenido principal
                        if (mainContent) {
                            mainContent.classList.remove('sidebar-collapsed');
                            mainContent.style.marginLeft = '250px';
                            mainContent.style.width = 'calc(100% - 250px)';
                        }
                        
                        // Cambiar icono
                        const toggleIcon = newToggleBtn.querySelector('i') || document.getElementById('toggle-icon');
                        if (toggleIcon) {
                            toggleIcon.className = 'fas fa-times';
                        }
                        
                        // Guardar estado
                        localStorage.setItem('asideCollapsed', 'false');
                        
                        console.log('Sidebar expandido (desktop)');
                    } else {
                        // Colapsar sidebar
                        sidebar.classList.add('collapsed');
                        sidebar.style.width = '70px';
                        
                        // Ocultar elementos
                        const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                        elementsToHide.forEach(el => {
                            el.style.display = 'none';
                        });
                        
                        // Ajustar contenido principal
                        if (mainContent) {
                            mainContent.classList.add('sidebar-collapsed');
                            mainContent.style.marginLeft = '70px';
                            mainContent.style.width = 'calc(100% - 70px)';
                        }
                        
                        // Cambiar icono
                        const toggleIcon = newToggleBtn.querySelector('i') || document.getElementById('toggle-icon');
                        if (toggleIcon) {
                            toggleIcon.className = 'fas fa-bars';
                        }
                        
                        // Guardar estado
                        localStorage.setItem('asideCollapsed', 'true');
                        
                        console.log('Sidebar colapsado (desktop)');
                    }
                } else {
                    // En móvil
                    if (sidebar.classList.contains('expanded')) {
                        // Colapsar sidebar
                        sidebar.classList.remove('expanded');
                        sidebar.classList.add('collapsed');
                        sidebar.style.width = '70px';
                        
                        // Ocultar elementos
                        const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                        elementsToHide.forEach(el => {
                            el.style.display = 'none';
                        });
                        
                        // Ocultar overlay si existe
                        const overlay = document.getElementById('sidebar-overlay-new') || document.getElementById('sidebar-overlay-fix') || document.getElementById('sidebar-overlay');
                        if (overlay) {
                            overlay.style.display = 'none';
                        }
                        
                        // Cambiar icono
                        const toggleIcon = newToggleBtn.querySelector('i') || document.getElementById('toggle-icon');
                        if (toggleIcon) {
                            toggleIcon.className = 'fas fa-bars';
                        }
                        
                        console.log('Sidebar colapsado (móvil)');
                    } else {
                        // Expandir sidebar
                        sidebar.classList.add('expanded');
                        sidebar.classList.remove('collapsed');
                        sidebar.style.width = '250px';
                        
                        // Mostrar elementos
                        const elementsToShow = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                        elementsToShow.forEach(el => {
                            el.style.display = 'block';
                        });
                        
                        // Mostrar u crear overlay
                        let overlay = document.getElementById('sidebar-overlay-new');
                        if (!overlay) {
                            overlay = document.createElement('div');
                            overlay.id = 'sidebar-overlay-new';
                            overlay.className = 'sidebar-overlay';
                            overlay.style.position = 'fixed';
                            overlay.style.top = '0';
                            overlay.style.left = '0';
                            overlay.style.width = '100%';
                            overlay.style.height = '100%';
                            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                            overlay.style.zIndex = '1040';
                            overlay.style.display = 'none';
                            document.body.appendChild(overlay);
                            
                            // Añadir evento de clic al overlay
                            overlay.addEventListener('click', function() {
                                const sidebarEl = document.getElementById('sidebar');
                                if (sidebarEl) {
                                    sidebarEl.classList.remove('expanded');
                                    sidebarEl.classList.add('collapsed');
                                    sidebarEl.style.width = '70px';
                                    
                                    // Ocultar elementos
                                    const elementsToHide = sidebarEl.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                                    elementsToHide.forEach(el => {
                                        el.style.display = 'none';
                                    });
                                }
                                this.style.display = 'none';
                                
                                // Cambiar icono
                                const tglIcon = document.getElementById('toggle-icon');
                                if (tglIcon) {
                                    tglIcon.className = 'fas fa-bars';
                                }
                            });
                        }
                        
                        overlay.style.display = 'block';
                        
                        // Cambiar icono
                        const toggleIcon = newToggleBtn.querySelector('i') || document.getElementById('toggle-icon');
                        if (toggleIcon) {
                            toggleIcon.className = 'fas fa-times';
                        }
                        
                        console.log('Sidebar expandido (móvil)');
                    }
                }
            });
        } else {
            console.error('No se encontró el botón toggle del sidebar');
        }
        
        // 3. Asegurarse de que el sidebar esté bien configurado
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar) {
            // Asegurar que el sidebar sea visible
            sidebar.style.display = 'block';
            sidebar.style.visibility = 'visible';
            sidebar.style.opacity = '1';
            sidebar.style.position = 'fixed';
            sidebar.style.top = '0';
            sidebar.style.left = '0';
            sidebar.style.height = '100vh';
            sidebar.style.zIndex = '9999';
            sidebar.style.transition = 'width 0.3s ease';
            sidebar.style.backgroundColor = '#6a1b9a';
            sidebar.style.overflowX = 'hidden';
            sidebar.style.overflowY = 'auto';
            
            if (isDesktop) {
                // En desktop, aplicar estado según localStorage
                if (savedState === 'true') {
                    // Sidebar colapsado
                    sidebar.classList.add('collapsed');
                    sidebar.classList.remove('expanded');
                    sidebar.style.width = '70px';
                    
                    if (mainContent) {
                        mainContent.classList.add('sidebar-collapsed');
                        mainContent.style.marginLeft = '70px';
                        mainContent.style.width = 'calc(100% - 70px)';
                    }
                    
                    // Ocultar elementos internos
                    const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                    elementsToHide.forEach(el => {
                        el.style.display = 'none';
                    });
                } else {
                    // Sidebar expandido
                    sidebar.classList.remove('collapsed');
                    sidebar.style.width = '250px';
                    
                    if (mainContent) {
                        mainContent.classList.remove('sidebar-collapsed');
                        mainContent.style.marginLeft = '250px';
                        mainContent.style.width = 'calc(100% - 250px)';
                    }
                }
            } else {
                // Estado inicial en móvil: colapsado
                sidebar.style.width = '70px';
                sidebar.classList.remove('expanded');
                sidebar.classList.add('collapsed');
                
                // Ocultar elementos internos
                const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                elementsToHide.forEach(el => {
                    el.style.display = 'none';
                });
            }
        } else {
            console.error('No se encontró el sidebar');
        }
        
        // 4. Limpieza de overlays duplicados
        const overlays = document.querySelectorAll('[id^="sidebar-overlay"], [id="emergency-overlay"]');
        if (overlays.length > 1) {
            console.log(`Encontrados ${overlays.length} overlays. Eliminando duplicados...`);
            for (let i = 1; i < overlays.length; i++) {
                if (overlays[i] && overlays[i].parentNode) {
                    overlays[i].parentNode.removeChild(overlays[i]);
                }
            }
        }
        
        console.log('Solución definitiva aplicada correctamente');
    }
    
    // Ejecutar la función de corrección
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixSidebar);
    } else {
        fixSidebar();
    }
    
    // También ejecutar después de que todo haya cargado para asegurar
    window.addEventListener('load', () => {
        setTimeout(fixSidebar, 500);
    });
    
    // Ejecutar inmediatamente
    setTimeout(fixSidebar, 0);
})();
