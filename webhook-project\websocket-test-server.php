<?php
/**
 * Servidor WebSocket simple para pruebas
 * 
 * Este script simula un servidor WebSocket para pruebas locales
 * sin necesidad de instalar Ratchet.
 */

require_once __DIR__ . '/src/WebhookHandler.php';
require_once __DIR__ . '/src/Logger.php';

// Crear instancias
$webhookHandler = new WebhookHandler();
$logger = new Logger();

// Simular recepción de eventos
$eventos = [
    [
        'tipo_evento' => 'pago_completado',
        'id_transaccion' => 'pay-' . rand(1000, 9999),
        'monto' => 100.50,
        'datos_adicionales' => [
            'cliente' => 'Cliente Prueba',
            'metodo_pago' => 'tarjeta'
        ]
    ],
    [
        'tipo_evento' => 'usuario_registrado',
        'id_usuario' => 'usr-' . rand(1000, 9999),
        'email' => '<EMAIL>',
        'datos_adicionales' => [
            'nombre' => 'Usuario Prueba',
            'fecha_registro' => date('Y-m-d H:i:s')
        ]
    ],
    [
        'tipo_evento' => 'producto_actualizado',
        'id_producto' => 'prod-' . rand(1000, 9999),
        'nombre' => 'Producto Prueba',
        'datos_adicionales' => [
            'precio' => 49.99,
            'stock' => 100
        ]
    ]
];

// Procesar cada evento
echo "=== SIMULACIÓN DE SERVIDOR WEBSOCKET ===\n\n";
echo "Procesando eventos de prueba...\n\n";

foreach ($eventos as $evento) {
    echo "Recibido evento: " . $evento['tipo_evento'] . "\n";
    echo "Datos: " . json_encode($evento, JSON_PRETTY_PRINT) . "\n";
    
    // Registrar evento
    $logger->registrarEvento('websocket_simulado', $evento);
    
    try {
        // Procesar evento
        $resultado = $webhookHandler->procesarEvento($evento['tipo_evento'], $evento);
        
        echo "Resultado: " . json_encode($resultado, JSON_PRETTY_PRINT) . "\n";
        echo "Evento procesado exitosamente\n\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n\n";
        
        // Registrar error
        $logger->registrarEvento('error', [
            'mensaje' => $e->getMessage(),
            'tipo_evento' => $evento['tipo_evento']
        ]);
    }
}

echo "=== SIMULACIÓN COMPLETADA ===\n";
echo "Revisa la carpeta 'logs/' para ver los registros generados.\n";
echo "Para probar con el cliente WebSocket real, necesitas instalar Ratchet:\n";
echo "1. Instala Composer desde https://getcomposer.org/\n";
echo "2. Ejecuta 'composer install' en la carpeta del proyecto\n";
echo "3. Ejecuta 'php start-websocket-server.php' para iniciar el servidor\n";
echo "4. Abre 'public/websocket-client.html' en tu navegador\n";
