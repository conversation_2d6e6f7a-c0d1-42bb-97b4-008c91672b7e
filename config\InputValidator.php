<?php
/**
 * InputValidator - Clase para validación y sanitización de entradas
 * Proporciona métodos seguros para validar y limpiar datos de entrada
 */

class InputValidator {
    
    /**
     * Sanitiza una cadena de texto
     */
    public static function sanitizeString($input, $maxLength = null) {
        if (!is_string($input)) {
            return '';
        }
        
        // Eliminar espacios en blanco al inicio y final
        $input = trim($input);
        
        // Convertir caracteres especiales a entidades HTML
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        // Limitar longitud si se especifica
        if ($maxLength && strlen($input) > $maxLength) {
            $input = substr($input, 0, $maxLength);
        }
        
        return $input;
    }
    
    /**
     * Valida y sanitiza un email
     */
    public static function validateEmail($email) {
        $email = trim($email);
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        // Verificar longitud máxima
        if (strlen($email) > 254) {
            return false;
        }
        
        // Verificar dominios sospechosos (opcional)
        $suspiciousDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com'];
        $domain = substr(strrchr($email, "@"), 1);
        
        if (in_array(strtolower($domain), $suspiciousDomains)) {
            return false;
        }
        
        return filter_var($email, FILTER_SANITIZE_EMAIL);
    }
    
    /**
     * Valida un RUT chileno
     */
    public static function validateRUT($rut) {
        $rut = preg_replace('/[^0-9kK]/', '', $rut);
        $rut = strtoupper($rut);
        
        if (strlen($rut) < 8 || strlen($rut) > 9) {
            return false;
        }
        
        $dv = substr($rut, -1);
        $numero = substr($rut, 0, -1);
        
        // Calcular dígito verificador
        $suma = 0;
        $multiplicador = 2;
        
        for ($i = strlen($numero) - 1; $i >= 0; $i--) {
            $suma += $numero[$i] * $multiplicador;
            $multiplicador = $multiplicador == 7 ? 2 : $multiplicador + 1;
        }
        
        $resto = $suma % 11;
        $dvCalculado = 11 - $resto;
        
        if ($dvCalculado == 11) {
            $dvCalculado = '0';
        } elseif ($dvCalculado == 10) {
            $dvCalculado = 'K';
        } else {
            $dvCalculado = (string)$dvCalculado;
        }
        
        return $dv === $dvCalculado ? $rut : false;
    }
    
    /**
     * Valida un teléfono chileno
     */
    public static function validatePhone($phone) {
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Teléfono móvil: 8 dígitos que empiecen con 9
        if (strlen($phone) == 8 && substr($phone, 0, 1) == '9') {
            return $phone;
        }
        
        // Teléfono fijo: 9 dígitos
        if (strlen($phone) == 9) {
            return $phone;
        }
        
        return false;
    }
    
    /**
     * Valida una fecha en formato DD/MM/YYYY
     */
    public static function validateDate($date) {
        if (!preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $date, $matches)) {
            return false;
        }
        
        $day = (int)$matches[1];
        $month = (int)$matches[2];
        $year = (int)$matches[3];
        
        if (!checkdate($month, $day, $year)) {
            return false;
        }
        
        // Verificar que la fecha no sea futura
        $inputDate = new DateTime("$year-$month-$day");
        $today = new DateTime();
        
        if ($inputDate > $today) {
            return false;
        }
        
        // Verificar edad mínima (18 años)
        $minDate = new DateTime();
        $minDate->sub(new DateInterval('P18Y'));
        
        if ($inputDate > $minDate) {
            return false;
        }
        
        return "$year-$month-$day";
    }
    
    /**
     * Valida una contraseña segura
     */
    public static function validatePassword($password) {
        // Longitud mínima de 12 caracteres (mejorado desde 8)
        if (strlen($password) < 12) {
            return ['valid' => false, 'message' => 'La contraseña debe tener al menos 12 caracteres'];
        }
        
        // Máximo 128 caracteres para evitar ataques DoS
        if (strlen($password) > 128) {
            return ['valid' => false, 'message' => 'La contraseña no puede exceder 128 caracteres'];
        }
        
        // Al menos una letra minúscula
        if (!preg_match('/[a-z]/', $password)) {
            return ['valid' => false, 'message' => 'La contraseña debe contener al menos una letra minúscula'];
        }
        
        // Al menos una letra mayúscula
        if (!preg_match('/[A-Z]/', $password)) {
            return ['valid' => false, 'message' => 'La contraseña debe contener al menos una letra mayúscula'];
        }
        
        // Al menos un número
        if (!preg_match('/[0-9]/', $password)) {
            return ['valid' => false, 'message' => 'La contraseña debe contener al menos un número'];
        }
        
        // Al menos un carácter especial
        if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
            return ['valid' => false, 'message' => 'La contraseña debe contener al menos un carácter especial'];
        }
        
        // Verificar que no contenga patrones comunes
        $commonPatterns = [
            '/(.)\1{3,}/', // 4 o más caracteres repetidos
            '/123456/', '/password/', '/qwerty/', '/admin/', '/user/',
            '/villarrica/', '/click/', '/chile/'
        ];
        
        foreach ($commonPatterns as $pattern) {
            if (preg_match($pattern, strtolower($password))) {
                return ['valid' => false, 'message' => 'La contraseña contiene patrones comunes no permitidos'];
            }
        }
        
        return ['valid' => true, 'message' => 'Contraseña válida'];
    }
    
    /**
     * Sanitiza un nombre de usuario
     */
    public static function validateUsername($username) {
        $username = trim($username);
        
        // Longitud entre 3 y 30 caracteres
        if (strlen($username) < 3 || strlen($username) > 30) {
            return false;
        }
        
        // Solo letras, números y guiones bajos
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            return false;
        }
        
        // No puede empezar con número
        if (is_numeric(substr($username, 0, 1))) {
            return false;
        }
        
        // Palabras reservadas
        $reserved = ['admin', 'root', 'user', 'test', 'guest', 'null', 'undefined'];
        if (in_array(strtolower($username), $reserved)) {
            return false;
        }
        
        return strtolower($username);
    }
    
    /**
     * Previene ataques XSS
     */
    public static function preventXSS($input) {
        if (is_array($input)) {
            return array_map([self::class, 'preventXSS'], $input);
        }
        
        return htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * Valida que una cadena no contenga SQL injection
     */
    public static function detectSQLInjection($input) {
        $sqlPatterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b|\bCREATE\b|\bALTER\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/[\'";]/',
            '/--/',
            '/\/\*.*\*\//',
            '/\bxp_\w+/i'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
}
?>
