# Documentación del Sistema de Login - Villarrica Click

## Introducción

Este documento describe el nuevo sistema de autenticación y gestión de sesiones implementado para Villarrica Click. El sistema ha sido rediseñado desde cero con un enfoque en:

- **Seguridad**: Protección contra múltiples vectores de ataque.
- **Mantenibilidad**: Código modular y bien documentado.
- **Experiencia de usuario**: Interfaz moderna y responsive.
- **Flexibilidad**: Fácil de extender con nuevas funcionalidades.

## Arquitectura del Sistema

El sistema está compuesto por los siguientes componentes principales:

### 1. Servicios Core

- **AuthService**: Servicio principal para la autenticación de usuarios.
- **SecurityService**: Gestión de aspectos de seguridad (CSRF, sanitización, etc.).
- **SessionManager**: Gestión optimizada de sesiones.
- **SessionTracker**: Seguimiento de estadísticas de sesiones.

### 2. Controladores

- **LoginHandler**: Procesa las solicitudes de login/logout.

### 3. Vistas

- **login.php**: Página de inicio de sesión con diseño moderno.
- **logout.php**: Página para cerrar sesión.

### 4. Scripts de Procesamiento

- **process_login.php**: Procesa las solicitudes de inicio de sesión.
- **process_logout.php**: Procesa las solicitudes de cierre de sesión.

## Flujo de Autenticación

1. El usuario accede a `login.php`
2. Si ya está autenticado, se redirige a `tienda_adm.php`
3. Si no está autenticado, se muestra el formulario de login
4. Al enviar el formulario, los datos se procesan en `process_login.php`
5. `LoginHandler` utiliza `AuthService` para verificar las credenciales
6. Si son válidas, `SessionManager` crea una sesión de usuario
7. El usuario es redirigido a la página apropiada

## Características de Seguridad

### Protección contra Ataques CSRF

Cada formulario incluye un token CSRF generado aleatoriamente:

```php
$csrf_token = $securityService->generateCsrfToken();
```

Este token se valida en cada solicitud POST:

```php
if (!$securityService->validateCsrfToken($csrf_token)) {
    throw new \Exception('security_violation');
}
```

### Protección contra Ataques de Fuerza Bruta

El sistema implementa bloqueo temporal de cuentas después de múltiples intentos fallidos:

```php
if ($this->isAccountLocked($username)) {
    throw new \Exception('account_locked');
}
```

### Gestión Segura de Sesiones

- Regeneración aleatoria de IDs para prevenir ataques de "session fixation"
- Validación de IP y User-Agent para prevenir "session hijacking"
- Timeout por inactividad
- Configuración segura de cookies (httpOnly, Secure, SameSite)

### Almacenamiento Seguro de Contraseñas

Las contraseñas se almacenan utilizando bcrypt:

```php
$hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
```

### Protección XSS

Todos los datos de salida son sanitizados:

```php
echo $securityService->sanitizeInput($error_message);
```

### Headers de Seguridad

Se establecen headers de seguridad HTTP:

```php
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("X-Content-Type-Options: nosniff");
```

## Funcionalidad "Recordarme"

El sistema implementa la funcionalidad "Recordarme" mediante cookies seguras:

1. Al iniciar sesión, si el usuario marca "Recordarme", se genera un token único
2. Este token se almacena en la base de datos y en una cookie segura
3. En futuras visitas, el sistema valida el token automáticamente
4. Si es válido, se crea una sesión sin necesidad de login

## Monitoreo y Logging

El sistema registra:

- Intentos de inicio de sesión (exitosos y fallidos)
- Creación y destrucción de sesiones
- Actividad sospechosa
- Páginas visitadas y tiempo en cada página

## Escalabilidad

El sistema está diseñado para ser fácilmente extendido con:

- Autenticación de dos factores (2FA)
- Autenticación social (Google, Facebook)
- Integración con servicios SSO

## Requisitos de Base de Datos

El sistema utiliza las siguientes tablas:

- `users`: Almacena datos de usuarios
- `auth_tokens`: Almacena tokens para "Recordarme" y reseteo de contraseñas
- `login_attempts`: Registra intentos de inicio de sesión
- `user_sessions`: Registra sesiones de usuario
- `session_page_views`: Registra páginas visitadas en cada sesión

## Actualización de Esquema de Base de Datos

Para implementar este sistema, se requiere crear o modificar las siguientes tablas:

```sql
-- Tabla para almacenar tokens de autenticación (recordarme, reset contraseña)
CREATE TABLE IF NOT EXISTS auth_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    selector VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires DATETIME NOT NULL,
    type ENUM('remember', 'reset', 'activation') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (selector),
    INDEX (user_id),
    INDEX (expires),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Guía de Mantenimiento

### Modificación de la Política de Contraseñas

Para modificar los requisitos de contraseñas, editar las constantes en `AuthService.php`:

```php
const PASSWORD_MIN_LENGTH = 8;
```

Y modificar la función `validatePasswordStrength()` para ajustar las reglas.

### Cambio del Tiempo de Expiración de Sesión

Modificar la constante `SESSION_LIFETIME` en `config.php`:

```php
define('SESSION_LIFETIME', 86400);  // 24 horas en segundos
```

### Configuración de Headers de Seguridad

Modificar el método `setSecurityHeaders()` en `SecurityService.php` para ajustar la política CSP y otros headers.

## Consideraciones para Despliegue

- Asegurar que la conexión sea HTTPS
- Configurar correctamente el dominio para cookies
- Ajustar el tiempo de expiración de sesión según las necesidades
- Modificar las rutas base según el entorno de despliegue

## Pruebas Recomendadas

1. Iniciar sesión con credenciales válidas
2. Intentar iniciar sesión con credenciales inválidas
3. Verificar bloqueo después de múltiples intentos fallidos
4. Probar la funcionalidad "Recordarme"
5. Verificar la regeneración de ID de sesión
6. Verificar el cierre de sesión
7. Probar la protección CSRF modificando el token
8. Verificar la redirección después del login
9. Comprobar la persistencia de sesión entre páginas
10. Probar el timeout por inactividad