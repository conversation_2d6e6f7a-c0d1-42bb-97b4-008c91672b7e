<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/session_config.php';

// Verificar que el usuario es administrador
$sessionManager = SessionManager::getInstance();
$sessionManager->requireAdmin();

// Obtener período de tiempo para las estadísticas
$period = isset($_GET['period']) ? (int)$_GET['period'] : 30;
$valid_periods = [7, 14, 30, 90, 180, 365];
if (!in_array($period, $valid_periods)) {
    $period = 30; // Valor por defecto
}

// Obtener estadísticas
$site_stats = $sessionManager->getSiteStats($period);
$most_visited = $sessionManager->getMostVisitedPages(20);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estadísticas de Sesiones - Panel de Administración</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #6366f1;
            --secondary: #a855f7;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
        }
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
        }
        .card-header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-top-left-radius: 10px !important;
            border-top-right-radius: 10px !important;
            font-weight: 600;
        }
        .stats-icon {
            background: rgba(255, 255, 255, 0.2);
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        .stats-icon i {
            font-size: 1.5rem;
            color: white;
        }
        .stats-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0;
            line-height: 1.2;
        }
        .stats-label {
            font-size: 0.875rem;
            color: #64748b;
            margin-bottom: 0;
        }
        .table-responsive {
            border-radius: 10px;
            background-color: white;
            overflow: hidden;
        }
        .table {
            margin-bottom: 0;
        }
        .table thead th {
            background-color: #f1f5f9;
            border-bottom: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            color: #64748b;
        }
        .table tbody tr:last-child td {
            border-bottom: none;
        }
        .chart-container {
            height: 300px;
            width: 100%;
        }
        .nav-pills .nav-link.active {
            background-color: var(--primary);
        }
        .nav-pills .nav-link {
            color: #64748b;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
        }
        .nav-pills .nav-link:hover:not(.active) {
            background-color: #f1f5f9;
        }
        .summary-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .summary-card-header {
            padding: 1rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .summary-card-body {
            padding: 1rem;
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-3"><i class="fas fa-chart-bar me-2"></i> Estadísticas de Sesiones</h1>
                <p class="text-muted">Análisis de actividad de usuarios en la plataforma</p>
            </div>
        </div>
        
        <!-- Filtros de período -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="get" class="d-flex align-items-center">
                            <label for="period" class="me-3">Ver estadísticas de los últimos:</label>
                            <select name="period" id="period" class="form-select me-3" style="width: auto;" onchange="this.form.submit()">
                                <option value="7" <?= $period == 7 ? 'selected' : '' ?>>7 días</option>
                                <option value="14" <?= $period == 14 ? 'selected' : '' ?>>14 días</option>
                                <option value="30" <?= $period == 30 ? 'selected' : '' ?>>30 días</option>
                                <option value="90" <?= $period == 90 ? 'selected' : '' ?>>3 meses</option>
                                <option value="180" <?= $period == 180 ? 'selected' : '' ?>>6 meses</option>
                                <option value="365" <?= $period == 365 ? 'selected' : '' ?>>1 año</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Actualizar</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tarjetas de resumen -->
        <div class="row g-4 mb-4">
            <div class="col-md-6 col-lg-3">
                <div class="card h-100">
                    <div class="card-body d-flex flex-column align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="stats-value"><?= number_format($site_stats['total_users'] ?? 0) ?></h3>
                        <p class="stats-label">Usuarios Totales</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="card h-100">
                    <div class="card-body d-flex flex-column align-items-center">
                        <div class="stats-icon" style="background: var(--secondary);">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h3 class="stats-value"><?= number_format($site_stats['total_sessions'] ?? 0) ?></h3>
                        <p class="stats-label">Sesiones Totales</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="card h-100">
                    <div class="card-body d-flex flex-column align-items-center">
                        <div class="stats-icon" style="background: var(--success);">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="stats-value"><?= number_format($site_stats['total_pageviews'] ?? 0) ?></h3>
                        <p class="stats-label">Vistas de Página</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 col-lg-3">
                <div class="card h-100">
                    <div class="card-body d-flex flex-column align-items-center">
                        <div class="stats-icon" style="background: var(--warning);">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="stats-value"><?= number_format($site_stats['avg_pages_per_session'] ?? 0, 1) ?></h3>
                        <p class="stats-label">Páginas por Sesión</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Gráficos -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Dispositivos</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="deviceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Navegadores</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="browserChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Páginas más visitadas -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Páginas más visitadas</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>URL</th>
                                        <th>Visitas</th>
                                        <th>Tiempo medio (s)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($most_visited)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center">No hay datos disponibles</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php $i = 1; foreach ($most_visited as $page): ?>
                                            <tr>
                                                <td><?= $i++ ?></td>
                                                <td><?= htmlspecialchars($page['page_url']) ?></td>
                                                <td><?= number_format($page['visit_count']) ?></td>
                                                <td><?= number_format($page['avg_time_spent'] ?? 0, 1) ?> s</td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Datos para los gráficos (se pueden extraer desde PHP)
        const deviceData = {
            labels: ['Desktop', 'Mobile', 'Tablet'],
            datasets: [{
                data: [
                    <?= $site_stats['desktop_percentage'] ?? 60 ?>,
                    <?= $site_stats['mobile_percentage'] ?? 30 ?>,
                    <?= $site_stats['tablet_percentage'] ?? 10 ?>
                ],
                backgroundColor: [
                    'rgba(99, 102, 241, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(16, 185, 129, 0.8)'
                ],
                borderColor: [
                    'rgba(99, 102, 241, 1)',
                    'rgba(168, 85, 247, 1)',
                    'rgba(16, 185, 129, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Datos de navegadores (ejemplo)
        const browserData = {
            labels: ['Chrome', 'Firefox', 'Safari', 'Edge', 'Otros'],
            datasets: [{
                data: [65, 15, 10, 8, 2],
                backgroundColor: [
                    'rgba(99, 102, 241, 0.8)',
                    'rgba(168, 85, 247, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)'
                ],
                borderColor: [
                    'rgba(99, 102, 241, 1)',
                    'rgba(168, 85, 247, 1)',
                    'rgba(16, 185, 129, 1)',
                    'rgba(245, 158, 11, 1)',
                    'rgba(239, 68, 68, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Configuración para gráficos de pastel
        const pieConfig = {
            type: 'pie',
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        };

        // Inicializar gráficos cuando la página carga
        document.addEventListener('DOMContentLoaded', function () {
            // Gráfico de dispositivos
            const deviceChart = new Chart(
                document.getElementById('deviceChart'),
                {
                    ...pieConfig,
                    data: deviceData
                }
            );
            
            // Gráfico de navegadores
            const browserChart = new Chart(
                document.getElementById('browserChart'),
                {
                    ...pieConfig,
                    data: browserData
                }
            );
        });
    </script>

    <!-- Incluir footer -->
    <div class="container-fluid mt-5">
        <div class="row">
            <div class="col-12 text-center">
                <p class="text-muted">© <?= date('Y') ?> Villarrica Click. Todos los derechos reservados.</p>
            </div>
        </div>
    </div>
</body>
</html>