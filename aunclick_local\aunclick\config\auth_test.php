<?php
require_once 'session_config.php';
require_once 'config.php';

$sessionManager = SessionManager::getInstance();

// Función para registrar errores detallados con más información
function logAuthError($error, $browser_info) {
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0777, true);
    }
    
    $logFile = $logDir . '/auth_errors_' . date('Y-m-d') . '.log';
    $logMessage = date('Y-m-d H:i:s') . " | " . 
                 "Error: " . $error . " | " . 
                 "Browser: " . $browser_info['browser'] . " | " . 
                 "Version: " . $browser_info['version'] . " | " . 
                 "Mobile: " . ($browser_info['isMobile'] ? 'Yes' : 'No') . " | " . 
                 "IP: " . $_SERVER['REMOTE_ADDR'] . " | " . 
                 "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . " | " .
                 "Session ID: " . session_id() . "\n";
    
    error_log($logMessage);
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Mejorar detección de navegador con más detalles
function getBrowserDetails() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $browser = 'Unknown';
    $version = '';
    $isMobile = false;
    
    // Detectar si es móvil con un patrón más completo
    if (preg_match('/(android|bb\d+|meego|mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino)/i', $userAgent)) {
        $isMobile = true;
    }
    
    // Mejorar la detección de Edge y otros navegadores
    if (strpos($userAgent, 'Edg/') !== false) {
        $browser = 'Edge';
        preg_match('/Edg\/([\d.]+)/', $userAgent, $matches);
        $version = $matches[1] ?? '';
        error_log("Edge browser detected - Version: " . $version . " - Mobile: " . ($isMobile ? 'Yes' : 'No'));
    } elseif (strpos($userAgent, 'Chrome') !== false) {
        $browser = 'Chrome';
        preg_match('/Chrome\/([\d.]+)/', $userAgent, $matches);
        $version = $matches[1] ?? '';
    } elseif (strpos($userAgent, 'Firefox') !== false) {
        $browser = 'Firefox';
        preg_match('/Firefox\/([\d.]+)/', $userAgent, $matches);
        $version = $matches[1] ?? '';
    } elseif (strpos($userAgent, 'Safari') !== false) {
        $browser = 'Safari';
        preg_match('/Version\/([\d.]+)/', $userAgent, $matches);
        $version = $matches[1] ?? '';
    }
    
    return [
        'browser' => $browser,
        'version' => $version,
        'isMobile' => $isMobile,
        'userAgent' => $userAgent
    ];
}

$browser_info = getBrowserDetails();
error_log("Browser detection - Type: " . $browser_info['browser'] . ", Version: " . $browser_info['version'] . ", Mobile: " . ($browser_info['isMobile'] ? 'Yes' : 'No'));

// Verificar el estado actual de la sesión
error_log("Estado actual de la sesión - ID: " . session_id());
error_log("Cookie de sesión presente: " . (isset($_COOKIE[session_name()]) ? 'Sí' : 'No'));

$error_message = '';
$debug_info = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    try {
        // Verificar si las cookies están habilitadas
        setcookie("test_cookie", "test", time() + 3600, '/');
        if (count($_COOKIE) == 0) {
            throw new Exception("Las cookies están deshabilitadas en su navegador");
        }
        
        // Intentar autenticación
        $stmt = $conn->prepare("SELECT id, username, password, role FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception("Usuario no encontrado");
        }
        
        $user = $result->fetch_assoc();
        
        if (!password_verify($password, $user['password'])) {
            throw new Exception("Contraseña incorrecta");
        }
        
        // Si llegamos aquí, la autenticación fue exitosa
        $sessionManager->createSession($user['id'], $user['role'], [
            'username' => $user['username'],
            'browser' => $browser_info['browser'],
            'is_mobile' => $browser_info['isMobile'],
            'logged_in' => true
        ]);
        
        error_log("Sesión creada exitosamente - Nuevo ID: " . session_id());
        header("Location: admin.php");
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        logAuthError($error_message, $browser_info);
        
        // Recopilar información de depuración
        $debug_info = [
            'cookies_enabled' => isset($_COOKIE),
            'session_status' => session_status(),
            'session_id' => session_id(),
            'https' => isset($_SERVER['HTTPS']),
            'time' => time(),
            'error' => $error_message,
            'browser' => $browser_info['browser'],
            'is_mobile' => $browser_info['isMobile']
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Autenticación</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error { color: red; margin: 10px 0; }
        .success { color: green; margin: 10px 0; }
        .debug-info { 
            background: #f5f5f5; 
            padding: 10px; 
            margin: 10px 0; 
            border: 1px solid #ddd; 
        }
        .browser-info {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <h1>Prueba de Autenticación</h1>
    
    <div class="browser-info">
        <h3>Información del Navegador:</h3>
        <p>Navegador detectado: <?php echo htmlspecialchars($browser_info['browser']); ?></p>
        <p>Versión: <?php echo htmlspecialchars($browser_info['version']); ?></p>
        <p>Dispositivo móvil: <?php echo $browser_info['isMobile'] ? 'Sí' : 'No'; ?></p>
    </div>

    <?php if ($error_message): ?>
        <div class="error">
            Error: <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <form method="POST" action="">
        <div>
            <label for="username">Usuario:</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div>
            <label for="password">Contraseña:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <button type="submit">Iniciar Sesión</button>
    </form>

    <?php if (!empty($debug_info)): ?>
        <div class="debug-info">
            <h3>Información de Depuración:</h3>
            <pre><?php print_r($debug_info); ?></pre>
        </div>
    <?php endif; ?>

    <script>
        // Verificar soporte de cookies y características del navegador
        function checkBrowserSupport() {
            var features = {
                cookies: navigator.cookieEnabled,
                localStorage: !!window.localStorage,
                sessionStorage: !!window.sessionStorage,
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language || navigator.userLanguage
            };

            var debugInfo = document.createElement('div');
            debugInfo.className = 'debug-info';
            debugInfo.innerHTML = '<h3>Características del Navegador:</h3>' +
                                '<p>Cookies habilitadas: ' + features.cookies + '</p>' +
                                '<p>LocalStorage disponible: ' + features.localStorage + '</p>' +
                                '<p>SessionStorage disponible: ' + features.sessionStorage + '</p>' +
                                '<p>Plataforma: ' + features.platform + '</p>' +
                                '<p>Idioma: ' + features.language + '</p>';
            document.body.appendChild(debugInfo);

            if (!features.cookies) {
                var warning = document.createElement('div');
                warning.className = 'error';
                warning.textContent = 'Las cookies están deshabilitadas. Por favor, habilítelas para continuar.';
                document.body.insertBefore(warning, document.querySelector('form'));
            }
        }

        window.onload = checkBrowserSupport;
    </script>
</body>
</html>