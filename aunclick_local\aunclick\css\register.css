/* =========================================
   ESTILOS GENERALES
   ========================================= */
/* Reseteo de estilos básicos para todos los elementos */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
}

/* Estilo del cuerpo de la página - Fondo gris claro y centrado de contenido */
body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 10px;
}

/* =========================================
   PANEL PRINCIPAL DE REGISTRO
   ========================================= */
/* Contenedor principal del formulario de registro */
.register-panel {
    width: 100%;
    max-width: 420px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 18px;
    /* Evitar cambios de tamaño o posición */
    min-height: 500px;
    position: relative;
}

/* Encabezado del panel con título y subtítulo */
.register-header {
    text-align: center;
    margin-bottom: 8px;
}

/* Título principal del formulario */
.register-header h1 {
    color: #6a1b9a;
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 3px;
}

/* Subtítulo descriptivo del formulario */
.register-header p {
    color: #666;
    font-size: 13px;
}

/* =========================================
   BARRA DE PASOS DEL REGISTRO
   ========================================= */
/* Contenedor de los pasos de registro con línea horizontal */
.register-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    position: relative;
}

/* Línea horizontal que conecta los pasos */
.register-steps::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

/* Cada paso individual en la barra de progreso */
.step {
    width: 22%;
    position: relative;
    z-index: 2;
    text-align: center;
}

/* Círculo numerado de cada paso */
.step-number {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 3px;
    font-weight: 600;
    font-size: 11px;
}

/* Estilo para el paso activo */
.step.active .step-number {
    background-color: #6a1b9a;
}

/* Título de cada paso */
.step-title {
    font-size: 11px;
    color: #666;
    font-weight: 500;
}

/* Estilo para el título del paso activo */
.step.active .step-title {
    color: #6a1b9a;
    font-weight: 600;
}

/* =========================================
   ELEMENTOS DEL FORMULARIO
   ========================================= */
/* Grupo de elementos del formulario */
.form-group {
    margin-bottom: 10px; /* Reducido para mayor compacidad */
}

/* Estilos generales para las etiquetas del formulario */
.form-group label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    font-size: 12px !important; /* Ajustado al mismo tamaño que Nombres y Apellidos */
}

/* Ajuste específico para el label de fecha de nacimiento */
.fecha-label {
    font-size: 12px !important;
    font-weight: 500;
}

/* Campos de entrada del formulario */
.form-control {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.3s;
}

/* Estilo al enfocar un campo */
.form-control:focus {
    border-color: #6a1b9a;
    outline: none;
    box-shadow: 0 0 0 2px rgba(106, 27, 154, 0.2);
}

/* Estilos para campos con error */
.form-control.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* Estilos para campos válidos */
.form-control.valid {
    border-color: #28a745;
    background-color: #f8fff8;
}

/* Fila con dos columnas */
.form-row {
    display: flex;
    gap: 8px;
    margin-bottom: 10px; /* Reducido el espacio entre filas */
}

/* Columna individual */
.form-col {
    flex: 1;
}

/* Ajuste específico para la fila de RUT y Fecha de Nacimiento */
.form-row .form-col {
    flex: 1 1 50% !important;
    width: 50% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
}

/* Ajuste específico para el input de RUT */
#rut {
    width: 100% !important;
    height: 35px !important;
    box-sizing: border-box !important;
}

/* Ajuste para el contenedor de fecha de nacimiento */
.date-input-container {
    display: flex !important;
    width: 100% !important;
    height: 35px !important; /* Asegurar misma altura que el input de RUT */
}

#fechaNacimiento {
    width: 100% !important;
    flex: 1 !important;
    height: 35px !important; /* Asegurar misma altura que el input de RUT */
    box-sizing: border-box !important;
}

/* Asegurar que el input de RUT tenga la misma altura */
#rut {
    height: 35px !important;
    box-sizing: border-box !important;
}

/* Asegurar que ambos contenedores tengan el mismo ancho */
.form-row .form-col {
    flex: 1 1 50% !important;
    width: 50% !important;
}

/* Asegurar que los grupos de formulario tengan el mismo ancho */
.form-group {
    width: 100% !important;
}

/* =========================================
   CAMPOS DE TELÉFONO
   ========================================= */
/* Contenedor para campo de teléfono con prefijo */
.phone-input {
    display: flex;
    align-items: center;
}

/* Prefijo del teléfono (+56) */
.phone-prefix {
    background-color: #f5f5f5;
    padding: 8px 6px;
    border: 1px solid #ddd;
    border-right: none;
    border-radius: 4px 0 0 4px;
    color: #333;
    font-weight: 500;
    font-size: 13px;
}

/* Campo de número de teléfono */
.phone-number {
    flex: 1;
    border-radius: 0 4px 4px 0;
}

/* Estilos para validación de teléfono */
.phone-number.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

.phone-number.valid {
    border-color: #28a745;
    background-color: #f8fff8;
}

/* Texto de ayuda para campos */
.form-hint {
    font-size: 11px;
    color: #666;
    margin-top: 4px;
    line-height: 1.3;
}

/* =========================================
   BOTONES Y ACCIONES
   ========================================= */
/* Contenedor de botones */
.form-actions {
    margin-top: 24px;
    margin-bottom: 24px;
    text-align: center; /* Centrar el botón */
}

/* Estilo base para botones */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-size: 13px;
}

/* Botón de siguiente paso */
.btn-next {
    background-color: #6a1b9a;
    color: white;
    width: 120px; /* Ancho fijo */
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-size: 13px;
}

/* Efecto hover para botón siguiente */
.btn-next:hover {
    background-color: #5c1786;
    transform: translateY(-1px);
}

/* Estilo para botón deshabilitado */
.btn-next:disabled,
.btn-next.btn-disabled {
    background-color: #cccccc;
    color: #888888;
    cursor: not-allowed;
    transform: none;
    opacity: 0.7;
}

.btn-next:disabled:hover,
.btn-next.btn-disabled:hover {
    background-color: #cccccc;
    transform: none;
}

/* =========================================
   ENLACES ADICIONALES
   ========================================= */
/* Enlace para iniciar sesión */
.login-link {
    text-align: center;
    margin-top: 6px;
    margin-bottom: 6px;
    color: #666;
    font-size: 12px;
}

/* Estilo del enlace de inicio de sesión */
.login-link a {
    color: #6a1b9a;
    font-weight: 600;
    text-decoration: none;
}

/* Efecto hover para enlace de inicio de sesión */
.login-link a:hover {
    text-decoration: underline;
}

/* Enlace a política de privacidad */
.privacy-link {
    text-align: center;
    margin-top: 8px;
    margin-bottom: 12px;
    font-size: 12px;
}

/* Estilo del enlace de privacidad */
.privacy-link a {
    color: #6a1b9a;
    text-decoration: underline;
    font-weight: bold;
    cursor: pointer;
}

/* Efecto hover para enlace de privacidad */
.privacy-link a:hover {
    color: #5c1786;
}

/* =========================================
   ELEMENTOS DE FORMULARIO ESPECIALES
   ========================================= */
/* Estilo para campos select con flecha personalizada */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 25px;
}

/* Grupo de botones de radio */
.radio-group {
    display: flex;
    gap: 12px;
}

/* Opción individual de radio */
.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 12px;
}

/* Input de radio */
.radio-option input {
    margin-right: 4px;
}

/* =========================================
   CAMPO DE FECHA DE NACIMIENTO
   ========================================= */
/* Contenedor del campo de fecha */
.date-input-container {
    display: flex;
    align-items: center;
    width: 100%;
}

/* Campo de fecha de nacimiento */
#fechaNacimiento {
    flex: 1;
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 13px;
}

#fechaNacimiento::placeholder {
    color: #999;
    font-size: 13px;
}

#fechaNacimiento.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* =========================================
   PASOS DEL FORMULARIO
   ========================================= */
/* Paso oculto del formulario */
.form-step {
    display: none;
}

/* Paso activo del formulario */
.form-step.active {
    display: block;
}

/* =========================================
   PASO 5: PAGO Y FACTURACIÓN
   ========================================= */
.payment-title {
    color: #6a1b9a;
    font-size: 18px;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.payment-info-container {
    background-color: #f9f5fd;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e6d8ed;
}

.payment-info-section {
    margin-bottom: 15px;
}

.payment-info-section h4 {
    color: #6a1b9a;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
    border-bottom: 1px solid #e6d8ed;
    padding-bottom: 5px;
}

.payment-info-section p {
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 10px;
}

.bank-details {
    background-color: white;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e0e0e0;
}

.bank-detail-item {
    display: flex;
    margin-bottom: 8px;
}

.bank-detail-label {
    font-weight: 600;
    width: 140px;
    color: #555;
    font-size: 13px;
}

.bank-detail-value {
    color: #333;
    font-size: 13px;
}

.billing-info-container {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
}

.billing-info-container h4 {
    color: #6a1b9a;
    font-size: 16px;
    margin-bottom: 15px;
    font-weight: 600;
    border-bottom: 1px solid #e6d8ed;
    padding-bottom: 5px;
}

/* Estilos para responsive */
@media (max-width: 767px) {
    .payment-title {
        font-size: 16px;
    }

    .payment-info-section h4,
    .billing-info-container h4 {
        font-size: 14px;
    }

    .payment-info-section p {
        font-size: 12px;
    }

    .bank-detail-label,
    .bank-detail-value {
        font-size: 12px;
    }

    .bank-detail-label {
        width: 120px;
    }
}



/* =========================================
   POPUP DE BIENVENIDA
   ========================================= */
.welcome-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.welcome-popup {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: popupFadeIn 0.3s ease;
    overflow: hidden;
}

.welcome-popup-header {
    padding: 15px 20px;
    background-color: #6a1b9a;
    color: white;
    text-align: center;
    position: relative;
}

.welcome-popup-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.welcome-popup-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.welcome-popup-close:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.welcome-popup-content {
    padding: 15px;
    overflow-y: auto;
}

.welcome-message {
    margin-bottom: 15px;
}

.welcome-message p {
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    margin-bottom: 10px;
}

.justified-text {
    text-align: justify;
}

.welcome-upgrade-options {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.welcome-upgrade-option {
    flex: 1;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    text-align: center;
}

.welcome-upgrade-option.premium {
    border-color: #d9c2e5;
    background-color: #f9f5fd;
}

.welcome-crown {
    font-size: 18px;
    margin-bottom: 3px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
}

.welcome-upgrade-option h4 {
    color: #6a1b9a;
    font-size: 14px;
    margin: 0 0 5px 0;
    display: inline-block;
    vertical-align: middle;
}

.welcome-upgrade-option p {
    font-size: 12px;
    margin: 0 0 5px 0;
    line-height: 1.3;
}

.welcome-price {
    font-weight: 700;
    color: #333;
    font-size: 12px;
}

.welcome-popup-footer {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #e0e0e0;
}

.welcome-finish-btn {
    background-color: #6a1b9a;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.welcome-finish-btn:hover {
    background-color: #5c1786;
}

/* Estilos para responsive */
@media (max-width: 767px) {
    .welcome-popup-header h3 {
        font-size: 18px;
    }

    .welcome-message p {
        font-size: 13px;
    }

    .welcome-upgrade-options {
        flex-direction: column;
        gap: 10px;
    }

    .welcome-upgrade-option h4 {
        font-size: 15px;
    }

    .welcome-upgrade-option p {
        font-size: 12px;
    }
}

/* =========================================
   ELEMENTOS CON ICONOS
   ========================================= */
/* Campo con icono adicional */
.input-with-icon {
    position: relative;
    display: flex;
    align-items: center;
}

/* Icono de ayuda */
.help-icon {
    color: #ffd700;
    margin-left: 10px;
    cursor: pointer;
    font-size: 18px;
}

/* =========================================
   CAMPOS DE CONTRASEÑA
   ========================================= */
/* Contenedor para campo de contraseña con icono */
.password-field {
    position: relative;
    width: 100%;
}

/* Icono para mostrar/ocultar contraseña */
.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

/* Ajuste para campos con iconos */
.form-control {
    width: 100%;
    padding-right: 35px;
}

/* =========================================
   ESTILOS DE ERROR
   ========================================= */
/* Campo de formulario con error */
.form-control.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* Error para textarea */
textarea.error {
    border-color: #dc3545 !important;
    background-color: #fff8f8 !important;
}

/* Error para selector de categorías */
.category-selector.error {
    border-color: #dc3545 !important;
    background-color: #fff8f8 !important;
    padding: 10px !important;
    border-radius: 4px !important;
    border: 1px solid #dc3545 !important;
}

/* Opción de radio con error */
.radio-option.error {
    color: #dc3545;
}

/* Select con error */
.form-group select.error {
    border-color: #dc3545;
    background-color: #fff8f8;
}

/* =========================================
   NAVEGACIÓN ENTRE PASOS
   ========================================= */
/* Contenedor para botones de navegación (anterior y siguiente) */
.form-actions-double {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    margin-bottom: 24px;
}

/* Botón de paso anterior */
.btn-prev {
    background-color: #e0e0e0;
    color: #6a1b9a;
    border: 1px solid #6a1b9a;
}

/* Efecto hover para botón anterior */
.btn-prev:hover {
    background-color: #d5d5d5;
}

/* =========================================
   ELEMENTOS DE TEXTO Y PLACEHOLDERS
   ========================================= */
/* Texto de placeholder en campos de entrada */
.form-group input::placeholder {
    font-size: 12px;
}

/* Etiqueta pequeña para formularios */
.form-label-small {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* =========================================
   SECCIÓN DE TIPO DE NEGOCIO
   ========================================= */
/* Contenedor para la selección de tipo de negocio */
.business-type-container {
    margin-top: 15px;
    margin-bottom: -10px;
}

/* Pregunta sobre el tipo de negocio */
.business-question {
    display: block;
    margin-bottom: 15px;
    font-size: 14px;
}

/* Opciones de radio para tipo de negocio */
.radio-options {
    display: flex;
    justify-content: space-between;
    padding: 15px 10px 0 10px;
    margin-bottom: 15px;
}

/* Etiqueta para botones de radio */
.radio-label {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

/* Estilo personalizado para botones de radio */
.radio-label input[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    border: 1px solid #6a1b9a;
    border-radius: 50%;
    outline: none;
    cursor: pointer;
    margin: 0;
    position: relative;
    top: 0;
}

/* Estilo para botón de radio seleccionado */
.radio-label input[type="radio"]:checked {
    background-color: #6a1b9a;
    border: 1px solid #6a1b9a;
    box-shadow: inset 0 0 0 2px white;
}

/* Texto de la opción de radio */
.radio-text {
    font-size: 12px;
}

/* =========================================
   PASO 4: PLANES DE SUSCRIPCIÓN
   ========================================= */
/* Título de cada paso */
.step-title {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
}

/* Título principal de la sección de suscripciones */
.subscription-subtitle {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Contenedor de planes de suscripción */
.subscription-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
        "free normal"
        "premium premium";
    gap: 15px;
    margin: 0 auto 20px;
    max-width: 600px;
}

/* Tarjeta individual de plan de suscripción */
.subscription-plan {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* Efecto hover para planes de suscripción */
.subscription-plan:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Estilo para el plan seleccionado */
.subscription-plan.selected-plan {
    border: 2px solid #6a1b9a;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.2);
}

/* Encabezado del plan de suscripción */
.plan-header {
    background-color: #6a1b9a;
    color: white;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-height: 80px;
    justify-content: center;
}

/* Asignar áreas de grid a cada plan */
#plan-free-productos, #plan-free-servicios {
    grid-area: free;
}

#plan-normal-productos, #plan-normal-servicios {
    grid-area: normal;
}

#plan-premium-productos, #plan-premium-servicios {
    grid-area: premium;
}

/* Icono del plan */
.plan-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

/* Título del plan */
.plan-title {
    font-size: 18px;
    margin-bottom: 5px;
}

/* Precio del plan */
.plan-price {
    font-size: 24px;
    font-weight: 700;
    margin-top: 5px;
}

/* Período de facturación */
.price-period {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.8;
}

/* Detalles del plan */
.plan-details {
    padding: 15px;
}

/* Sección dentro de los detalles del plan */
.plan-section {
    margin-bottom: 15px;
}

/* Título de sección en detalles del plan */
.section-title {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

/* Elemento de característica del plan */
.feature-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

/* Nombre de la característica */
.feature-name {
    color: #555;
}

/* Valor de la característica */
.feature-value {
    font-weight: 500;
    color: #333;
}

/* Valor positivo (incluido) */
.feature-value.positive {
    color: #28a745;
}

/* Valor negativo (no incluido) */
.feature-value.negative {
    color: #dc3545;
}

/* =========================================
   BOTÓN DE COMPARACIÓN DE PLANES
   ========================================= */
/* Contenedor del botón de comparación */
.compare-plans-container {
    text-align: center;
    margin-bottom: 20px;
}

/* Ocultar botón de comparación en modo responsive */
@media (max-width: 767px) {
    .compare-plans-container {
        display: none;
    }
}

/* Estilo del botón para comparar planes */
.btn-comparar-planes {
    background-color: transparent;
    color: #6a1b9a;
    border: none;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;
    margin: 5px auto 15px;
    display: block;
    text-align: center;
}

/* =========================================
   AJUSTES RESPONSIVE
   ========================================= */
/* Ocultar elementos en pantallas pequeñas */
@media screen and (max-width: 768px) {
    .desktop-only {
        display: none !important;
    }
}

/* =========================================
   SELECCIÓN DE PLANES
   ========================================= */
/* Contenedor de selección de plan */
.plan-selection {
    margin-bottom: 20px;
}

/* Título de la sección de selección */
.selection-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

/* Opciones de planes disponibles */
.plan-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Opción individual de plan */
.plan-option {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    transition: all 0.3s;
}

/* Efecto hover para opción de plan */
.plan-option:hover {
    background-color: #f9f9f9;
}

/* Etiqueta de texto para opción de plan */
.plan-option-label {
    font-size: 14px;
    color: #333;
}

/* =========================================
   POPUP DE COMPARACIÓN DE PLANES
   ========================================= */
/* Overlay para el popup de comparación */
.plans-comparison-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

/* Contenedor principal del popup */
.plans-comparison-popup {
    background-color: white;
    border-radius: 8px;
    width: 100%;
    max-width: 420px; /* Mismo ancho que el formulario */
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    position: relative; /* Para posicionar el botón de cierre */
    padding: 0; /* Sin padding */
    margin: 0; /* Sin margen */
}

/* Encabezado del popup de comparación */
.comparison-popup-header {
    padding: 0;
    text-align: right;
    position: relative;
    background-color: transparent;
    height: 20px; /* Altura mínima para el botón de cierre */
}

/* Botón para cerrar el popup */
.comparison-popup-close {
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    z-index: 10;
}

/* Contenido del popup de comparación */
.comparison-popup-content {
    padding: 0 5px 5px 5px; /* Sin padding superior para eliminar espacio */
}

/* Contenedor de planes en comparación */
.comparison-plans {
    display: flex;
    flex-direction: column; /* Mostrar planes verticalmente */
    gap: 8px; /* Reducido para hacer el popup más bajo */
    overflow-x: auto;
}

/* Plan individual en la comparación */
.comparison-plan {
    flex: 1;
    width: 100%;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 5px; /* Reducido para hacer el popup más bajo */
}

/* Encabezado de cada plan en la comparación */
.comparison-plan-header {
    background-color: #6a1b9a;
    color: white;
    padding: 6px; /* Reducido aún más para hacer el popup más bajo */
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px 8px 0 0; /* Redondear solo las esquinas superiores */
}

/* Contenedor para el icono y título en el plan premium */
.comparison-plan-header .premium-header-content {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

/* Título de cada plan en la comparación */
.comparison-plan-header h4 {
    font-size: 14px; /* Reducido aún más para hacer el popup más bajo */
    margin: 5px 0 3px; /* Reducido aún más para hacer el popup más bajo */
}

/* Detalles de cada plan en la comparación */
.comparison-plan-details {
    padding: 5px; /* Reducido aún más para hacer el popup más bajo */
}

/* Sección de características en la comparación */
.comparison-section {
    margin-bottom: 4px; /* Reducido aún más para hacer el popup más bajo */
}

/* Título de sección en la comparación */
.comparison-section h5 {
    font-size: 12px; /* Reducido aún más para hacer el popup más bajo */
    color: #333;
    margin-bottom: 3px; /* Reducido aún más para hacer el popup más bajo */
    border-bottom: 1px solid #eee;
    padding-bottom: 2px; /* Reducido aún más para hacer el popup más bajo */
}

/* Elemento de característica en la comparación */
.comparison-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px; /* Reducido aún más para hacer el popup más bajo */
    font-size: 11px; /* Reducido aún más para hacer el popup más bajo */
    line-height: 1.2; /* Reducido para acercar las líneas */
}

/* Pie del popup de comparación */
.comparison-popup-footer {
    padding: 8px; /* Reducido para hacer el popup más bajo */
    text-align: center;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: center;
}

/* Botón para cerrar la comparación */
.comparison-popup-close-btn {
    background-color: #6a1b9a;
    color: white;
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    margin: 0 auto;
    min-width: 120px;
}

/* Efecto hover para el botón de cerrar */
.comparison-popup-close-btn:hover {
    background-color: #5c1786;
}

/* Plan Popup Styles */
.plan-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.plan-popup {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.plan-popup-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    text-align: center;
    position: relative;
    background-color: #6a1b9a;
    color: white;
}

.plan-popup-header h3 {
    font-size: 20px;
    color: white;
    margin: 0;
}

.plan-popup-header.premium-header {
    display: flex;
    justify-content: center;
    align-items: center;
}

.plan-popup-header .premium-header-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.plan-popup-close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
    color: white;
}

.plan-popup-content {
    padding: 15px;
}

.plan-popup-content h4 {
    font-size: 16px;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.popup-section {
    margin-bottom: 15px;
}

.popup-section h5 {
    font-size: 14px;
    color: #555;
    margin-bottom: 8px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.popup-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.plan-popup-footer {
    padding: 15px;
    text-align: center;
    border-top: 1px solid #eee;
    margin-top: 10px;
    display: flex;
    justify-content: center;
}

.plan-popup-close-btn {
    background-color: #6a1b9a;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s;
    margin: 0 auto;
    min-width: 120px;
}

.plan-popup-close-btn:hover {
    background-color: #5c1786;
}

/* Media Queries for Responsive Design */
@media (min-width: 768px) {
    .plan-options {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .plan-option {
        flex: 1 0 calc(50% - 5px);
    }
}

@media (max-width: 767px) {
    /* Mantener la misma estructura de grid en móvil */
    .subscription-plans {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        grid-template-areas:
            "free normal"
            "premium premium";
        gap: 5px;
    }

    /* Hacer los planes más compactos en móvil */
    .plan-header {
        padding: 5px 3px;
        height: auto;
    }

    .plan-icon {
        font-size: 14px;
        margin-bottom: 3px;
    }

    .plan-title {
        font-size: 10px;
        margin-bottom: 1px;
    }

    .plan-price {
        font-size: 10px;
        white-space: nowrap;
        margin-top: 1px;
    }

    .price-period {
        font-size: 7px;
        white-space: nowrap;
    }

    /* Achicar botones en responsive */
    .plan-option {
        padding: 4px;
    }

    .plan-option-label {
        font-size: 28px !important;
        font-weight: 700 !important;
        line-height: 1.2 !important;
    }

    .crown-icon {
        font-size: 28px !important;
        margin-right: 4px !important;
    }

    /* Achicar contenido de los popups */
    .plan-popup-content h4 {
        font-size: 14px;
        margin-bottom: 5px;
    }

    .plan-popup-content p {
        font-size: 12px;
        margin-bottom: 8px;
    }

    .popup-section h5 {
        font-size: 12px;
        margin-bottom: 5px;
    }

    .popup-item {
        font-size: 11px;
        padding: 2px 0;
    }

    /* Achicar el título de selección */
    .selection-title {
        font-size: 14px;
        margin-bottom: 10px;
    }

    /* Achicar el espacio del contenedor de selección */
    .plan-selection {
        padding: 10px;
        margin-top: 10px;
    }
}

@media (max-width: 767px) {
    /* Ajustar la comparación de planes en móvil */
    .comparison-plans {
        flex-direction: column;
    }

    .comparison-plan {
        margin-bottom: 15px;
    }
}

/* Estilos para pantallas muy pequeñas */
@media (max-width: 480px) {
    /* Hacer los planes más altos y el contenido más grande */
    .plan-header {
        padding: 10px 3px !important;
        min-height: 80px !important;
    }

    .plan-title:after {
        font-size: 28px !important;
        font-weight: 700 !important;
    }

    .plan-price {
        font-size: 11px !important;
    }

    .price-period {
        font-size: 8px !important;
    }

    /* Achicar aún más los botones */
    .plan-option {
        padding: 3px !important;
    }

    .plan-option-label {
        font-size: 12px !important;
        font-weight: 600 !important;
    }

    .crown-icon {
        font-size: 12px !important;
        margin-right: 2px !important;
    }

    .plan-option input[type="radio"] {
        width: 12px !important;
        height: 12px !important;
    }

    /* Ajustes para el contenido de los popups */
    .plan-popup-content h4 {
        font-size: 12px !important;
    }

    .plan-popup-content p {
        font-size: 10px !important;
    }

    .popup-section h5 {
        font-size: 10px !important;
    }

    .popup-item {
        font-size: 10px !important;
    }

    .comparison-item {
        margin-bottom: 0px !important;
        font-size: 10px !important;
    }

    .comparison-section {
        margin-bottom: 6px !important;
    }

    .comparison-section h5 {
        font-size: 11px !important;
        margin-bottom: 3px !important;
    }

    .subscription-plans {
        gap: 3px !important;
    }
}

/* Plan Popup Styles */
.plan-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.plan-popup {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    width: 90%;
    max-width: 420px; /* Mismo ancho que el formulario */
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.plan-popup-content h4 {
    color: #6a1b9a;
    margin-bottom: 10px;
    font-size: 18px;
}

.plan-popup-content p {
    margin-bottom: 15px;
    color: #333;
}

.plan-popup-content ul {
    padding-left: 20px;
}

.plan-popup-content li {
    margin-bottom: 8px;
    color: #555;
}

.plan-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.plan-popup-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    color: #666;
}

.plan-popup-close:hover {
    color: #333;
}

.plan-popup-content {
    color: #666;
    line-height: 1.6;
}

/* Estilos para las secciones en los popups de planes */
.popup-section {
    margin-bottom: 15px;
}

.popup-section h5 {
    color: #6a1b9a;
    font-size: 14px;
    margin-bottom: 8px;
    border-bottom: 1px solid #f0e6f5;
    padding-bottom: 5px;
}

.popup-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    font-size: 13px;
}

/* Estilos adicionales extraídos del HTML */
.password-hint {
    font-size: 10px;
    margin-left: 5px;
}

/* Estilos para elementos ocultos */
.hidden-element {
    display: none;
}

/* Contenedor de opciones condicionales */
.conditional-options {
    width: 100%;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

/* Selector de categorías */
.category-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
}

/* Estilos específicos para el selector genérico */
#categorias-genericas {
    margin-top: 20px;
    margin-bottom: 20px;
    border-top: 1px solid #e0e0e0;
    padding-top: 20px;
}

#selector-generico {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px 15px;
}

#selector-generico span {
    color: #333;
    font-weight: 500;
}

#btn-selector-generico {
    background-color: #6a1b9a;
    color: white;
    font-weight: 500;
    padding: 8px 14px;
    font-size: 13px;
    margin-right: 5px;
    border-radius: 4px;
    border: none;
    box-shadow: 0 2px 4px rgba(106, 27, 154, 0.3);
    transition: all 0.2s ease;
}

#btn-selector-generico:hover {
    background-color: #7b1fa2;
    box-shadow: 0 3px 6px rgba(106, 27, 154, 0.4);
}

/* Estilos para el paso 4: Planes de suscripción */
.step-title {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
}

.subscription-subtitle {
    text-align: center;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.subscription-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
        "free normal"
        "premium premium";
    gap: 15px;
    margin-bottom: 20px;
}

/* Estilos específicos para el paso 4 de servicios */
#step4-servicios .subscription-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
        "free normal"
        "premium premium";
    gap: 15px;
    margin-bottom: 20px;
}

#step4-servicios #plan-premium-servicios {
    grid-area: premium;
    width: 100%;
}

/* Estilos específicos para los planes de servicios y productos */
.servicios-plans, .productos-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    grid-template-areas:
        "free normal"
        "premium premium";
    gap: 15px;
    margin-bottom: 20px;
}

.servicios-plans .premium-plan, .productos-plans .premium-plan {
    grid-area: premium;
    width: 100%;
}

/* Asegurar que en móvil se mantenga la estructura */
@media (max-width: 767px) {
    .servicios-plans, .productos-plans {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        grid-template-areas:
            "free normal"
            "premium premium";
        gap: 10px;
    }

    .servicios-plans .premium-plan, .productos-plans .premium-plan {
        grid-area: premium;
        width: 100%;
    }
}

.subscription-plan {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
}

/* Asegurar que los planes de la fila superior tengan la misma altura */
#plan-free, #plan-normal {
    height: 100%;
}

/* Asignar áreas específicas a cada plan */
#plan-free {
    grid-area: free;
}

#plan-normal {
    grid-area: normal;
}

#plan-premium {
    grid-area: premium;
}

.subscription-plan:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    /* Eliminado el efecto de movimiento */
}

.subscription-plan.selected-plan {
    border: 2px solid #6a1b9a;
    box-shadow: 0 4px 12px rgba(106, 27, 154, 0.2);
}

.plan-header {
    background-color: #6a1b9a;
    color: white;
    padding: 12px; /* Reducido para hacer más compacto */
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 90px; /* Altura fija para todos los encabezados - ajustada */
    justify-content: center; /* Centrar contenido verticalmente */
}

.premium-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 5px;
    height: 25px; /* Altura fija para el contenido del encabezado premium */
}

.plan-icon {
    font-size: 18px; /* Reducido para hacer más compacto */
}

.plan-title {
    margin: 0;
    font-size: 16px; /* Reducido para hacer más compacto */
    font-weight: 600;
}

.plan-price {
    font-size: 20px; /* Reducido para hacer más compacto */
    font-weight: 700;
    margin-top: 3px;
}

.price-period {
    font-size: 12px; /* Reducido para hacer más compacto */
    font-weight: 400;
    opacity: 0.8;
}

/* Estilos específicos para responsive */
@media (max-width: 767px) {
    .plan-header {
        padding: 15px 5px !important;
        height: auto !important;
        min-height: 90px !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
    }

    .plan-title {
        font-size: 0 !important;
        margin-bottom: 6px !important;
    }

    .plan-title:after {
        font-size: 32px !important;
        font-weight: 700 !important;
        letter-spacing: 0.5px !important;
    }

    .plan-price {
        font-size: 12px !important;
        white-space: nowrap !important;
        margin-top: 3px !important;
        font-weight: 700 !important;
    }

    .price-period {
        font-size: 9px !important;
        white-space: nowrap !important;
    }

    .plan-icon {
        font-size: 16px !important;
        margin-bottom: 2px !important;
    }

    .premium-header-content {
        gap: 5px !important;
        margin-bottom: 3px !important;
        justify-content: center !important;
        align-items: center !important;
    }

    /* Ocultar la palabra "Inscripción" en los títulos */
    .plan-title:before {
        content: "" !important;
    }

    #plan-free .plan-title:after {
        content: "Gratuita" !important;
    }

    #plan-normal .plan-title:after {
        content: "Normal" !important;
    }

    .premium-header-content .plan-title:after {
        content: "Premium" !important;
    }

    .plan-title {
        font-size: 0 !important;
    }

    .plan-title:after {
        font-size: 10px !important;
    }
}

.plan-details {
    padding: 15px;
    background-color: white;
    /* Ocultar siempre los detalles para que no afecten la altura */
    display: none !important;
}

.plan-section {
    margin-bottom: 15px;
}

.section-title {
    color: #6a1b9a;
    font-size: 16px;
    margin-bottom: 10px;
    border-bottom: 1px solid #f0e6f5;
    padding-bottom: 5px;
}

.feature-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    font-size: 14px;
}

.feature-name {
    color: #333;
}

.feature-value {
    color: #666;
    font-weight: 500;
}

.feature-value.positive {
    color: #4caf50;
    font-weight: 600;
}

.feature-value.negative {
    color: #f44336;
    font-weight: 600;
}

/* Botón para comparar planes */
.compare-plans-container {
    text-align: center;
    margin: 20px 0;
}

.btn-comparar-planes {
    background-color: transparent;
    color: #6a1b9a;
    border: none;
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: underline;
    margin: 5px auto 15px;
    display: block;
    text-align: center;
}

.btn-comparar-planes:hover {
    background-color: #e6d8ed;
    border-color: #c9a6d9;
    box-shadow: 0 2px 5px rgba(106, 27, 154, 0.2);
}

/* Selección de plan */
.plan-selection {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.selection-title {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.plan-options {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.plan-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.plan-option:hover {
    background-color: #f0f0f0;
}

.plan-option input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: #6a1b9a;
    margin-right: 3px;
}

.plan-option-label {
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
}

.crown-icon {
    margin-right: 2px;
    font-size: 14px;
    color: #FFD700; /* Color dorado para la corona */
    display: inline-block;
    vertical-align: middle;
    position: relative;
    top: -1px;
}

/* Estilos específicos para responsive */
@media (max-width: 767px) {
    .plan-option {
        padding: 8px 5px !important;
        gap: 4px !important;
        margin-bottom: 5px !important;
        border: 1px solid #ddd !important;
        border-radius: 8px !important;
    }

    .plan-option input[type="radio"] {
        width: 14px !important;
        height: 14px !important;
    }

    .plan-option-label {
        font-size: 12px !important;
        font-weight: 600 !important;
    }

    .crown-icon {
        font-size: 12px !important;
        margin-right: 1px !important;
    }

    .selection-title {
        font-size: 12px !important;
        margin-bottom: 8px !important;
    }

    .plan-selection {
        padding: 8px !important;
        margin-top: 10px !important;
    }

    .subscription-options .plan-option label {
        font-size: 12px !important;
        margin-left: 1px !important;
    }
}

    /* Ajustes para los popups */
    .comparison-item {
        margin-bottom: 1px !important;
        font-size: 11px !important;
    }

    .comparison-section {
        margin-bottom: 8px !important;
    }

    .comparison-section h5 {
        font-size: 12px !important;
        margin-bottom: 4px !important;
    }

    .popup-item {
        font-size: 11px !important;
    }

    .plan-popup-content h4 {
        font-size: 13px !important;
    }

    .plan-popup-content p {
        font-size: 11px !important;
    }

    /* Ocultar la palabra "Inscripción" en los popups */
    .comparison-plan-header h4 {
        font-size: 0 !important;
    }

    .comparison-plan-header h4:after {
        font-size: 12px !important;
    }

    .comparison-plan:nth-child(1) .comparison-plan-header h4:after {
        content: "Gratuita" !important;
    }

    .comparison-plan:nth-child(2) .comparison-plan-header h4:after {
        content: "Normal" !important;
    }

    .comparison-plan:nth-child(3) .comparison-plan-header h4:after {
        content: "Premium" !important;
    }


/* Pop-up de comparación de planes */
.plans-comparison-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.plans-comparison-popup {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: popupFadeIn 0.3s ease;
    overflow: hidden;
}

.comparison-popup-header {
    padding: 20px;
    background-color: #6a1b9a;
    color: white;
    text-align: center;
    position: relative;
}

.comparison-popup-header h3 {
    margin: 0 0 5px 0;
    font-size: 22px;
    font-weight: 600;
}

.comparison-popup-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.comparison-popup-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.comparison-popup-close:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.comparison-popup-content {
    padding: 20px;
    overflow-y: auto;
    flex-grow: 1;
}

.comparison-plans {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.comparison-plan {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.comparison-plan-header {
    background-color: #6a1b9a;
    color: white;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    height: 100px; /* Altura fija para todos los encabezados */
    justify-content: center; /* Centrar contenido verticalmente */
}

.comparison-plan-header h4 {
    margin: 5px 0;
    font-size: 16px;
    font-weight: 600;
}

.comparison-plan-header .plan-price {
    font-size: 20px;
    font-weight: 700;
    margin-top: 5px;
    height: 30px; /* Altura fija para los precios */
    display: flex;
    align-items: center;
    justify-content: center;
}

.comparison-plan-header .plan-price span {
    font-size: 12px;
    font-weight: 400;
    opacity: 0.8;
}

.comparison-plan-details {
    padding: 15px;
}

.comparison-section {
    margin-bottom: 15px;
}

.comparison-section h5 {
    color: #6a1b9a;
    font-size: 14px;
    margin-bottom: 8px;
    border-bottom: 1px solid #f0e6f5;
    padding-bottom: 5px;
    height: 30px; /* Altura fija para los títulos de sección */
    display: flex;
    align-items: center;
}

.comparison-item {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
    font-size: 13px;
}

.comparison-popup-footer {
    padding: 15px;
    border-top: 1px solid #e0e0e0;
    text-align: center;
}

.comparison-popup-close-btn {
    background-color: #6a1b9a;
    color: white;
    border: none;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.comparison-popup-close-btn:hover {
    background-color: #5c1786;
}

/* Ajuste para el paso 3 */
#step3 {
    padding-top: 5px;
}

/* Eliminar estilos de categorías */
.category-selector,
.selected-categories-grid,
.selected-category-tag,
.remove-category-btn,
[id^="selected-"][id$="-container"],
.categories-popup-overlay,
.categories-popup,
.categories-popup-header,
.categories-popup-close,
.categories-popup-content,
.categories-popup-grid,
.category-checkbox,
#categoriesPopupConfirm {
    display: none;
}

/* Eliminar animaciones de popup */
@keyframes popupFadeIn {
    /* Esta animación se elimina */
}

/* Eliminar estilos responsive para popups de categorías */
@media (max-width: 767px) {
    /* Estos estilos se eliminan */
}

/* Estilos para el selector de categorías */
.category-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
}

.category-selector span {
    font-weight: 500;
    color: #6a1b9a;
    font-size: 13px;
}

/* Estilo para el selector de categorías seleccionado */
.category-selector.selected span {
    font-weight: 600;
}

.select-categories-btn {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.select-categories-btn:hover {
    background-color: #5c1786;
}

.selected-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
    min-height: 30px;
}

.selected-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
    margin: 10px 0;
}

.selected-category-tag {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f0e6f5;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 13px;
}

.remove-category-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    padding: 0 0 0 8px;
    line-height: 1;
}

.remove-category-btn:hover {
    color: #d32f2f;
}

/* Contenedor para las categorías seleccionadas */
[id^="selected-"][id$="-container"] {
    margin: 10px 0;
    min-height: 10px; /* Para mantener el espacio incluso cuando está vacío */
}

/* Estilos para el pop-up de categorías */
.categories-popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.categories-popup {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px; /* Aumentado para mostrar mejor las categorías */
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: popupFadeIn 0.3s ease;
}

@keyframes popupFadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}

.categories-popup-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.categories-popup-header h3 {
    color: #6a1b9a;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.categories-popup-close {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 30px;
    height: 30px;
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
}

.categories-popup-close:hover {
    background-color: #5c1786;
}

.categories-popup-content {
    padding: 20px 20px 20px 20px;
    overflow-y: auto; /* Mantenemos esta barra de desplazamiento */
    flex-grow: 1;
    max-height: 450px; /* Limitamos la altura para asegurar que aparezca la barra de desplazamiento */
}

.categories-popup-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-height: 450px;
    /* Eliminamos el overflow-y: auto para quitar la barra de desplazamiento interna */
    padding-right: 0; /* Eliminamos el padding derecho */
}

.categories-popup-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    text-align: right;
}

/* Estilo base del botón de confirmar selección */
.categories-popup-confirm {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 500;
    width: auto;
    margin: 10px auto;
    display: block;
}

.categories-popup-confirm:hover {
    background-color: var(--primary-dark);
}

/* Ajustes responsive para el popup */
@media (max-width: 767px) {
    /* Ajustar tamaño de texto en las opciones del popup */
    .categories-popup-grid label {
        font-size: 11px;
    }

    /* Ajustar el checkbox para mantener proporción */
    .categories-popup-grid input[type="checkbox"] {
        width: 14px;
        height: 14px;
    }

    /* Ajustar el título del popup */
    .categories-popup-title {
        font-size: 13px;
    }

    /* Ajustar el botón de confirmar en responsive */
    .categories-popup-confirm {
        font-size: 12px;
        padding: 8px 16px;
    }

    /* Eliminar margen lateral derecho */
    .categories-popup-content {
        padding: 15px 10px 15px 15px;
    }

    /* Reducir el tamaño del texto en las categorías */
    .category-checkbox span {
        font-size: 9px;
        line-height: 1.1;
    }

    /* Reducir el padding para que ocupen menos espacio */
    .category-checkbox {
        padding: 6px 8px;
        gap: 6px;
    }
}

/* Para pantallas aún más pequeñas */
@media (max-width: 480px) {
    .categories-popup-grid label {
        font-size: 10px;
    }

    .categories-popup-title {
        font-size: 12px;
    }

    .categories-popup-confirm {
        font-size: 11px;
        padding: 6px 14px;
    }

    /* Eliminar margen lateral derecho para pantallas muy pequeñas */
    .categories-popup-content {
        padding: 10px 5px 10px 10px;
    }

    /* Reducir aún más el tamaño del texto en las categorías */
    .category-checkbox span {
        font-size: 8px;
        line-height: 1;
    }

    /* Reducir aún más el padding para que ocupen menos espacio */
    .category-checkbox {
        padding: 4px 6px;
        gap: 4px;
        border-radius: 4px;
    }

    /* Ajustar el checkbox para mantener proporción */
    .category-checkbox input[type="checkbox"] {
        width: 12px;
        height: 12px;
    }

    /* Ajustar el grid para mostrar mejor en pantallas pequeñas */
    .categories-popup-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 5px;
        padding: 0;
        margin-right: 0;
    }
}

.category-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 10px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.category-checkbox:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
}

.category-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #6a1b9a;
    flex-shrink: 0;
}

.category-checkbox span {
    font-size: 12px;
    color: #333;
    line-height: 1.2;
}

/* Estilos para el contador de palabras */
.label-with-counter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.word-counter {
    font-size: 12px;
    color: #666;
}

/* Estilos para las opciones de radio */
.radio-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-options-centered {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 10px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.radio-option input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #6a1b9a;
}

.radio-label {
    font-size: 14px;
    color: #333;
}

/* Estilos para el contenedor de Local Físico en línea */
.local-fisico-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    margin-bottom: 15px;
}

.local-fisico-label {
    font-weight: 500;
    color: #333;
    font-size: 13px;
}

.local-fisico-options {
    display: flex;
    gap: 12px;
    margin-right: 20px;
}

.radio-option-small {
    display: flex;
    align-items: center;
    gap: 3px;
    cursor: pointer;
}

.radio-option-small input[type="radio"] {
    width: 12px;
    height: 12px;
    accent-color: #6a1b9a;
}

.radio-label-small {
    font-size: 13px;
    color: #333;
}

.local-fisico-options.error {
    padding: 4px 8px;
    border: 1px solid #dc3545;
    border-radius: 4px;
    background-color: #fff8f8;
}

@media screen and (max-width: 768px) {
    .plan-title {
        font-size: 18px !important; /* Aumentamos significativamente el tamaño */
        margin-bottom: 5px;
    }

    .plan-price {
        font-size: 16px !important;
    }

    .price-period {
        font-size: 14px !important;
    }
}

@media screen and (max-width: 480px) {
    .plan-title {
        font-size: 16px !important;
    }

    .plan-price {
        font-size: 14px !important;
    }
}

@media (max-width: 767px) {
    .plan-title:after {
        content: '' !important; /* Removemos el contenido duplicado */
        font-size: 0 !important; /* Por si acaso, también ponemos el tamaño en 0 */
    }

    .plan-title {
        font-size: 18px !important; /* Mantenemos el tamaño que ya teníamos */
    }
}

@media (max-width: 767px) {
    .plan-option-label {
        font-size: 14px !important; /* Reducimos el tamaño de las opciones */
    }

    /* Corregimos específicamente la opción "Gratuitaan>" */
    .plan-option[data-plan="free"] .plan-option-label::after {
        content: 'Gratuita' !important;
    }
}

@media (max-width: 480px) {
    .plan-option-label {
        font-size: 13px !important; /* Aún más pequeño para móviles muy pequeños */
    }
}

@media (max-width: 767px) {
    /* Achicar los textos de las alternativas */
    .plan-selection .plan-option-label {
        font-size: 13px !important;
    }

    /* Ajustar el ícono de la corona */
    .plan-selection .crown-icon {
        font-size: 13px !important;
    }

    /* Ajustar el título de la selección */
    .plan-selection .selection-title {
        font-size: 14px !important;
    }
}

@media (max-width: 480px) {
    /* Aún más pequeño para móviles muy pequeños */
    .plan-selection .plan-option-label {
        font-size: 12px !important;
    }

    .plan-selection .crown-icon {
        font-size: 12px !important;
    }
}

@media (max-width: 767px) {
    .plan-price {
        font-size: 14px !important;
        white-space: nowrap !important;
        margin-top: 3px !important;
        font-weight: 700 !important;
    }

    .price-period {
        font-size: 11px !important;
        white-space: nowrap !important;
    }
}

@media (max-width: 480px) {
    .plan-price {
        font-size: 13px !important;
    }

    .price-period {
        font-size: 10px !important;
    }
}

@media (max-width: 767px) {
    .fecha-label {
        display: block !important;
        font-size: 14px !important;
    }
    .fecha-label::after {
        content: none !important;
    }
    .fecha-label {
        content: "F. Nacimiento" !important;
    }
}

@media (max-width: 767px) {
    /* Ajustar el contenedor de fecha */
    .date-input-container {
        width: 100% !important;
    }

    /* Hacer que el input de fecha ocupe todo el espacio disponible */
    #fechaNacimiento {
        width: 100% !important;
        padding: 8px !important;
    }
}

/* Estilos del calendario eliminados */

@media (max-width: 767px) {
    .form-group label,
    .fecha-label {
        font-size: 12px !important;
        font-weight: 500;
        margin-bottom: 3px !important;
    }
}

/* Estilos del calendario eliminados */

/* Estilos del calendario eliminados */

/* Estilos del calendario eliminados */

/* Estilo para el botón de confirmar selección */
#categoriesPopupConfirm {
    background-color: #6a1b9a;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 20px;
    font-size: 13px;
    cursor: pointer;
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    transition: background-color 0.2s ease;
    box-shadow: 0 2px 4px rgba(106, 27, 154, 0.2);
}

#categoriesPopupConfirm:hover {
    background-color: #8e24aa;
}

/* Ajustes responsive */
@media (max-width: 767px) {
    #categoriesPopupConfirm {
        font-size: 12px;
        padding: 6px 16px;
    }
}

/* Contenedor de las opciones de inscripción */
.subscription-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

/* Estilos base para las opciones */
.subscription-options .plan-option {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 10px 5px;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    justify-content: center;
}

.subscription-options .plan-option label {
    font-size: 14px;
    color: #333;
    margin-left: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.subscription-options .premium-option {
    display: flex;
    justify-content: center;
}

.subscription-options .premium-option input[type="radio"],
.subscription-options .premium-option label {
    display: inline-flex;
    align-items: center;
}

/* Ajuste para centrar el contenido de la opción Premium */
.subscription-options .premium-option {
    padding-left: 0;
    padding-right: 0;
}

.subscription-options .premium-option input[type="radio"] {
    margin-right: 5px;
}

/* Ajustes responsive */
@media (max-width: 767px) {
    .subscription-options .plan-option {
        padding: 8px;
        gap: 6px;
    }

    .subscription-options .plan-option label {
        font-size: 13px;
    }

    .subscription-options .plan-option input[type="radio"] {
        width: 16px;
        height: 16px;
    }

    /* Ajuste específico para el ícono de la corona */
    .subscription-options .plan-option:last-child label::before {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .subscription-options .plan-option {
        padding: 6px;
        gap: 5px;
    }

    .subscription-options .plan-option label {
        font-size: 12px;
    }

    .subscription-options .plan-option input[type="radio"] {
        width: 14px;
        height: 14px;
    }

    /* Ajuste específico para el ícono de la corona */
    .subscription-options .plan-option:last-child label::before {
        font-size: 12px;
         width: 100%;
    }
}
/* Estilo específico para la opción Premium */
.subscription-options .premium-option {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Mantener la misma estructura en responsive */
@media (max-width: 767px) {
    .subscription-options {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .subscription-options {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }

    .subscription-options .plan-option {
        padding: 6px 2px !important;
    }

    .subscription-options .plan-option label {
        font-size: 11px !important;
        margin-left: 1px !important;
    }

    .plan-option input[type="radio"] {
        width: 12px !important;
        height: 12px !important;
        margin-right: 2px !important;
    }

    .crown-icon {
        font-size: 10px !important;
        margin-right: 1px !important;
    }
}























