<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de API de Productos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #6a1b9a;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #43a047;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        button {
            background-color: #6a1b9a;
            color: white;
            border: none;
            cursor: pointer;
            padding: 10px 15px;
        }
        button:hover {
            background-color: #9c4dcc;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .error {
            color: #f44336;
            font-weight: bold;
        }
        .success {
            color: #43a047;
            font-weight: bold;
        }
        .maintenance-btn {
            display: inline-block;
            background-color: #2196f3;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            margin-top: 10px;
        }
        .maintenance-btn:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <h1>Prueba de API de Productos</h1>

    <div class="test-section">
        <h2>Herramientas de mantenimiento</h2>
        <p>Si estás viendo errores relacionados con tablas faltantes, puedes crear las tablas necesarias:</p>
        <a href="./API/productos/create_tags_table.php" class="maintenance-btn">Crear tabla de tags</a>
    </div>

    <div class="test-section">
        <h2>Prueba de get_product.php</h2>
        <div class="form-group">
            <label for="productId">ID del Producto:</label>
            <input type="text" id="productId" value="1">
            <button type="button" id="testGetProduct">Probar API</button>
        </div>
        <div class="result" id="getProductResult">Los resultados aparecerán aquí...</div>
    </div>

    <div class="test-section">
        <h2>Prueba de get_product_details.php</h2>
        <div class="form-group">
            <label for="productDetailsId">ID del Producto:</label>
            <input type="text" id="productDetailsId" value="1">
            <button type="button" id="testGetProductDetails">Probar API</button>
        </div>
        <div class="result" id="getProductDetailsResult">Los resultados aparecerán aquí...</div>
    </div>

    <script>
        // Función para mostrar resultados formateados
        function displayResult(elementId, data, isError = false) {
            const resultElement = document.getElementById(elementId);

            if (isError) {
                resultElement.innerHTML = `<span class="error">Error: ${data}</span>`;
                return;
            }

            try {
                // Si es un string JSON, intentar parsearlo para mostrarlo formateado
                if (typeof data === 'string') {
                    const jsonData = JSON.parse(data);
                    resultElement.innerHTML = `<span class="success">Éxito:</span>\n${JSON.stringify(jsonData, null, 2)}`;
                } else {
                    // Si ya es un objeto, solo formatearlo
                    resultElement.innerHTML = `<span class="success">Éxito:</span>\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (e) {
                // Si no es JSON válido, mostrar como texto
                resultElement.textContent = data;
            }
        }

        // Prueba de get_product.php
        document.getElementById('testGetProduct').addEventListener('click', async function() {
            const productId = document.getElementById('productId').value;
            const resultElement = document.getElementById('getProductResult');

            resultElement.textContent = 'Cargando...';

            try {
                const response = await fetch(`./API/productos/get_product.php?id=${productId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    credentials: 'include'
                });

                const responseText = await response.text();

                if (!response.ok) {
                    displayResult('getProductResult', `${response.status} ${response.statusText}: ${responseText}`, true);
                    return;
                }

                displayResult('getProductResult', responseText);
            } catch (error) {
                displayResult('getProductResult', error.message, true);
            }
        });

        // Prueba de get_product_details.php
        document.getElementById('testGetProductDetails').addEventListener('click', async function() {
            const productId = document.getElementById('productDetailsId').value;
            const resultElement = document.getElementById('getProductDetailsResult');

            resultElement.textContent = 'Cargando...';

            try {
                const response = await fetch(`./API/productos/get_product_details.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify({ id: productId }),
                    credentials: 'include'
                });

                const responseText = await response.text();

                if (!response.ok) {
                    displayResult('getProductDetailsResult', `${response.status} ${response.statusText}: ${responseText}`, true);
                    return;
                }

                displayResult('getProductDetailsResult', responseText);
            } catch (error) {
                displayResult('getProductDetailsResult', error.message, true);
            }
        });
    </script>
</body>
</html>
