<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Iniciar o reanudar sesión
session_start();

// Configurar logging detallado para este script
logInfo("Iniciando process_step3.php", [
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
    'session_id' => session_id()
]);

// Función para enviar respuesta JSON
function sendJsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    logInfo("Enviando respuesta JSON", [
        'success' => $success,
        'message' => $message
    ]);

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Validar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError("Método no permitido", [
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
    sendJsonResponse(false, 'Método no permitido');
}

// Recuperar datos del paso 1 y 2 desde la sesión
$paso1_data = $_SESSION['paso1_data'] ?? null;
$paso2_data = $_SESSION['paso2_data'] ?? null;

if (!$paso1_data) {
    logError("No se encontraron datos del paso 1 en la sesión", [
        'session_id' => session_id(),
        'session_data' => $_SESSION
    ]);
    sendJsonResponse(false, "No se encontraron los datos del paso 1. Por favor, vuelva a intentarlo.");
    exit;
}

// Obtener y validar los datos del paso 3
$nombre_negocio = isset($_POST['nombre_negocio']) ? trim($_POST['nombre_negocio']) : '';
$telefono_negocio = isset($_POST['telefono_negocio']) ? trim($_POST['telefono_negocio']) : '';
$whatsapp_negocio = isset($_POST['whatsapp_negocio']) ? trim($_POST['whatsapp_negocio']) : '';
$tipo_negocio = isset($_POST['tipo_negocio']) ? trim($_POST['tipo_negocio']) : '';
$descripcion_negocio = isset($_POST['descripcion_negocio']) ? trim($_POST['descripcion_negocio']) : '';

// Registrar los datos recibidos
logDebug("Datos del paso 3 recibidos", [
    'nombre_negocio' => $nombre_negocio,
    'telefono_negocio' => $telefono_negocio,
    'whatsapp_negocio' => $whatsapp_negocio,
    'tipo_negocio' => $tipo_negocio,
    'descripcion_negocio' => substr($descripcion_negocio, 0, 50) . '...' // Truncar para el log
]);

// Validar que los campos obligatorios estén presentes
if (empty($nombre_negocio) || empty($telefono_negocio) || empty($tipo_negocio) || empty($descripcion_negocio)) {
    $campos_vacios = [];
    if (empty($nombre_negocio)) $campos_vacios[] = 'nombre_negocio';
    if (empty($telefono_negocio)) $campos_vacios[] = 'telefono_negocio';
    if (empty($tipo_negocio)) $campos_vacios[] = 'tipo_negocio';
    if (empty($descripcion_negocio)) $campos_vacios[] = 'descripcion_negocio';

    logError("Campos obligatorios faltantes en paso 3", [
        'campos_vacios' => $campos_vacios
    ]);
    sendJsonResponse(false, 'Todos los campos son obligatorios excepto WhatsApp del negocio');
    exit;
}

// Guardar los datos del paso 2 y 3 en la sesión
if (!$paso2_data) {
    // Si no hay datos del paso 2, recuperarlos de la solicitud
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $backup_email = isset($_POST['backup_email']) ? trim($_POST['backup_email']) : null;
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';
    $confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
    $local_fisico = isset($_POST['local_fisico']) ? ($_POST['local_fisico'] === 'Si' ? 'Si' : 'No') : 'No';

    // Validar que los campos obligatorios del paso 2 estén presentes
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        logError("Campos obligatorios faltantes del paso 2", [
            'username' => empty($username),
            'email' => empty($email),
            'password' => empty($password),
            'confirm_password' => empty($confirm_password)
        ]);
        sendJsonResponse(false, 'Faltan datos del paso 2. Por favor, vuelva a intentarlo.');
        exit;
    }

    // Validar que las contraseñas coincidan
    if ($password !== $confirm_password) {
        logError("Las contraseñas no coinciden");
        sendJsonResponse(false, 'Las contraseñas no coinciden');
        exit;
    }

    // Guardar los datos del paso 2 en la sesión
    $_SESSION['paso2_data'] = [
        'username' => $username,
        'email' => $email,
        'backup_email' => $backup_email,
        'password' => $password,
        'local_fisico' => $local_fisico
    ];

    $paso2_data = $_SESSION['paso2_data'];
}

// Formatear el número de WhatsApp para incluir el prefijo 569 si no lo tiene
if (!empty($whatsapp_negocio)) {
    // Si el número ya comienza con 569, dejarlo como está
    if (substr($whatsapp_negocio, 0, 3) !== '569') {
        // Si comienza con 9, agregar solo 56
        if (substr($whatsapp_negocio, 0, 1) === '9') {
            $whatsapp_negocio = '56' . $whatsapp_negocio;
        } else {
            // Si no comienza con 9 ni con 569, agregar 569
            $whatsapp_negocio = '569' . $whatsapp_negocio;
        }
    }
}

// Guardar los datos del paso 3 en la sesión
$_SESSION['paso3_data'] = [
    'nombre_negocio' => $nombre_negocio,
    'telefono_negocio' => $telefono_negocio,
    'whatsapp_negocio' => $whatsapp_negocio,
    'tipo_negocio' => $tipo_negocio,
    'descripcion_negocio' => $descripcion_negocio
];

// Registrar en el log
logInfo("Datos del paso 3 guardados en sesión", [
    'session_id' => session_id(),
    'nombre_negocio' => $nombre_negocio,
    'tipo_negocio' => $tipo_negocio
]);

// Devolver respuesta exitosa
sendJsonResponse(true, 'Datos del paso 3 guardados correctamente');
