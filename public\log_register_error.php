register1.php:1 The source list for the Content Security Policy directive 'default-src' contains an invalid source: '\"self\"'. It will be ignored.
register1.php:1 The source list for the Content Security Policy directive 'style-src' contains an invalid source: '\"self\"'. It will be ignored.
register1.php:12 Refused to load the stylesheet 'http://**************/css/register1.css' because it violates the following Content Security Policy directive: "style-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Note that 'style-src-elem' was not explicitly set, so 'style-src' is used as a fallback.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" <URL> <URL>". Either the 'unsafe-inline' keyword, a hash ('sha256-biLFinpqYMtWHmXfkA1BPeCY0/fNt46SAZ+BBk5YUog='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

register1.php:982 Refused to apply inline style because it violates the following Content Security Policy directive: "style-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Either the 'unsafe-inline' keyword, a hash ('sha256-/Iautx0Javsh2xrtmennw7otc4pjudxZRLmKIG+NZb8='), or a nonce ('nonce-...') is required to enable inline execution. Note that hashes do not apply to event handlers, style attributes and javascript: navigations unless the 'unsafe-hashes' keyword is present.

register1.php:1 Refused to load the script 'http://**************/js/register1.js' because it violates the following Content Security Policy directive: "default-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Note that 'script-src-elem' was not explicitly set, so 'default-src' is used as a fallback.

register1.php:1 Refused to load the script 'http://**************/js/auto-fill-fix.js' because it violates the following Content Security Policy directive: "default-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Note that 'script-src-elem' was not explicitly set, so 'default-src' is used as a fallback.

register1.php:1156 Refused to execute inline script because it violates the following Content Security Policy directive: "default-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Either the 'unsafe-inline' keyword, a hash ('sha256-u9NZhcSGGyZoBBacf922eV6YuFmzMnWj1ZQoj/LvF/0='), or a nonce ('nonce-...') is required to enable inline execution. Note also that 'script-src' was not explicitly set, so 'default-src' is used as a fallback.

register1.php:1302 Refused to execute inline script because it violates the following Content Security Policy directive: "default-src \"self\" https://fonts.googleapis.com https://cdnjs.cloudflare.com". Either the 'unsafe-inline' keyword, a hash ('sha256-5NrSODQOr0B7lDLMfQoHMErDcKFyUxaVH9SbCuvWsE4='), or a nonce ('nonce-...') is required to enable inline execution. Note also that 'script-src' was not explicitly set, so 'default-src' is used as a fallback.

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

Refused to load the font '<URL>' because it violates the following Content Security Policy directive: "font-src <URL>".

register1.php:1 [DOM] Input elements should have autocomplete attributes (suggested: "new-password"): (More info: https://goo.gl/9p2vKq) <input type=​"password" id=​"password" name=​"password" class=​"form-control" placeholder=​"Exactamente 8 caracteres" maxlength=​"8" pattern=​"^(?=.*[A-Z]​)​(?=.*[0-9]​)​.{8,8}​$" title=​"La contraseña debe tener exactamente 8 caracteres, incluyendo al menos una letra mayúscula y un número" required>​
register1.php:1 [DOM] Input elements should have autocomplete attributes (suggested: "new-password"): (More info: https://goo.gl/9p2vKq) <input type=​"password" id=​"confirm_password" name=​"confirm_password" class=​"form-control" placeholder=​"Repita su contraseña" maxlength=​"8" required>​
