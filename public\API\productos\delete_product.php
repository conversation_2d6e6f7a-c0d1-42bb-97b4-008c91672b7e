<?php
header('Content-Type: application/json');
require_once '../../../config/config.php';

try {
    // Obtener el input del cliente, ya sea POST o JSON
    $input = null;
    
    // Verificar si la solicitud es JSON
    $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';
    if (strpos($contentType, 'application/json') !== false) {
        // Obtener el JSON enviado
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        
        // Usar 'id' del JSON
        $input = isset($data['id']) ? $data['id'] : null;
        error_log("ID recibido en JSON: " . print_r($input, true));
    } else {
        // Si no es JSON, buscar en POST
        $input = isset($_POST['id']) ? $_POST['id'] : null;
        error_log("ID recibido en POST: " . print_r($input, true));
    }
    
    // Validar que se recibió un ID
    if (!$input) {
        throw new Exception('No se proporcionó un ID de producto');
    }

    $id = intval($input);
    
    // Verificar que el ID sea válido
    if ($id <= 0) {
        throw new Exception('ID de producto inválido');
    }

    // Obtener información del producto (incluyendo la imagen) antes de eliminar
    $sql_get_image = "SELECT imagen_principal FROM tb_productos WHERE id = ?";
    $stmt = $conn->prepare($sql_get_image);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $producto = $result->fetch_assoc();

    // Preparar y ejecutar la consulta de eliminación
    $sql = "DELETE FROM tb_productos WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        error_log("Producto eliminado exitosamente: ID $id");
        
        // Si la eliminación fue exitosa y existe una imagen, eliminarla del servidor
        if ($producto && $producto['imagen_principal']) {
            $imagen_path = $_SERVER['DOCUMENT_ROOT'] . parse_url($producto['imagen_principal'], PHP_URL_PATH);
            if (file_exists($imagen_path)) {
                unlink($imagen_path);
                error_log("Imagen eliminada: $imagen_path");
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Producto eliminado exitosamente'
        ]);
    } else {
        throw new Exception('Error al eliminar el producto: ' . $stmt->error);
    }

} catch (Exception $e) {
    error_log("Error en delete_product.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}