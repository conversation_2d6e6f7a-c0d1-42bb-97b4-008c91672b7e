<?php
// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Verificar si estamos en la raíz o en un subdirectorio
$config_path = file_exists('config/logger.php') ? 'config/' : '../config/';

// Incluir primero el sistema de logging
require_once $config_path . 'logger.php';

// Registrar inicio de la página
// Comentamos temporalmente para evitar errores
// logInfo("Cargando página de registro");

// Incluir la configuración base
require_once $config_path . 'config.php';
require_once $config_path . 'SessionManager.php';

// Inicializar gestor de sesiones
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Flatpickr eliminado -->

    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="<?php echo (file_exists('css/register.css') ? 'css/register.css' : '../css/register.css'); ?>">

    <title>Crear Cuenta - Villarrica a un CLICK</title>

    <!-- Estilos adicionales para validación -->
    <style>
        /* Estilos para campos con error */
        .error {
            border: 1px solid #ff5252 !important;
            background-color: #fff8f8 !important;
        }

        .error:focus {
            box-shadow: 0 0 0 2px rgba(255, 82, 82, 0.25) !important;
        }

        /* Estilos para botón deshabilitado */
        .btn.disabled,
        .btn:disabled {
            background-color: #cccccc !important;
            color: #888888 !important;
            cursor: not-allowed !important;
            opacity: 0.7;
            pointer-events: none;
        }

        /* Estilos para campos de contraseña */
        .password-field {
            position: relative;
            display: flex;
            align-items: center;
        }

        .toggle-password {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 16px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .toggle-password:hover {
            color: #6a1b9a;
        }
    </style>

</head>

<body>
    <div class="register-panel">
        <div class="register-header">
            <h1>Crea tu cuenta</h1>
        </div>

        <div class="register-steps">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <div class="step-title">Información Personal</div>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <div class="step-title">Datos de Cuenta</div>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <div class="step-title">Datos del Negocio</div>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <div class="step-title">Plan</div>
            </div>
            <div class="step" data-step="5">
                <div class="step-number">5</div>
                <div class="step-title">Pago</div>
            </div>
        </div>

        <form id="registerForm" class="register-form" action="process_register.html" method="post">
            <!-- Paso 1: Información Personal -->
            <div class="form-step active" id="step1">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="nombres" class="form-label-small">Nombres</label>
                            <input type="text"
                                   id="nombres"
                                   name="nombres"
                                   class="form-control"
                                   placeholder="Juan Carlos"
                                   required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="apellidos" class="form-label-small">Apellidos</label>
                            <input type="text"
                                   id="apellidos"
                                   name="apellidos"
                                   class="form-control"
                                   placeholder="Pérez Gómez"
                                   required>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="rut">RUT</label>
                            <input type="text" id="rut" name="rut" class="form-control" placeholder="15364407k" maxlength="9" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="fechaNacimiento" class="fecha-label">Fecha de Nacimiento</label>
                            <div class="date-input-container">
                                <input type="text" id="fechaNacimiento" name="fechaNacimiento" class="form-control" placeholder="22/02/1982" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>Sexo</label>
                            <select class="form-control" name="sexo" required>
                                <option value="">Seleccionar...</option>
                                <option value="masculino">Masculino</option>
                                <option value="femenino">Femenino</option>
                                <option value="otro">Otro</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="telefono">Teléfono</label>
                            <div class="phone-input">
                                <span class="phone-prefix">+569</span>
                                <input type="text"
                                       id="telefono"
                                       name="telefono"
                                       class="form-control phone-number"
                                       placeholder="27899260"
                                       maxlength="8"
                                       pattern="[0-9]{8}"
                                       inputmode="numeric"
                                       required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="region">Región</label>
                            <select class="form-control" id="region" name="region" required>
                                <option value="">Seleccionar región...</option>
                                <option value="arica">Arica y Parinacota</option>
                                <option value="tarapaca">Tarapacá</option>
                                <option value="antofagasta">Antofagasta</option>
                                <option value="atacama">Atacama</option>
                                <option value="coquimbo">Coquimbo</option>
                                <option value="valparaiso">Valparaíso</option>
                                <option value="metropolitana">Metropolitana de Santiago</option>
                                <option value="ohiggins">Del Libertador General Bernardo O'Higgins</option>
                                <option value="maule">Del Maule</option>
                                <option value="nuble">Ñuble</option>
                                <option value="biobio">Del Biobío</option>
                                <option value="araucania">De la Araucanía</option>
                                <option value="losrios">De los Ríos</option>
                                <option value="loslagos">De los Lagos</option>
                                <option value="aysen">Aysén del General Carlos Ibáñez del Campo</option>
                                <option value="magallanes">Magallanes y de la Antártica Chilena</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="comuna">Comuna</label>
                            <select class="form-control" id="comuna" name="comuna" required disabled>
                                <option value="">Seleccione una región primero...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="direccion">Dirección</label>
                    <input type="text"
                           class="form-control"
                           id="direccion"
                           name="direccion"
                           placeholder="Calle, número, casa/departamento, villa"
                           required>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-next" id="step1-next">Siguiente</button>
                </div>
            </div>

            <!-- Paso 2: Datos de la Cuenta -->
            <div class="form-step" id="step2">
                <div class="form-group">
                    <label for="username">Nombre de Usuario</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="juanperez2023" required>
                </div>

                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>" required>
                </div>

                <div class="form-group">
                    <label for="backup_email">Correo de Respaldo (Opcional)</label>
                    <input type="email" id="backup_email" name="backup_email" class="form-control">
                </div>

                <div class="form-group">
                    <label for="password">
                        Contraseña <span class="password-hint">(Por lo menos una Mayúscula y un número)</span>
                    </label>
                    <div class="password-field">
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-control"
                               placeholder="Ingresa los 8 caracteres"
                               required>
                        <button type="button" class="toggle-password" aria-label="Mostrar contraseña">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirmar Contraseña</label>
                    <div class="password-field">
                        <input type="password"
                               id="confirm_password"
                               name="confirm_password"
                               class="form-control"
                               placeholder="Repita su contraseña"
                               required>
                        <button type="button" class="toggle-password" aria-label="Mostrar contraseña">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Local Físico -->
                <div class="form-group local-fisico-container">
                    <div class="local-fisico-label">¿Tiene Local Físico?</div>
                    <div class="local-fisico-options">
                        <label class="radio-option-small">
                            <input type="radio" name="local_fisico" value="si">
                            <span class="radio-label-small">Sí</span>
                        </label>
                        <label class="radio-option-small">
                            <input type="radio" name="local_fisico" value="no">
                            <span class="radio-label-small">No</span>
                        </label>
                    </div>
                </div>



                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step2-next">Siguiente</button>
                </div>
            </div>

            <!-- Paso 3: Datos del Negocio -->
            <div class="form-step" id="step3">

                <!-- Nombre del Negocio -->
                <div class="form-group">
                    <label for="nombre_negocio">Nombre del Negocio</label>
                    <input type="text"
                           class="form-control"
                           id="nombre_negocio"
                           name="nombre_negocio"
                           placeholder="Nombre que irá en la Página a crear"
                           required>
                </div>

                <!-- Teléfono y WhatsApp -->
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="telefono_negocio">Teléfono del negocio</label>
                            <div class="phone-input">
                                <span class="phone-prefix">+56</span>
                                <input type="text"
                                       id="telefono_negocio"
                                       name="telefono_negocio"
                                       class="form-control phone-number"
                                       placeholder="912345678"
                                       maxlength="9">
                            </div>

                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="whatsapp_negocio">WhatsApp del negocio</label>
                            <div class="phone-input">
                                <span class="phone-prefix">+569</span>
                                <input type="text"
                                       id="whatsapp_negocio"
                                       name="whatsapp_negocio"
                                       class="form-control phone-number"
                                       placeholder="87654321"
                                       maxlength="8">
                            </div>

                        </div>
                    </div>
                </div>

                <!-- Tipo de Negocio -->
                <div class="form-group business-type-container">
                    <label class="business-question">¿Qué tipo de negocio inscribirás?</label>
                    <div class="business-options">
                        <label class="business-option">
                            <input type="radio" name="tipo_negocio" value="venta" required>
                            <span class="option-circle"></span>
                            <span class="option-text">Venta de Productos</span>
                        </label>
                        <label class="business-option">
                            <input type="radio" name="tipo_negocio" value="servicios" required>
                            <span class="option-circle"></span>
                            <span class="option-text">Servicios</span>
                        </label>
                        <label class="business-option">
                            <input type="radio" name="tipo_negocio" value="arriendo" required>
                            <span class="option-circle"></span>
                            <span class="option-text">Arriendo</span>
                        </label>
                    </div>
                </div>

                <!-- Estilos para opciones de negocio -->
                <style>
                    /* Contenedor principal */
                    .business-type-container {
                        background-color: #f8f8f8;
                        border-radius: 10px;
                        padding: 16px;
                        margin: 15px 0;
                        border: 1px solid #e0e0e0;
                    }

                    /* Título de la pregunta */
                    .business-question {
                        font-weight: 500;
                        margin-bottom: 15px;
                        color: #333;
                        font-size: 14px;
                        font-family: 'Montserrat', sans-serif;
                        display: block;
                    }

                    /* Contenedor de opciones */
                    .business-options {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        gap: 10px;
                    }

                    /* Cada opción individual */
                    .business-option {
                        flex: 1;
                        display: flex;
                        align-items: center;
                        padding: 10px;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        position: relative;
                    }

                    /* Ocultar el radio button nativo */
                    .business-option input[type="radio"] {
                        position: absolute;
                        opacity: 0;
                        width: 0;
                        height: 0;
                    }

                    /* Círculo personalizado */
                    .option-circle {
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        border-radius: 50%;
                        border: 2px solid #d0d0d0;
                        margin-right: 10px;
                        position: relative;
                        background-color: white;
                        transition: all 0.2s ease;
                    }

                    /* Texto de la opción */
                    .option-text {
                        font-size: 13px;
                        color: #555;
                        font-family: 'Montserrat', sans-serif;
                        transition: color 0.2s ease;
                    }

                    /* Estilo cuando el radio está seleccionado */
                    .business-option input[type="radio"]:checked + .option-circle {
                        border-color: #6a1b9a;
                        background-color: white;
                    }

                    /* Punto interior cuando está seleccionado */
                    .business-option input[type="radio"]:checked + .option-circle:after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 10px;
                        height: 10px;
                        border-radius: 50%;
                        background-color: #6a1b9a;
                    }

                    /* Cambio de color del texto cuando está seleccionado */
                    .business-option input[type="radio"]:checked ~ .option-text {
                        color: #6a1b9a;
                        font-weight: 500;
                    }

                    /* Efecto hover */
                    .business-option:hover {
                        background-color: #f0f0f0;
                    }

                    /* Efecto focus para accesibilidad */
                    .business-option input[type="radio"]:focus + .option-circle {
                        box-shadow: 0 0 0 2px rgba(106, 27, 154, 0.2);
                    }
                </style>

                <!-- Descripción del Negocio -->
                <div class="form-group">
                    <div class="label-with-counter">
                        <label for="descripcion_negocio">Descripción del Negocio</label>
                        <span class="word-counter" id="descripcion-counter">0/200</span>
                    </div>
                    <textarea
                        class="form-control"
                        id="descripcion_negocio"
                        name="descripcion_negocio"
                        rows="4"
                        placeholder="Describe brevemente tu negocio (máximo 200 palabras)"
                        required></textarea>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step3-next">Siguiente</button>
                </div>
            </div>

            <!-- Script para el botón del paso 3 -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Configurar el botón del paso 3
                    setTimeout(function() {
                        const step3NextButton = document.getElementById('step3-next');
                        if (step3NextButton) {
                            // Agregar un manejador de eventos para el botón
                            step3NextButton.onclick = function(event) {
                                event.preventDefault();

                                console.log('Botón del paso 3 clickeado');

                                // Verificar si todos los campos del paso 3 son válidos
                                const nombre_negocio = document.getElementById('nombre_negocio').value.trim();
                                const telefono_negocio = document.getElementById('telefono_negocio').value.trim();
                                const whatsapp_negocio = document.getElementById('whatsapp_negocio').value.trim();
                                let tipo_negocio = document.querySelector('input[name="tipo_negocio"]:checked');

                                // Si no se encuentra el tipo de negocio, establecer un valor por defecto
                                if (!tipo_negocio) {
                                    console.warn('No se encontró un tipo de negocio seleccionado, usando valor por defecto');
                                    // Crear un objeto similar a un elemento radio
                                    tipo_negocio = {
                                        value: 'venta' // Valor por defecto
                                    };
                                }

                                const descripcion_negocio = document.getElementById('descripcion_negocio').value.trim();

                                console.log('Datos del formulario:', {
                                    nombre_negocio, telefono_negocio, whatsapp_negocio,
                                    tipo_negocio: tipo_negocio ? tipo_negocio.value : null,
                                    descripcion_negocio
                                });

                                // Verificar que todos los campos requeridos estén completos
                                if (!nombre_negocio || !telefono_negocio || !tipo_negocio || !descripcion_negocio) {
                                    // Mostrar mensaje de error si falta algún campo
                                    window.showErrorPopup(
                                        'Campos incompletos',
                                        'Por favor, complete todos los campos requeridos.',
                                        []
                                    );
                                    return false;
                                }

                                // Crear objeto FormData para enviar los datos
                                const formData = new FormData();
                                formData.append('nombre_negocio', nombre_negocio);
                                formData.append('telefono_negocio', telefono_negocio);
                                formData.append('whatsapp_negocio', whatsapp_negocio);
                                formData.append('tipo_negocio', tipo_negocio.value);
                                formData.append('descripcion_negocio', descripcion_negocio);

                                // Recuperar datos del paso 2 para incluirlos
                                const username = document.getElementById('username').value.trim();
                                const email = document.getElementById('email').value.trim();
                                const backup_email = document.getElementById('backup_email').value.trim();
                                const password = document.getElementById('password').value.trim();
                                const confirm_password = document.getElementById('confirm_password').value.trim();
                                const localFisico = document.querySelector('input[name="local_fisico"]:checked');

                                // Incluir datos del paso 2 en el FormData
                                formData.append('username', username);
                                formData.append('email', email);
                                formData.append('backup_email', backup_email);
                                formData.append('password', password);
                                formData.append('confirm_password', confirm_password);
                                formData.append('local_fisico', localFisico ? localFisico.value : 'No');

                                console.log('Enviando datos al servidor usando AjaxHandler...');

                                // Mostrar indicador de carga
                                const loadingIndicator = document.createElement('div');
                                loadingIndicator.className = 'loading-indicator';
                                loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                                loadingIndicator.style.position = 'fixed';
                                loadingIndicator.style.top = '0';
                                loadingIndicator.style.left = '0';
                                loadingIndicator.style.width = '100%';
                                loadingIndicator.style.height = '100%';
                                loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                                loadingIndicator.style.display = 'flex';
                                loadingIndicator.style.flexDirection = 'column';
                                loadingIndicator.style.justifyContent = 'center';
                                loadingIndicator.style.alignItems = 'center';
                                loadingIndicator.style.zIndex = '9999';
                                loadingIndicator.style.color = 'white';

                                // Agregar estilos para el spinner si no existen
                                if (!document.getElementById('spinner-styles')) {
                                    const spinnerStyles = document.createElement('style');
                                    spinnerStyles.id = 'spinner-styles';
                                    spinnerStyles.textContent = `
                                        .spinner {
                                            border: 5px solid rgba(255, 255, 255, 0.3);
                                            border-radius: 50%;
                                            border-top: 5px solid #6a1b9a;
                                            width: 50px;
                                            height: 50px;
                                            animation: spin 1s linear infinite;
                                            margin: 0 auto 20px auto;
                                        }

                                        @keyframes spin {
                                            0% { transform: rotate(0deg); }
                                            100% { transform: rotate(360deg); }
                                        }
                                    `;
                                    document.head.appendChild(spinnerStyles);
                                }
                                document.body.appendChild(loadingIndicator);

                                // Usar el manejador AJAX para enviar los datos
                                window.AjaxHandler.submitStep3(formData,
                                    // Callback de éxito
                                    function(response) {
                                        console.log('Éxito! Avanzando al paso 4...');

                                        // Eliminar indicador de carga
                                        document.body.removeChild(loadingIndicator);

                                        // Si la respuesta es exitosa, avanzar al paso 4 según el tipo de negocio
                                        const formSteps = document.querySelectorAll('.form-step');
                                        const steps = document.querySelectorAll('.step');

                                        // Ocultar todos los pasos
                                        formSteps.forEach(step => {
                                            step.style.display = 'none';
                                            step.classList.remove('active');
                                        });

                                        steps.forEach(step => {
                                            step.classList.remove('active');
                                        });

                                        // Mostrar el paso 4 según el tipo de negocio
                                        if (tipo_negocio.value === 'venta') {
                                            // Mostrar paso 4 para productos
                                            document.getElementById('step4-productos').style.display = 'block';
                                            document.getElementById('step4-productos').classList.add('active');
                                        } else {
                                            // Mostrar paso 4 para servicios/arriendos
                                            document.getElementById('step4-servicios').style.display = 'block';
                                            document.getElementById('step4-servicios').classList.add('active');
                                        }

                                        // Activar el paso 4 en el indicador de pasos
                                        if (steps[3]) {
                                            steps[3].classList.add('active');
                                        }

                                        console.log('Datos guardados y avanzado al paso 4');
                                    },
                                    // Callback de error
                                    function(errorMessage) {
                                        console.error('Error:', errorMessage);

                                        // Eliminar indicador de carga
                                        document.body.removeChild(loadingIndicator);

                                        // Mostrar mensaje de error
                                        window.showErrorPopup(
                                            'Error en la respuesta',
                                            errorMessage || 'Hubo un error al procesar la respuesta del servidor.',
                                            []
                                        );
                                    }
                                );

                                return false;
                            };
                        } else {
                            console.error('No se encontró el botón del paso 3');
                        }
                    }, 1000);
                });
            </script>

            <!-- Paso 4A: Planes de Suscripción para Venta de Productos -->
            <div class="form-step" id="step4-productos">
                <p class="subscription-subtitle">Elige el plan perfecto para tu negocio</p>

                <!-- Planes de suscripción -->
                <div class="subscription-plans productos-plans">
                    <!-- Plan Gratuito -->
                    <div class="subscription-plan" id="plan-free-productos">
                        <div class="plan-header">
                            <h4 class="plan-title">Gratuita</h4>
                            <div class="plan-price">$0<span class="price-period">/mes</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Características básicas</h5>
                                <div class="feature-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 10 imágenes</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Normal -->
                    <div class="subscription-plan" id="plan-normal-productos">
                        <div class="plan-header">
                            <h4 class="plan-title">Normal</h4>
                            <div class="plan-price">$2.990<span class="price-period">/mes + IVA</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Características básicas</h5>
                                <div class="feature-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 30 imágenes</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value">1 carrusel</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Premium -->
                    <div class="subscription-plan premium-plan" id="plan-premium-productos">
                        <div class="plan-header">
                            <div class="premium-header-content">
                                <div class="plan-icon">👑</div>
                                <h4 class="plan-title">Premium</h4>
                            </div>
                            <div class="plan-price">$5.990<span class="price-period">/mes + IVA</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Características básicas</h5>
                                <div class="feature-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 100 imágenes</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value">Hasta 3 carruseles</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botón para comparar planes -->
                <div class="compare-plans-container">
                    <button type="button" class="btn-comparar-planes" id="comparar-planes-productos">Ve las diferencias entre planes</button>
                </div>

                <!-- Selección de plan -->
                <div class="plan-selection">
                    <h4 class="selection-title">¿Qué inscripción quieres escoger?</h4>
                    <div class="subscription-options">
                        <div class="plan-option">
                            <input type="radio" name="subscription" id="plan-gratuita-productos" value="gratuita">
                            <label for="plan-gratuita-productos">Gratuita</label>
                        </div>
                        <div class="plan-option">
                            <input type="radio" name="subscription" id="plan-normal-productos" value="normal">
                            <label for="plan-normal-productos">Normal</label>
                        </div>
                        <div class="plan-option premium-option">
                            <input type="radio" name="subscription" id="plan-premium-productos" value="premium">
                            <label for="plan-premium-productos"><span class="crown-icon">👑</span>Premium</label>
                        </div>

                        <!-- Script para verificar la selección de plan -->
                        <script>
                            // Este script se ejecutará cuando el DOM esté listo
                            document.addEventListener('DOMContentLoaded', function() {
                                // Verificar que los elementos de radio existan
                                const radioButtons = document.querySelectorAll('input[name="subscription"]');
                                console.log('Elementos de radio encontrados:', radioButtons.length);

                                // Obtener el botón Siguiente
                                const nextButton = document.getElementById('step4-next-save');

                                // Verificar si hay algún plan seleccionado inicialmente
                                const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

                                // Habilitar o deshabilitar el botón según si hay un plan seleccionado
                                if (nextButton) {
                                    if (!planSeleccionado) {
                                        // Si no hay plan seleccionado, deshabilitar el botón visualmente
                                        // pero no interferir con los event listeners
                                        nextButton.style.backgroundColor = '#cccccc';
                                        nextButton.style.color = '#888888';
                                        nextButton.style.cursor = 'not-allowed';
                                    } else {
                                        // Si hay plan seleccionado, habilitar el botón visualmente
                                        nextButton.style.backgroundColor = '#6a1b9a';
                                        nextButton.style.color = 'white';
                                        nextButton.style.cursor = 'pointer';
                                    }
                                }

                                // Agregar event listeners a los elementos de radio
                                radioButtons.forEach(function(radio) {
                                    radio.addEventListener('change', function() {
                                        console.log('Radio seleccionado:', this.value);

                                        // Cuando se selecciona un plan, habilitar el botón Siguiente visualmente
                                        if (nextButton) {
                                            nextButton.style.backgroundColor = '#6a1b9a';
                                            nextButton.style.color = 'white';
                                            nextButton.style.cursor = 'pointer';
                                        }
                                    });
                                });
                            });
                        </script>
                    </div>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step4-next-save" style="background-color: #6a1b9a; color: white; font-weight: bold; cursor: pointer;">Siguiente</button>
                </div>

                <!-- Script para asegurarse de que el botón esté habilitado y funcione correctamente -->
                <script>
                    // Ejecutar inmediatamente
                    (function() {
                        console.log('Inicializando botón del paso 4 (productos)');
                        // Obtener el botón
                        const nextButton = document.getElementById('step4-next-save');
                        if (nextButton) {
                            // Asegurarse de que el botón esté habilitado
                            nextButton.classList.remove('btn-disabled');
                            nextButton.removeAttribute('disabled');
                            console.log('Botón del paso 4 (productos) habilitado');

                            // Asignar directamente el manejador de eventos
                            nextButton.onclick = function(event) {
                                event.preventDefault();
                                console.log('Botón del paso 4 (productos) clickeado (script inline)');

                                // Verificar si hay un plan seleccionado
                                const planSeleccionado = document.querySelector('input[name="subscription"]:checked');
                                if (!planSeleccionado) {
                                    alert('Por favor, seleccione un plan antes de continuar.');
                                    return false;
                                }

                                // Crear objeto FormData para enviar los datos
                                const formData = new FormData();
                                formData.append('plan_suscripcion', planSeleccionado.value);

                                // Mostrar indicador de carga
                                const loadingIndicator = document.createElement('div');
                                loadingIndicator.className = 'loading-indicator';
                                loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                                loadingIndicator.style.position = 'fixed';
                                loadingIndicator.style.top = '0';
                                loadingIndicator.style.left = '0';
                                loadingIndicator.style.width = '100%';
                                loadingIndicator.style.height = '100%';
                                loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                                loadingIndicator.style.display = 'flex';
                                loadingIndicator.style.flexDirection = 'column';
                                loadingIndicator.style.justifyContent = 'center';
                                loadingIndicator.style.alignItems = 'center';
                                loadingIndicator.style.zIndex = '9999';
                                loadingIndicator.style.color = 'white';
                                document.body.appendChild(loadingIndicator);

                                // Enviar solicitud AJAX directamente
                                const xhr = new XMLHttpRequest();
                                xhr.open('POST', 'process_step4.php', true);

                                xhr.onload = function() {
                                    if (xhr.status === 200) {
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            console.log('Respuesta del servidor:', response);

                                            // Eliminar indicador de carga
                                            document.body.removeChild(loadingIndicator);

                                            if (response.success) {
                                                // Verificar si es plan gratuito
                                                if (planSeleccionado.value === 'gratuita') {
                                                    // Mostrar popup de bienvenida
                                                    const welcomePopup = document.getElementById('welcome-popup-overlay');
                                                    if (welcomePopup) {
                                                        welcomePopup.style.display = 'flex';
                                                    } else {
                                                        alert('Gracias por registrarte con el plan gratuito.');
                                                        window.location.href = 'index.php';
                                                    }
                                                } else {
                                                    // Avanzar al paso 5
                                                    const formSteps = document.querySelectorAll('.form-step');
                                                    const steps = document.querySelectorAll('.step');

                                                    // Ocultar todos los pasos
                                                    formSteps.forEach(function(step) {
                                                        step.style.display = 'none';
                                                        step.classList.remove('active');
                                                    });

                                                    steps.forEach(function(step) {
                                                        step.classList.remove('active');
                                                    });

                                                    // Mostrar el paso 5
                                                    if (formSteps[4]) {
                                                        formSteps[4].style.display = 'block';
                                                        formSteps[4].classList.add('active');
                                                    }

                                                    if (steps[4]) {
                                                        steps[4].classList.add('active');
                                                    }
                                                }
                                            } else {
                                                alert('Error: ' + (response.message || 'Hubo un error al procesar la solicitud.'));
                                            }
                                        } catch (e) {
                                            console.error('Error al parsear la respuesta:', e);
                                            document.body.removeChild(loadingIndicator);
                                            alert('Error al procesar la respuesta del servidor.');
                                        }
                                    } else {
                                        console.error('Error HTTP:', xhr.status);
                                        document.body.removeChild(loadingIndicator);
                                        alert('Error de conexión: ' + xhr.status);
                                    }
                                };

                                xhr.onerror = function() {
                                    console.error('Error de red');
                                    document.body.removeChild(loadingIndicator);
                                    alert('Error de conexión con el servidor.');
                                };

                                xhr.send(formData);
                                return false;
                            };
                        }
                    })();
                </script>

                <!-- El script para manejar el botón del paso 4 ahora está en js/step4-handler.js -->

                <!-- Pop-up de Bienvenida para Plan Gratuito -->
                <div class="welcome-popup-overlay" id="welcome-popup-overlay" style="display: none;">
                    <div class="welcome-popup">
                        <div class="welcome-popup-header">
                            <h3>¡Bienvenido a AunClick!</h3>
                            <button type="button" class="welcome-popup-close">&times;</button>
                        </div>
                        <div class="welcome-popup-content">
                            <div class="welcome-icon">🎉</div>
                            <h4>¡Registro completado con éxito!</h4>
                            <p>Tu cuenta ha sido creada correctamente con el plan Gratuito.</p>
                            <p>Ahora puedes comenzar a utilizar nuestra plataforma para dar visibilidad a tu negocio.</p>
                            <div class="welcome-actions">
                                <button type="button" class="btn btn-primary" id="welcome-finish-btn">Finalizar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estilos para el popup de bienvenida -->
                <style>
                    .welcome-popup-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.7);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 1000;
                    }

                    .welcome-popup {
                        background-color: white;
                        border-radius: 10px;
                        width: 90%;
                        max-width: 500px;
                        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                        overflow: hidden;
                    }

                    .welcome-popup-header {
                        background-color: #6a1b9a;
                        color: white;
                        padding: 15px 20px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }

                    .welcome-popup-header h3 {
                        margin: 0;
                        font-size: 18px;
                    }

                    .welcome-popup-close {
                        background: none;
                        border: none;
                        color: white;
                        font-size: 24px;
                        cursor: pointer;
                    }

                    .welcome-popup-content {
                        padding: 30px;
                        text-align: center;
                    }

                    .welcome-icon {
                        font-size: 48px;
                        margin-bottom: 20px;
                    }

                    .welcome-popup-content h4 {
                        color: #6a1b9a;
                        margin-bottom: 15px;
                    }

                    .welcome-popup-content p {
                        margin-bottom: 15px;
                        color: #555;
                    }

                    .welcome-actions {
                        margin-top: 25px;
                    }

                    #welcome-finish-btn {
                        background-color: #6a1b9a;
                        color: white;
                        border: none;
                        padding: 10px 25px;
                        border-radius: 5px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    }

                    #welcome-finish-btn:hover {
                        background-color: #8e24aa;
                    }
                </style>

                <!-- El script para manejar el botón del paso 4 ahora está en js/step4-handler.js -->

                <!-- Pop-ups individuales para cada plan de productos -->
                <!-- Pop-up Plan Gratuito Productos -->
                <div class="plan-popup-overlay" id="plan-free-popup-productos">
                    <div class="plan-popup">
                        <div class="plan-popup-header">
                            <h3>Plan Gratuito</h3>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Gratuito</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 10 imágenes</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Baja</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de productos</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información de la tienda</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up Plan Normal Productos -->
                <div class="plan-popup-overlay" id="plan-normal-popup-productos">
                    <div class="plan-popup">
                        <div class="plan-popup-header">
                            <h3>Plan Normal</h3>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Normal</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 30 imágenes</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value">1 carrusel</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Media</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de productos</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información de la tienda</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up Plan Premium Productos -->
                <div class="plan-popup-overlay" id="plan-premium-popup-productos">
                    <div class="plan-popup">
                        <div class="plan-popup-header premium-header">
                            <div class="premium-header-content">
                                <div class="plan-icon">👑</div>
                                <h3>Plan Premium</h3>
                            </div>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Premium</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Imágenes permitidas</span>
                                    <span class="feature-value">Hasta 100 imágenes</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value">Hasta 3 carruseles</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Alta</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de productos</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información de la tienda</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up de comparación de planes para productos -->
                <div class="plans-comparison-overlay" id="plans-comparison-overlay-productos">
                    <div class="plans-comparison-popup">
                        <button type="button" class="comparison-popup-close">&times;</button>
                        <div class="comparison-popup-content" style="padding-top:0; margin-top:0;">
                            <div class="comparison-plans" style="margin-top:0; padding-top:0;">
                                <!-- Plan Gratuito -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <h4>Inscripción Gratuita</h4>
                                        <div class="plan-price">$0<span>/mes</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Imágenes permitidas</span>
                                                <span class="feature-value">Hasta 10 imágenes</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Baja</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de productos</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información de la tienda</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Normal -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <h4>Inscripción Normal</h4>
                                        <div class="plan-price">$2.990<span>/mes + IVA</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Imágenes permitidas</span>
                                                <span class="feature-value">Hasta 30 imágenes</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value">1 carrusel</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Media</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de productos</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información de la tienda</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Premium -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <div class="premium-header-content">
                                            <div class="plan-icon">👑</div>
                                            <h4>Inscripción Premium</h4>
                                        </div>
                                        <div class="plan-price">$5.990<span>/mes + IVA</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Imágenes permitidas</span>
                                                <span class="feature-value">Hasta 100 imágenes</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value">Hasta 3 carruseles</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Alta</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de productos</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información de la tienda</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="comparison-popup-footer">
                            <button type="button" class="btn comparison-popup-close-btn">Cerrar</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paso 4B: Planes de Suscripción para Servicios y Arriendos -->
            <div class="form-step" id="step4-servicios">
                <p class="subscription-subtitle">Elige el plan perfecto para tu negocio</p>

                <!-- Planes de suscripción -->
                <div class="subscription-plans servicios-plans">
                    <!-- Plan Gratuito -->
                    <div class="subscription-plan" id="plan-free-servicios">
                        <div class="plan-header">
                            <h4 class="plan-title">Gratuita</h4>
                            <div class="plan-price">$0<span class="price-period">/mes</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Espacio disponible</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Normal -->
                    <div class="subscription-plan" id="plan-normal-servicios">
                        <div class="plan-header">
                            <h4 class="plan-title">Normal</h4>
                            <div class="plan-price">$2.990<span class="price-period">/mes + IVA</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Espacio disponible</h5>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Premium -->
                    <div class="subscription-plan premium-plan" id="plan-premium-servicios">
                        <div class="plan-header">
                            <div class="premium-header-content">
                                <div class="plan-icon">👑</div>
                                <h4 class="plan-title">Premium</h4>
                            </div>
                            <div class="plan-price">$5.990<span class="price-period">/mes + IVA</span></div>
                        </div>
                        <div class="plan-details hidden-element">
                            <div class="plan-section">
                                <h5 class="section-title">Espacio disponible</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Botón para comparar planes -->
                <div class="compare-plans-container">
                    <button type="button" class="btn-comparar-planes" id="comparar-planes-servicios">Ve las diferencias entre planes</button>
                </div>

                <!-- Selección de plan -->
                <div class="plan-selection">
                    <h4 class="selection-title">¿Qué inscripción quieres escoger?</h4>
                    <div class="subscription-options">
                        <div class="plan-option">
                            <input type="radio" name="subscription" id="plan-gratuita-servicios" value="gratuita">
                            <label for="plan-gratuita-servicios">Gratuita</label>
                        </div>
                        <div class="plan-option">
                            <input type="radio" name="subscription" id="plan-normal-servicios" value="normal">
                            <label for="plan-normal-servicios">Normal</label>
                        </div>
                        <div class="plan-option premium-option">
                            <input type="radio" name="subscription" id="plan-premium-servicios" value="premium">
                            <label for="plan-premium-servicios"><span class="crown-icon">👑</span>Premium</label>
                        </div>

                        <!-- Script para verificar la selección de plan en servicios -->
                        <script>
                            // Este script se ejecutará inmediatamente
                            (function() {
                                // Verificar que los elementos de radio existan
                                const radioButtons = document.querySelectorAll('input[name="subscription"]');
                                console.log('Elementos de radio encontrados en servicios:', radioButtons.length);

                                // Obtener el botón Siguiente
                                const nextButton = document.getElementById('step4-next-save-servicios');

                                // Verificar si hay algún plan seleccionado inicialmente
                                const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

                                // Agregar event listeners a los elementos de radio
                                radioButtons.forEach(function(radio) {
                                    radio.addEventListener('change', function() {
                                        console.log('Radio seleccionado en servicios:', this.value);

                                        // Cuando se selecciona un plan, habilitar el botón Siguiente
                                        if (nextButton) {
                                            nextButton.classList.remove('btn-disabled');
                                            nextButton.style.backgroundColor = '#6a1b9a';
                                            nextButton.style.color = 'white';
                                            nextButton.style.cursor = 'pointer';
                                        }
                                    });
                                });

                                // Configurar el botón del paso 4 para servicios
                                if (nextButton) {
                                    // Agregar un manejador de eventos para el botón
                                    nextButton.addEventListener('click', function(event) {
                                        event.preventDefault();
                                        console.log('Botón del paso 4 para servicios clickeado');

                                        // Verificar si se ha seleccionado un plan
                                        let planSeleccionado = document.querySelector('input[name="subscription"]:checked');
                                        console.log('Plan seleccionado en servicios:', planSeleccionado ? planSeleccionado.value : 'ninguno');

                                        if (!planSeleccionado) {
                                            // Mostrar mensaje de error si no hay plan seleccionado
                                            alert('Por favor, seleccione un plan antes de continuar.');
                                            return false;
                                        }

                                        // Crear objeto FormData para enviar los datos
                                        const formData = new FormData();
                                        formData.append('plan_suscripcion', planSeleccionado.value);

                                        // Verificar si hay datos en la sesión
                                        console.log('Verificando datos en la sesión antes de enviar...');

                                        // Agregar un campo adicional para depuración
                                        formData.append('debug', 'true');
                                        formData.append('timestamp', Date.now());

                                        console.log('Enviando datos al servidor usando AjaxHandler...');

                                        // Mostrar indicador de carga
                                        const loadingIndicator = document.createElement('div');
                                        loadingIndicator.className = 'loading-indicator';
                                        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                                        loadingIndicator.style.position = 'fixed';
                                        loadingIndicator.style.top = '0';
                                        loadingIndicator.style.left = '0';
                                        loadingIndicator.style.width = '100%';
                                        loadingIndicator.style.height = '100%';
                                        loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                                        loadingIndicator.style.display = 'flex';
                                        loadingIndicator.style.flexDirection = 'column';
                                        loadingIndicator.style.justifyContent = 'center';
                                        loadingIndicator.style.alignItems = 'center';
                                        loadingIndicator.style.zIndex = '9999';
                                        loadingIndicator.style.color = 'white';
                                        document.body.appendChild(loadingIndicator);

                                        // Usar el manejador AJAX para enviar los datos
                                        window.AjaxHandler.submitStep4(formData,
                                            // Callback de éxito
                                            function(response) {
                                                console.log('Éxito! Registro completado:', response);

                                                // Eliminar indicador de carga
                                                document.body.removeChild(loadingIndicator);

                                                // Mostrar alerta con el ID de inserción
                                                if (response.data && response.data.insert_id) {
                                                    console.log('ID de inserción:', response.data.insert_id);
                                                    console.log('Usuario registrado correctamente. ID: ' + response.data.insert_id);
                                                    alert('Usuario registrado correctamente. ID: ' + response.data.insert_id);
                                                } else {
                                                    console.log('No se recibió ID de inserción en la respuesta');
                                                    alert('Usuario registrado correctamente.');
                                                }

                                                // Forzar una pausa para asegurarnos de que los datos se guarden
                                                setTimeout(function() {
                                                    // Verificar si es plan gratuito
                                                    if (planSeleccionado.value === 'gratuita') {
                                                        console.log('Plan gratuito seleccionado, mostrando popup de bienvenida');
                                                        // Mostrar popup de bienvenida para plan gratuito
                                                        const welcomePopup = document.getElementById('welcome-popup-overlay');
                                                        if (welcomePopup) {
                                                            welcomePopup.style.display = 'flex';
                                                            document.body.style.overflow = 'hidden'; // Evitar scroll en el fondo

                                                            // Configurar botón de finalizar
                                                            const finishButton = document.getElementById('welcome-finish-btn');
                                                            if (finishButton) {
                                                                finishButton.onclick = function() {
                                                                    // Redirigir a la página de inicio o dashboard
                                                                    window.location.href = 'index.php';
                                                                };
                                                            }

                                                            // Configurar botón de cerrar
                                                            const closeButton = welcomePopup.querySelector('.welcome-popup-close');
                                                            if (closeButton) {
                                                                closeButton.onclick = function() {
                                                                    // Redirigir a la página de inicio o dashboard
                                                                    window.location.href = 'index.php';
                                                                };
                                                            }
                                                        } else {
                                                            console.error('No se encontró el elemento welcome-popup-overlay');
                                                            alert('Gracias por registrarte con el plan gratuito.');
                                                            window.location.href = 'index.php';
                                                        }
                                                    } else {
                                                        console.log('Plan de pago seleccionado, avanzando al paso 5');
                                                        // Para planes de pago, avanzar al paso 5 (página de pago)
                                                        const formSteps = document.querySelectorAll('.form-step');
                                                        const steps = document.querySelectorAll('.step');

                                                        // Ocultar todos los pasos
                                                        formSteps.forEach(step => {
                                                            step.style.display = 'none';
                                                            step.classList.remove('active');
                                                        });

                                                        steps.forEach(step => {
                                                            step.classList.remove('active');
                                                        });

                                                        // Mostrar el paso 5 (pago)
                                                        const step5 = document.getElementById('step5');
                                                        if (step5) {
                                                            step5.style.display = 'block';
                                                            step5.classList.add('active');

                                                            // Activar el paso 5 en el indicador de pasos
                                                            if (steps[4]) {
                                                                steps[4].classList.add('active');
                                                            }

                                                            console.log('Avanzando al paso de pago');
                                                        } else {
                                                            console.error('No se encontró el elemento step5');
                                                            // Intentar con step5-pago como alternativa
                                                            const step5Pago = document.getElementById('step5-pago');
                                                            if (step5Pago) {
                                                                step5Pago.style.display = 'block';
                                                                step5Pago.classList.add('active');

                                                                // Activar el paso 5 en el indicador de pasos
                                                                if (steps[4]) {
                                                                    steps[4].classList.add('active');
                                                                }

                                                                console.log('Avanzando al paso de pago (alternativo)');
                                                            } else {
                                                                console.error('No se encontró ningún elemento para el paso 5');
                                                                alert('Error al avanzar al paso de pago. Por favor, contacte al administrador.');
                                                            }
                                                        }
                                                    }
                                                }, 1000); // Esperar 1 segundo para asegurarnos de que los datos se guarden
                                            },
                                            // Callback de error
                                            function(errorMessage) {
                                                console.error('Error:', errorMessage);

                                                // Eliminar indicador de carga
                                                document.body.removeChild(loadingIndicator);

                                                // Mostrar mensaje de error
                                                window.showErrorPopup(
                                                    'Error en la respuesta',
                                                    errorMessage || 'Hubo un error al procesar la respuesta del servidor.',
                                                    []
                                                );
                                            }
                                        );

                                        return false;
                                    });
                                }
                            })();
                        </script>
                    </div>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step4-next-save-servicios" style="background-color: #6a1b9a; color: white; font-weight: bold; cursor: pointer;">Siguiente</button>
                </div>

                <script>
                    // Ejecutar inmediatamente
                    (function() {
                        console.log('Inicializando botón del paso 4 (servicios)');
                        // Obtener el botón
                        const nextButton = document.getElementById('step4-next-save-servicios');
                        if (nextButton) {
                            // Asegurarse de que el botón esté habilitado
                            nextButton.classList.remove('btn-disabled');
                            nextButton.removeAttribute('disabled');
                            console.log('Botón del paso 4 (servicios) habilitado');

                            // Asignar directamente el manejador de eventos
                            nextButton.onclick = function(event) {
                                event.preventDefault();
                                console.log('Botón del paso 4 (servicios) clickeado (script inline)');

                                // Verificar si hay un plan seleccionado
                                const planSeleccionado = document.querySelector('#step4-servicios input[name="subscription"]:checked');
                                if (!planSeleccionado) {
                                    alert('Por favor, seleccione un plan antes de continuar.');
                                    return false;
                                }

                                // Crear objeto FormData para enviar los datos
                                const formData = new FormData();
                                formData.append('plan_suscripcion', planSeleccionado.value);

                                // Mostrar indicador de carga
                                const loadingIndicator = document.createElement('div');
                                loadingIndicator.className = 'loading-indicator';
                                loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                                loadingIndicator.style.position = 'fixed';
                                loadingIndicator.style.top = '0';
                                loadingIndicator.style.left = '0';
                                loadingIndicator.style.width = '100%';
                                loadingIndicator.style.height = '100%';
                                loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                                loadingIndicator.style.display = 'flex';
                                loadingIndicator.style.flexDirection = 'column';
                                loadingIndicator.style.justifyContent = 'center';
                                loadingIndicator.style.alignItems = 'center';
                                loadingIndicator.style.zIndex = '9999';
                                loadingIndicator.style.color = 'white';
                                document.body.appendChild(loadingIndicator);

                                // Enviar solicitud AJAX directamente
                                const xhr = new XMLHttpRequest();
                                xhr.open('POST', 'process_step4.php', true);

                                xhr.onload = function() {
                                    if (xhr.status === 200) {
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            console.log('Respuesta del servidor:', response);

                                            // Eliminar indicador de carga
                                            document.body.removeChild(loadingIndicator);

                                            if (response.success) {
                                                // Verificar si es plan gratuito
                                                if (planSeleccionado.value === 'gratuita') {
                                                    // Mostrar popup de bienvenida
                                                    const welcomePopup = document.getElementById('welcome-popup-overlay');
                                                    if (welcomePopup) {
                                                        welcomePopup.style.display = 'flex';
                                                    } else {
                                                        alert('Gracias por registrarte con el plan gratuito.');
                                                        window.location.href = 'index.php';
                                                    }
                                                } else {
                                                    // Avanzar al paso 5
                                                    const formSteps = document.querySelectorAll('.form-step');
                                                    const steps = document.querySelectorAll('.step');

                                                    // Ocultar todos los pasos
                                                    formSteps.forEach(function(step) {
                                                        step.style.display = 'none';
                                                        step.classList.remove('active');
                                                    });

                                                    steps.forEach(function(step) {
                                                        step.classList.remove('active');
                                                    });

                                                    // Mostrar el paso 5
                                                    if (formSteps[4]) {
                                                        formSteps[4].style.display = 'block';
                                                        formSteps[4].classList.add('active');
                                                    }

                                                    if (steps[4]) {
                                                        steps[4].classList.add('active');
                                                    }
                                                }
                                            } else {
                                                alert('Error: ' + (response.message || 'Hubo un error al procesar la solicitud.'));
                                            }
                                        } catch (e) {
                                            console.error('Error al parsear la respuesta:', e);
                                            document.body.removeChild(loadingIndicator);
                                            alert('Error al procesar la respuesta del servidor.');
                                        }
                                    } else {
                                        console.error('Error HTTP:', xhr.status);
                                        document.body.removeChild(loadingIndicator);
                                        alert('Error de conexión: ' + xhr.status);
                                    }
                                };

                                xhr.onerror = function() {
                                    console.error('Error de red');
                                    document.body.removeChild(loadingIndicator);
                                    alert('Error de conexión con el servidor.');
                                };

                                xhr.send(formData);
                                return false;
                            };
                        }
                    })();
                </script>

                <script>
                    // Esperar a que el DOM esté completamente cargado
                    document.addEventListener('DOMContentLoaded', function() {
                        console.log('DOM cargado, configurando botón del paso 4 para servicios...');

                        // Obtener el botón del paso 4 para servicios
                        const step4NextButton = document.getElementById('step4-next-save-servicios');

                        if (step4NextButton) {
                            console.log('Botón del paso 4 para servicios encontrado:', step4NextButton);

                            // No clonar el botón, solo asegurarse de que tenga el evento correcto
                            step4NextButton.onclick = function(event) {
                                event.preventDefault();
                                console.log('Botón del paso 4 para servicios clickeado (DOM loaded)');

                                // Verificar si hay un plan seleccionado
                                const planSeleccionado = document.querySelector('#step4-servicios input[name="subscription"]:checked');
                                if (!planSeleccionado) {
                                    alert('Por favor, seleccione un plan antes de continuar.');
                                    return false;
                                }

                                // Crear objeto FormData para enviar los datos
                                const formData = new FormData();
                                formData.append('plan_suscripcion', planSeleccionado.value);

                                // Mostrar indicador de carga
                                const loadingIndicator = document.createElement('div');
                                loadingIndicator.className = 'loading-indicator';
                                loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                                loadingIndicator.style.position = 'fixed';
                                loadingIndicator.style.top = '0';
                                loadingIndicator.style.left = '0';
                                loadingIndicator.style.width = '100%';
                                loadingIndicator.style.height = '100%';
                                loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                                loadingIndicator.style.display = 'flex';
                                loadingIndicator.style.flexDirection = 'column';
                                loadingIndicator.style.justifyContent = 'center';
                                loadingIndicator.style.alignItems = 'center';
                                loadingIndicator.style.zIndex = '9999';
                                loadingIndicator.style.color = 'white';
                                document.body.appendChild(loadingIndicator);

                                // Enviar solicitud AJAX directamente
                                const xhr = new XMLHttpRequest();
                                xhr.open('POST', 'process_step4.php', true);

                                xhr.onload = function() {
                                    if (xhr.status === 200) {
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            console.log('Respuesta del servidor:', response);

                                            // Eliminar indicador de carga
                                            document.body.removeChild(loadingIndicator);

                                            if (response.success) {
                                                // Verificar si es plan gratuito
                                                if (planSeleccionado.value === 'gratuita') {
                                                    // Mostrar popup de bienvenida
                                                    const welcomePopup = document.getElementById('welcome-popup-overlay');
                                                    if (welcomePopup) {
                                                        welcomePopup.style.display = 'flex';
                                                    } else {
                                                        alert('Gracias por registrarte con el plan gratuito.');
                                                        window.location.href = 'index.php';
                                                    }
                                                } else {
                                                    // Avanzar al paso 5
                                                    const formSteps = document.querySelectorAll('.form-step');
                                                    const steps = document.querySelectorAll('.step');

                                                    // Ocultar todos los pasos
                                                    formSteps.forEach(function(step) {
                                                        step.style.display = 'none';
                                                        step.classList.remove('active');
                                                    });

                                                    steps.forEach(function(step) {
                                                        step.classList.remove('active');
                                                    });

                                                    // Mostrar el paso 5
                                                    if (formSteps[4]) {
                                                        formSteps[4].style.display = 'block';
                                                        formSteps[4].classList.add('active');
                                                    }

                                                    if (steps[4]) {
                                                        steps[4].classList.add('active');
                                                    }
                                                }
                                            } else {
                                                alert('Error: ' + (response.message || 'Hubo un error al procesar la solicitud.'));
                                            }
                                        } catch (e) {
                                            console.error('Error al parsear la respuesta:', e);
                                            document.body.removeChild(loadingIndicator);
                                            alert('Error al procesar la respuesta del servidor.');
                                        }
                                    } else {
                                        console.error('Error HTTP:', xhr.status);
                                        document.body.removeChild(loadingIndicator);
                                        alert('Error de conexión: ' + xhr.status);
                                    }
                                };

                                xhr.onerror = function() {
                                    console.error('Error de red');
                                    document.body.removeChild(loadingIndicator);
                                    alert('Error de conexión con el servidor.');
                                };

                                xhr.send(formData);
                                return false;
                            };
                        }
                    });
                </script>

                <!-- Pop-ups individuales para cada plan de servicios -->
                <!-- Pop-up Plan Gratuito Servicios -->
                <div class="plan-popup-overlay" id="plan-free-popup-servicios">
                    <div class="plan-popup">
                        <div class="plan-popup-header">
                            <h3>Plan Gratuito</h3>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Gratuito</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Imágen de la Tienda o Tarjeta de Presentación</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Baja</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información del servicio</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up Plan Normal Servicios -->
                <div class="plan-popup-overlay" id="plan-normal-popup-servicios">
                    <div class="plan-popup">
                        <div class="plan-popup-header">
                            <h3>Plan Normal</h3>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Normal</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Podrás Subir 10 imágenes</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value negative">✕</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Media</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información del servicio</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up Plan Premium Servicios -->
                <div class="plan-popup-overlay" id="plan-premium-popup-servicios">
                    <div class="plan-popup">
                        <div class="plan-popup-header premium-header">
                            <div class="premium-header-content">
                                <div class="plan-icon">👑</div>
                                <h3>Plan Premium</h3>
                            </div>
                            <button type="button" class="plan-popup-close">&times;</button>
                        </div>
                        <div class="plan-popup-content">
                            <h4>Características del Plan Premium</h4>
                            <div class="popup-section">
                                <h5>Características básicas</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Podrás Subir 30 imágenes</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Carrusel de productos</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Estadísticas del panel</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Visibilidad</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página principal</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Aparece en página de secciones</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Buscadores</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Prioridad de aparición</span>
                                    <span class="feature-value">Alta</span>
                                </div>
                            </div>
                            <div class="popup-section">
                                <h5>Funcionalidades</h5>
                                <div class="popup-item">
                                    <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Panel de control</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Editar información</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Título</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Información del servicio</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Banner</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                                <div class="popup-item">
                                    <span class="feature-name">Slogan</span>
                                    <span class="feature-value positive">✓</span>
                                </div>
                            </div>
                            <div class="plan-popup-footer">
                                <button type="button" class="btn plan-popup-close-btn">Cerrar</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pop-up de comparación de planes para servicios -->
                <div class="plans-comparison-overlay" id="plans-comparison-overlay-servicios">
                    <div class="plans-comparison-popup">
                        <button type="button" class="comparison-popup-close">&times;</button>
                        <div class="comparison-popup-content" style="padding-top:0; margin-top:0;">
                            <div class="comparison-plans" style="margin-top:0; padding-top:0;">
                                <!-- Plan Gratuito -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <h4>Inscripción Gratuita</h4>
                                        <div class="plan-price">$0<span>/mes</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Imágen de la Tienda o Tarjeta de Presentación</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Baja</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Normal -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <h4>Inscripción Normal</h4>
                                        <div class="plan-price">$2.990<span>/mes + IVA</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Podrás Subir 10 imágenes</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value negative">✕</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Media</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Plan Premium -->
                                <div class="comparison-plan">
                                    <div class="comparison-plan-header">
                                        <div class="premium-header-content">
                                            <div class="plan-icon">👑</div>
                                            <h4>Inscripción Premium</h4>
                                        </div>
                                        <div class="plan-price">$5.990<span>/mes + IVA</span></div>
                                    </div>
                                    <div class="comparison-plan-details">
                                        <div class="comparison-section">
                                            <h5>Características básicas</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Podrás Subir 100 imágenes</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Carrusel de productos</span>
                                                <span class="feature-value">2 carruseles</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Estadísticas del panel</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Visibilidad</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página principal</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Aparece en página de secciones</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Buscadores</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Prioridad de aparición</span>
                                                <span class="feature-value">Alta</span>
                                            </div>
                                        </div>
                                        <div class="comparison-section">
                                            <h5>Funcionalidades</h5>
                                            <div class="comparison-item">
                                                <span class="feature-name">Categorías de Servicio/Arriendo</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Panel de control</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Editar información</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Título</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Banner</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                            <div class="comparison-item">
                                                <span class="feature-name">Slogan</span>
                                                <span class="feature-value positive">✓</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="comparison-popup-footer">
                            <button type="button" class="btn comparison-popup-close-btn">Cerrar</button>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Paso 5: Instrucciones de pago y facturación -->
            <div class="form-step" id="step5-pago">
                <h3 class="payment-title">Instrucciones de Pago</h3>

                <div class="payment-info-container">
                    <div class="payment-info-section">
                        <h4>Información Importante</h4>
                        <p class="justified-text">Tu página se activará en <strong>4 horas después del depósito</strong> si se realiza antes de las 19:00 hrs. Si se hace después de ese horario, la página se activará a las 10:00 am del día siguiente.</p>

                        <p class="justified-text">Por favor, envía el comprobante de transferencia a <strong><EMAIL></strong> adjuntando el comprobante y el correo electrónico con el que has creado la página.</p>
                    </div>

                    <div class="payment-info-section">
                        <h4>Datos Bancarios</h4>
                        <div class="bank-details">
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">Banco:</span>
                                <span class="bank-detail-value">Banco Estado</span>
                            </div>
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">Tipo de Cuenta:</span>
                                <span class="bank-detail-value">Cuenta Corriente</span>
                            </div>
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">Número de Cuenta:</span>
                                <span class="bank-detail-value">***********</span>
                            </div>
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">Nombre:</span>
                                <span class="bank-detail-value">Un Click SpA</span>
                            </div>
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">RUT:</span>
                                <span class="bank-detail-value">76.123.456-7</span>
                            </div>
                            <div class="bank-detail-item">
                                <span class="bank-detail-label">Email:</span>
                                <span class="bank-detail-value"><EMAIL></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="billing-info-container">
                    <h4>Información de Facturación</h4>

                    <div class="form-group">
                        <label>¿Requiere factura o boleta?</label>
                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="documento" value="factura" id="factura">
                                <span>Factura</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="documento" value="boleta" id="boleta">
                                <span>Boleta</span>
                            </label>
                        </div>
                    </div>

                    <div id="factura-fields" style="display: none;">
                        <div class="form-group">
                            <label for="empresa">Nombre de la Empresa</label>
                            <input type="text" id="empresa" name="empresa" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="rut_empresa">RUT de la Empresa</label>
                            <input type="text" id="rut_empresa" name="rut_empresa" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="direccion_empresa">Dirección</label>
                            <input type="text" id="direccion_empresa" name="direccion_empresa" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="giro_empresa">Giro</label>
                            <input type="text" id="giro_empresa" name="giro_empresa" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="telefono_empresa">Teléfono</label>
                            <input type="text" id="telefono_empresa" name="telefono_empresa" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="correo_empresa">Correo Electrónico</label>
                            <input type="email" id="correo_empresa" name="correo_empresa" class="form-control">
                        </div>
                    </div>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="finalizar-registro">Finalizar</button>
                </div>
            </div>
        </form>

        <div class="login-link">
            ¿Ya tienes una cuenta? <a href="login.php">Inicia sesión aquí</a>
        </div>

        <div class="privacy-link">
            <a href="#">Privacidad</a>
        </div>
    </div>

    <!-- Agregar los popups al final del body pero antes del cierre -->
    <div class="plan-popup-overlay" id="free-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Gratuito</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Gratuita - $0/mes</h4>
                <p>Con nuestro plan gratuito podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Imágenes permitidas</span>
                        <span class="feature-value">Hasta 10 imágenes</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Baja</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de productos</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información de la tienda</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="plan-popup-overlay" id="normal-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Normal</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Normal - $2.990/mes + IVA</h4>
                <p>Con nuestro plan normal podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Imágenes permitidas</span>
                        <span class="feature-value">Hasta 30 imágenes</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value">1 carrusel</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Media</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de productos</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información de la tienda</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="plan-popup-overlay" id="premium-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Premium</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Premium - $5.990/mes + IVA</h4>
                <p>Con nuestro plan premium podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Imágenes permitidas</span>
                        <span class="feature-value">Hasta 100 imágenes</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value">Hasta 3 carruseles</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Alta</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de productos</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información de la tienda</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Soporte prioritario</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popups para Servicios y Arriendos -->
    <div class="plan-popup-overlay" id="free-servicios-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Gratuito - Servicios/Arriendo</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Gratuita - $0/mes</h4>
                <p>Con nuestro plan gratuito podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Imágen de la Tienda o Tarjeta de Presentación</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Baja</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de Servicio/Arriendo</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="plan-popup-overlay" id="normal-servicios-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Normal - Servicios/Arriendo</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Normal - $2.990/mes + IVA</h4>
                <p>Con nuestro plan normal podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Podrás Subir 10 imágenes</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value negative">✕</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Media</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de Servicio/Arriendo</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="plan-popup-overlay" id="premium-servicios-plan-popup">
        <div class="plan-popup">
            <div class="plan-popup-header">
                <h3>Plan Premium - Servicios/Arriendo</h3>
                <button type="button" class="plan-popup-close">&times;</button>
            </div>
            <div class="plan-popup-content">
                <h4>Inscripción Premium - $5.990/mes + IVA</h4>
                <p>Con nuestro plan premium podrás:</p>

                <div class="popup-section">
                    <h5>Características básicas</h5>
                    <div class="popup-item">
                        <span class="feature-name">Podrás Subir 100 imágenes</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Carrusel de productos</span>
                        <span class="feature-value">2 carruseles</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Estadísticas del panel</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Visibilidad</h5>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página principal</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Aparece en página de secciones</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Buscadores</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Prioridad de aparición</span>
                        <span class="feature-value">Alta</span>
                    </div>
                </div>

                <div class="popup-section">
                    <h5>Funcionalidades</h5>
                    <div class="popup-item">
                        <span class="feature-name">Categorías de Servicio/Arriendo</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Panel de control</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Editar información</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Título</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Información (Dirección/Fono/whatssap)</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Banner</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                    <div class="popup-item">
                        <span class="feature-name">Slogan</span>
                        <span class="feature-value positive">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de Bienvenida para Plan Gratuito (oculto por defecto) -->
    <div class="welcome-popup-overlay" id="welcome-popup" style="display: none !important; visibility: hidden;" hidden>
        <div class="welcome-popup">
            <div class="welcome-popup-header">
                <h3>¡Bienvenido a aunCLICK!</h3>
                <button type="button" class="welcome-popup-close">&times;</button>
            </div>
            <div class="welcome-popup-content">
                <div class="welcome-message">
                    <p class="justified-text">Gracias por registrarte en nuestra plataforma. Recuerda la importancia de una buena visualización de tus productos, servicios o arriendos para maximizar tus ventas.</p>

                    <p class="justified-text">Con el plan gratuito ya estás listo para mostrar tu negocio en aunCLICK. Si en el futuro deseas aumentar tu visibilidad o acceder a más funcionalidades, también contamos con opciones de inscripción Normal o Premium disponibles para ti.</p>

                    <div class="welcome-upgrade-options">
                        <div class="welcome-upgrade-option">
                            <div>
                                <h4>Plan Normal</h4>
                            </div>
                            <p class="justified-text">Más imágenes, mejor visibilidad y herramientas adicionales.</p>
                            <p class="welcome-price">$2.990/mes + IVA</p>
                        </div>

                        <div class="welcome-upgrade-option premium">
                            <div>
                                <span class="welcome-crown">👑</span>
                                <h4>Plan Premium</h4>
                            </div>
                            <p class="justified-text">Máxima visibilidad, múltiples carruseles y prioridad en los resultados.</p>
                            <p class="welcome-price">$5.990/mes + IVA</p>
                        </div>
                    </div>
                </div>

                <div class="welcome-popup-footer">
                    <button type="button" class="btn welcome-finish-btn" id="welcome-finish">Finalizar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Script original de registro -->
    <script src="../js/register.js"></script>

    <!-- Script para convertir nombres y apellidos a minúsculas -->
    <script src="../js/lowercase-converter.js"></script>

    <!-- Script para manejar regiones y comunas -->
    <script src="../js/regiones-comunas.js"></script>

    <!-- Nuevo script para navegación entre pasos -->
    <script src="../js/form-navigation.js"></script>

    <!-- Script para manejar los pop-ups de planes -->
    <script src="../js/plan-popups.js"></script>

    <!-- Script para manejar los pop-ups de error -->
    <script src="../js/error-popups.js"></script>

    <!-- Script para las condiciones de validación -->
    <script src="../js/form-conditions.js"></script>

    <!-- Script para validación de formularios -->
    <script src="../js/form-validation.js"></script>

    <!-- Script para manejar solicitudes AJAX -->
    <script src="../js/ajax-handler.js"></script>

    <!-- Script para manejar el paso 4 -->
    <script src="../js/step4-handler.js"></script>

    <!-- Script para el botón del paso 1 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar el botón del paso 1
            setTimeout(function() {
                const step1NextButton = document.getElementById('step1-next');
                if (step1NextButton) {
                    // Agregar un manejador de eventos para el botón
                    step1NextButton.onclick = function(event) {
                        event.preventDefault();

                        console.log('Botón del paso 1 clickeado');

                        // Verificar si todos los campos del paso 1 son válidos
                        const nombres = document.getElementById('nombres').value.trim();
                        const apellidos = document.getElementById('apellidos').value.trim();
                        const rut = document.getElementById('rut').value.trim();
                        const fechaNacimiento = document.getElementById('fechaNacimiento').value.trim();
                        const sexo = document.querySelector('select[name="sexo"]').value;
                        const telefono = document.getElementById('telefono').value.trim();
                        const region = document.getElementById('region').value;
                        const comuna = document.getElementById('comuna').value;
                        const direccion = document.getElementById('direccion').value.trim();

                        console.log('Datos del formulario:', {
                            nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion
                        });

                        // Verificar que todos los campos estén completos
                        if (!nombres || !apellidos || !rut || !fechaNacimiento || !sexo || !telefono || !region || !comuna || !direccion) {
                            // Mostrar mensaje de error si falta algún campo
                            window.showErrorPopup(
                                'Campos incompletos',
                                'Por favor, complete todos los campos requeridos.',
                                []
                            );
                            return false;
                        }

                        // Crear objeto FormData para enviar los datos
                        const formData = new FormData();
                        formData.append('nombres', nombres);
                        formData.append('apellidos', apellidos);
                        formData.append('rut', rut);
                        formData.append('fechaNacimiento', fechaNacimiento);
                        formData.append('sexo', sexo);
                        formData.append('telefono', telefono);
                        formData.append('region', region);
                        formData.append('comuna', comuna);
                        formData.append('direccion', direccion);

                        console.log('Enviando datos al servidor usando AjaxHandler...');

                        // Mostrar indicador de carga
                        const loadingIndicator = document.createElement('div');
                        loadingIndicator.className = 'loading-indicator';
                        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                        loadingIndicator.style.position = 'fixed';
                        loadingIndicator.style.top = '0';
                        loadingIndicator.style.left = '0';
                        loadingIndicator.style.width = '100%';
                        loadingIndicator.style.height = '100%';
                        loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                        loadingIndicator.style.display = 'flex';
                        loadingIndicator.style.flexDirection = 'column';
                        loadingIndicator.style.justifyContent = 'center';
                        loadingIndicator.style.alignItems = 'center';
                        loadingIndicator.style.zIndex = '9999';
                        loadingIndicator.style.color = 'white';
                        document.body.appendChild(loadingIndicator);

                        // Usar el manejador AJAX para enviar los datos
                        window.AjaxHandler.submitStep1(formData,
                            // Callback de éxito
                            function(response) {
                                console.log('Éxito! Avanzando al paso 2...');

                                // Eliminar indicador de carga
                                document.body.removeChild(loadingIndicator);

                                // Si la respuesta es exitosa, avanzar al paso 2
                                const formSteps = document.querySelectorAll('.form-step');
                                const steps = document.querySelectorAll('.step');

                                // Ocultar todos los pasos
                                formSteps.forEach(step => {
                                    step.style.display = 'none';
                                    step.classList.remove('active');
                                });

                                steps.forEach(step => {
                                    step.classList.remove('active');
                                });

                                // Mostrar el paso 2
                                if (formSteps[1]) {
                                    formSteps[1].style.display = 'block';
                                    formSteps[1].classList.add('active');
                                }

                                if (steps[1]) {
                                    steps[1].classList.add('active');
                                }

                                console.log('Datos guardados y avanzado al paso 2');
                            },
                            // Callback de error
                            function(errorMessage) {
                                console.error('Error:', errorMessage);

                                // Eliminar indicador de carga
                                document.body.removeChild(loadingIndicator);

                                // Mostrar mensaje de error
                                window.showErrorPopup(
                                    'Error en la respuesta',
                                    errorMessage || 'Hubo un error al procesar la respuesta del servidor.',
                                    []
                                );
                            }
                        );

                        return false;
                    };
                } else {
                    console.error('No se encontró el botón del paso 1');
                }
            }, 1000);
        });
    </script>

    <!-- Script para el botón del paso 2 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar el botón del paso 2
            setTimeout(function() {
                const step2NextButton = document.getElementById('step2-next');
                if (step2NextButton) {
                    // Agregar un manejador de eventos para el botón
                    step2NextButton.onclick = function(event) {
                        event.preventDefault();

                        console.log('Botón del paso 2 clickeado');

                        // Verificar si todos los campos del paso 2 son válidos
                        const username = document.getElementById('username').value.trim();
                        const email = document.getElementById('email').value.trim();
                        const backup_email = document.getElementById('backup_email').value.trim();
                        const password = document.getElementById('password').value.trim();
                        const confirm_password = document.getElementById('confirm_password').value.trim();
                        const localFisico = document.querySelector('input[name="local_fisico"]:checked');

                        console.log('Datos del formulario:', {
                            username, email, backup_email, password, confirm_password,
                            localFisico: localFisico ? localFisico.value : null
                        });

                        // Verificar que todos los campos requeridos estén completos
                        if (!username || !email || !password || !confirm_password || !localFisico) {
                            // Mostrar mensaje de error si falta algún campo
                            window.showErrorPopup(
                                'Campos incompletos',
                                'Por favor, complete todos los campos requeridos.',
                                []
                            );
                            return false;
                        }

                        // Verificar que las contraseñas coincidan
                        if (password !== confirm_password) {
                            window.showErrorPopup(
                                'Contraseñas no coinciden',
                                'Las contraseñas ingresadas no coinciden.',
                                []
                            );
                            return false;
                        }

                        // Crear objeto FormData para enviar los datos
                        const formData = new FormData();
                        formData.append('username', username);
                        formData.append('email', email);
                        formData.append('backup_email', backup_email);
                        formData.append('password', password);
                        formData.append('confirm_password', confirm_password);
                        formData.append('local_fisico', localFisico.value);

                        console.log('Enviando datos al servidor usando AjaxHandler...');

                        // Mostrar indicador de carga
                        const loadingIndicator = document.createElement('div');
                        loadingIndicator.className = 'loading-indicator';
                        loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                        loadingIndicator.style.position = 'fixed';
                        loadingIndicator.style.top = '0';
                        loadingIndicator.style.left = '0';
                        loadingIndicator.style.width = '100%';
                        loadingIndicator.style.height = '100%';
                        loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                        loadingIndicator.style.display = 'flex';
                        loadingIndicator.style.flexDirection = 'column';
                        loadingIndicator.style.justifyContent = 'center';
                        loadingIndicator.style.alignItems = 'center';
                        loadingIndicator.style.zIndex = '9999';
                        loadingIndicator.style.color = 'white';
                        document.body.appendChild(loadingIndicator);

                        // Usar el manejador AJAX para enviar los datos
                        window.AjaxHandler.submitStep2(formData,
                            // Callback de éxito
                            function(response) {
                                console.log('Éxito! Avanzando al paso 3...');

                                // Eliminar indicador de carga
                                document.body.removeChild(loadingIndicator);

                                // Si la respuesta es exitosa, avanzar al paso 3
                                const formSteps = document.querySelectorAll('.form-step');
                                const steps = document.querySelectorAll('.step');

                                // Ocultar todos los pasos
                                formSteps.forEach(step => {
                                    step.style.display = 'none';
                                    step.classList.remove('active');
                                });

                                steps.forEach(step => {
                                    step.classList.remove('active');
                                });

                                // Mostrar el paso 3
                                if (formSteps[2]) {
                                    formSteps[2].style.display = 'block';
                                    formSteps[2].classList.add('active');
                                }

                                if (steps[2]) {
                                    steps[2].classList.add('active');
                                }

                                console.log('Datos guardados y avanzado al paso 3');
                            },
                            // Callback de error
                            function(errorMessage) {
                                console.error('Error:', errorMessage);

                                // Eliminar indicador de carga
                                document.body.removeChild(loadingIndicator);

                                // Mostrar mensaje de error
                                window.showErrorPopup(
                                    'Error en la respuesta',
                                    errorMessage || 'Hubo un error al procesar la respuesta del servidor.',
                                    []
                                );
                            }
                        );

                        return false;
                    };
                } else {
                    console.error('No se encontró el botón del paso 2');
                }
            }, 1000);
        });
    </script>

    <style>
        .spinner {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #8e44ad;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- Script para ocultar el popup de bienvenida al cargar la página -->
    <script>
        // Ejecutar inmediatamente
        (function() {
            // Ocultar el popup de bienvenida
            var welcomePopup = document.getElementById('welcome-popup');
            if (welcomePopup) {
                welcomePopup.style.display = 'none';
                console.log('Popup de bienvenida ocultado por script de emergencia');
            }
        })();

        // También ejecutar cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            // Ocultar el popup de bienvenida
            var welcomePopup = document.getElementById('welcome-popup');
            if (welcomePopup) {
                welcomePopup.style.display = 'none';
                console.log('Popup de bienvenida ocultado por script de emergencia (DOMContentLoaded)');
            }
        });

        // También ejecutar cuando la página esté completamente cargada
        window.addEventListener('load', function() {
            // Ocultar el popup de bienvenida
            var welcomePopup = document.getElementById('welcome-popup');
            if (welcomePopup) {
                welcomePopup.style.display = 'none';
                console.log('Popup de bienvenida ocultado por script de emergencia (load)');
            }

            // Configurar los botones del paso 4 directamente
            console.log('Configurando botones del paso 4 desde script de emergencia');

            // Función para manejar el clic en el botón del paso 4
            function emergencyStep4Handler(event) {
                event.preventDefault();
                console.log('Botón del paso 4 clickeado (script de emergencia)');

                // Verificar si hay un plan seleccionado
                var planSeleccionado = document.querySelector('input[name="subscription"]:checked');
                if (!planSeleccionado) {
                    alert('Por favor, seleccione un plan antes de continuar.');
                    return false;
                }

                // Crear objeto FormData para enviar los datos
                var formData = new FormData();
                formData.append('plan_suscripcion', planSeleccionado.value);

                // Mostrar indicador de carga
                var loadingIndicator = document.createElement('div');
                loadingIndicator.className = 'loading-indicator';
                loadingIndicator.innerHTML = '<div class="spinner"></div><p>Guardando datos...</p>';
                loadingIndicator.style.position = 'fixed';
                loadingIndicator.style.top = '0';
                loadingIndicator.style.left = '0';
                loadingIndicator.style.width = '100%';
                loadingIndicator.style.height = '100%';
                loadingIndicator.style.backgroundColor = 'rgba(0,0,0,0.5)';
                loadingIndicator.style.display = 'flex';
                loadingIndicator.style.flexDirection = 'column';
                loadingIndicator.style.justifyContent = 'center';
                loadingIndicator.style.alignItems = 'center';
                loadingIndicator.style.zIndex = '9999';
                loadingIndicator.style.color = 'white';
                document.body.appendChild(loadingIndicator);

                // Enviar solicitud AJAX directamente
                var xhr = new XMLHttpRequest();
                xhr.open('POST', 'process_step4.php', true);

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            console.log('Respuesta del servidor:', response);

                            // Eliminar indicador de carga
                            document.body.removeChild(loadingIndicator);

                            if (response.success) {
                                // Verificar si es plan gratuito
                                if (planSeleccionado.value === 'gratuita') {
                                    // Mostrar popup de bienvenida
                                    var welcomePopup = document.getElementById('welcome-popup');
                                    if (welcomePopup) {
                                        welcomePopup.style.display = 'flex';
                                    } else {
                                        alert('Gracias por registrarte con el plan gratuito.');
                                        window.location.href = 'index.php';
                                    }
                                } else {
                                    // Avanzar al paso 5
                                    var formSteps = document.querySelectorAll('.form-step');
                                    var steps = document.querySelectorAll('.step');

                                    // Ocultar todos los pasos
                                    formSteps.forEach(function(step) {
                                        step.style.display = 'none';
                                        step.classList.remove('active');
                                    });

                                    steps.forEach(function(step) {
                                        step.classList.remove('active');
                                    });

                                    // Mostrar el paso 5
                                    if (formSteps[4]) {
                                        formSteps[4].style.display = 'block';
                                        formSteps[4].classList.add('active');
                                    }

                                    if (steps[4]) {
                                        steps[4].classList.add('active');
                                    }
                                }
                            } else {
                                alert('Error: ' + (response.message || 'Hubo un error al procesar la solicitud.'));
                            }
                        } catch (e) {
                            console.error('Error al parsear la respuesta:', e);
                            document.body.removeChild(loadingIndicator);
                            alert('Error al procesar la respuesta del servidor.');
                        }
                    } else {
                        console.error('Error HTTP:', xhr.status);
                        document.body.removeChild(loadingIndicator);
                        alert('Error de conexión: ' + xhr.status);
                    }
                };

                xhr.onerror = function() {
                    console.error('Error de red');
                    document.body.removeChild(loadingIndicator);
                    alert('Error de conexión con el servidor.');
                };

                xhr.send(formData);
                return false;
            }

            // Asignar el manejador a los botones
            var step4ProductosButton = document.getElementById('step4-next-save');
            if (step4ProductosButton) {
                step4ProductosButton.onclick = emergencyStep4Handler;
                console.log('Manejador de emergencia asignado al botón de productos');
            }

            var step4ServiciosButton = document.getElementById('step4-next-save-servicios');
            if (step4ServiciosButton) {
                step4ServiciosButton.onclick = emergencyStep4Handler;
                console.log('Manejador de emergencia asignado al botón de servicios');
            }
        });
    </script>
</body>
</html>

