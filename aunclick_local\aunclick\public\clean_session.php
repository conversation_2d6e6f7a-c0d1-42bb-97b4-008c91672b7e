<?php
require_once '../config/session_config.php';

function cleanInactiveSessions($maxAge = 86400) {
    $sessionPath = session_save_path();
    $now = time();
    $cleanedCount = 0;
    
    echo "<h2>Limpieza de Sesiones</h2>";
    echo "<pre>";
    
    // Verificar si el directorio existe
    if (!is_dir($sessionPath)) {
        echo "Error: Directorio de sesiones no encontrado: $sessionPath\n";
        return;
    }
    
    // Listar archivos de sesión
    $sessionFiles = glob($sessionPath . "/sess_*");
    
    echo "Encontrados " . count($sessionFiles) . " archivos de sesión\n";
    echo "Ruta de sesiones: $sessionPath\n\n";
    
    foreach ($sessionFiles as $file) {
        $lastModified = filemtime($file);
        $age = $now - $lastModified;
        
        echo "Archivo: " . basename($file) . "\n";
        echo "Última modificación: " . date('Y-m-d H:i:s', $lastModified) . "\n";
        echo "Edad: " . round($age / 3600, 2) . " horas\n";
        
        if ($age > $maxAge) {
            if (unlink($file)) {
                echo "✓ ELIMINADO\n";
                $cleanedCount++;
            } else {
                echo "✗ ERROR AL ELIMINAR\n";
            }
        } else {
            echo "→ MANTENER\n";
        }
        echo "------------------------\n";
    }
    
    echo "\nResumen de limpieza:\n";
    echo "Total de sesiones: " . count($sessionFiles) . "\n";
    echo "Sesiones eliminadas: $cleanedCount\n";
    echo "</pre>";
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpieza de Sesiones</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        h1, h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Mantenimiento de Sesiones</h1>
    <?php
    // Limpiar sesiones inactivas por más de 24 horas
    cleanInactiveSessions(86400);
    ?>
</body>
</html>