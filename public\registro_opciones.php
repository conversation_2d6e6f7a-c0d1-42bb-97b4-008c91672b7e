<?php
// Iniciar la sesión antes de cualquier output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verificar si estamos en la raíz o en un subdirectorio
$config_path = file_exists('config/logger.php') ? 'config/' : '../config/';

// Incluir primero el sistema de logging
require_once $config_path . 'logger.php';

// Registrar inicio de la página
logInfo("Cargando página de opciones post-registro");

// Incluir la configuración base
require_once $config_path . 'config.php';

// Configuración de seguridad: Generar token CSRF si no existe
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Enviar cabeceras de seguridad
header("Content-Security-Policy: default-src 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; script-src 'self' 'unsafe-inline'");
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');

// Capturar y validar datos enviados (si hay)
$user_id = isset($_GET['user_id']) ? filter_var($_GET['user_id'], FILTER_VALIDATE_INT) : 0;
$nombre = isset($_GET['nombre']) ? htmlspecialchars($_GET['nombre'], ENT_QUOTES, 'UTF-8') : 'Usuario';

// Verificación básica de seguridad: Asegurarse de que user_id es un número positivo
if ($user_id <= 0) {
    // Registrar acceso no autorizado
    logInfo("Intento de acceso a página de registro con ID de usuario inválido");
    
    // Redireccionar a la página principal si no hay un ID de usuario válido
    header('Location: index.php');
    exit();
}

// Registrar el acceso
logInfo("Acceso a página de opciones post-registro - User ID: $user_id");
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro Exitoso - Villarrica a un CLICK</title>
    
    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #6a2a83;
            --primary-light: #8a3aa3;
            --success-color: #28a745;
            --text-color: #333;
            --light-bg: #f9f9f9;
            --border-color: #ddd;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #f5f5f5;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .success-icon {
            font-size: 80px;
            color: var(--success-color);
            margin-bottom: 20px;
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 32px;
        }
        
        p {
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        .options-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }
        
        .option-card {
            flex: 1 1 200px;
            max-width: 300px;
            background-color: var(--light-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 30px 20px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .option-icon {
            font-size: 40px;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .option-card h3 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 20px;
        }
        
        .option-card p {
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }
        
        .btn:hover {
            background-color: var(--primary-light);
        }
        
        .footer {
            margin-top: 40px;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <i class="fa-solid fa-circle-check success-icon"></i>
        <h1>¡Registro Exitoso!</h1>
        <p>Bienvenido <?php echo htmlspecialchars($nombre, ENT_QUOTES, 'UTF-8'); ?> a Villarrica a un CLICK.</p>
        <p>Tu cuenta ha sido creada correctamente. ¿Qué te gustaría hacer ahora?</p>
        
        <div class="options-container">
            <div class="option-card">
                <i class="fa-solid fa-user-gear option-icon"></i>
                <h3>Completar Perfil</h3>
                <p>Personaliza tu perfil y añade información importante sobre tu negocio.</p>
                <a href="profile.php?user_id=<?php echo intval($user_id); ?>&token=<?php echo urlencode($_SESSION['csrf_token']); ?>" class="btn">Ir a mi Perfil</a>
            </div>
            
            <div class="option-card">
                <i class="fa-solid fa-store option-icon"></i>
                <h3>Administrar Tienda</h3>
                <p>Configura tu tienda, añade productos y personaliza tu catálogo.</p>
                <a href="admin.php" class="btn">Ir al Panel</a>
            </div>
            
            <div class="option-card">
                <i class="fa-solid fa-house option-icon"></i>
                <h3>Página Principal</h3>
                <p>Explora la plataforma y descubre todas las funcionalidades disponibles.</p>
                <a href="index.php" class="btn">Ir al Inicio</a>
            </div>
        </div>
        
        <div class="footer">
            <p>Si tienes alguna pregunta, no dudes en contactarnos.</p>
            <p>&copy; <?php echo date('Y'); ?> Villarrica a un CLICK. Todos los derechos reservados.</p>
        </div>
    </div>
    
    <script>
        // Si es necesario, puedes agregar funcionalidad JavaScript aquí
        document.addEventListener('DOMContentLoaded', function() {
            // Por ejemplo, puedes enviar eventos de analítica o realizar alguna acción automática
            console.log('Página de opciones post-registro cargada');
        });
    </script>
</body>
</html>