#!/bin/bash

# Nombre de la base de datos
DB_NAME="villarrica_click"

# Archivo de salida
OUTPUT_FILE="villarrica_click_tables.txt"

# Limpiar el archivo de salida si existe
echo "" > $OUTPUT_FILE

# Obtener la lista de tablas
TABLES=$(mysql -e "SHOW TABLES FROM $DB_NAME;" | grep -v "Tables_in_")

# Para cada tabla, obtener su CREATE TABLE y añadirlo al archivo
for TABLE in $TABLES; do
  echo "-- Table structure for table $TABLE" >> $OUTPUT_FILE
  echo "" >> $OUTPUT_FILE
  mysql -e "SHOW CREATE TABLE $DB_NAME.$TABLE\G" | grep -v "row" | grep -v "***" >> $OUTPUT_FILE
  echo "" >> $OUTPUT_FILE
  echo "-- --------------------------------------------------------" >> $OUTPUT_FILE
  echo "" >> $OUTPUT_FILE
done

echo "Extracción completada. Revisa el archivo $OUTPUT_FILE"
