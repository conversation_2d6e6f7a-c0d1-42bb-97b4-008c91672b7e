<?php
// Verificar si Ratchet está instalado
if (!file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "Error: No se encontró el autoloader de Composer.\n";
    exit(1);
}

require __DIR__ . '/vendor/autoload.php';

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;

class SimpleWebSocket implements MessageComponentInterface {
    protected $clients;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        echo "Servidor WebSocket simple iniciado.\n";
    }

    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "Nueva conexión: {$conn->resourceId}\n";
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        echo "Mensaje recibido: {$msg}\n";
        foreach ($this->clients as $client) {
            $client->send("Eco: {$msg}");
        }
    }

    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        echo "Conexión {$conn->resourceId} cerrada.\n";
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "Error: {$e->getMessage()}\n";
        $conn->close();
    }
}

// Puerto para el servidor WebSocket
$port = 8080;

echo "Iniciando servidor WebSocket simple en el puerto {$port}...\n";

$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new SimpleWebSocket()
        )
    ),
    $port
);

echo "Servidor WebSocket iniciado en ws://localhost:{$port}\n";
echo "Presiona Ctrl+C para detener el servidor\n";

$server->run();
