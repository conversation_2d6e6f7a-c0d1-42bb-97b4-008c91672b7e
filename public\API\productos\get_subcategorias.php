<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Si es una solicitud OPTIONS, responder inmediatamente con 200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Ruta absoluta al archivo de configuración
    $configPath = realpath(__DIR__ . '/../../../config/config.php');
    error_log("Ruta al archivo de configuración: " . $configPath);

    if (!file_exists($configPath)) {
        error_log("ERROR: El archivo de configuración no existe en la ruta: " . $configPath);
        throw new Exception('Archivo de configuración no encontrado');
    }

    require_once $configPath;

    // Log inicial de la solicitud
    error_log("=== Inicio solicitud get_subcategorias.php ===");
    error_log("Método: " . $_SERVER['REQUEST_METHOD']);
    error_log("GET params: " . print_r($_GET, true));

    // Obtener categoria_id del GET
    $categoria_id = isset($_GET['categoria_id']) ? intval($_GET['categoria_id']) : 0;
    error_log("categoria_id recibido: " . $categoria_id);

    // Validación básica
    if ($categoria_id <= 0) {
        error_log("Error: ID de categoría inválido ($categoria_id)");
        throw new Exception('ID de categoría inválido');
    }

    // Verificar conexión
    if (!isset($conn)) {
        error_log("Error: Conexión a BD no disponible");
        throw new Exception('Error de conexión a la base de datos');
    }

    // Verificar si la tabla existe
    $tableCheckQuery = "SHOW TABLES LIKE 'tb_subcategorias'";
    $tableResult = $conn->query($tableCheckQuery);
    if ($tableResult->num_rows == 0) {
        error_log("La tabla tb_subcategorias no existe en la base de datos");
        // En lugar de lanzar una excepción, devolvemos un array vacío
        echo json_encode([
            'success' => true,
            'subcategorias' => [],
            'count' => 0,
            'message' => 'No hay subcategorías disponibles'
        ]);
        exit;
    }

    // Consulta preparada
    $query = "SELECT id, nombre FROM tb_subcategorias WHERE categoria_id = ? ORDER BY nombre";
    error_log("Query a ejecutar: " . $query);

    $stmt = $conn->prepare($query);
    if (!$stmt) {
        error_log("Error en prepare: " . $conn->error);
        // En lugar de lanzar una excepción, devolvemos un array vacío
        echo json_encode([
            'success' => true,
            'subcategorias' => [],
            'count' => 0,
            'message' => 'No hay subcategorías disponibles'
        ]);
        exit;
    }

    $stmt->bind_param("i", $categoria_id);
    error_log("Parámetros vinculados. Ejecutando consulta...");

    if (!$stmt->execute()) {
        error_log("Error en execute: " . $stmt->error);
        // En lugar de lanzar una excepción, devolvemos un array vacío
        echo json_encode([
            'success' => true,
            'subcategorias' => [],
            'count' => 0,
            'message' => 'No hay subcategorías disponibles'
        ]);
        exit;
    }

    $result = $stmt->get_result();
    $subcategorias = [];

    while ($row = $result->fetch_assoc()) {
        $subcategorias[] = [
            'id' => intval($row['id']),
            'nombre' => $row['nombre']
        ];
    }

    error_log("Subcategorías encontradas: " . count($subcategorias));
    error_log("Datos: " . print_r($subcategorias, true));

    // Cerrar statement
    $stmt->close();

    // Enviar respuesta
    $response = [
        'success' => true,
        'subcategorias' => $subcategorias,
        'count' => count($subcategorias)
    ];
    error_log("Respuesta a enviar: " . print_r($response, true));
    echo json_encode($response);

} catch (Exception $e) {
    error_log("Error en get_subcategorias.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ]);
}

// Cerrar conexión
if (isset($conn)) {
    $conn->close();
}
error_log("=== Fin solicitud get_subcategorias.php ===\n");
