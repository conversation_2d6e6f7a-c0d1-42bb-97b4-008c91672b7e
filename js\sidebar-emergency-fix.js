/**
 * Solución de emergencia para el sidebar en modo responsive
 * Este script se ejecuta inmediatamente y aplica estilos directamente al sidebar
 */

// Función auto-ejecutable para evitar conflictos con otras variables
(function() {
    console.log('EMERGENCY FIX: Inicializando solución de emergencia para el sidebar...');

    // Función para aplicar la solución
    function applySidebarFix() {
        console.log('EMERGENCY FIX: Aplicando solución...');

        // Obtener elementos
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.querySelector('.main-content');

        if (!sidebar) {
            console.error('EMERGENCY FIX: No se encontró el sidebar');
            return;
        }

        // Crear un nuevo botón de toggle
        const newToggleBtn = document.createElement('button');
        newToggleBtn.id = 'emergency-toggle-btn';
        newToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        newToggleBtn.style.position = 'fixed';
        newToggleBtn.style.top = '10px';
        newToggleBtn.style.left = '80px';
        newToggleBtn.style.zIndex = '10000';
        newToggleBtn.style.width = '40px';
        newToggleBtn.style.height = '40px';
        newToggleBtn.style.borderRadius = '50%';
        newToggleBtn.style.backgroundColor = '#6a1b9a';
        newToggleBtn.style.color = 'white';
        newToggleBtn.style.border = 'none';
        newToggleBtn.style.display = 'flex';
        newToggleBtn.style.alignItems = 'center';
        newToggleBtn.style.justifyContent = 'center';
        newToggleBtn.style.cursor = 'pointer';
        newToggleBtn.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';

        // Botón de restauración de UI eliminado

        // Crear overlay
        const overlay = document.createElement('div');
        overlay.id = 'emergency-overlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
        overlay.style.zIndex = '9998';
        overlay.style.display = 'none';

        // Aplicar estilos al sidebar
        if (window.innerWidth < 992) {
            console.log('EMERGENCY FIX: Aplicando estilos para móvil');

            // Estilos para el sidebar
            sidebar.style.position = 'fixed';
            sidebar.style.top = '0';
            sidebar.style.left = '0';
            sidebar.style.width = '70px';
            sidebar.style.height = '100%';
            sidebar.style.zIndex = '9999';
            sidebar.style.transition = 'width 0.3s ease';
            sidebar.style.overflowX = 'hidden';

            // Ocultar elementos del sidebar
            const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
            elementsToHide.forEach(el => {
                el.style.display = 'none';
            });

            // Estilos para el contenido principal
            if (mainContent) {
                mainContent.style.marginLeft = '70px';
                mainContent.style.width = 'calc(100% - 70px)';
            }

            // Agregar el botón de toggle y el overlay al body
            document.body.appendChild(newToggleBtn);
            document.body.appendChild(overlay);

            // Función para expandir el sidebar
            function expandSidebar() {
                sidebar.style.width = '250px';

                // Mostrar elementos del sidebar
                const elementsToShow = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                elementsToShow.forEach(el => {
                    el.style.display = 'block';
                });

                // Mostrar overlay
                overlay.style.display = 'block';

                // Cambiar ícono del botón
                newToggleBtn.innerHTML = '<i class="fas fa-times"></i>';

                console.log('EMERGENCY FIX: Sidebar expandido');
            }

            // Función para colapsar el sidebar
            function collapseSidebar() {
                sidebar.style.width = '70px';

                // Ocultar elementos del sidebar
                const elementsToHide = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
                elementsToHide.forEach(el => {
                    el.style.display = 'none';
                });

                // Ocultar overlay
                overlay.style.display = 'none';

                // Cambiar ícono del botón
                newToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';

                console.log('EMERGENCY FIX: Sidebar colapsado');
            }

            // Manejar clic en el botón de toggle
            newToggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('EMERGENCY FIX: Botón de toggle clickeado');

                if (sidebar.style.width === '250px') {
                    collapseSidebar();
                } else {
                    expandSidebar();
                }
            });

            // Manejar clic en el overlay
            overlay.addEventListener('click', function() {
                collapseSidebar();
            });

            console.log('EMERGENCY FIX: Eventos configurados');
        }
    }

    // Aplicar la solución cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applySidebarFix);
    } else {
        applySidebarFix();
    }

    // También aplicar la solución después de que la página haya cargado completamente
    window.addEventListener('load', function() {
        setTimeout(applySidebarFix, 500);
    });

    // Aplicar la solución inmediatamente
    setTimeout(applySidebarFix, 0);

    // Aplicar la solución cada segundo durante los primeros 5 segundos
    for (let i = 1; i <= 5; i++) {
        setTimeout(applySidebarFix, i * 1000);
    }

    console.log('EMERGENCY FIX: Script inicializado');
})();
