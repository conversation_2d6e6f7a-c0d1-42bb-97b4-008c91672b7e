<?php
// Verificar la autenticación y el rol de administrador
require_once 'session_config.php';
$sessionManager = SessionManager::getInstance();

if (!$sessionManager->validateSession() || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

$logFile = dirname(__FILE__) . '/error.log';
$lines = [];

if (file_exists($logFile)) {
    $lines = array_reverse(file($logFile));
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs del Sistema</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f5f5;
        }
        .log-container {
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-entry {
            padding: 10px;
            border-bottom: 1px solid #eee;
            white-space: pre-wrap;
        }
        .log-entry:nth-child(odd) {
            background: #f9f9f9;
        }
        .error {
            color: #dc3545;
        }
        .warning {
            color: #ffc107;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Logs del Sistema</h1>
    <div class="log-container">
        <?php if (empty($lines)): ?>
            <p>No hay logs disponibles.</p>
        <?php else: ?>
            <?php foreach ($lines as $line): ?>
                <div class="log-entry <?php 
                    if (stripos($line, 'error') !== false) echo 'error';
                    elseif (stripos($line, 'warning') !== false) echo 'warning';
                ?>">
                    <?php echo htmlspecialchars($line); ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    <button class="refresh-btn" onclick="location.reload()">Actualizar</button>
</body>
</html>