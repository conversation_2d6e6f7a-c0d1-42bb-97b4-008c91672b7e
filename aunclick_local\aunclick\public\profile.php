<?php
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Inicializar gestor de sesiones
$sessionManager = SessionManager::getInstance();

// Verificar autenticación
if (!$sessionManager->validateSession()) {
    header('Location: /projects/villarrica_click/public/login.php?error=unauthorized');
    exit();
}

// Obtener información del usuario actual
$user_id = $_SESSION['user_id'];
$user_data = null;
$error_message = '';
$warning_message = '';

// Manejar mensajes de error de redirección
if (isset($_GET['error']) && $_GET['error'] === 'unauthorized') {
    if (isset($_GET['reason']) && $_GET['reason'] === 'insufficient_permissions') {
        $warning_message = 'Tu plan actual no tiene acceso al panel de administración. Por favor, actualiza tu plan para acceder a todas las funciones.';
    }
}

try {
    // Consulta para obtener datos del usuario
    $user_query = "SELECT u.*, COUNT(n.id) as total_negocios 
                   FROM users u 
                   LEFT JOIN tb_negocios n ON n.user_id = u.id 
                   WHERE u.id = ?";
    $stmt = $conn->prepare($user_query);
    if (!$stmt) {
        throw new Exception("Error preparando la consulta de usuario: " . $conn->error);
    }
    
    $stmt->bind_param("i", $user_id);
    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando la consulta de usuario: " . $stmt->error);
    }
    
    $user_result = $stmt->get_result();
    if ($user_result->num_rows === 0) {
        throw new Exception("No se encontró información del usuario");
    }
    
    $user_data = $user_result->fetch_assoc();
    
    // Consulta para obtener negocios del usuario
    $negocios_query = "SELECT n.*, s.nombre as suscripcion_nombre, s.precio as suscripcion_precio
                       FROM tb_negocios n
                       LEFT JOIN tb_suscripciones s ON n.suscripcion_id = s.id
                       WHERE n.user_id = ?";
    $stmt = $conn->prepare($negocios_query);
    if (!$stmt) {
        throw new Exception("Error preparando la consulta de negocios: " . $conn->error);
    }
    
    $stmt->bind_param("i", $user_id);
    if (!$stmt->execute()) {
        throw new Exception("Error ejecutando la consulta de negocios: " . $stmt->error);
    }
    
    $negocios_result = $stmt->get_result();
    
} catch (Exception $e) {
    error_log("Error en profile.php: " . $e->getMessage());
    $error_message = "Ha ocurrido un error al cargar la información. Por favor, intente más tarde.";
}

// Incluir header
include 'header.php';
?>

<!-- Agregar el CSS de perfil -->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/css/profile.css">

<div class="container my-5">
    <?php if ($warning_message): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($warning_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger" role="alert">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php elseif ($user_data): ?>
        <div class="row">
            <!-- Información del Usuario -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Perfil de Usuario</h5>
                        <div class="text-center mb-3">
                            <img src="<?php echo $user_data['avatar'] ?? 'images/default-avatar.png'; ?>" 
                                 alt="Avatar" class="rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                        </div>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item">
                                <strong>Usuario:</strong> <?php echo htmlspecialchars($user_data['username'] ?? 'No disponible'); ?>
                            </li>
                            <li class="list-group-item">
                                <strong>Email:</strong> <?php echo htmlspecialchars($user_data['email'] ?? 'No disponible'); ?>
                            </li>
                            <li class="list-group-item">
                                <strong>Nombre completo:</strong> <?php echo htmlspecialchars($user_data['full_name'] ?? 'No disponible'); ?>
                            </li>
                            <li class="list-group-item">
                                <strong>Fecha de registro:</strong> 
                                <?php echo $user_data['created_at'] ? date('d/m/Y', strtotime($user_data['created_at'])) : 'No disponible'; ?>
                            </li>
                            <li class="list-group-item">
                                <strong>Último acceso:</strong>
                                <?php echo $user_data['last_login'] ? date('d/m/Y H:i', strtotime($user_data['last_login'])) : 'Nunca'; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Negocios del Usuario -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">Mis Negocios</h5>
                            <a href="register_business.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> Nuevo Negocio
                            </a>
                        </div>

                        <?php if ($negocios_result && $negocios_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Nombre</th>
                                            <th>Plan</th>
                                            <th>Estado</th>
                                            <th>Productos</th>
                                            <th>Acciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($negocio = $negocios_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($negocio['nombre'] ?? 'Sin nombre'); ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($negocio['suscripcion_nombre'] ?? 'Free'); ?>
                                                <br>
                                                <small class="text-muted">
                                                    $<?php echo number_format($negocio['suscripcion_precio'] ?? 0, 2); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if (isset($negocio['suscripcion_fin']) && $negocio['suscripcion_fin'] > date('Y-m-d H:i:s')): ?>
                                                    <span class="badge bg-success">Activo</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Expirado</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <?php 
                                                try {
                                                    $prod_query = "SELECT COUNT(*) as total FROM tb_productos WHERE negocio_id = ?";
                                                    $prod_stmt = $conn->prepare($prod_query);
                                                    $prod_stmt->bind_param("i", $negocio['id']);
                                                    $prod_stmt->execute();
                                                    $prod_result = $prod_stmt->get_result();
                                                    $prod_count = $prod_result->fetch_assoc()['total'] ?? 0;
                                                    echo $prod_count;
                                                } catch (Exception $e) {
                                                    error_log("Error contando productos: " . $e->getMessage());
                                                    echo "N/A";
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="admin_products.php?negocio_id=<?php echo $negocio['id']; ?>" 
                                                       class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> Gestionar
                                                    </a>
                                                    <?php if (!isset($negocio['suscripcion_id']) || $negocio['suscripcion_fin'] < date('Y-m-d H:i:s')): ?>
                                                        <a href="upgrade_plan.php?negocio_id=<?php echo $negocio['id']; ?>" 
                                                           class="btn btn-sm btn-success">
                                                            <i class="fas fa-arrow-up"></i> Mejorar Plan
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center my-4">
                                <p class="text-muted">No tienes negocios registrados</p>
                                <p>¡Comienza registrando tu primer negocio!</p>
                                <a href="register_business.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Registrar Negocio
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="alert alert-warning" role="alert">
            No se pudo encontrar la información del usuario. Por favor, contacte al administrador.
        </div>
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>