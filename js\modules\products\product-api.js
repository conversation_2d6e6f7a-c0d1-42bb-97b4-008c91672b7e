// Importar componentes necesarios
import { showNotification } from '../../components/notifications.js';
import { updateProductsTable } from './product-table.js';
import { createProductCards } from './product-cards.js';
// Importar closeEditPanel desde product-edit.js
import { closeEditPanel } from './product-edit.js';

// Función para cargar los productos
export async function loadProducts() {
    const currentPath = window.location.pathname;
    console.log("Current path:", currentPath);

    let apiUrl;
    if (currentPath.includes('/public/')) {
        apiUrl = './API/productos/get_products.php';
    } else {
        apiUrl = '/API/productos/get_products.php';
    }
    
    console.log("loadProducts() - Iniciando carga de productos");
    console.log("URL de API que se intentará cargar:", apiUrl);

    try {
        const response = await fetch(apiUrl, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        });
        
        console.log("Respuesta de API:", response.status, response.statusText);
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error("Contenido completo del error:", errorText);
            throw new Error(`Error al cargar productos: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log("Datos recibidos:", data);
        
        if (data.success && Array.isArray(data.productos)) {
            // Actualizar la tabla con los productos
            updateProductsTable(data.productos);
            
            // Crear tarjetas para vista móvil
            createProductCards(data.productos);
            
            return data;
        } else {
            throw new Error(data.message || 'Respuesta sin éxito o formato incorrecto');
        }
        
    } catch (error) {
        console.error("Error en loadProducts:", error);
        console.error("Stack trace:", error.stack);
        showNotification('Error al cargar productos: ' + error.message, 'error');
        throw error;
    }
}

// Función para eliminar producto
export async function handleDeleteProduct(productId) {
    try {
        console.log(`Eliminando producto ID: ${productId}`);
        
        if (!productId) {
            throw new Error("No se proporcionó un ID de producto válido");
        }
        
        // Mostrar el modal de confirmación
        const modal = document.getElementById('deleteConfirmModal');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('cancelDelete');
        const closeBtn = document.getElementById('closeDeleteModal');
        
        if (!modal || !confirmBtn || !cancelBtn || !closeBtn) {
            throw new Error("No se encontraron los elementos del modal");
        }
        
        // Función para cerrar el modal
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        // Mostrar el modal con animación
        modal.style.display = 'block';
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // Manejar el cierre del modal
        closeBtn.onclick = closeModal;
        cancelBtn.onclick = closeModal;
        modal.onclick = (e) => {
            if (e.target === modal) closeModal();
        };

        // Retornar una promesa que se resuelve cuando el usuario toma una decisión
        return new Promise((resolve, reject) => {
            confirmBtn.onclick = async () => {
                try {
                    const response = await fetch('../public/API/productos/delete_product.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ id: productId }),
                        credentials: 'include'
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Error al eliminar el producto');
                    }

                    const data = await response.json();
                    
                    if (data.success) {
                        showNotification(`Producto ${productId} eliminado con éxito`, 'success');
                        await loadProducts();
                        resolve(true);
                    } else {
                        throw new Error(data.message || 'Error al eliminar el producto');
                    }
                } catch (error) {
                    console.error("Error al eliminar producto:", error);
                    showNotification(`Error al eliminar producto: ${error.message}`, 'error');
                    reject(error);
                } finally {
                    closeModal();
                }
            };
        });
    } catch (error) {
        console.error("Error al eliminar producto:", error);
        showNotification(`Error al eliminar producto: ${error.message}`, 'error');
        throw error;
    }
}