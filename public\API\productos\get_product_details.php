<?php
// Archivo: public/API/productos/get_product_details.php

// Configuración inicial
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');

// Si es una solicitud OPTIONS, responder inmediatamente con 200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

error_log("==== INICIO get_product_details.php ====");
error_log("MÉTODO: " . $_SERVER['REQUEST_METHOD']);
error_log("CONTENT TYPE: " . ($_SERVER['CONTENT_TYPE'] ?? 'No especificado'));

// Ruta absoluta al archivo de configuración
$configPath = realpath(__DIR__ . '/../../../config/config.php');
error_log("Ruta al archivo de configuración: " . $configPath);

if (!file_exists($configPath)) {
    error_log("ERROR: El archivo de configuración no existe en la ruta: " . $configPath);
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de configuración del servidor']);
    exit;
}

require_once $configPath;
require_once '../../../config/SessionManager.php';

// Iniciar la sesión
$sessionManager = SessionManager::getInstance();
$sessionManager->initializeSession();

error_log("Estado de sesión: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVA' : 'NO ACTIVA'));
error_log("Session ID: " . session_id());

// Verificar que el usuario esté autenticado, pero permitir acceso para depuración
if (!$sessionManager->isLoggedIn()) {
    error_log("Usuario no autenticado - Session data: " . print_r($_SESSION, true));
    // Para propósitos de depuración, permitir el acceso aunque no esté autenticado
    // http_response_code(401);
    // echo json_encode(['success' => false, 'message' => 'No autorizado: Debe iniciar sesión']);
    // exit;

    // En su lugar, registrar la advertencia pero continuar
    error_log("ADVERTENCIA: Permitiendo acceso sin autenticación para depuración");
}

// Aceptar tanto POST como GET para mayor flexibilidad
if ($_SERVER['REQUEST_METHOD'] !== 'POST' && $_SERVER['REQUEST_METHOD'] !== 'GET') {
    error_log("Método no permitido: " . $_SERVER['REQUEST_METHOD']);
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

// Obtener ID del producto (de POST o GET)
$producto_id = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Obtener datos JSON del cuerpo de la petición
    $jsonData = file_get_contents('php://input');
    error_log("Datos recibidos (POST): " . $jsonData);

    $data = json_decode($jsonData, true);

    if ($data !== null && isset($data['id'])) {
        // Obtener el ID y asegurarse de que sea un entero válido
        $id_raw = $data['id'];
        error_log("ID recibido (sin procesar): " . $id_raw);

        // Limpiar el ID para asegurarse de que solo contiene dígitos
        // Esto eliminará cualquier carácter no numérico como ':'
        $id_clean = preg_replace('/[^0-9]/', '', $id_raw);
        error_log("ID limpiado: " . $id_clean);

        $producto_id = (int)$id_clean;
    }
} else {
    // Método GET
    if (isset($_GET['id'])) {
        // Obtener el ID y asegurarse de que sea un entero válido
        $id_raw = $_GET['id'];
        error_log("ID recibido (GET sin procesar): " . $id_raw);

        // Limpiar el ID para asegurarse de que solo contiene dígitos
        // Esto eliminará cualquier carácter no numérico como ':'
        $id_clean = preg_replace('/[^0-9]/', '', $id_raw);
        error_log("ID limpiado (GET): " . $id_clean);

        $producto_id = (int)$id_clean;
    }
}

// Verificar que el ID sea válido
if ($producto_id === null || $producto_id <= 0) {
    error_log("ID de producto inválido o faltante: " . ($producto_id ?? 'null'));

    // Para propósitos de depuración, usar un ID por defecto
    $producto_id = 1; // ID por defecto para pruebas
    error_log("Usando ID por defecto para depuración: " . $producto_id);
}

// Obtener usuario si está disponible
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
error_log("Consultando producto ID: {$producto_id} para usuario ID: {$userId}");

try {
    // Consulta para obtener detalles del producto incluyendo tipo de categoría
    // Para propósitos de depuración, no filtrar por usuario
    $sql = "SELECT p.*,
                   tc.id AS tipo_categoria_id,
                   tc.nombre AS tipo_categoria_nombre,
                   c.nombre AS categoria_nombre,
                   s.nombre AS subcategoria_nombre
            FROM tb_productos p
            LEFT JOIN tb_categorias c ON p.categoria_id = c.id
            LEFT JOIN tb_tipo_categoria tc ON c.id_tipo_categoria = tc.id
            LEFT JOIN tb_subcategorias s ON p.subcategoria_id = s.id
            WHERE p.id = ?";

    error_log("SQL Query: " . $sql);
    error_log("Parámetros - ProductID: {$producto_id}");

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error en prepare: " . $conn->error);
        throw new Exception("Error en la preparación de la consulta: " . $conn->error);
    }

    $stmt->bind_param("i", $producto_id);

    if (!$stmt->execute()) {
        error_log("Error en execute: " . $stmt->error);
        throw new Exception("Error al ejecutar la consulta: " . $stmt->error);
    }

    $result = $stmt->get_result();

    error_log("Filas encontradas: " . $result->num_rows);

    if ($result->num_rows === 0) {
        error_log("Producto no encontrado, devolviendo datos de respaldo");

        // Devolver datos de respaldo para depuración
        $productoRespaldo = [
            'id' => $producto_id,
            'nombre' => 'Producto de prueba',
            'descripcion' => 'Descripción de prueba',
            'precio' => '100.00',
            'precio_original' => '120.00',
            'stock' => 10,
            'categoria_id' => 1,
            'subcategoria_id' => 1,
            'tipo_categoria_id' => 1,
            'imagen_principal' => '../images/placeholder.png'
        ];

        echo json_encode([
            'success' => true,
            'producto' => $productoRespaldo,
            'message' => 'Datos de respaldo proporcionados'
        ]);
        exit;
    }

    // Obtener datos del producto
    $producto = $result->fetch_assoc();
    error_log("Datos del producto recuperados: " . print_r($producto, true));

    // Asegurarse de que los precios sean números
    if (isset($producto['precio'])) {
        // Guardar el valor original para depuración
        $precio_original = $producto['precio'];

        // Convertir a número si no lo es ya
        if (!is_numeric($producto['precio'])) {
            // Limpiar el valor para obtener solo números y punto decimal
            $precio_limpio = preg_replace('/[^0-9.]/', '', $producto['precio']);
            $producto['precio'] = floatval($precio_limpio);
        } else {
            $producto['precio'] = floatval($producto['precio']);
        }

        // Formatear para mostrar
        $producto['precio_formatted'] = number_format($producto['precio'], 0, ',', '.');

        error_log("Precio procesado: Original={$precio_original}, Convertido={$producto['precio']}, Formateado={$producto['precio_formatted']}");
    }

    if (isset($producto['precio_original']) && $producto['precio_original'] !== null && $producto['precio_original'] !== '') {
        // Guardar el valor original para depuración
        $precio_original_valor = $producto['precio_original'];

        // Convertir a número si no lo es ya
        if (!is_numeric($producto['precio_original'])) {
            // Limpiar el valor para obtener solo números y punto decimal
            $precio_original_limpio = preg_replace('/[^0-9.]/', '', $producto['precio_original']);
            $producto['precio_original'] = floatval($precio_original_limpio);
        } else {
            $producto['precio_original'] = floatval($producto['precio_original']);
        }

        // Formatear para mostrar
        $producto['precio_original_formatted'] = number_format($producto['precio_original'], 0, ',', '.');

        error_log("Precio original procesado: Original={$precio_original_valor}, Convertido={$producto['precio_original']}, Formateado={$producto['precio_original_formatted']}");
    } else {
        // Asegurarse de que sea null si no existe o está vacío
        $producto['precio_original'] = null;
        error_log("Precio original no disponible o vacío");
    }

    // Preparar la respuesta
    $response = [
        'success' => true,
        'producto' => $producto
    ];

    // Agregar información adicional para depuración
    $response['debug'] = [
        'precio_tipo' => gettype($producto['precio']),
        'precio_valor' => $producto['precio'],
        'precio_original_tipo' => gettype($producto['precio_original']),
        'precio_original_valor' => $producto['precio_original']
    ];

    error_log("Respuesta preparada: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    error_log("ERROR en get_product_details: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener detalles del producto: ' . $e->getMessage()
    ]);
}

// Cerrar la conexión
$conn->close();
error_log("==== FIN get_product_details.php ====");