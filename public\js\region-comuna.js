/**
 * Script para manejar la relación entre regiones y comunas
 * Este archivo se encarga de poblar dinámicamente las comunas según la región seleccionada
 * Utiliza los archivos JSON de regiones y comunas
 */

// Ejecutar cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando script de regiones y comunas');
    
    // Obtener referencias a los elementos select
    const regionSelect = document.getElementById('region');
    const comunaSelect = document.getElementById('comuna');
    
    // Verificar que los elementos existan
    if (!regionSelect || !comunaSelect) {
        console.error('No se encontraron los elementos de región o comuna');
        return;
    }
    
    // Mapeo de valores del select a códigos de región
    const regionValueToCodigo = {
        'arica': '15',
        'tarapaca': '01',
        'antofagasta': '02',
        'atacama': '03',
        'coquimbo': '04',
        'valparaiso': '05',
        'metropolitana': '13',
        'ohiggins': '06',
        'maule': '07',
        'nuble': '16',
        'biobio': '08',
        'araucania': '09',
        'losrios': '14',
        'loslagos': '10',
        'aysen': '11',
        'magallanes': '12'
    };
    
    // Variable para almacenar los datos de comunas
    let comunasData = null;
    
    // Cargar los datos de comunas desde el archivo JSON
    fetch('/js/comunas.json')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al cargar el archivo de comunas');
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos de comunas cargados correctamente');
            comunasData = data.comunas;
            
            // Verificar si ya hay una región seleccionada al cargar la página
            if (regionSelect.value) {
                actualizarComunas();
            }
        })
        .catch(error => {
            console.error('Error al cargar los datos de comunas:', error);
            // Intentar con una ruta alternativa
            fetch('../js/comunas.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Error al cargar el archivo de comunas (ruta alternativa)');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Datos de comunas cargados correctamente (ruta alternativa)');
                    comunasData = data.comunas;
                    
                    // Verificar si ya hay una región seleccionada al cargar la página
                    if (regionSelect.value) {
                        actualizarComunas();
                    }
                })
                .catch(error => {
                    console.error('Error al cargar los datos de comunas (ruta alternativa):', error);
                    // Intentar con una tercera ruta alternativa
                    fetch('js/comunas.json')
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Error al cargar el archivo de comunas (tercera ruta)');
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('Datos de comunas cargados correctamente (tercera ruta)');
                            comunasData = data.comunas;
                            
                            // Verificar si ya hay una región seleccionada al cargar la página
                            if (regionSelect.value) {
                                actualizarComunas();
                            }
                        })
                        .catch(error => {
                            console.error('Error al cargar los datos de comunas (tercera ruta):', error);
                        });
                });
        });
    
    // Función para actualizar las comunas según la región seleccionada
    function actualizarComunas() {
        // Obtener el valor de la región seleccionada
        const regionValue = regionSelect.value;
        console.log('Región seleccionada (valor):', regionValue);
        
        // Limpiar el selector de comunas
        comunaSelect.innerHTML = '<option value="">Seleccionar comuna...</option>';
        
        // Si hay una región seleccionada y tenemos los datos de comunas
        if (regionValue && comunasData) {
            // Obtener el código de la región
            const regionCodigo = regionValueToCodigo[regionValue];
            console.log('Código de región:', regionCodigo);
            
            if (regionCodigo && comunasData[regionCodigo]) {
                const comunas = comunasData[regionCodigo];
                console.log('Comunas encontradas:', comunas.length);
                
                // Agregar cada comuna como una opción
                comunas.forEach(comuna => {
                    const option = document.createElement('option');
                    option.value = comuna.nombre;
                    option.textContent = comuna.nombre;
                    comunaSelect.appendChild(option);
                });
                
                // Habilitar el selector de comunas
                comunaSelect.disabled = false;
            } else {
                console.error('No se encontraron comunas para la región:', regionValue);
                comunaSelect.innerHTML = '<option value="">No hay comunas disponibles</option>';
                comunaSelect.disabled = true;
            }
        } else {
            // Si no hay región seleccionada o no tenemos datos, deshabilitar el selector de comunas
            comunaSelect.innerHTML = '<option value="">Seleccione una región primero...</option>';
            comunaSelect.disabled = true;
        }
    }
    
    // Agregar el evento de cambio al selector de región
    regionSelect.addEventListener('change', actualizarComunas);
    
    // Verificar si ya hay una región seleccionada al cargar la página
    console.log('Valor inicial de región:', regionSelect.value);
});
