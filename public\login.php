<?php
/**
 * login.php
 * 
 * Versión simplificada del login para compatibilidad con el servidor de producción
 */

// Limpiar buffer de salida
while (ob_get_level()) ob_end_clean();
ob_start();

// Incluir archivos de configuración
require_once '../config/config.php';
require_once '../config/SessionManager.php';
require_once '../config/LoginAttempts.php';

// Inicializar el gestor de intentos de login
$loginAttempts = new LoginAttempts($conn);

// Inicializar el gestor de sesiones
$sessionManager = SessionManager::getInstance();

// Inicializar la sesión
$sessionManager->initializeSession();

// Verificar si el usuario ya está autenticado
if ($sessionManager->isLoggedIn()) {
    $currentUser = $sessionManager->getCurrentUser();
    error_log("Login.php - Usuario ya autenticado: " . $currentUser['username']);
    
    // Forzar escritura de sesión antes de redireccionar
    session_write_close();
    
    // Redirigir a tienda_adm.php
    $redirect_url = BASE_URL . '/public/tienda_adm.php';
    
    // Si hay una URL de retorno específica y es segura, usarla
    if (isset($_GET['return_to'])) {
        $return_to = $_GET['return_to'];
        // Verificar que es una URL relativa y pertenece a la aplicación
        if (substr($return_to, 0, 1) === '/' && 
            strpos($return_to, BASE_URL) === 0) {
            $redirect_url = $return_to;
        }
    }
    
    error_log("Login.php - Redirigiendo a: " . $redirect_url);
    header('Location: ' . $redirect_url);
    exit();
}

// Debug después de la verificación de autenticación
error_log("Login.php - Usuario no autenticado, mostrando formulario de login");

// Obtener mensaje de error si existe
$error_message = '';
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'empty':
            $error_message = 'Por favor ingrese usuario y contraseña';
            break;
        case 'invalid_credentials':
            $error_message = 'Usuario o contraseña incorrectos';
            break;
        case 'too_many_attempts':
            $error_message = 'Demasiados intentos fallidos. Por favor, intente nuevamente más tarde.';
            break;
        case 'security_violation':
            $error_message = 'Se detectó un problema de seguridad. Por favor, intente nuevamente.';
            break;
        case 'database_error':
            $error_message = 'Error en el sistema. Por favor, intente nuevamente más tarde.';
            break;
        case 'unauthorized':
            if (isset($_GET['reason']) && $_GET['reason'] === 'login_required') {
                $error_message = 'Por favor inicie sesión para acceder a esta página';
            } else {
                $error_message = 'No tiene permisos para acceder a esta página';
            }
            break;
        default:
            $error_message = 'Error al iniciar sesión. Por favor, intente nuevamente.';
    }
}

// Obtener mensaje de éxito si existe
$success_message = '';
if (isset($_GET['message']) && $_GET['message'] === 'logout_success') {
    $success_message = 'Has cerrado sesión exitosamente.';
}

// Generar token CSRF para protección contra ataques CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// Función auxiliar para sanitizar output
function sanitize_output($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

// Establecer algunos headers de seguridad básicos
header("X-Frame-Options: DENY");
header("X-XSS-Protection: 1; mode=block");
header("X-Content-Type-Options: nosniff");

// Procesar el formulario directamente en este archivo para evitar problemas de ruteo
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar token CSRF
    $submitted_token = isset($_POST['csrf_token']) ? $_POST['csrf_token'] : '';
    if (empty($submitted_token) || !hash_equals($_SESSION['csrf_token'], $submitted_token)) {
        header('Location: ' . BASE_URL . '/public/login.php?error=security_violation');
        exit();
    }
    
    // Obtener dirección IP del usuario
    $ip_address = $_SERVER['REMOTE_ADDR'];
    
    // Verificar intentos de login
    $attemptsInfo = $loginAttempts->checkAttempts($username, $ip_address);
    
    // Verificar si está bloqueado
    if ($attemptsInfo['locked']) {
        $locked_until = $loginAttempts->checkLockout($username, $ip_address);
        if ($locked_until) {
            $error_message = 'Demasiados intentos fallidos. Por favor, intente nuevamente en ' . 
                            ceil((strtotime($locked_until) - time()) / 60) . ' minutos.';
            header('Location: ' . BASE_URL . '/public/login.php?error=too_many_attempts');
            exit();
        }
    }
    
    // Validar y sanitizar inputs
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    // Verificar campos vacíos
    if (empty($username) || empty($password)) {
        header('Location: ' . BASE_URL . '/public/login.php?error=empty');
        exit();
    }
    
    // Conectar a la base de datos
    global $conn;
    
    // Buscar usuario
    $stmt = $conn->prepare("SELECT id, username, email, password, role FROM users WHERE username = ? OR email = ?");
    if (!$stmt) {
        error_log("Error en preparación de consulta: " . $conn->error);
        header('Location: ' . BASE_URL . '/public/login.php?error=database_error');
        exit();
    }
    
    $stmt->bind_param("ss", $username, $username);
    if (!$stmt->execute()) {
        error_log("Error en ejecución de consulta: " . $stmt->error);
        header('Location: ' . BASE_URL . '/public/login.php?error=database_error');
        exit();
    }
    
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        // Usuario no encontrado
        header('Location: ' . BASE_URL . '/public/login.php?error=invalid_credentials');
        exit();
    }
    
    $user = $result->fetch_assoc();
    
    // Verificar contraseña
    $password_valid = false;
    
    // Verificar si la contraseña está en formato hash
    if (preg_match('/^\$2[ayb]\$[0-9]{2}\$/', $user['password'])) {
        // Usar password_verify para comparar el hash
        $password_valid = password_verify($password, $user['password']);
    } 
    // Para compatibilidad con contraseñas en texto plano (temporal)
    else {
        $password_valid = ($password === $user['password']);
    }
    
    if (!$password_valid) {
        // Registrar intento fallido
        $loginAttempts->recordAttempt($username, $ip_address);
        
        // Verificar si se alcanzó el límite
        $attemptsInfo = $loginAttempts->checkAttempts($username, $ip_address);
        if ($attemptsInfo['attempts'] >= $loginAttempts->max_attempts) {
            // Establecer tiempo de bloqueo
            $stmt = $conn->prepare("UPDATE login_attempts 
                                SET locked_until = DATE_ADD(NOW(), INTERVAL ? MINUTE) 
                                WHERE username = ? AND ip_address = ?");
            
            $lockout_minutes = ceil($loginAttempts->lockout_duration / 60);
            $stmt->bind_param("iss", $lockout_minutes, $username, $ip_address);
            $stmt->execute();
        }
        
        // Contraseña incorrecta
        header('Location: ' . BASE_URL . '/public/login.php?error=invalid_credentials');
        exit();
    }
    
    // Autenticación exitosa, crear sesión
    $userData = [
        'id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'role' => $user['role'],
        'user_id' => $user['id']
    ];
    
    $sessionManager->createUserSession($userData);
    
    // Redirigir a la página principal
    $redirect_url = BASE_URL . '/public/tienda_adm.php';
    
    // Si hay una URL de retorno específica, usarla
    if (isset($_POST['return_to']) && !empty($_POST['return_to'])) {
        $return_to = $_POST['return_to'];
        // Verificar que es una URL relativa y pertenece a la aplicación
        if (substr($return_to, 0, 1) === '/' && 
            strpos($return_to, BASE_URL) === 0) {
            $redirect_url = $return_to;
        }
    }
    
    header('Location: ' . $redirect_url);
    exit();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Iniciar Sesión - Villarrica Click</title>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Estilos personalizados -->
    <style>
        :root {
            --primary-color: #4e73df;
            --primary-dark: #3a56c5;
            --secondary-color: #f8f9fc;
            --text-color: #2e384d;
            --error-color: #e74a3b;
            --success-color: #1cc88a;
            --border-color: #e3e6f0;
            --shadow-color: rgba(0, 0, 0, 0.05);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--secondary-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            width: 100%;
            max-width: 420px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 5px 20px var(--shadow-color);
            overflow: hidden;
        }
        
        .login-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h1 {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .form-group i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #adb5bd;
            z-index: 1;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            font-size: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
            color: var(--text-color);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
        }
        
        .form-text {
            display: block;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .form-check-input {
            margin-right: 0.5rem;
        }
        
        .form-check-label {
            font-size: 0.9rem;
            color: var(--text-color);
        }
        
        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 8px;
            transition: all 0.15s ease-in-out;
            cursor: pointer;
        }
        
        .btn-primary {
            color: #fff;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            width: 100%;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .alert-danger {
            background-color: #ffe8e6;
            color: var(--error-color);
            border: 1px solid #fcd9d7;
        }
        
        .alert-success {
            background-color: #e6f8f1;
            color: var(--success-color);
            border: 1px solid #d1f3e5;
        }
        
        .login-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid var(--border-color);
            text-align: center;
            background-color: #f9f9f9;
        }
        
        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.15s ease-in-out;
        }
        
        .login-footer a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }
        
        .divider {
            display: flex;
            align-items: center;
            margin: 1.5rem 0;
            color: #6c757d;
        }
        
        .divider::before,
        .divider::after {
            content: "";
            flex: 1;
            border-bottom: 1px solid var(--border-color);
        }
        
        .divider::before {
            margin-right: 1rem;
        }
        
        .divider::after {
            margin-left: 1rem;
        }
        
        .social-login {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .btn-social {
            padding: 0.75rem;
            border-radius: 8px;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.15s ease-in-out;
        }
        
        .btn-google {
            color: #fff;
            background-color: #ea4335;
            border-color: #ea4335;
        }
        
        .btn-facebook {
            color: #fff;
            background-color: #3b5998;
            border-color: #3b5998;
        }
        
        .btn-social:hover {
            opacity: 0.9;
        }
        
        /* Animaciones */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .login-container {
            animation: fadeIn 0.5s ease-out;
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                max-width: 100%;
                border-radius: 0;
                box-shadow: none;
            }
            
            .login-header,
            .login-form,
            .login-footer {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Bienvenido</h1>
            <p>Inicia sesión para acceder a tu cuenta</p>
        </div>
        
        <div class="login-form">
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo sanitize_output($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo sanitize_output($success_message); ?>
                </div>
            <?php endif; ?>
            
            <form action="" method="POST" id="loginForm">
                <!-- Token CSRF para protección contra ataques CSRF -->
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                
                <?php if (isset($_GET['return_to'])): ?>
                    <input type="hidden" name="return_to" value="<?php echo sanitize_output($_GET['return_to']); ?>">
                <?php endif; ?>
                
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" 
                           class="form-control" 
                           name="username" 
                           id="username"
                           placeholder="Usuario o Email" 
                           required
                           autocomplete="username">
                    <small class="form-text">Ingresa tu nombre de usuario o correo electrónico</small>
                </div>
                
                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" 
                           class="form-control" 
                           name="password" 
                           id="password"
                           placeholder="Contraseña" 
                           required
                           autocomplete="current-password">
                    <small class="form-text">Ingresa tu contraseña</small>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Recordarme en este dispositivo</label>
                </div>
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Iniciar Sesión
                </button>
                
                <div class="divider">O</div>
                
                <div class="social-login">
                    <a href="#" class="btn btn-social btn-google" title="Iniciar sesión con Google">
                        <i class="fab fa-google"></i>
                    </a>
                    <a href="#" class="btn btn-social btn-facebook" title="Iniciar sesión con Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                </div>
            </form>
        </div>
        
        <div class="login-footer">
            <p><a href="recuperar.php">¿Olvidaste tu contraseña?</a></p>
            <p>¿No tienes una cuenta? <a href="register.php">Regístrate aquí</a></p>
        </div>
    </div>
    
    <script>
        // Script para mejorar la experiencia de usuario
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            
            // Enfocar el primer campo al cargar
            usernameInput.focus();
            
            // Validación básica en el cliente
            form.addEventListener('submit', function(e) {
                let valid = true;
                
                if (!usernameInput.value.trim()) {
                    valid = false;
                    highlightError(usernameInput, 'Por favor ingrese su usuario o email');
                } else {
                    removeError(usernameInput);
                }
                
                if (!passwordInput.value) {
                    valid = false;
                    highlightError(passwordInput, 'Por favor ingrese su contraseña');
                } else {
                    removeError(passwordInput);
                }
                
                if (!valid) {
                    e.preventDefault();
                }
            });
            
            // Funciones auxiliares
            function highlightError(element, message) {
                element.classList.add('is-invalid');
                
                let errorElement = element.nextElementSibling;
                if (errorElement && errorElement.classList.contains('form-text')) {
                    errorElement.innerHTML = message;
                    errorElement.style.color = 'var(--error-color)';
                }
            }
            
            function removeError(element) {
                element.classList.remove('is-invalid');
                
                let errorElement = element.nextElementSibling;
                if (errorElement && errorElement.classList.contains('form-text')) {
                    errorElement.style.color = '';
                    errorElement.innerHTML = errorElement.getAttribute('data-original-text') || '';
                }
            }
            
            // Guardar el texto original para restaurarlo
            document.querySelectorAll('.form-text').forEach(element => {
                element.setAttribute('data-original-text', element.innerHTML);
            });
        });
    </script>
</body>
</html>