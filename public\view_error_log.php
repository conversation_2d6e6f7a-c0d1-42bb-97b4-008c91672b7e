<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/SessionManager.php';

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Validar la sesión antes de cualquier otra operación
if (!$sessionManager->validateSession()) {
    error_log("view_error_log.php - Sesión no válida o usuario no autenticado");
    header('Location: ' . BASE_URL . '/public/login.php?error=unauthorized&reason=login_required');
    exit();
}

// Verificar si el usuario es administrador
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    error_log("view_error_log.php - Usuario no es administrador: " . ($_SESSION['role'] ?? 'no role'));
    header('Location: ' . BASE_URL . '/public/login.php?error=unauthorized&reason=admin_required');
    exit();
}

// Asegurar que tenemos los permisos adecuados
session_start();
if (!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    echo "Acceso denegado";
    exit();
}

$error_log_path = $_SERVER['DOCUMENT_ROOT'] . '/projects/villarrica_click/error_log.txt';

echo '<html>
<head>
    <title>Visor de Logs de Error</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        pre {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 14px;
        }
        .error {
            color: #d9534f;
            font-weight: bold;
        }
        .request {
            color: #5bc0de;
            font-weight: bold;
        }
        .success {
            color: #5cb85c;
            font-weight: bold;
        }
        .warning {
            color: #f0ad4e;
            font-weight: bold;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        button:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <h1>Visor de Logs de Error</h1>
    <div>
        <button onclick="window.location.reload()">Actualizar</button>
        <button onclick="window.location=\'view_error_log.php?clear=1\'">Limpiar Logs</button>
        <button onclick="window.location=\'../\'">Volver</button>
    </div>
    <h2>Contenido del archivo: <?php echo $error_log_path; ?></h2>';

// Limpiar el log si se solicita
if (isset($_GET['clear']) && $_GET['clear'] == '1') {
    if (file_exists($error_log_path)) {
        file_put_contents($error_log_path, "Log limpiado en " . date('Y-m-d H:i:s') . "\n");
        echo '<p class="success">El archivo de log ha sido limpiado.</p>';
    }
}

// Mostrar el contenido del log con formato
if (file_exists($error_log_path)) {
    $content = file_get_contents($error_log_path);
    
    // Aplicar colores a diferentes tipos de mensajes
    $content = preg_replace('/\b(ERROR|EXCEPCIÓN|Error|error|excepción|fault|Fault)\b/', '<span class="error">$1</span>', $content);
    $content = preg_replace('/\b(REQUEST|SOLICITUD|Método HTTP|Content-Type)\b/', '<span class="request">$1</span>', $content);
    $content = preg_replace('/\b(SUCCESS|ÉXITO|Producto insertado|success|true)\b/', '<span class="success">$1</span>', $content);
    $content = preg_replace('/\b(WARNING|ADVERTENCIA|AVISO)\b/', '<span class="warning">$1</span>', $content);
    
    // Resaltar líneas de inicio y fin de solicitud
    $content = preg_replace('/(===.*===)/', '<strong>$1</strong>', $content);
    
    echo '<pre>' . $content . '</pre>';
} else {
    echo '<p>El archivo de log no existe o no se puede leer.</p>';
}

echo '</body></html>';
?>