<?php
/**
 * <PERSON><PERSON> Logger
 * 
 * Maneja el registro de eventos y errores del sistema
 */
class Logger {
    /**
     * Directorio donde se guardarán los logs
     * 
     * @var string
     */
    private $logDir;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->logDir = dirname(__DIR__) . '/logs';
        
        // Crear directorio si no existe
        if (!is_dir($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }
    }
    
    /**
     * Registra un evento en el archivo de log correspondiente
     * 
     * @param string $tipo Tipo de evento
     * @param array $datos Datos adicionales
     * @return bool
     */
    public function registrarEvento($tipo, $datos = []) {
        $archivo = $this->logDir . '/' . $tipo . '_' . date('Y-m-d') . '.log';
        
        // Crear mensaje de log
        $timestamp = date('Y-m-d H:i:s');
        $mensaje = "[{$timestamp}] Evento: {$tipo}\n";
        $mensaje .= "Datos: " . json_encode($datos, JSON_PRETTY_PRINT) . "\n";
        $mensaje .= "-------------------------\n";
        
        // Escribir en archivo
        return file_put_contents($archivo, $mensaje, FILE_APPEND) !== false;
    }
    
    /**
     * Obtiene los eventos registrados de un tipo específico
     * 
     * @param string $tipo Tipo de evento
     * @param string $fecha Fecha en formato Y-m-d (opcional)
     * @return array
     */
    public function obtenerEventos($tipo, $fecha = null) {
        if ($fecha === null) {
            $fecha = date('Y-m-d');
        }
        
        $archivo = $this->logDir . '/' . $tipo . '_' . $fecha . '.log';
        
        if (!file_exists($archivo)) {
            return [];
        }
        
        // Leer contenido del archivo
        $contenido = file_get_contents($archivo);
        $eventos = [];
        
        // Dividir por eventos
        $bloques = explode("-------------------------\n", $contenido);
        
        foreach ($bloques as $bloque) {
            if (empty(trim($bloque))) {
                continue;
            }
            
            // Extraer timestamp
            if (preg_match('/\[(.*?)\]/', $bloque, $matches)) {
                $timestamp = $matches[1];
                
                // Extraer datos JSON
                if (preg_match('/Datos: (.*)/s', $bloque, $matches)) {
                    $datos = json_decode($matches[1], true);
                    
                    $eventos[] = [
                        'timestamp' => $timestamp,
                        'datos' => $datos
                    ];
                }
            }
        }
        
        return $eventos;
    }
}
