# Script para transferir archivos al servidor Villarrica Click
Write-Host "Iniciando transferencia de archivos..." -ForegroundColor Green

# Configuración del servidor
$SERVER = "root@**************"
$PORT = "22222"
$KEY_PATH = "c:\Users\<USER>\OneDrive - kayze\Laboral\villarrica_click\villarrica_click\ssh_keys\id_rsa"
$DEST_BASE = "/var/www/aunclick"

# Transferencia de archivos HTML y JS
Write-Host "Transfiriendo tienda_adm.html..." -ForegroundColor Yellow
scp -i "$KEY_PATH" -P $PORT "public\tienda_adm.html" "${SERVER}:${DEST_BASE}/public/"
Write-Host "Transfiriendo tienda_adm.js..." -ForegroundColor Yellow
scp -i "$KEY_PATH" -P $PORT "js\tienda_adm.js" "${SERVER}:${DEST_BASE}/js/"

# Transferencia de archivos CSS modulares
Write-Host "Transfiriendo archivos CSS modulares..." -ForegroundColor Yellow
Write-Host "- variables.css" -ForegroundColor Cyan
scp -i "$KEY_PATH" -P $PORT "css\variables.css" "${SERVER}:${DEST_BASE}/css/"
Write-Host "- layout.css" -ForegroundColor Cyan
scp -i "$KEY_PATH" -P $PORT "css\layout.css" "${SERVER}:${DEST_BASE}/css/"
Write-Host "- components.css" -ForegroundColor Cyan
scp -i "$KEY_PATH" -P $PORT "css\components.css" "${SERVER}:${DEST_BASE}/css/"
Write-Host "- responsive.css" -ForegroundColor Cyan
scp -i "$KEY_PATH" -P $PORT "css\responsive.css" "${SERVER}:${DEST_BASE}/css/"

Write-Host "Transferencia completada exitosamente!" -ForegroundColor Green