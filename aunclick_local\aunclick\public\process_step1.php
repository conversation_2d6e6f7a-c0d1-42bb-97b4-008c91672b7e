<?php
// Incluir configuración y conexión a la base de datos
require_once '../config/config.php';
require_once '../config/db.php';
require_once '../config/logger.php';

// Iniciar o reanudar sesión
session_start();

// Configurar logging detallado para este script
logInfo("Iniciando process_step1.php", [
    'request_method' => $_SERVER['REQUEST_METHOD'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT']
]);

// Función para enviar respuesta JSON
function sendJsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    logInfo("Enviando respuesta JSON", [
        'success' => $success,
        'message' => $message
    ]);
    
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Validar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError("Método no permitido", [
        'method' => $_SERVER['REQUEST_METHOD']
    ]);
    sendJsonResponse(false, 'Método no permitido');
}

// Obtener y validar los datos del formulario
$nombres = isset($_POST['nombres']) ? trim($_POST['nombres']) : '';
$apellidos = isset($_POST['apellidos']) ? trim($_POST['apellidos']) : '';
$rut = isset($_POST['rut']) ? trim($_POST['rut']) : '';
$fechaNacimiento = isset($_POST['fechaNacimiento']) ? trim($_POST['fechaNacimiento']) : '';
$sexo = isset($_POST['sexo']) ? trim($_POST['sexo']) : '';
$telefono = isset($_POST['telefono']) ? trim($_POST['telefono']) : '';
$region = isset($_POST['region']) ? trim($_POST['region']) : '';
$comuna = isset($_POST['comuna']) ? trim($_POST['comuna']) : '';
$direccion = isset($_POST['direccion']) ? trim($_POST['direccion']) : '';

// Registrar los datos recibidos
logDebug("Datos recibidos", [
    'nombres' => $nombres,
    'apellidos' => $apellidos,
    'rut' => $rut,
    'fechaNacimiento' => $fechaNacimiento,
    'sexo' => $sexo,
    'telefono' => $telefono,
    'region' => $region,
    'comuna' => $comuna,
    'direccion' => $direccion
]);

// Validar que todos los campos requeridos estén presentes
if (empty($nombres) || empty($apellidos) || empty($rut) || empty($fechaNacimiento) || 
    empty($sexo) || empty($telefono) || empty($region) || empty($comuna) || empty($direccion)) {
    
    $campos_vacios = [];
    if (empty($nombres)) $campos_vacios[] = 'nombres';
    if (empty($apellidos)) $campos_vacios[] = 'apellidos';
    if (empty($rut)) $campos_vacios[] = 'rut';
    if (empty($fechaNacimiento)) $campos_vacios[] = 'fechaNacimiento';
    if (empty($sexo)) $campos_vacios[] = 'sexo';
    if (empty($telefono)) $campos_vacios[] = 'telefono';
    if (empty($region)) $campos_vacios[] = 'region';
    if (empty($comuna)) $campos_vacios[] = 'comuna';
    if (empty($direccion)) $campos_vacios[] = 'direccion';
    
    logError("Campos obligatorios faltantes", [
        'campos_vacios' => $campos_vacios
    ]);
    
    sendJsonResponse(false, 'Todos los campos son obligatorios');
}

// Guardar los datos en la sesión para usarlos en el paso 2
$_SESSION['paso1_data'] = [
    'nombres' => $nombres,
    'apellidos' => $apellidos,
    'rut' => $rut,
    'fechaNacimiento' => $fechaNacimiento,
    'sexo' => $sexo,
    'telefono' => $telefono,
    'region' => $region,
    'comuna' => $comuna,
    'direccion' => $direccion
];

// Registrar en el log
logInfo("Datos del paso 1 guardados en sesión", [
    'session_id' => session_id(),
    'nombres' => $nombres,
    'apellidos' => $apellidos
]);

// Devolver respuesta exitosa
sendJsonResponse(true, 'Datos del paso 1 guardados correctamente');


