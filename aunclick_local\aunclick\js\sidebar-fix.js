/**
 * Solución para el sidebar en modo responsive
 * Este script reemplaza completamente la funcionalidad del sidebar en modo responsive
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando sidebar-fix.js...');

    // Elementos principales
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');
    const body = document.body;

    if (!sidebar || !toggleBtn) {
        console.error('No se encontró el sidebar o el botón de toggle');
        return;
    }

    // Crear overlay
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay-fix';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '1040';
    overlay.style.display = 'none';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.3s ease';
    document.body.appendChild(overlay);

    // Función para mostrar el sidebar
    function showSidebar() {
        console.log('Mostrando sidebar...');
        sidebar.classList.add('expanded');
        sidebar.classList.remove('collapsed');

        // Forzar estilos inline para asegurar que el sidebar se expanda correctamente
        sidebar.style.width = '250px';

        // Mostrar overlay
        overlay.style.display = 'block';
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);

        // Cambiar ícono
        const toggleIcon = document.getElementById('toggle-icon');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-times';
        }

        // Mostrar elementos del sidebar
        const sidebarElements = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
        sidebarElements.forEach(element => {
            element.style.display = 'block';
        });
    }

    // Función para ocultar el sidebar
    function hideSidebar() {
        console.log('Ocultando sidebar...');
        sidebar.classList.remove('expanded');
        sidebar.classList.add('collapsed');

        // Forzar estilos inline para asegurar que el sidebar se colapse correctamente
        sidebar.style.width = '70px';

        // Ocultar overlay
        overlay.style.opacity = '0';
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);

        // Cambiar ícono
        const toggleIcon = document.getElementById('toggle-icon');
        if (toggleIcon) {
            toggleIcon.className = 'fas fa-bars';
        }

        // Ocultar elementos del sidebar
        const sidebarElements = sidebar.querySelectorAll('.sidebar-logo, .sidebar-subtitle, .user-info, .nav-section-title, .nav-link span');
        sidebarElements.forEach(element => {
            element.style.display = 'none';
        });
    }

    // Manejar clic en el botón de toggle
    toggleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Toggle button clicked');

        if (window.innerWidth < 992) {
            if (sidebar.classList.contains('expanded')) {
                hideSidebar();
            } else {
                showSidebar();
            }
        } else {
            // Comportamiento normal en desktop
            sidebar.classList.toggle('collapsed');
            localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
        }
    });

    // Manejar clic en el overlay
    overlay.addEventListener('click', function() {
        hideSidebar();
    });

    // Manejar cambios de tamaño de ventana
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            // En desktop, ocultar overlay
            overlay.style.display = 'none';
            overlay.style.opacity = '0';

            // Restaurar estado del sidebar según localStorage
            const savedState = localStorage.getItem('asideCollapsed');
            if (savedState === 'true') {
                sidebar.classList.add('collapsed');
                sidebar.classList.remove('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                sidebar.classList.add('expanded');
            }
        } else {
            // En móvil, asegurar que el sidebar esté colapsado inicialmente
            if (!sidebar.classList.contains('expanded')) {
                sidebar.classList.remove('expanded');
                sidebar.classList.add('collapsed');
                overlay.style.display = 'none';
                overlay.style.opacity = '0';
            }
        }
    });

    // Inicializar estado del sidebar
    if (window.innerWidth < 992) {
        // En móvil, asegurar que el sidebar esté colapsado inicialmente
        sidebar.classList.remove('expanded');
        sidebar.classList.add('collapsed');

        // Forzar estilos inline para asegurar que el sidebar sea visible
        sidebar.style.position = 'fixed';
        sidebar.style.top = '0';
        sidebar.style.left = '0';
        sidebar.style.height = '100vh';
        sidebar.style.width = '70px';
        sidebar.style.zIndex = '9999';
        sidebar.style.transition = 'width 0.3s ease';
        sidebar.style.overflowX = 'hidden';
        sidebar.style.overflowY = 'auto';
    } else {
        // En desktop, restaurar estado según localStorage
        const savedState = localStorage.getItem('asideCollapsed');
        if (savedState === 'true') {
            sidebar.classList.add('collapsed');
            sidebar.classList.remove('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            sidebar.classList.add('expanded');
        }
    }

    console.log('sidebar-fix.js inicializado correctamente');
});
