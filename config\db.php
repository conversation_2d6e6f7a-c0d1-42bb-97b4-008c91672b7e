<?php
/**
 * Archivo de conexión a la base de datos centralizado
 * Este archivo proporciona funciones para conectar a la base de datos de manera consistente
 */

// Incluir el sistema de logging si no está incluido
if (!function_exists('logInfo')) {
    require_once __DIR__ . '/logger.php';
}

/**
 * Obtiene una conexión a la base de datos
 *
 * @return mysqli Objeto de conexión a la base de datos
 */
function getDbConnection() {
    // Configuración de la base de datos desde config.php
    global $db_host, $db_user, $db_pass, $db_name, $conn;

    // Si ya existe una conexión global, devolverla
    if (isset($conn) && $conn instanceof mysqli && !$conn->connect_error) {
        logInfo("Usando conexión global existente");
        return $conn;
    }

    // Si no hay configuración global, usar valores predeterminados seguros
    $host = $db_host ?? 'localhost';
    $user = $db_user ?? 'pcornejo';
    $pass = $db_pass ?? 'Pcornejo@2025';
    $dbname = $db_name ?? 'aunclick_prueba';

    // Crear nueva conexión
    $connection = new mysqli($host, $user, $pass, $dbname);

    // Verificar conexión
    if ($connection->connect_error) {
        logError("Error de conexión a la base de datos", [
            'error' => $connection->connect_error,
            'host' => $host,
            'database' => $dbname
        ]);

        // Devolver null en caso de error
        return null;
    }

    // Establecer charset
    $connection->set_charset("utf8");

    logInfo("Nueva conexión a base de datos establecida", [
        'host' => $host,
        'database' => $dbname
    ]);

    // Guardar la conexión en la variable global para futuras llamadas
    $conn = $connection;

    return $connection;
}

/**
 * Cierra una conexión a la base de datos de manera segura
 *
 * @param mysqli $connection Conexión a cerrar
 * @return bool Éxito o fracaso de la operación
 */
function closeDbConnection($connection = null) {
    global $conn;

    // Si no se proporciona conexión, usar la global
    if ($connection === null) {
        $connection = $conn ?? null;
    }

    // Verificar que la conexión sea válida
    if ($connection instanceof mysqli) {
        $result = $connection->close();
        logInfo("Conexión a base de datos cerrada");
        return $result;
    }

    return false;
}

/**
 * Ejecuta una consulta preparada de manera segura
 *
 * @param string $sql Consulta SQL con marcadores de posición
 * @param string $types Tipos de los parámetros (s: string, i: integer, d: double, b: blob)
 * @param array $params Parámetros para la consulta
 * @return array|bool Resultado de la consulta o false en caso de error
 */
function executeQuery($sql, $types = '', $params = []) {
    $connection = getDbConnection();

    if (!$connection) {
        logError("No se pudo obtener conexión para ejecutar consulta", ['sql' => $sql]);
        return false;
    }

    // Preparar la consulta
    $stmt = $connection->prepare($sql);
    if (!$stmt) {
        logError("Error al preparar consulta", ['error' => $connection->error, 'sql' => $sql]);
        return false;
    }

    // Vincular parámetros si hay
    if (!empty($params)) {
        $bindParams = array_merge([$types], $params);
        $bindResult = call_user_func_array([$stmt, 'bind_param'], $bindParams);

        if (!$bindResult) {
            logError("Error al vincular parámetros", ['error' => $stmt->error, 'params' => $params]);
            $stmt->close();
            return false;
        }
    }

    // Ejecutar la consulta
    if (!$stmt->execute()) {
        logError("Error al ejecutar consulta", ['error' => $stmt->error, 'sql' => $sql]);
        $stmt->close();
        return false;
    }

    // Obtener resultados si es una consulta SELECT
    $result = [];
    if ($stmt->result_metadata()) {
        $stmt->store_result();

        // Obtener metadatos de las columnas
        $meta = $stmt->result_metadata();
        $fields = [];
        $row = [];

        while ($field = $meta->fetch_field()) {
            $fields[] = $field->name;
            $row[$field->name] = null;
        }

        // Vincular resultados a variables
        $bindResult = call_user_func_array([$stmt, 'bind_result'], $row);

        // Obtener todos los resultados
        while ($stmt->fetch()) {
            $rowData = [];
            foreach ($row as $key => $val) {
                $rowData[$key] = $val;
            }
            $result[] = $rowData;
        }
    } else {
        // Para consultas INSERT, UPDATE, DELETE
        $result = [
            'affected_rows' => $stmt->affected_rows,
            'insert_id' => $stmt->insert_id
        ];
    }

    $stmt->close();
    return $result;
}
