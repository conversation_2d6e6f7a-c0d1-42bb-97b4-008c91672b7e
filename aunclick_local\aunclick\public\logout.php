<?php
error_log('Logout.php - Iniciando proceso de logout');
error_log('Logout.php - Session ID: ' . session_id());
error_log('Logout.php - Session state: ' . print_r($_SESSION, true));

// Capturar el ID de sesión al inicio
$session_id_before = session_id();

// Forzar que no haya output previo
while (ob_get_level()) ob_end_clean();

// Incluir configuraciones
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Registrar el estado de la sesión antes de destruirla
error_log('Logout.php - Session ID antes: ' . $session_id_before);
error_log('Logout.php - $_SESSION antes: ' . print_r($_SESSION, true));
error_log('Logout.php - Cookies antes: ' . print_r($_COOKIE, true));

// Destruir la sesión usando el SessionManager
$sessionManager->destroySession();

// Registrar el estado después de destruir la sesión
error_log('Logout.php - Session ID después: ' . session_id());
error_log('Logout.php - $_SESSION después: ' . print_r($_SESSION, true));
error_log('Logout.php - Cookies después: ' . print_r($_COOKIE, true));

// Prevenir el caché
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');
header('Expires: Wed, 11 Jan 1984 05:00:00 GMT');

// Redirigir al login con parámetro de depuración
header('Location: ' . BASE_URL . '/public/login.php?message=logout_success&debug=true');
exit();