<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el archivo de configuración principal
require_once '../config/config.php';

// Verificar si la tabla tb_registros existe
$result = $conn->query("SHOW TABLES LIKE 'tb_registros'");
if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✓ La tabla tb_registros existe.</p>";

    // Mostrar estructura de la tabla
    echo "<h3>Estructura de la tabla:</h3>";
    $result = $conn->query("DESCRIBE tb_registros");
    if ($result->num_rows > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . (isset($row['Default']) ? htmlspecialchars($row['Default']) : 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ No se pudo obtener la estructura de la tabla.</p>";
    }

    // Mostrar registros existentes
    echo "<h3>Registros existentes:</h3>";
    $result = $conn->query("SELECT * FROM tb_registros");
    if ($result->num_rows > 0) {
        echo "<p>Número de registros: " . $result->num_rows . "</p>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Nombres</th><th>Apellidos</th><th>RUT</th><th>Fecha Nacimiento</th><th>Sexo</th><th>Teléfono</th><th>Región</th><th>Comuna</th><th>Dirección</th><th>Nombre Usuario</th><th>Email</th><th>Email Respaldo</th><th>Local Físico</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nombres']) . "</td>";
            echo "<td>" . htmlspecialchars($row['apellidos']) . "</td>";
            echo "<td>" . htmlspecialchars($row['rut']) . "</td>";
            echo "<td>" . htmlspecialchars($row['fechaNacimiento']) . "</td>";
            echo "<td>" . htmlspecialchars($row['sexo']) . "</td>";
            echo "<td>" . htmlspecialchars($row['telefono']) . "</td>";
            echo "<td>" . htmlspecialchars($row['region']) . "</td>";
            echo "<td>" . htmlspecialchars($row['comuna']) . "</td>";
            echo "<td>" . htmlspecialchars($row['direccion']) . "</td>";
            echo "<td>" . htmlspecialchars($row['NombreUsuario']) . "</td>";
            echo "<td>" . htmlspecialchars($row['mail']) . "</td>";
            echo "<td>" . htmlspecialchars($row['mailRespaldo'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($row['localFisico']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No hay registros en la tabla.</p>";
    }
} else {
    echo "<p style='color: red;'>✗ La tabla tb_registros no existe.</p>";
}

// Mostrar información de conexión
echo "<h3>Información de Conexión</h3>";
echo "<p><strong>Servidor:</strong> " . htmlspecialchars($db_host) . "</p>";
echo "<p><strong>Usuario:</strong> " . htmlspecialchars($db_user) . "</p>";
echo "<p><strong>Base de datos:</strong> " . htmlspecialchars($db_name) . "</p>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>MySQL Version:</strong> " . htmlspecialchars($conn->server_info) . "</p>";

// Cerrar la conexión
$conn->close();
?>
