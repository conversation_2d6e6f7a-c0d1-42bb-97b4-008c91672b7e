/**
 * Implementación del CategoryManager
 *
 * Este archivo muestra cómo implementar el CategoryManager tanto en el formulario
 * de creación como en el de edición de productos.
 */

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando implementación de CategoryManager');

    // Dar tiempo a que todos los elementos del DOM estén disponibles
    setTimeout(function() {
        // Detectar si estamos en el formulario de creación o edición
        const isEditForm = document.getElementById('editProductPanel') !== null;

        // Verificar si estamos en el formulario de creación (buscando elementos específicos)
        const productNameElement = document.getElementById('productName');
        const productCategoryElement = document.getElementById('productCategory');
        const saveProductBtnElement = document.getElementById('saveProductBtn');

        const isNewForm = productNameElement !== null &&
                         productCategoryElement !== null &&
                         saveProductBtnElement !== null;

        console.log('Detección de formularios - Edit:', isEditForm, 'New:', isNewForm);
        console.log('Elementos encontrados:', {
            'productName': productNameElement ? 'Sí' : 'No',
            'productCategory': productCategoryElement ? 'Sí' : 'No',
            'saveProductBtn': saveProductBtnElement ? 'Sí' : 'No'
        });

        if (isEditForm) {
            console.log('Inicializando gestor de categorías para formulario de edición');
            initEditFormCategories();
        } else if (isNewForm) {
            console.log('Inicializando gestor de categorías para formulario de creación');
            initNewFormCategories();
        } else {
            console.log('No se detectó un formulario de producto válido');
        }
    }, 500); // Esperar 500ms para asegurar que todos los elementos estén disponibles
});

/**
 * Inicializar el gestor de categorías para el formulario de creación
 */
function initNewFormCategories() {
    try {
        console.log('Inicializando CategoryManager para formulario de creación');

        // Verificar que los elementos existen
        const tipoSelect = document.getElementById('productTipoCategoria');
        const categoriaSelect = document.getElementById('productCategory');
        const subcategoriaSelect = document.getElementById('productSubcategory');

        if (!tipoSelect || !categoriaSelect || !subcategoriaSelect) {
            console.error('No se encontraron todos los elementos necesarios para el CategoryManager');
            console.log('Elementos encontrados:', {
                'tipoSelect': tipoSelect ? 'Sí' : 'No',
                'categoriaSelect': categoriaSelect ? 'Sí' : 'No',
                'subcategoriaSelect': subcategoriaSelect ? 'Sí' : 'No'
            });
            return;
        }

        // Configuración para el formulario de creación
        const categoryManager = new CategoryManager({
            tipoSelectId: 'productTipoCategoria',
            categoriaSelectId: 'productCategory',
            subcategoriaSelectId: 'productSubcategory',
            baseUrl: 'API/productos', // Ruta relativa desde public
            onError: function(message) {
                console.error('Error en CategoryManager:', message);
                // No mostrar notificaciones al usuario
                // if (typeof showNotification === 'function') {
                //     showNotification(message, 'error');
                // }
            }
        });

        // Inicializar y cargar datos
        console.log('Inicializando CategoryManager para formulario de creación...');

        // Intentar inicializar con reintentos
        let intentos = 0;
        const maxIntentos = 3;

        function intentarInicializar() {
            categoryManager.init().then(() => {
                console.log('CategoryManager inicializado correctamente para formulario de creación');
                // Guardar referencia global para acceso desde otras funciones
                window.productCategoryManager = categoryManager;
            }).catch(error => {
                console.error(`Error al inicializar CategoryManager (intento ${intentos + 1}/${maxIntentos}):`, error);
                intentos++;
                if (intentos < maxIntentos) {
                    console.log(`Reintentando inicialización en 500ms...`);
                    setTimeout(intentarInicializar, 500);
                } else {
                    console.error('Se agotaron los intentos de inicialización');
                }
            });
        }

        // Iniciar el proceso de inicialización
        intentarInicializar();

        // Guardar referencia global para acceso desde otras funciones (incluso antes de inicializar)
        window.productCategoryManager = categoryManager;

        console.log('CategoryManager para formulario de creación configurado correctamente');
    } catch (error) {
        console.error('Error al configurar CategoryManager para formulario de creación:', error);
        // No mostrar notificaciones al usuario
        // if (typeof showNotification === 'function') {
        //     showNotification('Error al cargar categorías: ' + error.message, 'error');
        // }
    }
}

/**
 * Inicializar el gestor de categorías para el formulario de edición
 */
function initEditFormCategories() {
    try {
        console.log('Inicializando CategoryManager para formulario de edición');

        // Verificar que los elementos existen
        const tipoSelect = document.getElementById('editProductTipoCategoria');
        const categoriaSelect = document.getElementById('editProductCategory');
        const subcategoriaSelect = document.getElementById('editProductSubcategory');

        if (!tipoSelect || !categoriaSelect || !subcategoriaSelect) {
            console.error('No se encontraron todos los elementos necesarios para el CategoryManager de edición');
            console.log('Elementos encontrados:', {
                'tipoSelect': tipoSelect ? 'Sí' : 'No',
                'categoriaSelect': categoriaSelect ? 'Sí' : 'No',
                'subcategoriaSelect': subcategoriaSelect ? 'Sí' : 'No'
            });
            return;
        }

        // Configuración para el formulario de edición
        const categoryManager = new CategoryManager({
            tipoSelectId: 'editProductTipoCategoria',
            categoriaSelectId: 'editProductCategory',
            subcategoriaSelectId: 'editProductSubcategory',
            baseUrl: 'API/productos', // Ruta relativa desde public
            onError: function(message) {
                console.error('Error en CategoryManager de edición:', message);
                // No mostrar notificaciones al usuario
                // if (typeof showNotification === 'function') {
                //     showNotification(message, 'error');
                // }
            }
        });

        // Inicializar y cargar datos
        console.log('Inicializando CategoryManager para formulario de edición...');

        // Intentar inicializar con reintentos
        let intentos = 0;
        const maxIntentos = 3;

        function intentarInicializar() {
            categoryManager.init().then(() => {
                console.log('CategoryManager inicializado correctamente para formulario de edición');
                // Guardar referencia global para acceso desde otras funciones
                window.editProductCategoryManager = categoryManager;
            }).catch(error => {
                console.error(`Error al inicializar CategoryManager de edición (intento ${intentos + 1}/${maxIntentos}):`, error);
                intentos++;
                if (intentos < maxIntentos) {
                    console.log(`Reintentando inicialización en 500ms...`);
                    setTimeout(intentarInicializar, 500);
                } else {
                    console.error('Se agotaron los intentos de inicialización para el formulario de edición');
                }
            });
        }

        // Iniciar el proceso de inicialización
        intentarInicializar();

        // Los valores específicos se establecerán cuando se cargue un producto para editar

        // Guardar referencia global para acceso desde otras funciones (incluso antes de inicializar)
        window.editProductCategoryManager = categoryManager;

        console.log('CategoryManager para formulario de edición configurado correctamente');
    } catch (error) {
        console.error('Error al configurar CategoryManager para formulario de edición:', error);
        // No mostrar notificaciones al usuario
        // if (typeof showNotification === 'function') {
        //     showNotification('Error al cargar categorías: ' + error.message, 'error');
        // }
    }
}

/**
 * Función para establecer valores de categoría al cargar un producto para editar
 * Esta función debe ser llamada desde la función que carga los datos del producto
 *
 * @param {Object} producto - Datos del producto
 */
function setCategoriesForEditProduct(producto) {
    if (!window.editProductCategoryManager) {
        console.error('CategoryManager para edición no está inicializado');
        return;
    }

    try {
        console.log('Estableciendo valores de categoría para producto en edición:', producto);

        // Establecer valores según los datos del producto
        window.editProductCategoryManager.init({
            tipoId: producto.tipo_categoria_id || null,
            categoriaId: producto.categoria_id || null,
            subcategoriaId: producto.subcategoria_id || null
        });

        console.log('Valores de categoría establecidos correctamente');
    } catch (error) {
        console.error('Error al establecer valores de categoría:', error);
        if (typeof showNotification === 'function') {
            showNotification('Error al cargar categorías del producto: ' + error.message, 'error');
        }
    }
}

/**
 * Función para obtener los valores de categoría seleccionados
 * Esta función debe ser llamada al guardar el producto
 *
 * @param {boolean} isEdit - Indica si estamos en modo edición
 * @returns {Object} Valores de categoría seleccionados
 */
function getSelectedCategoryValues(isEdit = false) {
    const manager = isEdit ? window.editProductCategoryManager : window.productCategoryManager;

    if (!manager) {
        console.error('CategoryManager no está inicializado');
        return {
            tipoId: null,
            categoriaId: null,
            subcategoriaId: null
        };
    }

    return manager.getSelectedValues();
}

// Exportar funciones para uso en otros archivos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initNewFormCategories,
        initEditFormCategories,
        setCategoriesForEditProduct,
        getSelectedCategoryValues
    };
}

// También hacer disponibles globalmente
window.initNewFormCategories = initNewFormCategories;
window.initEditFormCategories = initEditFormCategories;
window.setCategoriesForEditProduct = setCategoriesForEditProduct;
window.getSelectedCategoryValues = getSelectedCategoryValues;
