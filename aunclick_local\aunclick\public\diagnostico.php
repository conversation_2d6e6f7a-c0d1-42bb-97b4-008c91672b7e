<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Función para mostrar información en formato HTML
function showInfo($title, $data, $success = true) {
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid " . ($success ? "#4CAF50" : "#F44336") . "; border-radius: 4px;'>";
    echo "<h3 style='margin-top: 0; color: " . ($success ? "#4CAF50" : "#F44336") . ";'>" . $title . "</h3>";
    
    if (is_array($data) || is_object($data)) {
        echo "<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;'>";
        print_r($data);
        echo "</pre>";
    } else {
        echo "<p>" . $data . "</p>";
    }
    
    echo "</div>";
}

// Función para verificar si un archivo existe y es legible
function checkFile($path) {
    if (file_exists($path)) {
        if (is_readable($path)) {
            return [
                'exists' => true,
                'readable' => true,
                'size' => filesize($path),
                'modified' => date("Y-m-d H:i:s", filemtime($path))
            ];
        } else {
            return [
                'exists' => true,
                'readable' => false,
                'error' => 'El archivo existe pero no es legible'
            ];
        }
    } else {
        return [
            'exists' => false,
            'error' => 'El archivo no existe'
        ];
    }
}

// Función para verificar si un directorio existe y es escribible
function checkDirectory($path) {
    if (file_exists($path)) {
        if (is_dir($path)) {
            if (is_writable($path)) {
                return [
                    'exists' => true,
                    'is_dir' => true,
                    'writable' => true
                ];
            } else {
                return [
                    'exists' => true,
                    'is_dir' => true,
                    'writable' => false,
                    'error' => 'El directorio existe pero no es escribible'
                ];
            }
        } else {
            return [
                'exists' => true,
                'is_dir' => false,
                'error' => 'La ruta existe pero no es un directorio'
            ];
        }
    } else {
        return [
            'exists' => false,
            'error' => 'El directorio no existe'
        ];
    }
}

// Función para probar la conexión a la base de datos
function testDbConnection($host, $user, $pass, $dbname) {
    try {
        $conn = new mysqli($host, $user, $pass, $dbname);
        
        if ($conn->connect_error) {
            return [
                'success' => false,
                'error' => $conn->connect_error
            ];
        }
        
        return [
            'success' => true,
            'server_info' => $conn->server_info,
            'host_info' => $conn->host_info,
            'charset' => $conn->character_set_name()
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// Iniciar la salida HTML
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnóstico del Sistema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2196F3;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }
        h2 {
            color: #2196F3;
            margin-top: 30px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .action-button {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 0;
        }
        .action-button:hover {
            background-color: #0b7dda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Diagnóstico del Sistema</h1>
        
        <h2>Información del Servidor</h2>
        <?php
        showInfo("Versión de PHP", phpversion());
        showInfo("Información del Servidor", $_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido');
        showInfo("Directorio Raíz", $_SERVER['DOCUMENT_ROOT'] ?? 'Desconocido');
        showInfo("Directorio Actual", getcwd());
        ?>
        
        <h2>Verificación de Archivos Críticos</h2>
        <?php
        // Verificar archivos principales
        $files = [
            'register.php',
            'config/config.php',
            'config/logger.php',
            'db_connection.php',
            'process_step1.php'
        ];
        
        foreach ($files as $file) {
            $result = checkFile($file);
            showInfo("Archivo: $file", $result, $result['exists'] && ($result['readable'] ?? false));
        }
        ?>
        
        <h2>Verificación de Directorios</h2>
        <?php
        // Verificar directorios principales
        $directories = [
            'config',
            'logs',
            'css',
            'js'
        ];
        
        foreach ($directories as $dir) {
            $result = checkDirectory($dir);
            showInfo("Directorio: $dir", $result, $result['exists'] && ($result['is_dir'] ?? false) && ($result['writable'] ?? false));
        }
        ?>
        
        <h2>Prueba de Conexión a la Base de Datos</h2>
        <?php
        // Intentar cargar configuración
        $db_config = [];
        
        // Intentar desde config.php
        if (file_exists('config/config.php')) {
            // Capturar variables sin ejecutar el archivo
            $config_content = file_get_contents('config/config.php');
            preg_match('/\$db_host\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config['host'] = $matches[1] ?? 'localhost';
            
            preg_match('/\$db_user\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config['user'] = $matches[1] ?? 'root';
            
            preg_match('/\$db_pass\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config['pass'] = $matches[1] ?? '';
            
            preg_match('/\$db_name\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config['name'] = $matches[1] ?? 'click2';
            
            showInfo("Configuración desde config.php", $db_config);
        }
        
        // Intentar desde db_connection.php
        if (file_exists('db_connection.php')) {
            // Capturar variables sin ejecutar el archivo
            $config_content = file_get_contents('db_connection.php');
            preg_match('/\$servername\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config2['host'] = $matches[1] ?? 'localhost';
            
            preg_match('/\$username\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config2['user'] = $matches[1] ?? 'root';
            
            preg_match('/\$password\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config2['pass'] = $matches[1] ?? '';
            
            preg_match('/\$dbname\s*=\s*[\'"](.+?)[\'"]/', $config_content, $matches);
            $db_config2['name'] = $matches[1] ?? 'click2';
            
            showInfo("Configuración desde db_connection.php", $db_config2);
        }
        
        // Probar conexión con config.php
        if (!empty($db_config)) {
            $result = testDbConnection($db_config['host'], $db_config['user'], $db_config['pass'], $db_config['name']);
            showInfo("Prueba de conexión con config.php", $result, $result['success'] ?? false);
        }
        
        // Probar conexión con db_connection.php
        if (!empty($db_config2)) {
            $result = testDbConnection($db_config2['host'], $db_config2['user'], $db_config2['pass'], $db_config2['name']);
            showInfo("Prueba de conexión con db_connection.php", $result, $result['success'] ?? false);
        }
        ?>
        
        <h2>Acciones</h2>
        <a href="register.php" class="action-button">Ir a la página de registro</a>
        <a href="test_connection.php" class="action-button">Probar conexión a la base de datos</a>
    </div>
</body>
</html>
