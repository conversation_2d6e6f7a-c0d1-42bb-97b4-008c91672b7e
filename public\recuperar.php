<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Función para mostrar mensajes
function showMessage($message, $type = 'info') {
    $colors = [
        'info' => '#2196F3',
        'success' => '#4CAF50',
        'warning' => '#FF9800',
        'error' => '#F44336'
    ];
    
    $color = $colors[$type] ?? $colors['info'];
    
    echo "<div style='margin: 10px 0; padding: 15px; border-left: 5px solid $color; background-color: #f9f9f9;'>";
    echo "<p style='margin: 0; color: #333;'>$message</p>";
    echo "</div>";
}

// Función para restaurar un archivo desde una copia de seguridad
function restoreFile($file) {
    $backup_file = $file . '.bak';
    
    if (!file_exists($backup_file)) {
        return [
            'success' => false,
            'message' => "No existe copia de seguridad para $file"
        ];
    }
    
    if (file_exists($file)) {
        if (!is_writable($file)) {
            return [
                'success' => false,
                'message' => "El archivo $file no es escribible"
            ];
        }
    }
    
    if (!copy($backup_file, $file)) {
        return [
            'success' => false,
            'message' => "Error al restaurar $file desde $backup_file"
        ];
    }
    
    return [
        'success' => true,
        'message' => "Archivo $file restaurado correctamente"
    ];
}

// Función para crear una copia de seguridad de un archivo
function backupFile($file) {
    $backup_file = $file . '.bak';
    
    if (!file_exists($file)) {
        return [
            'success' => false,
            'message' => "El archivo $file no existe"
        ];
    }
    
    if (file_exists($backup_file)) {
        if (!is_writable($backup_file)) {
            return [
                'success' => false,
                'message' => "La copia de seguridad $backup_file no es escribible"
            ];
        }
    }
    
    if (!copy($file, $backup_file)) {
        return [
            'success' => false,
            'message' => "Error al crear copia de seguridad de $file"
        ];
    }
    
    return [
        'success' => true,
        'message' => "Copia de seguridad de $file creada correctamente"
    ];
}

// Procesar acciones
$action = $_GET['action'] ?? '';
$file = $_GET['file'] ?? '';
$result = null;

if ($action && $file) {
    switch ($action) {
        case 'backup':
            $result = backupFile($file);
            break;
        case 'restore':
            $result = restoreFile($file);
            break;
    }
}

// Lista de archivos críticos
$critical_files = [
    'register.php',
    'config/config.php',
    'config/logger.php',
    'db_connection.php',
    'process_step1.php'
];

// Iniciar la salida HTML
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recuperación del Sistema</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2196F3;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }
        h2 {
            color: #2196F3;
            margin-top: 30px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-item:hover {
            background-color: #f9f9f9;
        }
        .file-name {
            font-weight: bold;
        }
        .file-actions {
            display: flex;
            gap: 10px;
        }
        .action-button {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
        .action-button.backup {
            background-color: #4CAF50;
        }
        .action-button.restore {
            background-color: #FF9800;
        }
        .action-button:hover {
            opacity: 0.9;
        }
        .navigation {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Recuperación del Sistema</h1>
        
        <?php if ($result): ?>
            <?php showMessage($result['message'], $result['success'] ? 'success' : 'error'); ?>
        <?php endif; ?>
        
        <p>Esta herramienta permite crear copias de seguridad y restaurar archivos críticos del sistema.</p>
        
        <h2>Archivos Críticos</h2>
        <div class="file-list">
            <?php foreach ($critical_files as $critical_file): ?>
                <div class="file-item">
                    <div class="file-info">
                        <span class="file-name"><?php echo $critical_file; ?></span>
                        <span class="file-status">
                            <?php if (file_exists($critical_file)): ?>
                                <span style="color: #4CAF50;">✓ Existe</span>
                            <?php else: ?>
                                <span style="color: #F44336;">✗ No existe</span>
                            <?php endif; ?>
                            
                            <?php if (file_exists($critical_file . '.bak')): ?>
                                <span style="color: #4CAF50; margin-left: 10px;">✓ Tiene copia de seguridad</span>
                            <?php else: ?>
                                <span style="color: #FF9800; margin-left: 10px;">⚠ Sin copia de seguridad</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="file-actions">
                        <a href="?action=backup&file=<?php echo $critical_file; ?>" class="action-button backup">Crear copia de seguridad</a>
                        <?php if (file_exists($critical_file . '.bak')): ?>
                            <a href="?action=restore&file=<?php echo $critical_file; ?>" class="action-button restore">Restaurar</a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <h2>Acciones Rápidas</h2>
        <div class="quick-actions">
            <a href="diagnostico.php" class="action-button">Ejecutar diagnóstico</a>
            <a href="register.php" class="action-button">Ir a la página de registro</a>
        </div>
        
        <div class="navigation">
            <p><strong>Nota:</strong> Si estás experimentando problemas con el sistema, primero ejecuta el diagnóstico para identificar la causa y luego utiliza esta herramienta para restaurar los archivos afectados.</p>
        </div>
    </div>
</body>
</html>
