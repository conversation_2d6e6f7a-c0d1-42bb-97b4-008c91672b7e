<?php
// Habilitar el reporte de errores para depuración
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el sistema de logging
require_once '../config/logger.php';
require_once '../config/config.php';
require_once '../config/db.php';

// Función para mostrar mensajes en el navegador
function showMessage($message, $isError = false) {
    echo '<div style="padding: 10px; margin: 5px; border-radius: 5px; ' .
         'background-color: ' . ($isError ? '#ffdddd' : '#ddffdd') . '; ' .
         'color: ' . ($isError ? '#990000' : '#009900') . ';">' .
         $message . '</div>';
}

// Iniciar o reanudar sesión
session_start();

echo "<h1>Prueba de conexión a la base de datos</h1>";

// Mostrar información de la sesión
echo '<h2>Información de la sesión</h2>';
echo '<pre>';
echo 'Session ID: ' . session_id() . "\n";
echo 'Session Status: ' . session_status() . "\n";
echo 'Session Data: ';
print_r($_SESSION);
echo '</pre>';

try {
    // Registrar inicio de la prueba
    logInfo("Iniciando prueba de base de datos");

    // Obtener conexión a la base de datos
    $conn = getDirectConnection();

    if (!$conn) {
        showMessage("Error: No se pudo conectar a la base de datos", true);
    } else {
        showMessage("Conexión exitosa a la base de datos");

        // Mostrar información de la conexión
        echo '<h3>Información de la conexión</h3>';
        echo '<pre>';
        echo 'Host: ' . $db_host . "\n";
        echo 'Usuario: ' . $db_user . "\n";
        echo 'Base de datos: ' . $db_name . "\n";
        echo 'Charset: ' . $conn->character_set_name() . "\n";
        echo '</pre>';

        // Verificar si la tabla existe
        $tableCheckQuery = "SHOW TABLES LIKE 'tb_registros'";
        $tableCheckResult = $conn->query($tableCheckQuery);

        if ($tableCheckResult->num_rows > 0) {
            showMessage("La tabla 'tb_registros' existe en la base de datos.");

            // Verificar la estructura de la tabla tb_registros
            echo '<h3>Estructura de la tabla tb_registros</h3>';

            $table_info_query = "SHOW COLUMNS FROM tb_registros";
            $table_info_result = $conn->query($table_info_query);

            if (!$table_info_result) {
                showMessage("Error al obtener la estructura de la tabla: " . $conn->error, true);
            } else {
                echo '<table border="1" cellpadding="5" cellspacing="0">';
                echo '<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>';

                while ($column = $table_info_result->fetch_assoc()) {
                    echo '<tr>';
                    echo '<td>' . $column['Field'] . '</td>';
                    echo '<td>' . $column['Type'] . '</td>';
                    echo '<td>' . $column['Null'] . '</td>';
                    echo '<td>' . $column['Key'] . '</td>';
                    echo '<td>' . ($column['Default'] === null ? 'NULL' : $column['Default']) . '</td>';
                    echo '<td>' . $column['Extra'] . '</td>';
                    echo '</tr>';
                }

                echo '</table>';

                // Contar registros en la tabla
                $count_query = "SELECT COUNT(*) as total FROM tb_registros";
                $count_result = $conn->query($count_query);

                if ($count_result) {
                    $count_row = $count_result->fetch_assoc();
                    echo '<p>Total de registros en la tabla: ' . $count_row['total'] . '</p>';
                } else {
                    showMessage("Error al contar registros: " . $conn->error, true);
                }
            }
        } else {
            showMessage("La tabla 'tb_registros' no existe en la base de datos. Intentando crearla...", true);

            // Intentar crear la tabla
            $createTableSQL = "CREATE TABLE IF NOT EXISTS tb_registros (
                id INT NOT NULL AUTO_INCREMENT,
                nombres VARCHAR(100) NOT NULL COMMENT 'Almacena dos nombres',
                apellidos VARCHAR(100) NOT NULL COMMENT 'Almacena dos apellidos',
                rut VARCHAR(10) NOT NULL COMMENT 'Numérico con posible K al final',
                fechaNacimiento VARCHAR(10) NOT NULL COMMENT 'Formato dd/mm/aaaa',
                sexo ENUM('Masculino','Femenino','Otro') NOT NULL,
                telefono VARCHAR(10) NOT NULL COMMENT 'Solo números',
                region VARCHAR(50) NOT NULL,
                comuna VARCHAR(50) NOT NULL,
                direccion VARCHAR(150) NOT NULL COMMENT 'Letras y números',
                NombreUsuario VARCHAR(50) NOT NULL COMMENT 'Letras y números',
                mail VARCHAR(100) NOT NULL COMMENT 'Letras, símbolos y números',
                mailRespaldo VARCHAR(100) DEFAULT NULL COMMENT 'Letras, símbolos y números',
                contraseña VARCHAR(255) NOT NULL COMMENT 'Almacena contraseña encriptada',
                localFisico ENUM('Si','No') NOT NULL COMMENT 'Indica si tiene local físico',
                nombreNegocio VARCHAR(100) DEFAULT NULL COMMENT 'Nombre del negocio (letras y números)',
                telefonoNegocio VARCHAR(10) DEFAULT NULL COMMENT 'Teléfono del negocio (solo números)',
                whatsappNegocio VARCHAR(11) DEFAULT NULL COMMENT 'WhatsApp del negocio (se agregará 569 automáticamente)',
                tipoNegocio ENUM('Venta','Servicios','Arriendo') DEFAULT NULL COMMENT 'Tipo de negocio',
                descripcionNegocio TEXT COMMENT 'Descripción del negocio (hasta 200 palabras)',
                planSuscripcion ENUM('Gratuita','Normal','Premium') DEFAULT NULL COMMENT 'Plan de suscripción elegido en el paso 4',
                fecha_registro TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            if ($conn->query($createTableSQL)) {
                showMessage("Tabla 'tb_registros' creada correctamente.");
            } else {
                showMessage("Error al crear la tabla 'tb_registros': " . $conn->error, true);
            }
        }

        // Mostrar información de la base de datos
        echo "<h2>Información de la base de datos:</h2>";
        echo "<ul>";
        echo "<li>Servidor: " . $conn->host_info . "</li>";
        echo "<li>Versión del servidor: " . $conn->server_info . "</li>";
        echo "<li>Versión del protocolo: " . $conn->protocol_version . "</li>";
        echo "<li>Conjunto de caracteres: " . $conn->character_set_name() . "</li>";
        echo "</ul>";

        // Listar todas las tablas
        echo "<h2>Tablas en la base de datos:</h2>";
        $tablesQuery = "SHOW TABLES";
        $tablesResult = $conn->query($tablesQuery);

        if ($tablesResult->num_rows > 0) {
            echo "<ul>";
            while ($row = $tablesResult->fetch_row()) {
                echo "<li>" . $row[0] . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No hay tablas en la base de datos.</p>";
        }

        // Probar una inserción de prueba
        echo '<h3>Prueba de inserción</h3>';

        if (isset($_GET['test_insert']) && $_GET['test_insert'] === 'true') {
            // Crear datos de prueba
            $test_data = [
                'nombres' => 'Usuario Test',
                'apellidos' => 'Prueba ' . time(),
                'rut' => '12345678-9',
                'fechaNacimiento' => '01/01/1990',
                'sexo' => 'Masculino',
                'telefono' => '1234567890',
                'region' => 'Región Metropolitana',
                'comuna' => 'Santiago',
                'direccion' => 'Calle Prueba 123',
                'NombreUsuario' => 'test_user_' . time(),
                'mail' => 'test_' . time() . '@example.com',
                'mailRespaldo' => null,
                'contraseña' => password_hash('test123', PASSWORD_DEFAULT),
                'localFisico' => 'Si',
                'nombreNegocio' => 'Negocio Test',
                'telefonoNegocio' => '1234567890',
                'whatsappNegocio' => '1234567890',
                'tipoNegocio' => 'Venta',
                'descripcionNegocio' => 'Descripción de prueba',
                'planSuscripcion' => 'Gratuita'
            ];

            // Construir consulta SQL
            $sql = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion,
                                           NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                           nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                           planSuscripcion)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $conn->prepare($sql);

            if (!$stmt) {
                showMessage("Error al preparar la consulta: " . $conn->error, true);
            } else {
                $stmt->bind_param("ssssssssssssssssssss",
                    $test_data['nombres'], $test_data['apellidos'], $test_data['rut'],
                    $test_data['fechaNacimiento'], $test_data['sexo'], $test_data['telefono'],
                    $test_data['region'], $test_data['comuna'], $test_data['direccion'],
                    $test_data['NombreUsuario'], $test_data['mail'], $test_data['mailRespaldo'], $test_data['contraseña'], $test_data['localFisico'],
                    $test_data['nombreNegocio'], $test_data['telefonoNegocio'], $test_data['whatsappNegocio'],
                    $test_data['tipoNegocio'], $test_data['descripcionNegocio'],
                    $test_data['planSuscripcion']);

                if ($stmt->execute()) {
                    showMessage("Inserción de prueba exitosa. ID: " . $conn->insert_id);
                } else {
                    showMessage("Error al ejecutar la consulta: " . $stmt->error, true);

                    // Intentar inserción directa
                    $sql_direct = "INSERT INTO tb_registros (nombres, apellidos, rut, fechaNacimiento, sexo, telefono, region, comuna, direccion,
                                                         NombreUsuario, mail, mailRespaldo, contraseña, localFisico,
                                                         nombreNegocio, telefonoNegocio, whatsappNegocio, tipoNegocio, descripcionNegocio,
                                                         planSuscripcion)
                                  VALUES (
                                      '" . $conn->real_escape_string($test_data['nombres']) . "',
                                      '" . $conn->real_escape_string($test_data['apellidos']) . "',
                                      '" . $conn->real_escape_string($test_data['rut']) . "',
                                      '" . $conn->real_escape_string($test_data['fechaNacimiento']) . "',
                                      '" . $conn->real_escape_string($test_data['sexo']) . "',
                                      '" . $conn->real_escape_string($test_data['telefono']) . "',
                                      '" . $conn->real_escape_string($test_data['region']) . "',
                                      '" . $conn->real_escape_string($test_data['comuna']) . "',
                                      '" . $conn->real_escape_string($test_data['direccion']) . "',
                                      '" . $conn->real_escape_string($test_data['NombreUsuario']) . "',
                                      '" . $conn->real_escape_string($test_data['mail']) . "',
                                      " . ($test_data['mailRespaldo'] ? "'" . $conn->real_escape_string($test_data['mailRespaldo']) . "'" : "NULL") . ",
                                      '" . $conn->real_escape_string($test_data['contraseña']) . "',
                                      '" . $conn->real_escape_string($test_data['localFisico']) . "',
                                      '" . $conn->real_escape_string($test_data['nombreNegocio']) . "',
                                      '" . $conn->real_escape_string($test_data['telefonoNegocio']) . "',
                                      '" . $conn->real_escape_string($test_data['whatsappNegocio']) . "',
                                      '" . $conn->real_escape_string($test_data['tipoNegocio']) . "',
                                      '" . $conn->real_escape_string($test_data['descripcionNegocio']) . "',
                                      '" . $conn->real_escape_string($test_data['planSuscripcion']) . "'
                                  )";

                    if ($conn->query($sql_direct) === TRUE) {
                        showMessage("Inserción directa exitosa. ID: " . $conn->insert_id);
                    } else {
                        showMessage("Error en la inserción directa: " . $conn->error, true);
                    }
                }
            }
        } else {
            echo '<p><a href="?test_insert=true" style="padding: 10px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Realizar prueba de inserción</a></p>';
        }

        // Cerrar la conexión
        $conn->close();
    }
} catch (Exception $e) {
    showMessage("Error: " . $e->getMessage(), true);
}
?>
