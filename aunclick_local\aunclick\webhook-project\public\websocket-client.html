<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cliente WebSocket</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .status {
            padding: 10px;
            margin: 15px 0;
            border-radius: 4px;
        }
        
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        
        .message-list {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        
        .message {
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 4px;
        }
        
        .message.incoming {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        
        .message.outgoing {
            background-color: #e8f5e9;
            border-left: 3px solid #4caf50;
            text-align: right;
        }
        
        .message-time {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 3px;
        }
        
        .controls {
            display: flex;
            margin-bottom: 15px;
        }
        
        input[type="text"] {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        button {
            padding: 8px 16px;
            margin-left: 10px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        button:disabled {
            background-color: #7f8c8d;
            cursor: not-allowed;
        }
        
        .event-selector {
            margin-bottom: 15px;
        }
        
        .event-selector select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cliente WebSocket</h1>
        
        <div id="status" class="status disconnected">Desconectado</div>
        
        <div class="server-info">
            <label for="serverUrl">URL del servidor:</label>
            <div class="controls">
                <input type="text" id="serverUrl" value="ws://localhost:8080" placeholder="ws://localhost:8080">
                <button id="connectBtn">Conectar</button>
                <button id="disconnectBtn" disabled>Desconectar</button>
            </div>
        </div>
        
        <div class="message-list" id="messageList"></div>
        
        <div class="event-selector">
            <select id="eventType">
                <option value="">-- Seleccionar evento --</option>
                <option value="pago_completado">Pago Completado</option>
                <option value="usuario_registrado">Usuario Registrado</option>
                <option value="producto_actualizado">Producto Actualizado</option>
                <option value="pedido_creado">Pedido Creado</option>
                <option value="pedido_enviado">Pedido Enviado</option>
            </select>
            <button id="simulateEventBtn" disabled>Simular Evento</button>
        </div>
        
        <div class="controls">
            <input type="text" id="messageInput" placeholder="Escribe un mensaje..." disabled>
            <button id="sendBtn" disabled>Enviar</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Referencias a elementos del DOM
            const statusEl = document.getElementById('status');
            const messageListEl = document.getElementById('messageList');
            const messageInputEl = document.getElementById('messageInput');
            const serverUrlEl = document.getElementById('serverUrl');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendBtn = document.getElementById('sendBtn');
            const eventTypeEl = document.getElementById('eventType');
            const simulateEventBtn = document.getElementById('simulateEventBtn');
            
            // Variable para almacenar la conexión WebSocket
            let socket = null;
            
            // Función para actualizar el estado
            function updateStatus(state, message) {
                statusEl.className = 'status ' + state;
                statusEl.textContent = message;
            }
            
            // Función para agregar mensajes a la lista
            function addMessage(message, type = 'incoming') {
                const messageEl = document.createElement('div');
                messageEl.className = 'message ' + type;
                
                const timeEl = document.createElement('div');
                timeEl.className = 'message-time';
                timeEl.textContent = new Date().toLocaleTimeString();
                
                const contentEl = document.createElement('div');
                contentEl.className = 'message-content';
                
                // Si el mensaje es un objeto, formatearlo
                if (typeof message === 'object') {
                    contentEl.textContent = JSON.stringify(message, null, 2);
                } else {
                    contentEl.textContent = message;
                }
                
                messageEl.appendChild(timeEl);
                messageEl.appendChild(contentEl);
                messageListEl.appendChild(messageEl);
                
                // Hacer scroll hasta el mensaje más reciente
                messageListEl.scrollTop = messageListEl.scrollHeight;
            }
            
            // Función para conectar al servidor WebSocket
            function connect() {
                const url = serverUrlEl.value.trim();
                
                if (!url) {
                    alert('Por favor, ingresa una URL válida');
                    return;
                }
                
                try {
                    updateStatus('connecting', 'Conectando...');
                    
                    socket = new WebSocket(url);
                    
                    socket.onopen = function() {
                        updateStatus('connected', 'Conectado');
                        
                        // Actualizar estado de botones
                        connectBtn.disabled = true;
                        disconnectBtn.disabled = false;
                        sendBtn.disabled = false;
                        messageInputEl.disabled = false;
                        simulateEventBtn.disabled = false;
                        
                        addMessage('Conexión establecida con ' + url);
                    };
                    
                    socket.onmessage = function(event) {
                        try {
                            const data = JSON.parse(event.data);
                            addMessage(data);
                        } catch (e) {
                            addMessage(event.data);
                        }
                    };
                    
                    socket.onclose = function(event) {
                        if (event.wasClean) {
                            updateStatus('disconnected', `Desconectado: Código ${event.code} - ${event.reason}`);
                            addMessage(`Conexión cerrada. Código: ${event.code}, Razón: ${event.reason}`);
                        } else {
                            updateStatus('disconnected', 'Conexión interrumpida');
                            addMessage('Conexión interrumpida');
                        }
                        
                        // Actualizar estado de botones
                        resetConnectionState();
                    };
                    
                    socket.onerror = function(error) {
                        updateStatus('disconnected', 'Error en la conexión');
                        addMessage('Error: ' + error.message);
                        
                        // Actualizar estado de botones
                        resetConnectionState();
                    };
                    
                } catch (error) {
                    updateStatus('disconnected', 'Error al conectar');
                    addMessage('Error al conectar: ' + error.message);
                    resetConnectionState();
                }
            }
            
            // Función para desconectar
            function disconnect() {
                if (socket) {
                    socket.close();
                }
            }
            
            // Función para enviar un mensaje
            function sendMessage() {
                const message = messageInputEl.value.trim();
                
                if (!message) return;
                
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(message);
                    addMessage(message, 'outgoing');
                    messageInputEl.value = '';
                } else {
                    addMessage('No se puede enviar: No hay conexión', 'error');
                }
            }
            
            // Función para simular un evento
            function simulateEvent() {
                const eventType = eventTypeEl.value;
                
                if (!eventType) {
                    alert('Por favor, selecciona un tipo de evento');
                    return;
                }
                
                let eventData = {
                    tipo_evento: eventType,
                    timestamp: new Date().toISOString()
                };
                
                // Datos adicionales según el tipo de evento
                switch (eventType) {
                    case 'pago_completado':
                        eventData.id_transaccion = 'pay-' + Math.floor(Math.random() * 1000);
                        eventData.importe = 100.50;
                        eventData.moneda = 'USD';
                        break;
                        
                    case 'usuario_registrado':
                        eventData.id_usuario = 'usr-' + Math.floor(Math.random() * 1000);
                        eventData.email = '<EMAIL>';
                        break;
                        
                    case 'producto_actualizado':
                        eventData.id_producto = 'prod-' + Math.floor(Math.random() * 1000);
                        eventData.nombre = 'Producto Ejemplo';
                        eventData.precio = 49.99;
                        break;
                        
                    case 'pedido_creado':
                        eventData.id_pedido = 'ord-' + Math.floor(Math.random() * 1000);
                        eventData.total = 119.97;
                        break;
                        
                    case 'pedido_enviado':
                        eventData.id_pedido = 'ord-' + Math.floor(Math.random() * 1000);
                        eventData.tracking = 'TR' + Math.floor(Math.random() * 1000000);
                        break;
                }
                
                // Enviar el evento
                if (socket && socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify(eventData));
                    addMessage(eventData, 'outgoing');
                } else {
                    addMessage('No se puede enviar: No hay conexión', 'error');
                }
            }
            
            // Función para resetear el estado de la conexión
            function resetConnectionState() {
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
                messageInputEl.disabled = true;
                simulateEventBtn.disabled = true;
                socket = null;
            }
            
            // Event listeners
            connectBtn.addEventListener('click', connect);
            disconnectBtn.addEventListener('click', disconnect);
            sendBtn.addEventListener('click', sendMessage);
            simulateEventBtn.addEventListener('click', simulateEvent);
            
            messageInputEl.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // Estado inicial
            updateStatus('disconnected', 'Desconectado');
        });
    </script>
</body>
</html>
