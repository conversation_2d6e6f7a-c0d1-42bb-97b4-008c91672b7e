<!DOCTYPE html>
<html lang="es">
    <head>
        <meta charset="UTF-8">
        <!-- Modificar el viewport y agregar metas para apariencia de app -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
        <meta name="theme-color" content="#6a1b9a">
        <!-- Importación de fuentes -->
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
        <!-- Importación de iconos -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
        <!-- Importación de Chart.js para gráficos -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <title>Panel de Administración - Villarrica a un CLICKX</title>
        <link rel="stylesheet" href="../css/variables.css">
        <link rel="stylesheet" href="../css/layout.css">
        <link rel="stylesheet" href="../css/components.css">
        <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Barra lateral - Contiene el menú de navegación principal -->
        <aside class="sidebar" id="sidebar">
            <!-- Botón de toggle actualizado -->
            <button class="nav-link menu-toggle" id="aside-toggle" type="button" aria-label="Toggle menu">
                <i class="fas fa-bars" id="toggle-icon"></i>
            </button>
            <!-- Cabecera de la barra lateral - Logo y título -->
            <div class="sidebar-header">
                <div class="sidebar-logo">Villarricax a un CLICK</div>
                <div class="sidebar-subtitle">Panel de Administración</div>
            </div>
            
            <!-- Información del usuario -->
            <div class="sidebar-user">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-info">
                    <div class="user-name">Administrador</div>
                    <div class="user-role">Super Admin</div>
                </div>
            </div>
            
            <!-- Navegación principal -->
            <nav class="sidebar-nav">
                <!-- Sección General -->
                <div class="nav-section">
                    <div class="nav-section-title">General</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link active" id="dashboardLink">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <!-- Eliminamos el enlace "Mi Tienda" ya que se moverá a las pestañas superiores -->
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="statsLink">
                                <i class="fas fa-chart-bar"></i>
                                <span>Estadísticas</span>
                            </a>
                        </li>                        
                    </ul>
                </div>
                
                <!-- Sección Catálogo -->
                <div class="nav-section">
                    <div class="nav-section-title">Catálogo</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="productsNavLink">
                                <i class="fas fa-box"></i>
                                <span>Productos</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" id="categoriesLink">
                                <i class="fas fa-tags"></i>
                                <span>Categorías</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- Sección Contenido -->
                <div class="nav-section">
                    <div class="nav-section-title">Contenido</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-images"></i>
                                <span>Galería</span>
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- Sección Configuración -->
                <div class="nav-section">
                    <div class="nav-section-title">Configuración</div>
                    <ul class="nav-items">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-user-cog"></i>
                                <span>Perfil</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-cog"></i>
                                <span>Ajustes</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Cerrar Sesión</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </aside>
        
        <!-- Contenido principal - Área donde se muestra el contenido de la aplicación -->
        <main class="main-content">            
            <!-- Contenedor principal - Limita el ancho y añade márgenes -->
            <div class="container">
                <!-- Pestañas de navegación principal - Permiten cambiar entre secciones principales -->
                <div class="main-tabs">
                    <button class="tab-btn active" id="productsTab">
                        <i class="fas fa-box"></i>
                        Productos
                    </button>
                    <button class="tab-btn" id="editProductTab">
                        <i class="fas fa-edit"></i>
                        Agregar Producto
                    </button>
                    <button class="tab-btn" id="storeTab">
                        <i class="fas fa-store"></i>
                        Mi Tienda
                    </button>
                </div>
                
                <!-- Listado de productos - Muestra todos los productos en una tabla -->
                <section class="content-section" id="productsSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-box"></i>
                            Productos
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" type="button">
                                <i class="fas fa-filter"></i>
                                Filtrar
                            </button>
                            <button class="btn btn-primary" id="newProductBtn" type="button">
                                <i class="fas fa-plus"></i>
                                Nuevo
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-body">
                        <!-- Contenedor para productos en vista móvil y desktop -->
                        <div class="productos-container">
                            <!-- Contenedor específico para tarjetas en modo móvil -->
                            <div id="product-cards-container" class="product-cards-container"></div>
                            
                            <!-- Tabla de productos para desktop -->
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Imagen</th>
                                        <th>Producto</th>
                                        <th>Precio</th>
                                        <th>Categ.</th>
                                        <th>Sub-categ.</th>
                                        <th>Condición</th>
                                        <th>Estado</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                
                                <tbody>
                                   <!-- Fila de producto: Smartphone -->
                                <tr>
                                    <td class="product-image-cell">
                                        <img src="https://via.placeholder.com/300x200" alt="Smartphone" class="product-image-preview">
                                    </td>
                                    <td class="product-name-cell">
                                        <div class="product-name">Smartphone X12 Pro</div>
                                        <div class="product-category">Smartphones</div>
                                    </td>
                                    <td>
                                        <div class="product-price">$399.990</div>
                                        <div class="product-original-price">$469.990</div>
                                        <div class="product-discount">-15%</div>
                                    </td>
                                    <td>
                                        <!-- Categoría fija -->
                                        <div class="category-text">Electrónica</div>
                                    </td>
                                    <td>
                                        <!-- Subcategoría fija -->
                                        <div class="category-text">Smartphones</div>
                                    </td>
                                    <td>
                                        <!-- Condición fija -->
                                        <span class="condition-badge condition-destacado">Destacados</span>
                                    </td>
                                    <td>
                                        <!-- Estado fijo -->
                                        <span class="status-badge status-draft">Activo</span>
                                    </td>
                                    
                                    <td>
                                        <!-- Botones de acción para cada producto -->
                                        <div class="table-actions">
                                            <button class="action-btn view-btn" title="Ver">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="Eliminar">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Fila de producto: Audífonos -->
                                <tr>
                                    <td class="product-image-cell">
                                        <img src="https://via.placeholder.com/300x200" alt="Audífonos" class="product-image-preview">
                                    </td>
                                    <td class="product-name-cell">
                                        <div class="product-name">Audífonos SoundPlus</div>
                                        <div class="product-category">Audio</div>
                                    </td>
                                    <td>
                                        <div class="product-price">$89.990</div>
                                    </td>
                                    <td>
                                        <!-- Categoría fija -->
                                        <div class="category-text">Audio</div>
                                    </td>
                                    <td>
                                        <!-- Subcategoría fija -->
                                        <div class="category-text">Auriculares</div>
                                    </td>
                                    <td>
                                        <!-- Condición fija -->
                                        <span class="condition-badge condition-oferta">Ofertas</span>
                                    </td>
                                    <td>
                                        <!-- Estado fijo -->
                                        <span class="status-badge status-draft">Borrador</span>
                                    </td>
                                    
                                    <td>
                                        <div class="table-actions">
                                            <button class="action-btn view-btn" title="Ver">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="action-btn edit-btn" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="action-btn delete-btn" title="Eliminar">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                    
                                    <!-- Más filas de productos... -->
                                    <!-- Se omiten algunas filas para brevedad -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Paginación -->
                        <div class="pagination">
                            <button class="pagination-item">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-item active">1</button>
                            <button class="pagination-item">2</button>
                            <button class="pagination-item">3</button>
                            <button class="pagination-item">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </section>
                
                <!-- Formulario de producto - Permite crear o editar productos -->
                <section class="content-section" id="editProductSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-edit"></i>
                            Agregar Producto
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary" id="cancelEditBtn">
                                <i class="fas fa-times"></i>
                                Cancelar
                            </button>
                            <button class="btn btn-success">
                                <i class="fas fa-save"></i>
                                Guardar Cambios
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-body">
                        <div class="form-grid">
                            <!-- Columna principal del formulario -->
                            <div class="form-main">
                                <!-- Información básica del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Información Básica</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productName">Nombre del Producto *</label>
                                            <input type="text" class="form-control" id="productName" value="Smartphone Premium X12 Pro">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productDescription">Descripción *</label>
                                            <textarea class="form-control" id="productDescription">El Smartphone Premium X12 Pro cuenta con una pantalla AMOLED de 6.7", procesador octa-core, 8GB de RAM y 256GB de almacenamiento. Cámara principal de 108MP y batería de 5000mAh con carga rápida.</textarea>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productShortDescription">Descripción Corta</label>
                                            <textarea class="form-control" id="productShortDescription">Smartphone de alta gama con cámara profesional y batería de larga duración.</textarea>
                                            <div class="form-hint">Esta descripción se mostrará en las vistas de lista y tarjetas.</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Imágenes del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Imágenes</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <!-- Área para arrastrar y soltar imágenes -->
                                        <div class="dropzone" id="productImageDropzone">
                                            <div class="dropzone-icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <div class="dropzone-title">Arrastra y suelta imágenes aquí</div>
                                            <div class="dropzone-hint">o haz clic para seleccionar archivos (máximo 5 imágenes, formato JPG, PNG o WEBP)</div>
                                        </div>
                                        
                                        <!-- Galería de imágenes del producto -->
                                        <div class="image-gallery">
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 1">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 2">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="image-item">
                                                <img src="https://via.placeholder.com/300x200" alt="Producto 3">
                                                <div class="image-actions">
                                                    <button class="image-action-btn" title="Establecer como principal">
                                                        <i class="fas fa-star"></i>
                                                    </button>
                                                    <button class="image-action-btn" title="Eliminar">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Precios y stock del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Precios y Stock</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productPrice">Precio *</label>
                                            <input type="number" class="form-control" id="productPrice" value="399990">
                                            <div class="form-hint">Ingresa el precio sin puntos ni símbolos.</div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productOriginalPrice">Precio Original</label>
                                            <input type="number" class="form-control" id="productOriginalPrice" value="469990">
                                            <div class="form-hint">Si el producto está en oferta, ingresa el precio original.</div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productStock">Stock *</label>
                                            <input type="number" class="form-control" id="productStock" value="25">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productSKU">SKU</label>
                                            <input type="text" class="form-control" id="productSKU" value="SP-X12-PRO-BLK">
                                            <div class="form-hint">Código único para identificar el producto.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Columna lateral del formulario -->
                            <div class="form-sidebar">
                                <!-- Estado y condición del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Estado y Condición</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <!-- Estado del producto -->
                                        <div class="form-group">
                                            <label class="form-label">Estado</label>
                                            <div class="radio-group">
                                                <input type="radio" id="statusActive" name="productStatus" checked>
                                                <label class="radio-label" for="statusActive">Publicado</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="statusDraft" name="productStatus">
                                                <label class="radio-label" for="statusDraft">Borrador</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="statusOutOfStock" name="productStatus">
                                                <label class="radio-label" for="statusOutOfStock">Agotado</label>
                                            </div>
                                        </div>
                                        
                                        <!-- Condición del producto (reemplaza a Visibilidad) -->
                                        <div class="form-group">
                                            <label class="form-label">Condición</label>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionDestacado" name="productCondition" checked>
                                                <label class="radio-label" for="conditionDestacado">Productos Destacados</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionOferta" name="productCondition">
                                                <label class="radio-label" for="conditionOferta">Ofertas Especiales</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionLiquidacion" name="productCondition">
                                                <label class="radio-label" for="conditionLiquidacion">Liquidación</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionNuevo" name="productCondition">
                                                <label class="radio-label" for="conditionNuevo">Recién Llegados</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionExclusivo" name="productCondition">
                                                <label class="radio-label" for="conditionExclusivo">Colección Exclusiva</label>
                                            </div>
                                            <div class="radio-group">
                                                <input type="radio" id="conditionNinguno" name="productCondition">
                                                <label class="radio-label" for="conditionNinguno">Ninguna</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Categorización del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Categorización</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productCategory">Categoría *</label>
                                            <div class="select-container">
                                                <select class="form-control" id="productCategory">
                                                    <option value="">Seleccionar categoría</option>
                                                    <option value="1" selected>Electrónica</option>
                                                    <option value="2">Smartphones</option>
                                                    <option value="3">Audio</option>
                                                    <option value="4">Fotografía</option>
                                                    <option value="5">Computación</option>
                                                </select>
                                                <div class="select-arrow">
                                                    <i class="fas fa-chevron-down"></i>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productSubcategory">Subcategoría</label>
                                            <div class="select-container">
                                                <select class="form-control" id="productSubcategory">
                                                    <option value="">Seleccionar subcategoría</option>
                                                    <option value="1" selected>Smartphones</option>
                                                    <option value="2">Tablets</option>
                                                    <option value="3">Accesorios</option>
                                                </select>
                                                <div class="select-arrow">
                                                    <i class="fas fa-chevron-down"></i>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productTags">Etiquetas</label>
                                            <input type="text" class="form-control" id="productTags" value="smartphone, premium, android, cámara, batería">
                                            <div class="form-hint">Separa las etiquetas con comas.</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Atributos del producto -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Atributos</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="productBrand">Marca</label>
                                            <input type="text" class="form-control" id="productBrand" value="TechPro">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productColor">Color</label>
                                            <input type="text" class="form-control" id="productColor" value="Negro">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productWeight">Peso (g)</label>
                                            <input type="number" class="form-control" id="productWeight" value="189">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="productDimensions">Dimensiones (cm)</label>
                                            <input type="text" class="form-control" id="productDimensions" value="16.2 x 7.5 x 0.8">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Categorías - Gestión de categorías de productos -->
                <section class="content-section" id="categoriesSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-tags"></i>
                            Categorías
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Nueva Categoría
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-body">
                        <!-- Cuadrícula de tarjetas de categorías -->
                        <div class="categories-grid">
                            <!-- Categoría: Smartphones -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Smartphones</div>
                                    <div class="category-count">32 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categoría: Laptops -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-laptop"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Laptops</div>
                                    <div class="category-count">18 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categoría: Audio -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-headphones"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Audio</div>
                                    <div class="category-count">24 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categoría: Fotografía -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-camera"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Fotografía</div>
                                    <div class="category-count">15 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categoría: TV & Video -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-tv"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">TV & Video</div>
                                    <div class="category-count">12 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Categoría: Gaming -->
                            <div class="category-card">
                                <div class="category-header">
                                    <i class="fas fa-gamepad"></i>
                                </div>
                                <div class="category-body">
                                    <div class="category-name">Gaming</div>
                                    <div class="category-count">23 productos</div>
                                    <div class="category-actions">
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">
                                            <i class="fas fa-trash"></i>
                                            Eliminar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Información de la tienda - Configuración de la tienda -->
                <section class="content-section" id="storeInfoSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-store"></i>
                            Información de la Tienda
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-success">
                                <i class="fas fa-save"></i>
                                Guardar Cambios
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-body">
                        <div class="form-grid">
                            <!-- Columna principal del formulario de tienda -->
                            <div class="form-main">
                                <!-- Información básica de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Información Básica</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeName">Nombre de la Tienda *</label>
                                            <input type="text" class="form-control" id="storeName" value="Electrónica Digital">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeDescription">Descripción *</label>
                                            <textarea class="form-control" id="storeDescription">Somos especialistas en productos electrónicos de alta calidad. Ofrecemos una amplia gama de dispositivos, accesorios y servicios técnicos para satisfacer todas tus necesidades tecnológicas.</textarea>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">Logo de la Tienda</label>
                                            <div class="dropzone" id="storeLogoDropzone">
                                                <div class="dropzone-icon">
                                                    <i class="fas fa-cloud-upload-alt"></i>
                                                </div>
                                                <div class="dropzone-title">Arrastra y suelta el logo aquí</div>
                                                <div class="dropzone-hint">o haz clic para seleccionar archivo (formato JPG, PNG o SVG)</div>
                                            </div>
                                            
                                            <div class="image-gallery" style="grid-template-columns: 1fr;">
                                                <div class="image-item">
                                                    <img src="https://via.placeholder.com/150" alt="Logo de la tienda">
                                                    <div class="image-actions">
                                                        <button class="image-action-btn" title="Eliminar">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Información de contacto de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Información de Contacto</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeAddress">Dirección *</label>
                                            <input type="text" class="form-control" id="storeAddress" value="Av. Pedro de Valdivia 123">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeCity">Ciudad *</label>
                                            <input type="text" class="form-control" id="storeCity" value="Villarrica">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeRegion">Región *</label>
                                            <input type="text" class="form-control" id="storeRegion" value="Araucanía">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storePhone">Teléfono *</label>
                                            <input type="text" class="form-control" id="storePhone" value="+56 9 1234 5678">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeEmail">Email *</label>
                                            <input type="email" class="form-control" id="storeEmail" value="<EMAIL>">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeWhatsapp">WhatsApp</label>
                                            <input type="text" class="form-control" id="storeWhatsapp" value="+56 9 1234 5678">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Horarios de atención de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Horarios de Atención</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeHours">Horario de Atención *</label>
                                            <input type="text" class="form-control" id="storeHours" value="Lun-Sáb: 9:00 - 19:00">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">Días de Atención</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayMonday" checked>
                                                <label class="checkbox-label" for="dayMonday">Lunes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayTuesday" checked>
                                                <label class="checkbox-label" for="dayTuesday">Martes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayWednesday" checked>
                                                <label class="checkbox-label" for="dayWednesday">Miércoles</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayThursday" checked>
                                                <label class="checkbox-label" for="dayThursday">Jueves</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="dayFriday" checked>
                                                <label class="checkbox-label" for="dayFriday">Viernes</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="daySaturday" checked>
                                                <label class="checkbox-label" for="daySaturday">Sábado</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="daySunday">
                                                <label class="checkbox-label" for="daySunday">Domingo</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Columna lateral del formulario de tienda -->
                            <div class="form-sidebar">
                                <!-- Redes sociales de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Redes Sociales</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeFacebook">Facebook</label>
                                            <input type="text" class="form-control" id="storeFacebook" value="https://facebook.com/electronicadigital">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeInstagram">Instagram</label>
                                            <input type="text" class="form-control" id="storeInstagram" value="https://instagram.com/electronicadigital">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeTwitter">Twitter</label>
                                            <input type="text" class="form-control" id="storeTwitter" value="">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeYoutube">YouTube</label>
                                            <input type="text" class="form-control" id="storeYoutube" value="">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Ubicación en el mapa de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Ubicación en el Mapa</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label" for="storeLatitude">Latitud</label>
                                            <input type="text" class="form-control" id="storeLatitude" value="-39.2746">
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label" for="storeLongitude">Longitud</label>
                                            <input type="text" class="form-control" id="storeLongitude" value="-72.2254">
                                        </div>
                                        
                                        <div class="form-group">
                                            <div style="background-color: #e9e9e9; height: 200px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-top: 10px;">
                                                <span style="color: #666;">Vista previa del mapa</span>
                                            </div>
                                            <div class="form-hint">Haz clic en el mapa para seleccionar la ubicación exacta de tu tienda.</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Características de la tienda -->
                                <div class="form-section">
                                    <div class="form-section-header">
                                        <h3 class="form-section-title">Características</h3>
                                    </div>
                                    <div class="form-section-body">
                                        <div class="form-group">
                                            <label class="form-label">Servicios Ofrecidos</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceDelivery" checked>
                                                <label class="checkbox-label" for="serviceDelivery">Envío a domicilio</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="servicePickup" checked>
                                                <label class="checkbox-label" for="servicePickup">Retiro en tienda</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceInstallation" checked>
                                                <label class="checkbox-label" for="serviceInstallation">Servicio de instalación</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceRepair1" checked>
                                                <label class="checkbox-label" for="serviceRepair1">Servicio técnico</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceRepair2" checked>
                                                <label class="checkbox-label" for="serviceRepair2">Servicio técnico</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="serviceWarranty" checked>
                                                <label class="checkbox-label" for="serviceWarranty">Garantía extendida</label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label class="form-label">Métodos de Pago</label>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentCash" checked>
                                                <label class="checkbox-label" for="paymentCash">Efectivo</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentCard" checked>
                                                <label class="checkbox-label" for="paymentCard">Tarjeta de crédito/débito</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentTransfer" checked>
                                                <label class="checkbox-label" for="paymentTransfer">Transferencia bancaria</label>
                                            </div>
                                            <div class="checkbox-group">
                                                <input type="checkbox" id="paymentOnline">
                                                <label class="checkbox-label" for="paymentOnline">Pago online</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
    
                <!-- Sección de estadísticas - Muestra métricas y gráficos -->
                <section class="content-section" id="statsSection">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-chart-bar"></i>
                            Estadísticas Detalladas
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Exportar
                            </button>
                            <button class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-body">
                        <!-- Tarjetas de estadísticas - Resumen de métricas clave -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon products">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">124</div>
                                    <div class="stat-label">Productos</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon users">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">287</div>
                                    <div class="stat-label">Visitantes únicos</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon views">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">1,254</div>
                                    <div class="stat-label">Vistas de productos</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon" style="background-color: rgba(255, 87, 34, 0.1); color: #FF5722;">
                                    <i class="fas fa-share-alt"></i>
                                </div>
                                <div class="stat-info">
                                    <div class="stat-value">48</div>
                                    <div class="stat-label">Compartidos</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Gráficos de estadísticas -->
                        <div class="charts-container" style="margin-top: 30px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <!-- Gráfico de visitas mensuales -->
                                        <!-- Gráfico de visitas mensuales -->
                            <div class="chart-card">
                                <h3>Comportamiento Visitas Mensuales</h3>
                                <div class="chart-container">
                                    <canvas id="visitsMonthlyChart"></canvas>
                                </div>
                            </div>

                            
                            <!-- Gráfico de dispositivos -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Dispositivos de Acceso</h3>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="devicesChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- Gráfico de productos más vistos -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Productos Más Vistos</h3>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="topProductsChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- Gráfico de interés por categoría -->
                            <div class="chart-card" style="background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                                <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Interés por Categoría</h3>
                                <div class="chart-container" style="position: relative; height: 300px;">
                                    <canvas id="categoriesChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tabla de resumen de actividad -->
                        <div class="summary-table" style="margin-top: 30px; background-color: white; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 20px;">
                            <h3 style="margin-bottom: 15px; font-size: 16px; font-weight: 600;">Resumen de Actividad Mensual</h3>
                            
                            <!-- Contenedor con estilo inline adicional -->
                            <div class="admin-table-container" style="max-width: 100%;">
                                <!-- Tabla con width: max-content para que termine exactamente en la última columna -->
                                <table class="admin-table" style="width: max-content; min-width: 100%;">
                                    <thead>
                                        <tr>
                                            <th>Mes</th>
                                            <th>Visitas</th>
                                            <th>Visitantes únicos</th>
                                            <th>Tiempo promedio</th>
                                            <th>Tasa de rebote</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>Enero</td>
                                        <td>1,245</td>
                                        <td>845</td>
                                        <td>2m 35s</td>
                                        <td>42.5%</td>
                                    </tr>
                                    <tr>
                                        <td>Febrero</td>
                                        <td>1,350</td>
                                        <td>920</td>
                                        <td>2m 48s</td>
                                        <td>40.2%</td>
                                    </tr>
                                    <tr>
                                        <td>Marzo</td>
                                        <td>1,520</td>
                                        <td>1,050</td>
                                        <td>3m 12s</td>
                                        <td>38.7%</td>
                                    </tr>
                                    <tr>
                                        <td>Abril</td>
                                        <td>1,254</td>
                                        <td>890</td>
                                        <td>2m 55s</td>
                                        <td>41.3%</td>
                                    </tr>
                                    <tr>
                                        <td>Mayo</td>
                                        <td>1,380</td>
                                        <td>965</td>
                                        <td>3m 05s</td>
                                        <td>39.8%</td>
                                    </tr>
                                    <tr>
                                        <td>Junio</td>
                                        <td>1,420</td>
                                        <td>980</td>
                                        <td>3m 10s</td>
                                        <td>38.5%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Script de diagnóstico añadido -->
    <script>
        console.log('Diagnosticando interactividad...');
        document.addEventListener('click', function(e) {
            console.log('Clic en:', e.target.tagName, e.target.className || 'sin clase', e.target.id || 'sin ID');
        }, true);
    </script>
    
    <script src="../js/tienda_adm.js"></script>
</body>
</html>
