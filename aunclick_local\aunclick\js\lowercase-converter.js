// Script para convertir nombres y apellidos a minúsculas cuando se hace clic fuera del campo
document.addEventListener('DOMContentLoaded', function() {
    // Obtener referencias a los campos de nombres y apellidos
    const nombresInput = document.getElementById('nombres');
    const apellidosInput = document.getElementById('apellidos');

    // Función para convertir a minúsculas cuando se hace clic fuera del campo
    function convertToLowercase(input) {
        if (input) {
            input.addEventListener('blur', function() {
                // Solo convertir si hay contenido
                if (this.value.trim() !== '') {
                    this.value = this.value.toLowerCase();
                }
            });
        }
    }

    // Aplicar la función a ambos campos
    convertToLowercase(nombresInput);
    convertToLowercase(apellidosInput);
});
