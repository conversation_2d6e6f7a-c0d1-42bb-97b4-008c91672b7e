<?php
/**
 * Webhook Endpoint
 * 
 * Este archivo procesa las solicitudes webhook entrantes
 */

// Incluir archivos necesarios
require_once '../src/WebhookHandler.php';
require_once '../src/Logger.php';

// Inicializar el manejador de webhooks
$webhookHandler = new WebhookHandler();

// Obtener el cuerpo de la petición
$requestBody = file_get_contents('php://input');
$data = json_decode($requestBody, true);

// Verificar que se recibieron datos válidos
if (empty($data)) {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['error' => 'Datos inválidos']);
    exit;
}

// Verificar firma (si corresponde)
$signature = $_SERVER['HTTP_X_WEBHOOK_SIGNATURE'] ?? '';
$secret = 'tu_clave_secreta'; // Debería estar en un archivo de configuración

if (!$webhookHandler->verificarFirma($requestBody, $signature, $secret)) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['error' => 'Firma inválida']);
    exit;
}

// Obtener el tipo de evento
$tipoEvento = $data['tipo_evento'] ?? '';

// Registrar evento recibido
$logger = new Logger();
$logger->registrarEvento('webhook_recibido', [
    'tipo' => $tipoEvento,
    'datos' => $data,
    'ip' => $_SERVER['REMOTE_ADDR']
]);

// Procesar el evento
try {
    $resultado = $webhookHandler->procesarEvento($tipoEvento, $data);
    
    // Responder con éxito
    header('HTTP/1.1 200 OK');
    echo json_encode(['status' => 'success', 'result' => $resultado]);
} catch (Exception $e) {
    // Registrar error
    $logger->registrarEvento('error', [
        'mensaje' => $e->getMessage(),
        'tipo_evento' => $tipoEvento
    ]);
    
    // Responder con error
    header('HTTP/1.1 500 Internal Server Error');
    echo json_encode(['error' => $e->getMessage()]);
}
