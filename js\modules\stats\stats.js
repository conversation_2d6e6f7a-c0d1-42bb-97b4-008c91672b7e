// Importar componentes necesarios
import { initializeCharts } from '../../components/charts.js';

// Inicializar módulo de estadísticas
function initStatsModule() {
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar listeners y cargar datos iniciales
        setupStatsView();
        loadStatsData();
    });
}

// Configurar vista de estadísticas
function setupStatsView() {
    // Configurar selectores de rango de fechas y otros controles
    const dateRangeSelect = document.getElementById('statsDateRange');
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            loadStatsData(this.value);
        });
    }
    
    // Configurar botones de cambio de vista (gráficos/datos)
    const viewToggleBtns = document.querySelectorAll('.stats-view-toggle');
    viewToggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Quitar clase activa de todos los botones
            viewToggleBtns.forEach(b => b.classList.remove('active'));
            // Añadir clase activa al botón actual
            this.classList.add('active');
            
            // Cambiar vista según el valor del botón
            const viewType = this.getAttribute('data-view');
            toggleStatsView(viewType);
        });
    });
}

// Cambiar entre vista de gráficos y datos
function toggleStatsView(viewType) {
    const chartsContainer = document.getElementById('statsChartsContainer');
    const tableContainer = document.getElementById('statsTableContainer');
    
    if (viewType === 'charts') {
        chartsContainer.style.display = 'block';
        tableContainer.style.display = 'none';
        // Asegurarse que los gráficos se rendericen correctamente
        setTimeout(() => {
            initializeCharts();
        }, 100);
    } else {
        chartsContainer.style.display = 'none';
        tableContainer.style.display = 'block';
    }
}

// Cargar datos de estadísticas
async function loadStatsData(dateRange = 'month') {
    try {
        // Mostrar indicadores de carga
        showStatsLoading();
        
        // Realizar petición a la API
        const response = await fetch('../public/API/stats/get_stats.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                dateRange: dateRange 
            }),
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error('Error al cargar estadísticas');
        }
        
        const data = await response.json();
        
        if (data.success) {
            // Actualizar los componentes con los datos
            updateStatsCounters(data.stats);
            updateStatsCharts(data.stats);
            updateStatsTable(data.stats);
        } else {
            throw new Error(data.message || 'Error al cargar estadísticas');
        }
    } catch (error) {
        console.error('Error al cargar estadísticas:', error);
        showStatsError(error.message);
    } finally {
        hideStatsLoading();
    }
}

// Mostrar indicadores de carga
function showStatsLoading() {
    const loadingElements = document.querySelectorAll('.stats-loading');
    loadingElements.forEach(el => {
        el.style.display = 'flex';
    });
    
    // Ocultar posibles mensajes de error
    const errorElements = document.querySelectorAll('.stats-error');
    errorElements.forEach(el => {
        el.style.display = 'none';
    });
}

// Ocultar indicadores de carga
function hideStatsLoading() {
    const loadingElements = document.querySelectorAll('.stats-loading');
    loadingElements.forEach(el => {
        el.style.display = 'none';
    });
}

// Mostrar errores de estadísticas
function showStatsError(message) {
    const errorElements = document.querySelectorAll('.stats-error');
    errorElements.forEach(el => {
        el.textContent = `Error: ${message}`;
        el.style.display = 'block';
    });
}

// Actualizar contadores de estadísticas
function updateStatsCounters(statsData) {
    const counters = {
        'totalVisits': statsData.visits?.total || 0,
        'totalSales': statsData.sales?.total || 0,
        'totalRevenue': formatCurrency(statsData.revenue?.total || 0),
        'averageOrder': formatCurrency(statsData.sales?.average || 0)
    };
    
    // Actualizar cada contador
    for (const [id, value] of Object.entries(counters)) {
        const counterElement = document.getElementById(id);
        if (counterElement) {
            counterElement.textContent = value;
        }
    }
}

// Actualizar gráficos de estadísticas
function updateStatsCharts(statsData) {
    // Aquí deberíamos actualizar los datos de los gráficos
    // Pero como los gráficos son inicializados con datos de ejemplo,
    // podemos dejar esto para una implementación real posterior
    
    // Forzar una reinicialización de los gráficos con nuevos datos
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }
}

// Actualizar tabla de estadísticas
function updateStatsTable(statsData) {
    const salesTableBody = document.querySelector('#statsTableContainer tbody');
    if (!salesTableBody) return;
    
    // Verificar si hay datos de ventas
    if (!statsData.salesData || statsData.salesData.length === 0) {
        salesTableBody.innerHTML = `
            <tr>
                <td colspan="5" class="no-data">No hay datos de ventas disponibles</td>
            </tr>
        `;
        return;
    }
    
    // Generar filas de la tabla
    salesTableBody.innerHTML = statsData.salesData.map(sale => `
        <tr>
            <td>${sale.date}</td>
            <td>${sale.order_id}</td>
            <td>${sale.customer_name}</td>
            <td>${sale.items}</td>
            <td>${formatCurrency(sale.total)}</td>
        </tr>
    `).join('');
}

// Función auxiliar para formatear moneda
function formatCurrency(value) {
    return `${Number(value).toLocaleString('es-CL')}`;
}

// Exportar funciones
export {
    initStatsModule,
    loadStatsData
};