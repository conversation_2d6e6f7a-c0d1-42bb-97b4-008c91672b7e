<?php
ini_set('display_errors', 1);
error_reporting(E_ALL & ~E_WARNING); // Suprimir advertencias pero mantener otros errores

// Verificación del entorno y sesiones al inicio
error_log("Admin.php - Verificación de entorno");
error_log("Admin.php - Session save path: " . session_save_path());
error_log("Admin.php - Session save path permisos: " . substr(sprintf('%o', fileperms(session_save_path())), -4));
error_log("Admin.php - Todas las cookies HTTP: " . $_SERVER['HTTP_COOKIE']);

// Depurar cookies individuales
foreach ($_COOKIE as $name => $value) {
    error_log("Admin.php - Cookie individual: $name = $value");
}

// Asegurar que no hay output previo
while (ob_get_level()) ob_end_clean();

// Configuración manual de sesión
session_name('VILLARRICA_SESSION');
session_set_cookie_params([
    'lifetime' => 86400,
    'path' => '/projects/villarrica_click',
    'domain' => '',
    'secure' => false,
    'httponly' => true,
    'samesite' => 'Lax'
]);

// Eliminar cookies duplicadas si existen
if (isset($_COOKIE['VILLARRICA_SESSION'])) {
    $currentCookie = $_COOKIE['VILLARRICA_SESSION'];
    error_log("Admin.php - Cookie actual antes de iniciar sesión: " . $currentCookie);
}

// Iniciar o reanudar sesión
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionStartResult = session_start();
    error_log("Admin.php - Session start resultado: " . ($sessionStartResult ? "éxito" : "fallo"));
}

// Log de estado actual
error_log("Admin.php - Session ID: " . session_id());
error_log("Admin.php - SESSION data: " . print_r($_SESSION, true));
error_log("Admin.php - COOKIE data después de session_start: " . print_r($_COOKIE, true));

// Verificar autenticación
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    error_log("Admin.php - No hay sesión activa");
    session_write_close();
    header('Location: /projects/villarrica_click/public/login.php?error=unauthorized&reason=no_session');
    exit();
}

// Verificar roles permitidos (admin, premium, pro)
$allowed_roles = ['admin', 'premium', 'pro'];
if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], $allowed_roles)) {
    error_log("Admin.php - Usuario no tiene permisos suficientes - Role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'no definido'));
    session_write_close();
    header('Location: /projects/villarrica_click/public/profile.php?error=unauthorized&reason=insufficient_permissions');
    exit();
}

// Verificar tiempo de inactividad
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 86400)) {
    error_log("Admin.php - Sesión expirada por inactividad");
    session_unset();
    session_destroy();
    session_write_close();
    header('Location: /projects/villarrica_click/public/login.php?error=session_expired');
    exit();
}

// Actualizar tiempo de actividad
$_SESSION['last_activity'] = time();

// Verificar archivo de sesión
$sessionFile = session_save_path() . '/sess_' . session_id();
error_log("Admin.php - Verificando archivo de sesión: " . $sessionFile);
error_log("Admin.php - ¿Existe archivo?: " . (file_exists($sessionFile) ? "SÍ" : "NO"));
if (file_exists($sessionFile)) {
    error_log("Admin.php - Permisos archivo: " . substr(sprintf('%o', fileperms($sessionFile)), -4));
    error_log("Admin.php - Tamaño archivo: " . filesize($sessionFile));
}

// Forzar escritura de sesión y reabrir
session_write_close();
session_start();

error_log("Admin.php - Autenticación exitosa para usuario: " . $_SESSION['username']);

// Conectar a la base de datos
require_once '../config/config.php';
include 'header.php'; // Cambiar a header_simple.php

try {
    // Obtener información del negocio
    $sql_negocio = "SELECT * FROM tb_negocios LIMIT 1";
    $result_negocio = $conn->query($sql_negocio);
    $negocio = $result_negocio->fetch_assoc();

    // Obtener lista de productos
    $sql_productos = "SELECT * FROM tb_productos ORDER BY id DESC";
    $result_productos = $conn->query($sql_productos);

    $productos = [];
    while ($row = $result_productos->fetch_assoc()) {
        $productos[] = $row;
    }
} catch (Exception $e) {
    error_log("Admin.php - Error en consulta DB: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel Administrativo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>    
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <title>MercadoLibre Header Clone</title>
    
    <style>
        :root {
            --azure-blue: #0078D4;
            --azure-light-blue: #50E6FF;
            --azure-dark: #106EBE;
            --azure-gray: #323130;
            --azure-light-gray: #F3F2F1;
            --azure-accent: #2BC4C4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #FAF9F8;
            margin: 0;
            padding: 0;
            color: var(--azure-gray);
        }

        .dashboard {
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
            margin-top: 100px; /* Ajustar según la altura del header */
        }

        .card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            color: var(--azure-gray);
            font-weight: 600;
        }

        .chart-card {
            grid-column: span 2;
        }

        .chart-container {
            position: relative;
            margin: auto;
            height: 300px;
            width: 100%;
        }

        .kpi-card {
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .kpi-content {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }

        .kpi-info {
            flex-grow: 1;
        }

        .indicator {
            font-size: 2rem;
            font-weight: 600;
            color: var(--azure-blue);
            margin: 0.5rem 0;
        }

        .kpi-trend {
            font-size: 0.9rem;
            color: #107C10;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .kpi-trend.negative {
            color: #A4262C;
        }

        .kpi-icon {
            background-color: rgba(0, 120, 212, 0.1);
            padding: 1rem;
            border-radius: 8px;
            color: var(--azure-blue);
        }

        .filters {
            margin-bottom: 1.5rem;
            display: flex;
            gap: 0.5rem;
        }

        .filters button {
            padding: 0.5rem 1rem;
            border: 1px solid var(--azure-blue);
            background-color: white;
            color: var(--azure-blue);
            cursor: pointer;
            border-radius: 2px;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .filters button.active {
            background-color: var(--azure-blue);
            color: white;
        }

        .filters button:hover {
            background-color: var(--azure-light-gray);
        }

        .table-card {
            grid-column: 1 / -1;
            overflow: auto;
        }

        .table-container {
            overflow-x: auto;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .products-table th,
        .products-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--azure-light-gray);
        }

        .products-table th {
            background-color: var(--azure-light-gray);
            font-weight: 600;
            color: var(--azure-gray);
        }

        .products-table tbody tr:hover {
            background-color: rgba(0, 120, 212, 0.05);
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
                padding: 1rem;
            }
            .card {
                margin-bottom: 1rem;
            }
            .chart-card {
                grid-column: 1 / -1;
            }
            .table-card {
                grid-column: 1 / -1;
            }
            .filters {
                flex-direction: column;
                gap: 0.5rem;
            }
            .filters button {
                width: 100%;
            }
            .kpi-content {
                flex-direction: column;
                align-items: flex-start;
            }
            .kpi-icon {
                margin-top: 1rem;
            }
            .products-table th,
            .products-table td {
                padding: 0.5rem;
            }
        }

        @media (min-width: 769px) {
            .chart-card {
                grid-column: 1 / 2;
            }
            .table-card {
                grid-column: 2 / 3;
            }
            .dashboard {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            /* Definir 4 columnas en pantallas grandes */
            .dashboard {
                grid-template-columns: repeat(4, 1fr);
            }
            /* Las tarjetas KPI ocupan 1 columna (25% de ancho) */
            .kpi-card {
                grid-column: span 1;
            }
            /* El chart-card ocupará el 50% del ancho (2 columnas) */
            .chart-card {
                grid-column: span 2;
            }
            /* La tabla se extiende en las 4 columnas */
            .table-card {
                grid-column: span 4;
            }
        }
    </style>
    
</head>
<body>
    
    
    <div class="dashboard">

    
        <div class="card chart-card">
            <h2>Analytics Overview</h2>
            <div class="filters">
                <button onclick="updateChart('daily')" class="active">Diario</button>
                <button onclick="updateChart('weekly')">Semanal</button>
                <button onclick="updateChart('monthly')">Mensual</button>
            </div>
            <div class="chart-container">
                <canvas id="visitsChart"></canvas>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Visitas Totales</h2>
                    <div class="indicator">150,000</div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>12.5% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-users fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Duración Promedio</h2>
                    <div class="indicator">3:45</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>8.2% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-clock fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Tasa de Rebote</h2>
                    <div class="indicator">40%</div>
                    <div class="kpi-trend negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>5.1% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-percentage fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Páginas por Visita</h2>
                    <div class="indicator">5</div>
                    <div class="kpi-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>3.8% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-file fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Usuarios Activos</h2>
                    <div class="indicator">850</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>15.3% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-user-circle fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card kpi-card">
            <div class="kpi-content">
                <div class="kpi-info">
                    <h2>Conversiones</h2>
                    <div class="indicator">25%</div>
                    <div class="kpi-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>4.2% vs prev. period</span>
                    </div>
                </div>
                <div class="kpi-icon">
                    <i class="fas fa-chart-pie fa-lg"></i>
                </div>
            </div>
        </div>

        <div class="card table-card">
            <h2>Productos Más Revisados</h2>
            <div class="table-container">
                <table class="products-table">
                    <thead>
                        <tr>
                            <th>Imagen</th>
                            <th>Producto</th>
                            <th>Visitas Diarias</th>
                            <th>Visitas Semanales</th>
                            <th>Visitas Mensuales</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 1" style="width:40px; height:40px;"></td>
                            <td>Producto 1</td>
                            <td>100</td>
                            <td>700</td>
                            <td>3000</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 2" style="width:40px; height:40px;"></td>
                            <td>Producto 2</td>
                            <td>120</td>
                            <td>800</td>
                            <td>3200</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 3" style="width:40px; height:40px;"></td>
                            <td>Producto 3</td>
                            <td>140</td>
                            <td>900</td>
                            <td>3500</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 4" style="width:40px; height:40px;"></td>
                            <td>Producto 4</td>
                            <td>160</td>
                            <td>1000</td>
                            <td>3800</td>
                        </tr>
                        <tr>
                            <td><img src="https://via.placeholder.com/40" alt="Producto 5" style="width:40px; height:40px;"></td>
                            <td>Producto 5</td>
                            <td>180</td>
                            <td>1100</td>
                            <td>4000</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    
</body>
</html>