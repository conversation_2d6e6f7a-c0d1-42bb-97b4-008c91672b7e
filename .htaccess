# Habilitar el motor de reescritura
RewriteEngine On

# Establecer la base de reescritura
RewriteBase /

# Redirigir la página principal al login
RewriteRule ^$ public/login.php [L]
RewriteRule ^index\.php$ public/login.php [L]

# No aplicar reglas a archivos existentes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Asegurarse de que las cookies se envían con el dominio correcto
<IfModule mod_headers.c>
    Header set Set-Cookie "path=/; HttpOnly; SameSite=Lax"
</IfModule>

# Redirigir 404 al login
ErrorDocument 404 /public/login.php

# Configuración PHP
<IfModule mod_php.c>
    php_flag display_errors on
    php_value error_reporting E_ALL
    php_flag session.use_only_cookies on
    php_flag session.use_trans_sid off
</IfModule>

# Proteger archivos sensibles
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Establecer el índice por defecto
DirectoryIndex public/login.php
