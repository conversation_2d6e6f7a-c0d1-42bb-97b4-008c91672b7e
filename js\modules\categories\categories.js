// Importar componentes necesarios
import { showNotification } from '../../components/notifications.js';

// Función para inicializar la gestión de categorías
function initCategoriesModule() {
    document.addEventListener('DOMContentLoaded', function() {
        // Configurar los listeners para los botones de la sección de categorías
        setupCategoryButtons();
        
        // Cargar categorías
        loadCategories();
    });
}

// Configurar botones de la sección de categorías
function setupCategoryButtons() {
    // Botón para añadir nueva categoría
    const addCategoryBtn = document.getElementById('addCategoryBtn');
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            showCategoryForm();
        });
    }
    
    // Botón para guardar categoría
    const saveCategoryBtn = document.getElementById('saveCategoryBtn');
    if (saveCategoryBtn) {
        saveCategoryBtn.addEventListener('click', function() {
            saveCategory();
        });
    }
    
    // Botón para cancelar edición de categoría
    const cancelCategoryBtn = document.getElementById('cancelCategoryBtn');
    if (cancelCategoryBtn) {
        cancelCategoryBtn.addEventListener('click', function() {
            hideCategoryForm();
        });
    }
}

// Mostrar formulario de categoría
function showCategoryForm(categoryId = null) {
    const categoryForm = document.getElementById('categoryForm');
    if (!categoryForm) return;
    
    // Limpiar formulario
    document.getElementById('categoryName').value = '';
    document.getElementById('categoryDescription').value = '';
    document.getElementById('categoryId').value = '';
    
    // Si hay un ID, cargar datos para editar
    if (categoryId) {
        document.getElementById('categoryFormTitle').innerHTML = '<i class="fas fa-edit"></i> Editar Categoría';
        loadCategoryData(categoryId);
    } else {
        document.getElementById('categoryFormTitle').innerHTML = '<i class="fas fa-plus"></i> Nueva Categoría';
    }
    
    // Mostrar el formulario
    categoryForm.style.display = 'block';
}

// Ocultar formulario de categoría
function hideCategoryForm() {
    const categoryForm = document.getElementById('categoryForm');
    if (categoryForm) {
        categoryForm.style.display = 'none';
    }
}

// Cargar datos de una categoría para editar
async function loadCategoryData(categoryId) {
    try {
        const response = await fetch('../public/API/categorias/get_category.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: categoryId }),
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error('Error al cargar datos de la categoría');
        }
        
        const data = await response.json();
        
        if (data.success && data.categoria) {
            document.getElementById('categoryId').value = data.categoria.id;
            document.getElementById('categoryName').value = data.categoria.nombre;
            document.getElementById('categoryDescription').value = data.categoria.descripcion || '';
        } else {
            throw new Error(data.message || 'Error al cargar datos de la categoría');
        }
    } catch (error) {
        console.error('Error al cargar categoría:', error);
        showNotification('Error: ' + error.message, 'error');
    }
}

// Guardar categoría (nueva o editada)
async function saveCategory() {
    try {
        // Obtener datos del formulario
        const categoryData = {
            id: document.getElementById('categoryId').value,
            nombre: document.getElementById('categoryName').value,
            descripcion: document.getElementById('categoryDescription').value
        };
        
        // Validar campos requeridos
        if (!categoryData.nombre) {
            throw new Error('El nombre de la categoría es obligatorio');
        }
        
        // Mostrar indicador de carga
        const saveBtn = document.getElementById('saveCategoryBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveBtn.disabled = true;
        
        // Determinar si es nueva o edición
        const isNew = !categoryData.id;
        const endpoint = isNew ? 
            '../public/API/categorias/add_category.php' : 
            '../public/API/categorias/update_category.php';
        
        // Enviar datos a la API
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(categoryData),
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error('Error al guardar la categoría');
        }
        
        const data = await response.json();
        
        if (data.success) {
            showNotification(`Categoría ${isNew ? 'creada' : 'actualizada'} correctamente`, 'success');
            hideCategoryForm();
            loadCategories(); // Recargar la lista de categorías
        } else {
            throw new Error(data.message || 'Error al guardar la categoría');
        }
    } catch (error) {
        console.error('Error al guardar categoría:', error);
        showNotification('Error: ' + error.message, 'error');
    } finally {
        // Restaurar el botón de guardar
        const saveBtn = document.getElementById('saveCategoryBtn');
        saveBtn.innerHTML = '<i class="fas fa-save"></i> Guardar Categoría';
        saveBtn.disabled = false;
    }
}

// Cargar listado de categorías
async function loadCategories() {
    try {
        const categoriesTable = document.querySelector('#categoriesSection .admin-table tbody');
        if (!categoriesTable) return;
        
        // Mostrar indicador de carga
        categoriesTable.innerHTML = `
            <tr>
                <td colspan="4" class="loading-row">
                    <i class="fas fa-spinner fa-spin"></i> Cargando categorías...
                </td>
            </tr>
        `;
        
        // Cargar categorías desde la API
        const response = await fetch('../public/API/categorias/get_categories.php', {
            method: 'GET',
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error('Error al cargar categorías');
        }
        
        const data = await response.json();
        
        if (data.success && Array.isArray(data.categorias)) {
            // Generar filas de la tabla
            if (data.categorias.length === 0) {
                categoriesTable.innerHTML = `
                    <tr>
                        <td colspan="4" class="no-data">No hay categorías disponibles</td>
                    </tr>
                `;
                return;
            }
            
            categoriesTable.innerHTML = data.categorias.map(categoria => `
                <tr data-category-id="${categoria.id}">
                    <td>${categoria.id}</td>
                    <td>${categoria.nombre}</td>
                    <td>${categoria.descripcion || ''}</td>
                    <td class="actions">
                        <div class="table-actions">
                            <button class="action-btn edit-category-btn" title="Editar categoría" data-id="${categoria.id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete-category-btn" title="Eliminar categoría" data-id="${categoria.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // Configurar botones de acción
            setupCategoryActionButtons();
        } else {
            throw new Error(data.message || 'Error al cargar categorías');
        }
    } catch (error) {
        console.error('Error al cargar categorías:', error);
        
        const categoriesTable = document.querySelector('#categoriesSection .admin-table tbody');
        if (categoriesTable) {
            categoriesTable.innerHTML = `
                <tr>
                    <td colspan="4" class="error-row">
                        Error al cargar categorías: ${error.message}
                        <button id="retryLoadCategories" class="btn btn-primary btn-sm" style="margin-top: 10px;">
                            <i class="fas fa-sync-alt"></i> Reintentar
                        </button>
                    </td>
                </tr>
            `;
            
            // Agregar evento al botón de reintentar
            const retryBtn = document.getElementById('retryLoadCategories');
            if (retryBtn) {
                retryBtn.addEventListener('click', function() {
                    loadCategories();
                });
            }
        }
        
        showNotification('Error al cargar categorías: ' + error.message, 'error');
    }
}

// Configurar botones de acción para categorías
function setupCategoryActionButtons() {
    // Botones de editar
    document.querySelectorAll('.edit-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            showCategoryForm(categoryId);
        });
    });
    
    // Botones de eliminar
    document.querySelectorAll('.delete-category-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const categoryId = this.getAttribute('data-id');
            if (confirm('¿Está seguro que desea eliminar esta categoría?')) {
                deleteCategory(categoryId);
            }
        });
    });
}

// Eliminar categoría
async function deleteCategory(categoryId) {
    try {
        const response = await fetch('../public/API/categorias/delete_category.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: categoryId }),
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error('Error al eliminar la categoría');
        }
        
        const data = await response.json();
        
        if (data.success) {
            showNotification('Categoría eliminada correctamente', 'success');
            loadCategories(); // Recargar la lista
        } else {
            throw new Error(data.message || 'Error al eliminar la categoría');
        }
    } catch (error) {
        console.error('Error al eliminar categoría:', error);
        showNotification('Error: ' + error.message, 'error');
    }
}

// Exportar funciones
export {
    initCategoriesModule,
    loadCategories
};