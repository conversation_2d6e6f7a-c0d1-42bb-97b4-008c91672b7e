<?php
/**
 * SessionManager.php
 * 
 * Clase para la gestión de sesiones con implementación optimizada
 * y mejores prácticas de seguridad.
 */

require_once __DIR__ . '/SessionTracker.php';
require_once __DIR__ . '/logger.php';

class SessionManager {
    // Instancia única (Singleton)
    private static $instance = null;
    
    // Configuración de sesión
    private $sessionLifetime;
    private $cookiePath;
    private $cookieDomain;
    private $sessionName;
    private $sessionTracker = null;
    private $isHttps;
    
    // Constantes de configuración
    const SESSION_REGENERATE_PROBABILITY = 10; // 10% de probabilidad de regenerar ID en cada solicitud
    const INACTIVITY_TIMEOUT = 1800;           // 30 minutos de inactividad
    
    /**
     * Constructor privado (patrón Singleton)
     */
    private function __construct() {
        // Usar constantes definidas en config.php
        $this->sessionLifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 86400;
        $this->cookiePath = defined('COOKIE_PATH') ? COOKIE_PATH : '/';
        $this->cookieDomain = defined('COOKIE_DOMAIN') ? COOKIE_DOMAIN : '';
        $this->sessionName = defined('SESSION_NAME') ? SESSION_NAME : 'VILLARRICA_SID';
        
        // Determinar si estamos en HTTPS
        $this->isHttps = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        
        // Comprobar si estamos detrás de un proxy HTTPS
        if (!$this->isHttps && isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $this->isHttps = true;
        }
        
        // Log de configuración
        logInfo("Inicialización de SessionManager", [
            'session_name' => $this->sessionName,
            'cookie_path' => $this->cookiePath,
            'cookie_domain' => $this->cookieDomain,
            'session_lifetime' => $this->sessionLifetime . " segundos",
            'https' => $this->isHttps ? 'true' : 'false'
        ]);
        
        // Eliminar cookies de sesión antiguas o predeterminadas
        $this->cleanOldSessionCookies();
    }
    
    /**
     * Obtiene la instancia única del SessionManager
     *
     * @return SessionManager
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Limpia cookies de sesión antiguas o predeterminadas
     */
    private function cleanOldSessionCookies() {
        $old_cookies_to_remove = ['PHPSESSID'];
        
        // Detectar si hay cookies antiguas que no coinciden con el nombre actual
        foreach ($_COOKIE as $name => $value) {
            if ($name !== $this->sessionName && strpos($name, 'SID') !== false) {
                $old_cookies_to_remove[] = $name;
            }
        }
        
        // Eliminar todas las cookies antiguas
        foreach ($old_cookies_to_remove as $cookie_name) {
            if (isset($_COOKIE[$cookie_name])) {
                setcookie($cookie_name, '', [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => $this->isHttps,
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]);
                
                logInfo("Cookie de sesión antigua eliminada", ['cookie_name' => $cookie_name]);
            }
        }
    }
    
    /**
     * Inicializa la sesión con configuración óptima de seguridad
     */
    public function initializeSession() {
        // Si la sesión ya está activa, verificar validez
        if (session_status() === PHP_SESSION_ACTIVE) {
            logInfo("Sesión ya activa", ['session_id' => session_id()]);
            
            // Validar la sesión activa
            if (!$this->isValid()) {
                logWarning("Sesión activa inválida, regenerando", ['session_id' => session_id()]);
                $this->regenerateSession();
            }
            
            // Actualizar tiempo de actividad
            $this->updateActivity();
            return;
        }
        
        // Configurar seguridad básica de sesiones
        $this->configureSessionSettings();
        
        // Establecer el nombre de la sesión
        session_name($this->sessionName);
        
        // Configurar parámetros de cookie para la sesión
        $cookie_params = [
            'lifetime' => $this->sessionLifetime,
            'path' => $this->cookiePath,
            'domain' => $this->cookieDomain,
            'secure' => $this->isHttps,
            'httponly' => true,
            'samesite' => 'Lax'
        ];
        
        logInfo("Configurando cookie para sesión", $cookie_params);
        session_set_cookie_params($cookie_params);
        
        // Iniciar sesión
        $result = session_start();
        
        if (!$result) {
            logError("Error al iniciar sesión");
            // Si no se puede iniciar la sesión, intentar limpiar cookies y reintentar
            $this->cleanOldSessionCookies();
            session_start();
        }
        
        logInfo("Sesión iniciada", ['session_id' => session_id()]);
        
        // Si la sesión no es válida, regenerarla
        if (!$this->isValid()) {
            logInfo("Sesión inválida, regenerando", ['session_id' => session_id()]);
            $this->regenerateSession();
        }
        
        // Registrar la actividad de la sesión
        $this->initializeSessionData();
        
        // Regeneración aleatoria de ID para prevenir session fixation
        $this->randomIdRegeneration();
    }
    
    /**
     * Configura parámetros de seguridad para la sesión
     */
    private function configureSessionSettings() {
        // Configuración estricta de sesiones
        ini_set('session.use_strict_mode', 1);
        ini_set('session.use_cookies', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_samesite', 'Lax');
        ini_set('session.cookie_secure', $this->isHttps ? 'On' : 'Off');
        
        // Configurar garbage collector
        ini_set('session.gc_maxlifetime', $this->sessionLifetime);
        ini_set('session.gc_probability', 1);
        ini_set('session.gc_divisor', 100); // 1% de probabilidad
    }
    
    /**
     * Inicializa datos básicos en la sesión
     */
    private function initializeSessionData() {
        $_SESSION['created_at'] = $_SESSION['created_at'] ?? time();
        $_SESSION['last_activity'] = time();
        $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Generar token CSRF si no existe
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
    }
    
    /**
     * Regenera aleatoriamente el ID de sesión para prevenir ataques
     */
    private function randomIdRegeneration() {
        // Probabilidad configurable de regenerar ID en cada solicitud
        if (mt_rand(1, 100) <= self::SESSION_REGENERATE_PROBABILITY) {
            logInfo("Regenerando aleatoriamente ID de sesión", ['old_id' => session_id()]);
            $this->regenerateSession();
        }
    }
    
    /**
     * Genera un token CSRF para formularios
     * 
     * @return string Token CSRF
     */
    public function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Valida un token CSRF
     * 
     * @param string $token Token a validar
     * @param bool $regenerate Si se debe regenerar el token después de la validación
     * @return bool True si el token es válido
     */
    public function validateCsrfToken($token, $regenerate = true) {
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        $valid = hash_equals($_SESSION['csrf_token'], $token);
        
        // Regenerar el token después de la validación (por seguridad)
        if ($valid && $regenerate) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $valid;
    }
    
    /**
     * Actualiza la ruta de cookies de sesión
     * 
     * @param string $path Nueva ruta
     */
    public function updateCookiePath($path) {
        $this->cookiePath = rtrim($path, '/');
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Actualizar la cookie con la nueva ruta
            setcookie(session_name(), session_id(), [
                'expires' => time() + $this->sessionLifetime,
                'path' => $this->cookiePath,
                'domain' => $this->cookieDomain,
                'secure' => $this->isHttps,
                'httponly' => true,
                'samesite' => 'Lax'
            ]);
            
            logInfo("Ruta de cookie actualizada", ['path' => $this->cookiePath]);
        }
    }
    
    /**
     * Obtiene la ruta de cookies actual
     * 
     * @return string
     */
    public function getCookiePath() {
        return $this->cookiePath;
    }
    
    /**
     * Obtiene el dominio de cookies actual
     * 
     * @return string
     */
    public function getCookieDomain() {
        return $this->cookieDomain;
    }
    
    /**
     * Verifica si la sesión actual es válida
     * 
     * @return bool
     */
    public function isValid() {
        // Verificar si existe marca de tiempo de creación
        if (!isset($_SESSION['created_at'])) {
            logWarning("Sesión sin fecha de creación");
            return false;
        }
        
        // Verificar si la sesión no ha expirado
        $session_age = time() - $_SESSION['created_at'];
        if ($session_age > $this->sessionLifetime) {
            logWarning("Sesión expirada por tiempo", [
                'age' => $session_age, 
                'limit' => $this->sessionLifetime
            ]);
            return false;
        }
        
        // Verificar tiempo de inactividad
        if (!isset($_SESSION['last_activity'])) {
            logWarning("Sesión sin registro de actividad");
            return false;
        }
        
        $inactivity_time = time() - $_SESSION['last_activity'];
        if ($inactivity_time > self::INACTIVITY_TIMEOUT) {
            logWarning("Sesión expirada por inactividad", ['inactivity_time' => $inactivity_time]);
            return false;
        }
        
        // Verificar IP si está configurada
        if (isset($_SESSION['ip']) && $_SESSION['ip'] !== $_SERVER['REMOTE_ADDR']) {
            logWarning("IP de sesión no coincide", [
                'session_ip' => $_SESSION['ip'], 
                'current_ip' => $_SERVER['REMOTE_ADDR']
            ]);
            return false;
        }
        
        // Verificar User-Agent si está configurado (puede ser útil contra session hijacking)
        if (isset($_SESSION['user_agent']) && !empty($_SESSION['user_agent']) && 
            isset($_SERVER['HTTP_USER_AGENT']) && $_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            logWarning("User-Agent de sesión no coincide");
            return false;
        }
        
        return true;
    }
    
    /**
     * Regenera la sesión preservando datos importantes
     */
    public function regenerateSession() {
        // Guardar datos importantes
        $oldData = $_SESSION;
        $oldId = session_id();
        
        // Limpiar sesión
        $_SESSION = [];
        
        // Regenerar ID con opción de eliminar archivo antiguo
        if (session_regenerate_id(true)) {
            // Restaurar datos básicos
            $_SESSION['created_at'] = time();
            $_SESSION['last_activity'] = time();
            $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
            $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Restaurar datos de autenticación si existían
            if (isset($oldData['user_id'])) {
                $_SESSION['user_id'] = $oldData['user_id'];
                $_SESSION['username'] = $oldData['username'] ?? null;
                $_SESSION['role'] = $oldData['role'] ?? null;
                $_SESSION['logged_in'] = $oldData['logged_in'] ?? false;
            }
            
            // Restaurar token CSRF
            if (isset($oldData['csrf_token'])) {
                $_SESSION['csrf_token'] = $oldData['csrf_token'];
            } else {
                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            }
            
            logInfo("Sesión regenerada exitosamente", [
                'old_id' => $oldId,
                'new_id' => session_id()
            ]);
        } else {
            logError("Error al regenerar sesión");
        }
    }
    
    /**
     * Crea una sesión de usuario autenticado
     * 
     * @param array $userData Datos del usuario
     */
    public function createUserSession($userData) {
        global $conn; // Usamos la conexión global a la base de datos
        
        logInfo("Creando sesión de usuario", ['username' => $userData['username']]);
        
        // Regenerar sesión para obtener nueva ID
        $this->regenerateSession();
        
        // Establecer datos de usuario
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['username'] = $userData['username'];
        $_SESSION['role'] = $userData['role'];
        $_SESSION['logged_in'] = true;
        $_SESSION['created_at'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['ip'] = $_SERVER['REMOTE_ADDR'];
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Inicializar tracker de sesiones y registrarla en la BD
        $this->sessionTracker = SessionTracker::getInstance($conn);
        $this->sessionTracker->registerSession($userData['id'], $userData['username']);
        
        // Forzar escritura de sesión
        session_write_close();
        session_start();
        
        logInfo("Sesión de usuario creada", [
            'user_id' => $userData['id'],
            'username' => $userData['username'],
            'session_id' => session_id()
        ]);
    }
    
    /**
     * Actualiza el registro de actividad en la sesión
     */
    public function updateActivity() {
        global $conn;
        
        $_SESSION['last_activity'] = time();
        
        // Si es una nueva página, registrar la visita
        if (isset($_SESSION['current_page']) && $_SESSION['current_page'] !== $_SERVER['REQUEST_URI']) {
            // Actualizar tiempo en la página anterior
            if ($this->sessionTracker === null) {
                $this->sessionTracker = SessionTracker::getInstance($conn);
            }
            
            $this->sessionTracker->updateTimeSpent($_SESSION['current_page']);
            $this->sessionTracker->trackPageView();
        }
        
        // Actualizar página actual
        $_SESSION['current_page'] = $_SERVER['REQUEST_URI'];
    }
    
    /**
     * Destruye la sesión actual de forma segura
     */
    public function destroySession() {
        global $conn;
        
        // Registrar cierre de sesión en la BD si el usuario estaba logueado
        if ($this->isLoggedIn() && isset($_SESSION['user_id'])) {
            if ($this->sessionTracker === null) {
                $this->sessionTracker = SessionTracker::getInstance($conn);
            }
            $this->sessionTracker->logoutSession();
        }
        
        // Guardar el ID de sesión actual para logging
        $oldSessionId = session_id();
        logInfo("Destruyendo sesión", ['session_id' => $oldSessionId]);
        
        // Limpiar todas las variables de sesión
        $_SESSION = [];
        
        // Eliminar la cookie de sesión
        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 3600,
                    'path' => $this->cookiePath,
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => 'Lax'
                ]
            );
            
            unset($_COOKIE[session_name()]);
        }
        
        // Si hay otras cookies de sesión, también eliminarlas
        $this->cleanOldSessionCookies();
        
        // Destruir la sesión
        session_destroy();
        
        // Forzar la escritura y cierre de la sesión
        session_write_close();
        
        logInfo("Sesión destruida", ['old_session_id' => $oldSessionId]);
    }
    
    /**
     * Verifica si el usuario actual está autenticado
     * 
     * @return bool
     */
    public function isLoggedIn() {
        $loggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
        return $loggedIn;
    }
    
    /**
     * Requiere que el usuario esté autenticado o redirige
     */
    public function requireLogin() {
        if (!$this->isLoggedIn() && !headers_sent()) {
            logInfo("Acceso no autorizado, redirigiendo a login");
            header('Location: ' . BASE_URL . '/public/login.php?error=unauthorized&reason=login_required');
            exit();
        } else if (!$this->isLoggedIn()) {
            exit('No autorizado. Por favor <a href="' . BASE_URL . '/public/login.php">inicie sesión</a>.');
        }
    }
    
    /**
     * Requiere que el usuario tenga rol de administrador o redirige
     */
    public function requireAdmin() {
        $this->requireLogin();
        
        $allowed_roles = ['admin', 'premium', 'pro'];
        if (!isset($_SESSION['role']) || !in_array($_SESSION['role'], $allowed_roles)) {
            logWarning("Intento de acceso a área administrativa sin permisos", [
                'user_id' => $_SESSION['user_id'] ?? 'unknown',
                'role' => $_SESSION['role'] ?? 'none'
            ]);
            
            if (!headers_sent()) {
                header('Location: ' . BASE_URL . '/public/profile.php?error=unauthorized&reason=insufficient_permissions');
                exit();
            } else {
                exit('Permisos insuficientes. <a href="' . BASE_URL . '/public/profile.php">Volver al perfil</a>.');
            }
        }
    }
    
    /**
     * Obtiene datos del usuario actual
     * 
     * @return array|null Datos del usuario o null si no está autenticado
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'role' => $_SESSION['role']
        ];
    }
    
    /**
     * Valida el estado de la sesión actual y actualiza actividad
     * 
     * @return bool True si la sesión es válida
     */
    public function validateSession() {
        global $conn;
        
        logInfo("Validando sesión");
        
        if (session_status() !== PHP_SESSION_ACTIVE) {
            logInfo("Sesión no activa, inicializando");
            $this->initializeSession();
        }
        
        // Actualizar última actividad
        if (isset($_SESSION['last_activity'])) {
            $_SESSION['last_activity'] = time();
        }
        
        $isValid = $this->isValid();
        $isLoggedIn = $this->isLoggedIn();
        
        // Si el usuario está logueado, verificar si necesitamos registrar la sesión
        if ($isLoggedIn && !isset($this->sessionTracker)) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
            // Registrar sesión si no está registrada
            if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
                $this->sessionTracker->registerSession($_SESSION['user_id'], $_SESSION['username']);
            }
        }
        
        logInfo("Resultado de validación", [
            'valid' => $isValid ? 'true' : 'false',
            'logged_in' => $isLoggedIn ? 'true' : 'false'
        ]);
        
        return $isLoggedIn && $isValid;
    }
    
    /**
     * Obtiene estadísticas de la sesión del usuario actual
     * 
     * @return array|null Estadísticas de sesión
     */
    public function getCurrentUserStats() {
        global $conn;
        
        if (!$this->isLoggedIn() || !isset($_SESSION['user_id'])) {
            return null;
        }
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getUserSessionStats($_SESSION['user_id']);
    }
    
    /**
     * Obtiene estadísticas generales del sitio
     * 
     * @param int $days Número de días para la consulta
     * @return array|null Estadísticas del sitio
     */
    public function getSiteStats($days = 30) {
        global $conn;
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getSiteStats($days);
    }
    
    /**
     * Obtiene páginas más visitadas
     * 
     * @param int $limit Número máximo de páginas a devolver
     * @return array Páginas más visitadas
     */
    public function getMostVisitedPages($limit = 10) {
        global $conn;
        
        if ($this->sessionTracker === null) {
            $this->sessionTracker = SessionTracker::getInstance($conn);
        }
        
        return $this->sessionTracker->getMostVisitedPages($limit);
    }
}