<?php
// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Verificar si estamos en la raíz o en un subdirectorio
$config_path = file_exists('config/logger.php') ? 'config/' : '../config/';

// Incluir primero el sistema de logging
require_once $config_path . 'logger.php';

// Registrar inicio de la página
// Comentamos temporalmente para evitar errores
// logInfo("Cargando página de registro");

// Incluir la configuración base
require_once $config_path . 'config.php';
require_once $config_path . 'SessionManager.php';
require_once $config_path . 'RateLimiter.php';

// SECURITY: Check rate limiting for form access
if (isset($conn)) {
    $rateLimiter = new RateLimiter($conn);
    $clientIdentifier = RateLimiter::getIdentifier();
    $rateCheck = $rateLimiter->isAllowed($clientIdentifier, 'form_access', 10, 300); // 10 accesses per 5 minutes

    if (!$rateCheck['allowed']) {
        http_response_code(429);
        die('Too many requests. Please try again later.');
    }
    $rateLimiter->recordAttempt($clientIdentifier, 'form_access');
}

// Inicializar gestor de sesiones
// ---------------- Seguridad ----------------
// Iniciar sesión para CSRF
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
// Generar token CSRF si no existe
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
// Enviar cabeceras de seguridad mejoradas - SECURITY: Enhanced CSP
$nonce = base64_encode(random_bytes(16));
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; object-src 'none'");
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
// SECURITY: Additional security headers
header('X-Permitted-Cross-Domain-Policies: none');
header('Cross-Origin-Embedder-Policy: require-corp');
header('Cross-Origin-Opener-Policy: same-origin');
header('Cross-Origin-Resource-Policy: same-origin');
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos - SECURITY: Added integrity check -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
          integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    <!-- Importación de estilos propios -->
    <link rel="stylesheet" href="/css/register1.css">

    <title>Crear Cuenta - Villarrica a un CLICK</title>
</head>

<body>
    <div class="register-panel">
        <div class="register-header">
            <h1>Crea tu cuenta</h1>
        </div>

        <div class="register-steps">
            <div class="step active" data-step="1">
                <div class="step-number">1</div>
                <div class="step-title">Información Personal</div>
            </div>
            <div class="step" data-step="2">
                <div class="step-number">2</div>
                <div class="step-title">Datos de Cuenta</div>
            </div>
            <div class="step" data-step="3">
                <div class="step-number">3</div>
                <div class="step-title">Datos del Negocio</div>
            </div>
            <div class="step" data-step="4">
                <div class="step-number">4</div>
                <div class="step-title">Confirmación</div>
            </div>
        </div>

        <form id="registerForm" class="register-form" action="process_register.php" method="post" onsubmit="return submitFormWithAjax(event)">
            <!-- Campo CSRF -->
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token'], ENT_QUOTES, 'UTF-8'); ?>">
            <!-- Paso 1: Información Personal -->
            <div class="form-step active" id="step1">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="nombres" class="form-label-small">Nombres</label>
                            <input type="text"
                                   id="nombres"
                                   name="nombres"
                                   class="form-control"
                                   placeholder="Ej: Juan Carlos"
                                   required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="apellidos" class="form-label-small">Apellidos</label>
                            <input type="text"
                                   id="apellidos"
                                   name="apellidos"
                                   class="form-control"
                                   placeholder="Ej: Pérez Gómez"
                                   required>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="rut">RUT</label>
                            <input type="text" id="rut" name="rut" class="form-control" placeholder="12345678-9" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="fechaNacimiento" class="fecha-nacimiento-label">
                                <span class="texto-completo">Fecha de Nacimiento</span>
                                <span class="texto-abreviado">F.Nacim</span>
                            </label>
                            <div class="date-input-container">
                                <input type="text" id="fechaNacimiento" name="fechaNacimiento" required placeholder="DD/MM/AAAA" maxlength="10">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label>Sexo</label>
                            <select class="form-control" name="sexo" required>
                                <option value="">Seleccionar...</option>
                                <option value="masculino">Masculino</option>
                                <option value="femenino">Femenino</option>
                                <option value="otro">Otro</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="telefono">Teléfono</label>
                            <div class="phone-input">
                                <span class="phone-prefix">+569</span>
                                <input type="text"
                                       id="telefono"
                                       name="telefono"
                                       class="form-control phone-number"
                                       placeholder="Ingrese los 8 dígitos"
                                       maxlength="8"
                                       pattern="[0-9]{8}"
                                       inputmode="numeric"
                                       required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="region">Región</label>
                            <select class="form-control" id="region" name="region" required>
                                <option value="">Seleccionar región...</option>
                                <option value="arica">Arica y Parinacota</option>
                                <option value="tarapaca">Tarapacá</option>
                                <option value="antofagasta">Antofagasta</option>
                                <option value="atacama">Atacama</option>
                                <option value="coquimbo">Coquimbo</option>
                                <option value="valparaiso">Valparaíso</option>
                                <option value="metropolitana">Metropolitana de Santiago</option>
                                <option value="ohiggins">Del Libertador General Bernardo O'Higgins</option>
                                <option value="maule">Del Maule</option>
                                <option value="nuble">Ñuble</option>
                                <option value="biobio">Del Biobío</option>
                                <option value="araucania">De la Araucanía</option>
                                <option value="losrios">De los Ríos</option>
                                <option value="loslagos">De los Lagos</option>
                                <option value="aysen">Aysén del General Carlos Ibáñez del Campo</option>
                                <option value="magallanes">Magallanes y de la Antártica Chilena</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="comuna">Comuna</label>
                            <select class="form-control" id="comuna" name="comuna" required disabled>
                                <option value="">Seleccione una región primero...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="direccion">Dirección</label>
                    <input type="text"
                           class="form-control"
                           id="direccion"
                           name="direccion"
                           placeholder="Calle, número, casa/departamento, villa"
                           required>
                </div>

                <div class="auto-fill-container">
                    <button type="button" class="btn btn-auto-fill" id="auto-fill-btn" onclick="autoFillStep1()">Auto-llenar datos</button>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-next" id="step1-next">Siguiente</button>
                    <input type="hidden" id="session_id" name="session_id" value="">
                </div>

                <!-- Los estilos se han movido al archivo register.css -->
            </div>

            <!-- Paso 2: Datos de la Cuenta -->
            <div class="form-step" id="step2">
                <div class="form-group">
                    <label for="username">Nombre de Usuario</label>
                    <input type="text" id="username" name="username" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email"
                           id="email"
                           name="email"
                           class="form-control"
                           placeholder="Ej: <EMAIL>"
                           pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
                           title="Ingrese un correo electrónico válido (ejemplo: <EMAIL>)"
                           required>
                </div>

                <div class="form-group">
                    <label for="backup_email">Correo de Respaldo (Opcional)</label>
                    <input type="email"
                           id="backup_email"
                           name="backup_email"
                           class="form-control"
                           placeholder="Ej: <EMAIL>"
                           pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
                           title="Ingrese un correo electrónico válido (ejemplo: <EMAIL>)">
                </div>

                <div class="form-group">
                    <label for="password" class="password-label">
                        Contraseña
                        <span class="info-icon" id="password-info-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </span>
                    </label>
                    <div class="password-field">
                        <input type="password"
                               id="password"
                               name="password"
                               class="form-control"
                               placeholder="Mínimo 12 caracteres"
                               minlength="12"
                               maxlength="128"
                               pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*()_+\-=\[\]{};':\"\\|,.<>\/?]).{12,128}$"
                               title="La contraseña debe tener al menos 12 caracteres, incluyendo mayúsculas, minúsculas, números y caracteres especiales"
                               autocomplete="new-password"
                               required>
                        <i class="fas fa-eye toggle-password"></i>
                    </div>
                    <!-- Los requisitos de contraseña se han movido al popup -->
                </div>

                <!-- Popup de información de contraseña -->
                <div id="password-info-popup" class="password-popup">
                    <div class="password-popup-content">
                        <div class="password-popup-header">
                            <h3>Requisitos de Contraseña</h3>
                        </div>
                        <div class="password-popup-body">
                            <ul class="password-requirements-list">
                                <li><i class="fas fa-check-circle"></i> Mínimo 12 caracteres</li>
                                <li><i class="fas fa-check-circle"></i> Al menos una letra minúscula</li>
                                <li><i class="fas fa-check-circle"></i> Al menos una letra mayúscula</li>
                                <li><i class="fas fa-check-circle"></i> Al menos un número</li>
                                <li><i class="fas fa-check-circle"></i> Al menos un carácter especial (!@#$%^&*)</li>
                                <li><i class="fas fa-check-circle"></i> No patrones comunes (123456, password, etc.)</li>
                            </ul>
                        </div>
                        <div class="password-popup-footer">
                            <button type="button" class="btn-close-popup" id="close-password-popup">Cerrar</button>
                        </div>
                    </div>
                </div>

                <!-- Los estilos se han movido al archivo register.css -->

                <div class="form-group">
                    <label for="confirm_password">Confirmar Contraseña</label>
                    <div class="password-field">
                        <input type="password"
                               id="confirm_password"
                               name="confirm_password"
                               class="form-control"
                               placeholder="Repita su contraseña"
                               minlength="12"
                               maxlength="128"
                               autocomplete="new-password"
                               required>
                        <i class="fas fa-eye toggle-password"></i>
                    </div>
                    <div class="password-match-message" id="password-match-message"></div>
                </div>

                <!-- Los estilos se han movido al archivo register.css -->

                <!-- La sección "¿Qué negocio inscribirás?" ha sido eliminada -->

                <div class="auto-fill-container">
                    <button type="button" class="btn btn-auto-fill" id="auto-fill-step2-btn" onclick="autoFillStep2()">Auto-llenar datos</button>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step2-next">Siguiente</button>
                </div>
            </div>

            <!-- Paso 3: Datos del Negocio -->
            <div class="form-step" id="step3">

                <!-- Nombre del Negocio -->
                <div class="form-group">
                    <label for="nombre_negocio">Nombre del Negocio</label>
                    <input type="text"
                           class="form-control"
                           id="nombre_negocio"
                           name="nombre_negocio"
                           placeholder="Nombre que irá en la Página a crear"
                           required>
                </div>

                <!-- Descripción del Negocio -->
                <div class="form-group">
                    <div class="label-with-counter">
                        <label for="descripcion_negocio">Descripción del Negocio (Opcional)</label>
                        <div class="word-counter"><span id="word-count">0</span>/100 palabras</div>
                    </div>
                    <textarea
                        id="descripcion_negocio"
                        name="descripcion_negocio"
                        class="form-control"
                        placeholder="Máximo 100 palabras. Esta descripción aparecerá en la parte inferior de su página creada."
                        rows="4"
                        maxlength="1000"></textarea>
                </div>

                <!-- Local Físico -->
                <div class="form-group local-fisico-container">
                    <div class="local-fisico-label">¿Tiene Local Físico?</div>
                    <div class="local-fisico-options">
                        <label class="radio-option-small">
                            <input type="radio" name="local_fisico" value="si">
                            <span class="radio-label-small">Sí</span>
                        </label>
                        <label class="radio-option-small">
                            <input type="radio" name="local_fisico" value="no">
                            <span class="radio-label-small">No</span>
                        </label>
                    </div>
                </div>

                <!-- Teléfono y WhatsApp -->
                <div class="form-row aligned-inputs">
                    <div class="form-col">
                        <label for="telefono_negocio">Teléfono</label>
                        <div class="form-group">
                            <div class="phone-input">
                                <span class="phone-prefix">+56</span>
                                <input type="text"
                                       id="telefono_negocio"
                                       name="telefono_negocio"
                                       class="phone-number"
                                       placeholder="9 1234 5678"
                                       maxlength="9"
                                       pattern="[0-9]{9}"
                                       inputmode="numeric"
                                       oninput="this.value = this.value.replace(/[^0-9]/g, '').substring(0,9)"
                                       required>
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <label for="whatsapp_negocio">WhatsApp <span class="optional-label">(Opcional)</span></label>
                        <div class="form-group">
                            <div class="phone-input">
                                <span class="phone-prefix">+569</span>
                                <input type="text"
                                       id="whatsapp_negocio"
                                       name="whatsapp_negocio"
                                       class="phone-number"
                                       placeholder="12345678"
                                       maxlength="8"
                                       pattern="[0-9]{8}"
                                       inputmode="numeric"
                                       oninput="this.value = this.value.replace(/[^0-9]/g, '').substring(0,8)">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Las opciones condicionales según tipo de negocio han sido eliminadas -->

                <div class="auto-fill-container">
                    <button type="button" class="btn btn-auto-fill" id="auto-fill-step3-btn" onclick="autoFillStep3()">Auto-llenar datos</button>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="step3-next">Siguiente</button>
                </div>
            </div>

            <!-- Paso 4: Planes de Suscripción -->
            <div class="form-step" id="step4">
                <p class="subscription-subtitle">Elige el plan perfecto para tu negocio</p>
                
                <!-- Campo oculto para guardar la selección de plan -->
                <input type="hidden" name="subscription" id="selected_subscription" value="">
                
                <!-- Planes de suscripción - Ahora solo los popups -->
                <div class="plan-details-popups">
                    <!-- Popups para Venta de Productos -->
                    <div class="plan-popup-overlay" id="free-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Gratuito - $0</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Hasta 10 imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Carrusel de productos</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Baja</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de productos</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información de la tienda</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="plan-popup-overlay" id="normal-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Normal - $2.990 + IVA Mensual</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Hasta 30 imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Carrusel de productos</span>
                                        <span class="feature-value">1 Carrusel de Imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Media</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de productos</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información de la tienda</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="plan-popup-overlay" id="premium-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Premium - $5.990 + IVA Mensual</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Hasta 80 imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Carrusel de productos</span>
                                        <span class="feature-value">3 Carruseles de Imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Alta</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de productos</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información de la tienda</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Popups para Servicios y Arriendos -->
                    <div class="plan-popup-overlay" id="free-servicios-arriendo-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Gratuito - $0</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Imagen de la Empresa</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Carrusel de Imágenes</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Baja</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de servicios</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">Solo datos de la Empresa</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información del servicio</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value negative">✕</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="plan-popup-overlay" id="normal-servicios-arriendo-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Normal - $1.990 + IVA Mensual</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Hasta 10 imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Galería de servicios</span>
                                        <span class="feature-value">1 Carrusel de Imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Media</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de servicios</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información del servicio</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="plan-popup-overlay" id="premium-servicios-arriendo-plan-popup">
                        <div class="plan-popup">
                            <div class="plan-popup-header">
                                <h3>Plan Premium - $4.990 + IVA Mensual</h3>
                                <button type="button" class="plan-popup-close">&times;</button>
                            </div>
                            <div class="plan-popup-content">
                                <div class="plan-section">
                                    <h5 class="section-title">Características básicas</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Imágenes permitidas</span>
                                        <span class="feature-value">Hasta 80 imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Galería de servicios</span>
                                        <span class="feature-value">3 Carruseles de Imágenes</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Estadísticas del panel</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Visibilidad</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página principal</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Aparece en página de secciones</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Buscadores</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Prioridad de aparición</span>
                                        <span class="feature-value">Alta</span>
                                    </div>
                                </div>
                                <div class="plan-section">
                                    <h5 class="section-title">Funcionalidades</h5>
                                    <div class="feature-item">
                                        <span class="feature-name">Categorías de servicios</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Panel de control</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Editar información</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Título</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Información del servicio</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Banner</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                    <div class="feature-item">
                                        <span class="feature-name">Slogan</span>
                                        <span class="feature-value positive">✓</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Banner de comparación de planes eliminado -->

                <!-- Selección de tipo de negocio y plan -->
                <div class="plan-selection">
                    <h4 class="selection-title">¿Qué ofrecerás?</h4>

                    <!-- Fila de selección con tipo de negocio a la izquierda y botones a la derecha -->
                    <div class="plan-selection-row">
                        <!-- Opciones de tipo de negocio (izquierda) -->
                        <div class="business-type-options">
                            <div class="business-type-option selected-type">
                                <input type="radio" id="tipo_venta" name="tipo_negocio" value="venta" checked>
                                <label for="tipo_venta">Productos</label>
                            </div>
                        </div>

                        <!-- Planes para Venta de productos (derecha) -->
                        <div class="plan-options-container" id="venta-plans">
                            <div class="plan-options">
                                <div class="plan-option">
                                    <div class="plan-option-content">
                                        <!-- Etiqueta eliminada -->
                                    </div>
                                    <input type="radio" name="selected_plan_venta" value="free" checked style="display: none;">
                                    <button type="button" class="plan-option-button" data-plan="free" data-type="venta">
                                        <span class="plan-name">Gratuita</span>
                                        <span class="plan-price">$0</span>
                                    </button>
                                </div>
                                <div class="plan-option">
                                    <div class="plan-option-content">
                                        <!-- Etiqueta eliminada -->
                                    </div>
                                    <input type="radio" name="selected_plan_venta" value="normal" style="display: none;">
                                    <button type="button" class="plan-option-button" data-plan="normal" data-type="venta">
                                        <span class="plan-name">Normal</span>
                                        <span class="plan-price">$2.990 + IVA</span>
                                    </button>
                                </div>
                                <div class="plan-option">
                                    <div class="plan-option-content">
                                        <!-- Etiqueta eliminada -->
                                    </div>
                                    <input type="radio" name="selected_plan_venta" value="premium" style="display: none;">
                                    <button type="button" class="plan-option-button" data-plan="premium" data-type="venta">
                                        <span class="plan-name">Premium</span>
                                        <span class="plan-price">$5.990 + IVA</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Opción de Servicios/Arriendos con sus planes -->
                    <div class="servicios-arriendos-section">
                        <!-- Fila de selección con tipo de negocio a la izquierda y botones a la derecha -->
                        <div class="plan-selection-row">
                            <!-- Opciones de tipo de negocio (izquierda) -->
                            <div class="business-type-options servicios-arriendos-option">
                                <div class="business-type-option">
                                    <input type="radio" id="tipo_servicios_arriendos" name="tipo_negocio" value="servicios-arriendo">
                                    <label for="tipo_servicios_arriendos">Servicios/Arriendos</label>
                                </div>
                            </div>

                            <!-- Planes para Servicios y Arriendos (derecha) -->
                            <div class="plan-options-container" id="servicios-arriendo-plans">
                                <div class="plan-options">
                                    <div class="plan-option">
                                        <div class="plan-option-content">
                                            <!-- Etiqueta eliminada -->
                                        </div>
                                        <input type="radio" name="selected_plan_servicios_arriendo" value="free" checked style="display: none;">
                                        <button type="button" class="plan-option-button" data-plan="free" data-type="servicios-arriendo">
                                            <span class="plan-name">Gratuita</span>
                                            <span class="plan-price">$0</span>
                                        </button>
                                    </div>
                                    <div class="plan-option">
                                        <div class="plan-option-content">
                                            <!-- Etiqueta eliminada -->
                                        </div>
                                        <input type="radio" name="selected_plan_servicios_arriendo" value="normal" style="display: none;">
                                        <button type="button" class="plan-option-button" data-plan="normal" data-type="servicios-arriendo">
                                            <span class="plan-name">Normal</span>
                                            <span class="plan-price">$1.990 + IVA</span>
                                        </button>
                                    </div>
                                    <div class="plan-option">
                                        <div class="plan-option-content">
                                            <!-- Etiqueta eliminada -->
                                        </div>
                                        <input type="radio" name="selected_plan_servicios_arriendo" value="premium" style="display: none;">
                                        <button type="button" class="plan-option-button" data-plan="premium" data-type="servicios-arriendo">
                                            <span class="plan-name">Premium</span>
                                            <span class="plan-price">$4.990 + IVA</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Estilos movidos al archivo register.css -->

                <!-- Pop-up de comparación de planes eliminado -->

                <!-- Pregunta sobre qué plan más te acomoda -->
                <div class="plan-preference-container">
                    <h3 class="plan-preference-title">¿Qué plan más te acomoda?</h3>
                    <div class="plan-preference-options">
                        <button type="button" class="plan-preference-option" data-plan="gratuito">Gratuita</button>
                        <button type="button" class="plan-preference-option" data-plan="normal">Normal</button>
                        <button type="button" class="plan-preference-option" data-plan="premium">Premium</button>
                    </div>
                    <!-- Campo oculto para guardar la preferencia de plan -->
                    <input type="hidden" name="plan_preferido" id="plan_preferido" value="">
                </div>

                <!-- Opciones de documento (condicionado) -->
                <div id="documento-options-container" class="documento-preference-container" style="display: none;">
                    <h3 class="plan-preference-title">Necesitarás:</h3>
                    <div class="plan-preference-options">
                        <button type="button" id="boleta-btn" class="plan-preference-option documento-option" data-documento="boleta">Boleta</button>
                        <button type="button" id="factura-btn" class="plan-preference-option documento-option" data-documento="factura">Factura</button>
                    </div>
                    <!-- Campo oculto para guardar el tipo de documento -->
                    <input type="hidden" name="tipo_documento" id="tipo_documento" value="">
                </div>

                <div class="auto-fill-container">
                    <button type="button" class="btn btn-auto-fill" id="auto-fill-step4-btn" onclick="autoFillStep4()">Auto-llenar datos</button>
                </div>

                <!-- Aceptación simple de términos -->
                <div class="simple-terms-container">
                    <div class="simple-terms-item">
                        <input type="checkbox" id="accept_terms" name="accept_terms" value="Aceptado">
                        <label for="accept_terms">Acepto Condiciones y Términos</label>
                    </div>
                    <div id="terms-error" style="color: #dc3545; font-size: 11px; margin-top: 3px; display: none;">
                        Debe aceptar los términos y condiciones para continuar
                    </div>
                </div>

                <div class="form-actions-double">
                    <button type="button" class="btn btn-prev">Anterior</button>
                    <button type="button" class="btn btn-next" id="submit-form" disabled>Finalizar</button>
                </div>
                
                <!-- Campos ocultos para el tipo de negocio -->
                <input type="hidden" name="tipo_negocio" id="hidden_tipo_negocio" value="venta">
                <input type="hidden" name="local_fisico" id="hidden_local_fisico" value="">
            </div>

            <!-- Los estilos se han movido al archivo register.css -->
            <!-- El script se ha movido al archivo register.js -->
        </form>

        <div class="login-link">
            ¿Ya tienes una cuenta? <a href="login.php">Inicia sesión aquí</a>
        </div>

        <div class="privacy-link">
            <a href="#">Privacidad</a>
        </div>
    </div>

    <!-- Popup para ingresar datos de factura -->
    <div id="factura-popup-overlay" class="popup-overlay">
        <div class="factura-popup">
            <div class="factura-popup-header">
                <h3>Datos para la Factura</h3>
                <button type="button" class="factura-popup-close">&times;</button>
            </div>
            <div class="factura-popup-content">
                <div class="auto-fill-container">
                    <button type="button" class="btn-auto-fill" id="auto-fill-factura-btn">Auto-llenar datos</button>
                </div>
                <form id="factura-form">
                    <div class="form-group">
                        <label for="empresa_nombre">Nombre de la empresa</label>
                        <input type="text" id="empresa_nombre" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="empresa_rut">RUT de la empresa</label>
                        <input type="text" id="empresa_rut" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="empresa_giro">Giro o Razón social</label>
                        <input type="text" id="empresa_giro" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="empresa_correo">Correo de la empresa</label>
                        <input type="email" id="empresa_correo" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="empresa_telefono">Teléfono de la empresa</label>
                        <input type="text" id="empresa_telefono" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="empresa_direccion">Dirección de la empresa</label>
                        <input type="text" id="empresa_direccion" class="form-control" required>
                    </div>
                    <div class="factura-popup-footer">
                        <button type="submit" class="btn-aceptar-factura">Aceptar</button>
                        <button type="button" class="btn-modificar-factura" style="display: none;">Modificar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Popup de confirmación -->
    <div id="confirmacion-popup-overlay" class="popup-overlay">
        <div class="confirmacion-popup">
            <div class="confirmacion-popup-header">
                <h3>Confirmación</h3>
            </div>
            <div class="confirmacion-popup-content">
                <p id="mensaje-factura">Al correo ingresado, en los próximos minutos llegarán los detalles de la transferencia para hacer efectiva la inscripción.</p>
                <p id="recordatorio-factura">Recuerde corroborar los datos de la empresa para la factura.</p>
                <div class="confirmacion-popup-footer">
                    <button type="button" class="btn-aceptar-confirmacion">Aceptar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de error para fecha de nacimiento -->
    <div id="fecha-error-popup-overlay" class="popup-overlay">
        <div class="error-popup">
            <div class="error-popup-header">
                <h3>Error en Fecha de Nacimiento</h3>
                <button type="button" class="error-popup-close">&times;</button>
            </div>
            <div class="error-popup-content">
                <div class="error-message-container">
                    <!-- SECURITY: Generic error messages to prevent information disclosure -->
                    <p id="formato-error-message">Formato de fecha inválido. Use DD/MM/AAAA</p>
                    <p id="numeros-error-message" style="display: none;">Solo se permiten números en el campo de fecha</p>
                    <p id="edad-error-message" style="display: none;">Debe ser mayor de 18 años para crear una cuenta</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de error para correo electrónico -->
    <div id="email-error-popup-overlay" class="popup-overlay">
        <div class="error-popup">
            <div class="error-popup-header">
                <h3>Error en Correo Electrónico</h3>
                <button type="button" class="email-error-popup-close">&times;</button>
            </div>
            <div class="error-popup-content">
                <div class="error-message-container">
                    <p id="email-error-message">El formato del correo electrónico no es válido</p>
                    <p>Por favor, ingrese un correo con formato válido (ejemplo: <EMAIL>)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de error para contraseñas que no coinciden -->
    <div id="password-match-error-popup-overlay" class="popup-overlay">
        <div class="error-popup">
            <div class="error-popup-header">
                <h3>Error en Contraseñas</h3>
                <button type="button" class="password-match-error-popup-close">&times;</button>
            </div>
            <div class="error-popup-content">
                <div class="error-message-container">
                    <p>Las contraseñas no coinciden</p>
                    <p>Por favor, asegúrese de que ambas contraseñas sean idénticas.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de error para usuario o correo duplicado -->
    <div id="duplicate-error-popup-overlay" class="popup-overlay">
        <div class="error-popup">
            <div class="error-popup-header">
                <h3>Error de Registro</h3>
                <button type="button" class="duplicate-error-popup-close">&times;</button>
            </div>
            <div class="error-popup-content">
                <div class="error-message-container">
                    <p id="duplicate-error-message">El nombre de usuario o correo electrónico ya está en uso.</p>
                    <p>Por favor, intente con otro valor.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popup de error para nombre de negocio duplicado -->
    <div id="business-name-error-popup-overlay" class="popup-overlay">
        <div class="error-popup">
            <div class="error-popup-header">
                <h3>Error de Registro</h3>
                <button type="button" class="business-name-error-popup-close">&times;</button>
            </div>
            <div class="error-popup-content">
                <div class="error-message-container">
                    <p>El nombre del negocio ya está en uso.</p>
                    <p>Por favor, intente con otro nombre para su negocio.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/register1.js"></script>
    <script src="../js/auto-fill-fix.js"></script>
    
    <!-- Auto-fill functions removed for security - SECURITY FIX -->
    <script>
    // SECURITY: Auto-fill functions have been disabled in production for security reasons
    // These functions contained hardcoded test data that could be exploited
    function autoFillStep1() {
        console.warn('Auto-fill functions are disabled for security reasons');
        return false;
    }

    // SECURITY: Auto-fill function disabled for security reasons
    function autoFillStep2() {
        console.warn('Auto-fill functions are disabled for security reasons');
        return false;
    }

    // SECURITY: Auto-fill function disabled for security reasons
    function autoFillStep3() {
        console.warn('Auto-fill functions are disabled for security reasons');
        return false;
    }

    // SECURITY: Auto-fill function disabled for security reasons
    function autoFillStep4() {
        console.warn('Auto-fill functions are disabled for security reasons');
        return false;
    }
    </script>

    <!-- Script específico para manejar el popup de bienvenida -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Obtener referencias a los elementos
            const bienvenidaPopup = document.getElementById('bienvenida-popup-overlay');
            const bienvenidaPopupClose = document.querySelector('.bienvenida-popup-close');
            const btnCerrarBienvenida = document.querySelector('.btn-cerrar-bienvenida');

            // Añadir eventos para cerrar el popup de bienvenida
            if (bienvenidaPopupClose) {
                bienvenidaPopupClose.addEventListener('click', function() {
                    bienvenidaPopup.style.display = 'none';
                    // Enviar el formulario después de cerrar el popup
                    document.getElementById('registerForm').submit();
                });
            }

            if (btnCerrarBienvenida) {
                btnCerrarBienvenida.addEventListener('click', function() {
                    bienvenidaPopup.style.display = 'none';
                    // Enviar el formulario después de cerrar el popup
                    document.getElementById('registerForm').submit();
                });
            }

            // Evitar que el popup se cierre al hacer clic fuera
            // Solo se cerrará con los botones específicos
            if (bienvenidaPopup) {
                bienvenidaPopup.addEventListener('click', function(e) {
                    if (e.target === this) {
                        // No hacemos nada, para evitar que se cierre al hacer clic fuera
                        e.stopPropagation();
                    }
                });
            }
        });
    </script>
<!-- Agregar los popups al final del body pero antes del cierre -->

<!-- Popup de Bienvenida -->
<div id="bienvenida-popup-overlay" class="popup-overlay">
    <div class="bienvenida-popup">
        <div class="bienvenida-popup-header">
            <h3>¡Bienvenido!</h3>
            <button type="button" class="bienvenida-popup-close">&times;</button>
        </div>
        <div class="bienvenida-popup-content">
            <p>¡Felicidades! Ya te encuentras registrado en Villarrica a un CLICK.</p>
            <p>Gracias por formar parte de nuestra comunidad.</p>
        </div>
        <div class="bienvenida-popup-footer">
            <button type="button" class="btn-cerrar-bienvenida">Cerrar</button>
        </div>
    </div>
</div>

<!-- Popups de términos y condiciones eliminados -->

<!-- Popups de confirmación eliminados -->

<!-- Los estilos se han movido al archivo register.css -->

<!-- Script de términos y condiciones eliminado -->

</body>
</html>
