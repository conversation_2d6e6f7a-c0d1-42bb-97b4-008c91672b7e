#!/bin/bash

# URL de tu webhook
WEBHOOK_URL="http://localhost/webhook-project/public/webhook.php"

# Datos de ejemplo (evento de pago completado)
JSON_DATA='{
    "tipo_evento": "pago_completado",
    "id_transaccion": "test-123",
    "monto": 100.50,
    "datos_adicionales": {
        "cliente": "Cliente Prueba",
        "metodo_pago": "tarjeta"
    }
}'

# Clave secreta (debe coincidir con la configurada en webhook.php)
SECRET="tu_clave_secreta"

# Calcular firma HMAC-SHA256
SIGNATURE=$(echo -n "$JSON_DATA" | openssl dgst -sha256 -hmac "$SECRET" | cut -d ' ' -f 2)

# Enviar solicitud POST con curl
curl -X POST "$WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "X-Webhook-Signature: $SIGNATURE" \
  -d "$JSON_DATA" \
  -v
