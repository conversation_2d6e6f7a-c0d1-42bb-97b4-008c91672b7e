// Función para mostrar notificaciones
function showNotification(message, type = 'success') {
    // Si es una notificación de éxito de actualización de producto, no mostrarla
    if (type === 'success' && (
        message.includes('actualizado correctamente') ||
        message.includes('Producto actualizado')
    )) {
        console.log('Notificación de actualización suprimida:', message);
        return;
    }

    // Crear el elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;

    // Agregar la notificación al DOM
    document.body.appendChild(notification);

    // Mostrar la notificación con animación
    setTimeout(() => notification.classList.add('show'), 100);

    // Remover la notificación después de 3 segundos
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Exportar la función
export {
    showNotification
};