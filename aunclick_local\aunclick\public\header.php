<?php
// Asegurar que no hay salida antes
ob_start();

require_once '../config/SessionManager.php';
require_once '../config/config.php';

// Log para verificar el contexto del header
error_log("Header.php - BASE_URL: " . BASE_URL);
error_log("Header.php - Session ID actual: " . (session_id() ?: 'no hay sesión'));

$sessionManager = SessionManager::getInstance();

// Asegurar que tenemos una sesión válida
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

$sessionManager->updateCookiePath(BASE_URL);
$isAuthenticated = $sessionManager->validateSession();

error_log("Header.php - Estado de autenticación: " . ($isAuthenticated ? 'true' : 'false'));
error_log("Header.php - Datos de sesión: " . print_r($_SESSION, true));

// Verificar que el archivo CSS existe
$cssPath = '../css/header.css';
$absoluteCssPath = __DIR__ . '/' . $cssPath;
if (!file_exists($absoluteCssPath)) {
    error_log("El archivo CSS no existe en la ruta: " . $absoluteCssPath);
}

// Limpiar cualquier salida pendiente antes de enviar headers
if (headers_sent($file, $line)) {
    error_log("Headers ya enviados en $file:$line");
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Villarrica Click</title>
    <link rel="stylesheet" href="<?php echo $cssPath; ?>">
    <!-- Agregar Font Awesome para los iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
<header class="header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <div class="logo-text">
                    <span class="city">Villarrica</span>
                    <span class="tagline">a un CLICK</span>
                </div>
            </div>
            <div class="search-bar">
                <input type="text" placeholder="Buscar productos y +...">
                <button class="search-button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </button>
            </div>
            <div class="promo-banner">
                <img src="https://http2.mlstatic.com/D_NQ_957153-MLA69318147677_052023-OO.webp" alt="Suscríbete al nivel 6">
            </div>
        </div>
    </div>
    <nav class="header-nav">
        <div class="container">
            <div class="nav-left">
                <ul>
                    <li class="location-item">
                        <div class="location-icons">
                            <i class="fas fa-map"></i>
                            <i class="fas fa-location-dot"></i>
                        </div>
                        <span>Busqueda por ubicación</span>
                    </li>
                </ul>
            </div>
            <div class="nav-center">
                <ul class="nav-categories">
                    <li class="category-dropdown">
                        <a href="#" class="categories-trigger">Categorías</a>
                        <div class="dropdown-content categories-modal">
                            <div class="modal-header">
                                <h3 class="modal-title">Categorías</h3>
                                <button class="modal-close">×</button>
                            </div>
                            <ul class="categories-list">
                                <li><i class="fas fa-laptop"></i><span>Tecnología y Electrónica</span></li>
                                <li><i class="fas fa-tshirt"></i><span>Moda y Accesorios</span></li>
                                <li><i class="fas fa-home"></i><span>Hogar y Decoración</span></li>
                                <li><i class="fas fa-dumbbell"></i><span>Deportes y Fitness</span></li>
                                <li><i class="fas fa-car"></i><span>Automotriz</span></li>
                                <li><i class="fas fa-utensils"></i><span>Gastronomía</span></li>
                                <li><i class="fas fa-book"></i><span>Libros y Educación</span></li>
                                <li><i class="fas fa-gamepad"></i><span>Juegos y Entretenimiento</span></li>
                                <li><i class="fas fa-paint-brush"></i><span>Arte y Manualidades</span></li>
                                <li><i class="fas fa-paw"></i><span>Mascotas</span></li>
                                <li><i class="fas fa-gift"></i><span>Regalos y Ocasiones</span></li>
                                <li><i class="fas fa-tools"></i><span>Servicios Profesionales</span></li>
                            </ul>
                        </div>
                    </li>
                    <li class="services-dropdown">
                        <a href="#" class="services-trigger">Servicios</a>
                        <div class="dropdown-content services-modal">
                            <div class="modal-header">
                                <h3 class="modal-title">Servicios</h3>
                                <button class="modal-close">×</button>
                            </div>
                            <ul class="services-list">
                                <li><i class="fas fa-wrench"></i><span>Reparación y Mantenimiento</span></li>
                                <li><i class="fas fa-truck"></i><span>Transporte y Mudanzas</span></li>
                                <li><i class="fas fa-paint-roller"></i><span>Pintura y Decoración</span></li>
                                <li><i class="fas fa-broom"></i><span>Limpieza y Aseo</span></li>
                                <li><i class="fas fa-cut"></i><span>Peluquería y Belleza</span></li>
                                <li><i class="fas fa-laptop-code"></i><span>Servicios Informáticos</span></li>
                                <li><i class="fas fa-graduation-cap"></i><span>Clases Particulares</span></li>
                                <li><i class="fas fa-camera"></i><span>Fotografía y Video</span></li>
                                <li><i class="fas fa-guitar"></i><span>Música y Eventos</span></li>
                                <li><i class="fas fa-hammer"></i><span>Construcción</span></li>
                                <li><i class="fas fa-heartbeat"></i><span>Salud y Bienestar</span></li>
                                <li><i class="fas fa-car-mechanic"></i><span>Mecánica Automotriz</span></li>
                            </ul>
                        </div>
                    </li>
                    <li class="rentals-dropdown">
                        <a href="#" class="rentals-trigger">Arriendos</a>
                        <div class="dropdown-content rentals-modal">
                            <div class="modal-header">
                                <h3 class="modal-title">Arriendos</h3>
                                <button class="modal-close">×</button>
                            </div>
                            <ul class="rentals-list">
                                <li><i class="fas fa-home"></i><span>Casas y Departamentos</span></li>
                                <li><i class="fas fa-building"></i><span>Oficinas y Locales</span></li>
                                <li><i class="fas fa-warehouse"></i><span>Bodegas</span></li>
                                <li><i class="fas fa-car"></i><span>Vehículos</span></li>
                                <li><i class="fas fa-tools"></i><span>Herramientas</span></li>
                                <li><i class="fas fa-camera"></i><span>Equipos Audiovisuales</span></li>
                                <li><i class="fas fa-umbrella-beach"></i><span>Equipamiento Turístico</span></li>
                                <li><i class="fas fa-chair"></i><span>Mobiliario para Eventos</span></li>
                                <li><i class="fas fa-truck-loading"></i><span>Maquinaria Pesada</span></li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="nav-right">
                <ul class="nav-account">
                    <?php if ($isAuthenticated): ?>
                        <li class="user-menu-dropdown">
                            <a href="#" class="user-menu-trigger">
                                <i class="fas fa-user-circle"></i>
                                <span><?php echo htmlspecialchars($_SESSION['username']); ?></span>
                            </a>
                            <div class="dropdown-content user-menu-modal">
                                <ul class="user-menu-list">
                                    <li><a href="<?php echo BASE_URL; ?>/public/profile.php"><i class="fas fa-user"></i>Mi Perfil</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/public/admin.php"><i class="fas fa-tachometer-alt"></i>Dashboard</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/public/admin_products.php"><i class="fas fa-box"></i>Administrar Productos</a></li>
                                    <li><a href="<?php echo BASE_URL; ?>/public/logout.php"><i class="fas fa-sign-out-alt"></i>Cerrar Sesión</a></li>
                                </ul>
                            </div>
                        </li>
                    <?php else: ?>
                        <li><a href="<?php echo BASE_URL; ?>/public/register.php"><i class="fas fa-user-plus"></i><span>Crea tu cuenta</span></a></li>
                        <li><a href="<?php echo BASE_URL; ?>/public/login.php"><i class="fas fa-sign-in-alt"></i><span>Ingresa</span></a></li>
                    <?php endif; ?>
                    <li><a href="#"><i class="fas fa-envelope"></i><span>Contacto</span></a></li>
                </ul>
                <div class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </nav>
</header>


<div class="mobile-menu" id="mobileMenu">
    <div class="mobile-menu-content">
        <div class="mobile-menu-header">
            <h2>Menú</h2>
            <button class="mobile-menu-close" id="mobileMenuClose">&times;</button>
        </div>
        <ul class="mobile-menu-list">
            <?php if ($isAuthenticated): ?>
                <li><a href="<?php echo BASE_URL; ?>/public/admin.php"><i class="fas fa-tachometer-alt"></i> <span>Dashboard</span></a></li>
                <li><a href="<?php echo BASE_URL; ?>/public/admin_products.php"><i class="fas fa-box"></i> <span>Administrar Productos</span></a></li>
                <li><a href="<?php echo BASE_URL; ?>/public/logout.php"><i class="fas fa-sign-out-alt"></i> <span>Cerrar Sesión</span></a></li>
            <?php else: ?>
                <li><a href="<?php echo BASE_URL; ?>/public/register.php"><i class="fas fa-user-plus"></i> <span>Crea tu cuenta</span></a></li>
                <li><a href="<?php echo BASE_URL; ?>/public/login.php"><i class="fas fa-sign-in-alt"></i> <span>Ingresa</span></a></li>
            <?php endif; ?>
            <li><a href="#"><i class="fas fa-envelope"></i> <span>Contacto</span></a></li>
        </ul>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Menú de usuario
        const userMenuTrigger = document.querySelector('.user-menu-trigger');
        const userMenuDropdown = document.querySelector('.user-menu-modal');

        if (userMenuTrigger && userMenuDropdown) {
            userMenuTrigger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userMenuDropdown.classList.toggle('show');
            });

            document.addEventListener('click', function(e) {
                if (!userMenuTrigger.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                    userMenuDropdown.classList.remove('show');
                }
            });
        }

        // Menú móvil - Usando IDs para mayor especificidad
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.getElementById('mobileMenu');
        const closeButton = document.getElementById('mobileMenuClose');
        
        if (menuToggle && mobileMenu) {
            // Agregamos manipulación directa del DOM para debugging
            menuToggle.addEventListener('click', function() {
                console.log('Menú móvil toggle clickeado');
                mobileMenu.style.display = 'block';
                setTimeout(function() {
                    mobileMenu.classList.add('active');
                }, 10); // Pequeño retraso para asegurar que la transición funcione
                document.body.style.overflow = 'hidden';
            });
            
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    console.log('Botón cerrar clickeado');
                    mobileMenu.classList.remove('active');
                    setTimeout(function() {
                        mobileMenu.style.display = 'none';
                    }, 300); // Tiempo para que termine la transición
                    document.body.style.overflow = '';
                });
            }
            
            // Cierra el menú al hacer clic fuera
            mobileMenu.addEventListener('click', function(e) {
                if (e.target === mobileMenu) {
                    closeButton.click();
                }
            });
        }
        
        // // DEBUG: Comprobación de elementos
        // console.log('Menu toggle exists:', !!menuToggle);
        // console.log('Mobile menu exists:', !!mobileMenu);
        // console.log('Close button exists:', !!closeButton);

        // // DEBUG: Comprobación de clases CSS
        // console.log('Mobile menu classes:', mobileMenu.classList);
        // console.log('Mobile menu styles:', window.getComputedStyle(mobileMenu));
    });
</script>
</body>
</html>


