<?php
// Asegurar que no haya output antes de los headers
if (ob_get_level()) ob_end_clean();

// Habilitar registro de errores en un archivo
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', $_SERVER['DOCUMENT_ROOT'] . '/projects/villarrica_click/error_log.txt');
error_log("=== Inicio de solicitud save_negocio.php - " . date('Y-m-d H:i:s') . " ===");

// Registrar detalles de la solicitud
error_log("Método HTTP: " . $_SERVER['REQUEST_METHOD']);
error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'No especificado'));

// Configurar headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true'); // Permitir credenciales en CORS

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../../config/config.php';
require_once '../../../config/SessionManager.php';

// Inicializar el gestor de sesiones y verificar estado de la sesión
$sessionManager = SessionManager::getInstance();

// Registrar información de la sesión para depuración
error_log("Session ID al inicio: " . session_id());
error_log("Estado de la sesión: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVA' : 'NO ACTIVA'));

// Asegurar que la sesión está activa
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
    error_log("Sesión inicializada, nuevo ID: " . session_id());
}

// Verificar autenticación
if (!$sessionManager->isLoggedIn()) {
    error_log("save_negocio.php - Usuario no autenticado");
    error_log("Session data: " . print_r($_SESSION, true));
    echo json_encode(['success' => false, 'message' => 'No autorizado - Sesión no válida']);
    exit;
}

// Obtener el ID del usuario de la sesión
if (!isset($_SESSION['user_id'])) {
    error_log("save_negocio.php - user_id no está definido en la sesión");
    error_log("Session data: " . print_r($_SESSION, true));
    echo json_encode(['success' => false, 'message' => 'No autorizado - ID de usuario no encontrado']);
    exit;
}

$user_id = $_SESSION['user_id'];
error_log("save_negocio.php - user_id de la sesión: " . $user_id);

try {
    // Verificar el método HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        error_log("save_negocio.php - Método no permitido: " . $_SERVER['REQUEST_METHOD']);
        throw new Exception('Método no permitido: ' . $_SERVER['REQUEST_METHOD']);
    }

    // Obtener y validar los datos
    $raw_input = file_get_contents('php://input');
    error_log("save_negocio.php - Datos de entrada: " . $raw_input);
    
    $data = json_decode($raw_input, true);
    if (!$data) {
        error_log("save_negocio.php - Error decodificando JSON: " . json_last_error_msg());
        throw new Exception('Datos inválidos: ' . json_last_error_msg());
    }

    error_log("save_negocio.php - Datos decodificados: " . print_r($data, true));

    // Validación extendida de campos requeridos
    $required_fields = [
        'negocio_id' => 'ID del negocio',
        'nombre' => 'Nombre del negocio',
        'descripcion' => 'Descripción',
        'direccion' => 'Dirección',
        'ciudad' => 'Ciudad',
        'region' => 'Región',
        'telefono' => 'Teléfono',
        'email' => 'Email'
    ];

    $missing_fields = [];
    $invalid_fields = [];
    
    foreach ($required_fields as $field => $label) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            $missing_fields[] = $label;
            error_log("save_negocio.php - Campo faltante: {$field}");
        }
    }

    // Validaciones específicas de tipo de dato
    if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $invalid_fields[] = 'El email debe tener un formato válido';
    }

    // Si hay campos faltantes o inválidos, lanzar excepción
    if (!empty($missing_fields) || !empty($invalid_fields)) {
        $error_message = '';
        if (!empty($missing_fields)) {
            $error_message .= "Campos requeridos faltantes: " . implode(', ', $missing_fields) . ". ";
        }
        if (!empty($invalid_fields)) {
            $error_message .= "Campos inválidos: " . implode(', ', $invalid_fields);
        }
        error_log("save_negocio.php - Validación fallida: " . $error_message);
        throw new Exception($error_message);
    }

    // Limpiar y preparar los datos
    $negocio_id = intval($data['negocio_id']);
    $nombre = $conn->real_escape_string($data['nombre']);
    $descripcion = $conn->real_escape_string($data['descripcion']);
    $direccion = $conn->real_escape_string($data['direccion']);
    $ciudad = $conn->real_escape_string($data['ciudad']);
    $region = $conn->real_escape_string($data['region']);
    $telefono = $conn->real_escape_string($data['telefono']);
    $email = $conn->real_escape_string($data['email']);
    $facebook = $conn->real_escape_string($data['facebook'] ?? '');
    $twitter = $conn->real_escape_string($data['twitter'] ?? '');
    $instagram = $conn->real_escape_string($data['instagram'] ?? '');
    $youtube = $conn->real_escape_string($data['youtube'] ?? '');
    $whatsapp = $conn->real_escape_string($data['whatsapp'] ?? '');
    $latitud = isset($data['latitud']) ? floatval($data['latitud']) : null;
    $longitud = isset($data['longitud']) ? floatval($data['longitud']) : null;
    $horario = $conn->real_escape_string($data['horario'] ?? '');
    
    error_log("Datos preparados para actualización:");
    error_log(print_r([
        'negocio_id' => $negocio_id,
        'nombre' => $nombre,
        'descripcion' => $descripcion,
        'direccion' => $direccion,
        'ciudad' => $ciudad,
        'region' => $region,
        'telefono' => $telefono,
        'email' => $email
    ], true));

    // Verificar que el negocio existe y pertenece al usuario
    $check_sql = "SELECT id FROM tb_negocios WHERE id = ? AND usuario_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    if (!$check_stmt) {
        error_log("Error preparando la consulta de verificación: " . $conn->error);
        throw new Exception('Error preparando la consulta: ' . $conn->error);
    }

    $check_stmt->bind_param("ii", $negocio_id, $user_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        error_log("Negocio no encontrado o no pertenece al usuario: ID {$negocio_id}, Usuario {$user_id}");
        throw new Exception('No tienes permiso para editar este negocio');
    }

    // Construir la consulta SQL para actualizar
    $sql = "UPDATE tb_negocios SET 
        nombre = ?, 
        descripcion = ?, 
        direccion = ?, 
        ciudad = ?, 
        region = ?, 
        telefono = ?, 
        email = ?, 
        facebook = ?, 
        twitter = ?, 
        instagram = ?, 
        youtube = ?, 
        whatsapp = ?, 
        latitud = ?, 
        longitud = ?, 
        horario = ?, 
        fecha_actualizacion = NOW() 
    WHERE id = ? AND usuario_id = ?";
    
    error_log("Consulta SQL preparada");
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparando la consulta: " . $conn->error);
        throw new Exception('Error preparando la consulta: ' . $conn->error);
    }

    $stmt->bind_param(
        "ssssssssssssddsii",
        $nombre, $descripcion, $direccion, $ciudad, $region,
        $telefono, $email, $facebook, $twitter, $instagram,
        $youtube, $whatsapp, $latitud, $longitud, $horario,
        $negocio_id, $user_id
    );
    
    error_log("Ejecutando consulta preparada");
    if (!$stmt->execute()) {
        error_log("Error ejecutando la consulta: " . $stmt->error);
        throw new Exception('Error al actualizar el negocio: ' . $stmt->error);
    }

    error_log("Negocio actualizado con ID: {$negocio_id}");

    $response = [
        'success' => true,
        'message' => 'Información del negocio actualizada exitosamente',
        'negocio_id' => $negocio_id
    ];
    error_log("Respuesta: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    error_log("EXCEPCIÓN: " . $e->getMessage());
    error_log("Trace: " . $e->getTraceAsString());
    
    http_response_code(400);
    $error_response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
    error_log("Respuesta de error: " . json_encode($error_response));
    echo json_encode($error_response);
}

// Registrar fin de la solicitud
error_log("=== Fin de solicitud save_negocio.php - " . date('Y-m-d H:i:s') . " ===");
