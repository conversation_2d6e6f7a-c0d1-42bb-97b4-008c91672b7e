<?php
require_once __DIR__ . '/WebhookHandler.php';
require_once __DIR__ . '/Logger.php';

// Incluir el autoloader de Composer
$autoloadFile = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoloadFile)) {
    require_once $autoloadFile;
} else {
    echo "Error: No se encontró el autoloader de Composer. Por favor, ejecuta 'composer install'\n";
    exit(1);
}

// Verificar si Ratchet está instalado
if (!class_exists('\Ratchet\Server\IoServer')) {
    echo "Error: Ratchet no está instalado correctamente. Por favor, ejecuta 'composer require cboden/ratchet'\n";
    exit(1);
}

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;

/**
 * Clase WebSocketServer
 * 
 * Implementa un servidor WebSocket simple para recibir y procesar eventos
 */
class WebSocketServer implements MessageComponentInterface {
    protected $clients;
    protected $webhookHandler;
    protected $logger;

    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->webhookHandler = new WebhookHandler();
        $this->logger = new Logger();
        
        echo "Servidor WebSocket iniciado. Esperando conexiones...\n";
    }

    public function onOpen(ConnectionInterface $conn) {
        // Almacenar la nueva conexión
        $this->clients->attach($conn);
        
        echo "Nueva conexión: {$conn->resourceId}\n";
        
        // Registrar evento de conexión
        $this->logger->registrarEvento('websocket_conexion', [
            'id' => $conn->resourceId,
            'ip' => $conn->remoteAddress
        ]);
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        echo "Mensaje recibido de {$from->resourceId}: $msg\n";
        
        // Intentar decodificar el mensaje JSON
        $data = json_decode($msg, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "Error: Mensaje JSON inválido\n";
            $from->send(json_encode([
                'error' => 'Formato JSON inválido'
            ]));
            return;
        }
        
        // Registrar evento recibido
        $this->logger->registrarEvento('websocket_mensaje', [
            'id_conexion' => $from->resourceId,
            'datos' => $data
        ]);
        
        // Procesar el evento si tiene un tipo definido
        if (isset($data['tipo_evento'])) {
            try {
                $tipoEvento = $data['tipo_evento'];
                $resultado = $this->webhookHandler->procesarEvento($tipoEvento, $data);
                
                // Enviar respuesta de éxito
                $from->send(json_encode([
                    'status' => 'success',
                    'result' => $resultado
                ]));
                
            } catch (\Exception $e) {
                // Registrar error
                $this->logger->registrarEvento('error', [
                    'mensaje' => $e->getMessage(),
                    'tipo_evento' => $data['tipo_evento'] ?? 'desconocido'
                ]);
                
                // Enviar respuesta de error
                $from->send(json_encode([
                    'error' => $e->getMessage()
                ]));
            }
        } else {
            // Si no tiene tipo de evento, simplemente hacer eco del mensaje
            $from->send(json_encode([
                'echo' => $data,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
        }
    }

    public function onClose(ConnectionInterface $conn) {
        // Eliminar la conexión cerrada
        $this->clients->detach($conn);
        
        echo "Conexión {$conn->resourceId} cerrada\n";
        
        // Registrar evento de desconexión
        $this->logger->registrarEvento('websocket_desconexion', [
            'id' => $conn->resourceId
        ]);
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "Error: {$e->getMessage()}\n";
        
        // Registrar error
        $this->logger->registrarEvento('error', [
            'mensaje' => $e->getMessage(),
            'id_conexion' => $conn->resourceId
        ]);
        
        // Cerrar la conexión
        $conn->close();
    }
}

// Si este archivo se ejecuta directamente, iniciar el servidor
if (realpath(__FILE__) === realpath($_SERVER['SCRIPT_FILENAME'])) {
    // Puerto para el servidor WebSocket
    $port = 8080;
    
    echo "Iniciando servidor WebSocket en el puerto $port...\n";
    
    $server = IoServer::factory(
        new HttpServer(
            new WsServer(
                new WebSocketServer()
            )
        ),
        $port
    );
    
    echo "Servidor WebSocket iniciado en ws://localhost:$port\n";
    echo "Presiona Ctrl+C para detener el servidor\n";
    
    $server->run();
}
