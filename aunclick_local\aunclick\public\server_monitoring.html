<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Importación de Chart.js para gráficos -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>Monitoreo del Servidor - Villarrica a un CLICK</title>
    <link rel="stylesheet" href="../css/variables.css">
    <link rel="stylesheet" href="../css/layout.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Barra lateral -->
        <aside class="sidebar" id="sidebar">
            <button class="nav-link menu-toggle" id="aside-toggle" type="button" aria-label="Toggle menu">
                <i class="fas fa-bars" id="toggle-icon"></i>
            </button>
            <div class="sidebar-header">
                <div class="sidebar-logo">Villarrica a un CLICK</div>
                <div class="sidebar-subtitle">Monitoreo del Servidor</div>
            </div>
        </aside>

        <!-- Contenido principal -->
        <main class="main-content">
            <div class="container">
                <div class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-server"></i>
                            Monitoreo en Tiempo Real
                        </h2>
                        <div class="section-actions">
                            <button class="btn btn-secondary">
                                <i class="fas fa-download"></i>
                                Exportar Datos
                            </button>
                            <button class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i>
                                Actualizar
                            </button>
                        </div>
                    </div>

                    <div class="section-body">
                        <!-- Filtros de tiempo -->
                        <div class="time-filters">
                            <span>Mostrar datos de los últimos:</span>
                            <button class="filter-btn" data-time="30m">30 minutos</button>
                            <button class="filter-btn active" data-time="1h">1 hora</button>
                            <button class="filter-btn" data-time="6h">6 horas</button>
                            <button class="filter-btn" data-time="12h">12 horas</button>
                            <button class="filter-btn" data-time="1d">1 día</button>
                            <button class="filter-btn" data-time="3d">3 días</button>
                            <button class="filter-btn" data-time="7d">7 días</button>
                            <button class="filter-btn" data-time="30d">30 días</button>
                        </div>

                        <!-- Contenedor de gráficos -->
                        <div class="charts-container server-charts">
                            <div class="chart-card">
                                <h3>Solicitudes fallidas</h3>
                                <div class="chart-container">
                                    <canvas id="failedRequestsChart"></canvas>
                                </div>
                            </div>
                            <div class="chart-card">
                                <h3>Tiempo de respuesta del servidor</h3>
                                <div class="chart-container">
                                    <canvas id="responseTimeChart"></canvas>
                                </div>
                            </div>
                            <div class="chart-card">
                                <h3>Solicitudes al servidor</h3>
                                <div class="chart-container">
                                    <canvas id="serverRequestsChart"></canvas>
                                </div>
                            </div>
                            <div class="chart-card">
                                <h3>Disponibilidad</h3>
                                <div class="chart-container">
                                    <canvas id="availabilityChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../js/server_monitoring.js"></script>
</body>
</html>