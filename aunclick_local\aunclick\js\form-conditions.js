/**
 * form-conditions.js
 * Script para manejar todas las condiciones de validación del formulario de registro
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando condiciones de validación del formulario');

    // Inicializar todas las condiciones
    initFormConditions();

    /**
     * Inicializa todas las condiciones de validación del formulario
     */
    function initFormConditions() {
        // Inicializar control de botones para todos los pasos
        setupButtonControls();

        // Condiciones para el paso 1
        setupStep1Conditions();

        // Condiciones para el paso 2
        setupStep2Conditions();

        // Condiciones para el paso 3
        setupStep3Conditions();

        // Condiciones para el paso 4
        setupStep4Conditions();

        // Condiciones para el paso 5
        setupStep5Conditions();
    }

    /**
     * Configura el control de botones para todos los pasos
     */
    function setupButtonControls() {
        // Deshabilitar todos los botones "Siguiente" inicialmente
        const nextButtons = document.querySelectorAll('.btn-next');
        nextButtons.forEach(button => {
            if (button.id !== 'step1-next') { // El paso 1 ya tiene su propia lógica
                button.disabled = true;
                button.classList.add('disabled');
            }
        });

        // Configurar validación en tiempo real para cada paso
        setupStep1ButtonControl();
        setupStep2ButtonControl();
        setupStep3ButtonControl();
        setupStep4ButtonControl();
    }

    /**
     * Configura el control del botón para el paso 1
     * (Ya implementado en el código existente)
     */
    function setupStep1ButtonControl() {
        // Esta función está vacía porque el paso 1 ya tiene su propia lógica
    }

    /**
     * Configura el control del botón para el paso 2
     */
    function setupStep2ButtonControl() {
        const step2NextBtn = document.getElementById('step2-next');
        if (!step2NextBtn) return;

        // Validar campos cuando cambian
        const step2Fields = document.querySelectorAll('#step2 input, #step2 select');
        step2Fields.forEach(field => {
            field.addEventListener('input', updateStep2Button);
            field.addEventListener('change', updateStep2Button);
            field.addEventListener('blur', updateStep2Button);
        });

        // Actualizar estado inicial del botón
        updateStep2Button();
    }

    /**
     * Configura el control del botón para el paso 3
     */
    function setupStep3ButtonControl() {
        const step3NextBtn = document.getElementById('step3-next');
        if (!step3NextBtn) return;

        // Validar campos cuando cambian
        const step3Fields = document.querySelectorAll('#step3 input, #step3 select, #step3 textarea');
        step3Fields.forEach(field => {
            field.addEventListener('input', updateStep3Button);
            field.addEventListener('change', updateStep3Button);
            field.addEventListener('blur', updateStep3Button);
        });

        // Actualizar estado inicial del botón
        updateStep3Button();
    }

    /**
     * Configura el control del botón para el paso 4
     */
    function setupStep4ButtonControl() {
        // Para productos
        const submitFormBtn = document.getElementById('submit-form');
        if (submitFormBtn) {
            // Validar campos cuando cambian
            const step4ProductosFields = document.querySelectorAll('#step4-productos input');
            step4ProductosFields.forEach(field => {
                field.addEventListener('change', updateStep4ProductosButton);
            });

            // Actualizar estado inicial del botón
            updateStep4ProductosButton();
        }

        // Para servicios
        const submitFormServiciosBtn = document.getElementById('submit-form-servicios');
        if (submitFormServiciosBtn) {
            // Validar campos cuando cambian
            const step4ServiciosFields = document.querySelectorAll('#step4-servicios input');
            step4ServiciosFields.forEach(field => {
                field.addEventListener('change', updateStep4ServiciosButton);
            });

            // Actualizar estado inicial del botón
            updateStep4ServiciosButton();
        }
    }

    /**
     * Configura las condiciones para el paso 1
     */
    function setupStep1Conditions() {
        // Validación de nombres y apellidos
        const nombresInput = document.getElementById('nombres');
        const apellidosInput = document.getElementById('apellidos');

        if (nombresInput) {
            nombresInput.addEventListener('blur', function() {
                validateRequiredField(this);
            });
        }

        if (apellidosInput) {
            apellidosInput.addEventListener('blur', function() {
                validateApellidos(this);
            });
        }

        // Validación de RUT
        const rutInput = document.getElementById('rut');
        if (rutInput) {
            // Permitir solo números y la letra K
            rutInput.addEventListener('input', function() {
                // Convertir a mayúsculas para normalizar la K
                this.value = this.value.toUpperCase();

                // Reemplazar cualquier carácter que no sea número o K
                let newValue = '';
                for (let i = 0; i < this.value.length; i++) {
                    const char = this.value.charAt(i);
                    // Permitir números en cualquier posición
                    if (/^\d$/.test(char)) {
                        newValue += char;
                    }
                    // Permitir K solo en la última posición
                    else if (char === 'K' && i === this.value.length - 1) {
                        newValue += char;
                    }
                }
                this.value = newValue;

                // Limitar a 9 caracteres
                if (this.value.length > 9) {
                    this.value = this.value.slice(0, 9);
                }
            });

            rutInput.addEventListener('blur', function() {
                validateRut(this);
            });
        }

        // Validación de fecha de nacimiento
        const fechaNacimientoInput = document.getElementById('fechaNacimiento');
        if (fechaNacimientoInput) {
            fechaNacimientoInput.addEventListener('blur', function() {
                validateFechaNacimiento(this);
            });
        }

        // Validación de teléfono
        const telefonoInput = document.getElementById('telefono');
        if (telefonoInput) {
            telefonoInput.addEventListener('blur', function() {
                validateTelefono(this);
            });
        }

        // Validación de región y comuna
        const regionSelect = document.getElementById('region');
        const comunaSelect = document.getElementById('comuna');

        if (regionSelect) {
            regionSelect.addEventListener('change', function() {
                validateRequiredField(this);
                // La actualización de comunas se maneja en regiones-comunas.js
            });
        }

        if (comunaSelect) {
            comunaSelect.addEventListener('change', function() {
                validateRequiredField(this);
            });
        }

        // Validación de dirección
        const direccionInput = document.getElementById('direccion');
        if (direccionInput) {
            direccionInput.addEventListener('blur', function() {
                validateRequiredField(this);
            });
        }
    }

    /**
     * Configura las condiciones para el paso 2
     */
    function setupStep2Conditions() {
        // Validación de nombre de usuario
        const usernameInput = document.getElementById('username');
        if (usernameInput) {
            usernameInput.addEventListener('input', function() {
                validateRequiredField(this);
            });

            usernameInput.addEventListener('blur', function() {
                validateRequiredField(this);
            });
        }

        // Validación de correo electrónico
        const emailInput = document.getElementById('email');
        if (emailInput) {
            emailInput.addEventListener('input', function() {
                validateEmail(this);
            });

            emailInput.addEventListener('blur', function() {
                validateEmail(this);
            });
        }

        // Validación de correo de respaldo (opcional)
        const backupEmailInput = document.getElementById('backup_email');
        if (backupEmailInput) {
            backupEmailInput.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    validateEmail(this);
                } else {
                    // Es opcional, así que quitamos cualquier error
                    this.classList.remove('error');
                }
            });

            backupEmailInput.addEventListener('blur', function() {
                if (this.value.trim() !== '') {
                    validateEmail(this);
                } else {
                    // Es opcional, así que quitamos cualquier error
                    this.classList.remove('error');
                }
            });
        }

        // Configurar botones de visibilidad de contraseña
        setupPasswordVisibilityToggles();

        // Validación de contraseña (exactamente 8 caracteres, al menos una mayúscula y un número)
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            // Validar cuando el usuario sale del campo
            passwordInput.addEventListener('blur', function() {
                // Validar requisitos de la contraseña
                validatePassword(this);

                // Si hay confirmación de contraseña y no está vacía, validar coincidencia
                const confirmPasswordInput = document.getElementById('confirm_password');
                if (confirmPasswordInput && confirmPasswordInput.value.trim() !== '') {
                    validatePasswordsMatch(this, confirmPasswordInput);
                }
            });

            // Quitar errores mientras el usuario está escribiendo
            passwordInput.addEventListener('input', function() {
                this.classList.remove('error');

                // Si hay confirmación de contraseña y no está vacía, validar coincidencia
                const confirmPasswordInput = document.getElementById('confirm_password');
                if (confirmPasswordInput && confirmPasswordInput.value.trim() !== '') {
                    // Verificar coincidencia sin mostrar pop-up
                    if (this.value.trim() === confirmPasswordInput.value.trim()) {
                        confirmPasswordInput.classList.remove('error');
                    }
                }
            });
        }

        // Validación de confirmación de contraseña
        const confirmPasswordInput = document.getElementById('confirm_password');
        if (confirmPasswordInput && passwordInput) {
            // Validar cuando el usuario sale del campo
            confirmPasswordInput.addEventListener('blur', function() {
                // Validar coincidencia de contraseñas
                validatePasswordsMatch(passwordInput, this);
            });

            // Validar coincidencia mientras el usuario escribe
            confirmPasswordInput.addEventListener('input', function() {
                this.classList.remove('error');

                // Verificar coincidencia sin mostrar pop-up
                if (passwordInput.value.trim() === this.value.trim()) {
                    this.classList.remove('error');
                }
            });
        }

        // Validación de local físico
        const localFisicoRadios = document.getElementsByName('local_fisico');
        localFisicoRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                validateLocalFisico();
            });
        });
    }

    /**
     * Valida un correo electrónico
     * @param {HTMLElement} field - Campo de correo
     * @returns {boolean} - True si el campo es válido
     */
    function validateEmail(field) {
        const value = field.value.trim();

        // Si es el campo de correo de respaldo y está vacío, es válido (es opcional)
        if (field.id === 'backup_email' && !value) {
            field.classList.remove('error');
            return true;
        }

        // Para el correo principal, es requerido
        if (!value) {
            field.classList.add('error');
            return false;
        }

        // Expresión regular para validar correo electrónico
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            field.classList.add('error');

            // Mostrar popup de error para formato inválido
            if (field.id === 'email') {
                window.showErrorPopup(
                    'Formato de correo inválido',
                    'Por favor, ingrese un correo electrónico válido.',
                    ['El formato debe ser: <EMAIL>']
                );
            }

            return false;
        } else {
            field.classList.remove('error');
            return true;
        }
    }

    /**
     * Valida una contraseña
     * @param {HTMLElement} field - Campo de contraseña
     * @returns {boolean} - True si el campo es válido
     */
    function validatePassword(field) {
        const value = field.value.trim();

        // La contraseña es requerida
        if (!value) {
            field.classList.add('error');
            return false;
        }

        // Verificar que tenga exactamente 8 caracteres, al menos una mayúscula y un número
        const hasExactly8Chars = value.length === 8;
        const hasUpperCase = /[A-Z]/.test(value);
        const hasNumber = /\d/.test(value);

        if (!hasExactly8Chars || !hasUpperCase || !hasNumber) {
            field.classList.add('error');

            // Mostrar popup de error con el mensaje específico solicitado
            window.showErrorPopup(
                'Formato de contraseña incorrecto',
                'La contraseña ha de tener 8 caracteres, con una mayúscula y un número por lo menos.',
                []
            );

            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Valida que las contraseñas coincidan
     * @param {HTMLElement} passwordField - Campo de contraseña
     * @param {HTMLElement} confirmField - Campo de confirmación de contraseña
     * @returns {boolean} - True si las contraseñas coinciden
     */
    function validatePasswordsMatch(passwordField, confirmField) {
        const passwordValue = passwordField.value.trim();
        const confirmValue = confirmField.value.trim();

        // Ambos campos deben tener valor
        if (!passwordValue || !confirmValue) {
            return false;
        }

        // Verificar que coincidan
        if (passwordValue !== confirmValue) {
            passwordField.classList.add('error');
            confirmField.classList.add('error');

            // Mostrar popup de error
            window.showErrorPopup(
                'Las contraseñas no coinciden',
                'Las contraseñas no coinciden. Por favor, verifique ambos campos.',
                []
            );

            return false;
        }

        // Si coinciden, quitar errores
        passwordField.classList.remove('error');
        confirmField.classList.remove('error');
        return true;
    }

    /**
     * Valida que se haya seleccionado una opción de local físico
     * @returns {boolean} - True si se ha seleccionado una opción
     */
    function validateLocalFisico() {
        const localFisicoRadios = document.getElementsByName('local_fisico');
        let selected = false;

        localFisicoRadios.forEach(radio => {
            if (radio.checked) {
                selected = true;
            }
        });

        const container = document.querySelector('.local-fisico-container');
        if (!selected && container) {
            container.classList.add('error-container');

            // Mostrar popup de error
            window.showErrorPopup(
                'Selección requerida',
                'Por favor, indique si tiene local físico.',
                ['Debe seleccionar "Sí" o "No"']
            );

            return false;
        } else if (container) {
            container.classList.remove('error-container');
            return true;
        }

        return selected;
    }

    /**
     * Configura los botones de visibilidad para los campos de contraseña
     */
    function setupPasswordVisibilityToggles() {
        // Seleccionar todos los botones de visibilidad de contraseña
        const toggleButtons = document.querySelectorAll('.toggle-password');

        // Configurar cada botón
        toggleButtons.forEach(button => {
            // Asegurarse de que el botón sea clickeable
            button.style.cursor = 'pointer';

            // Encontrar el campo de contraseña asociado (el input hermano)
            const passwordField = button.parentElement.querySelector('input[type="password"]');

            if (passwordField) {
                // Configurar el evento de clic
                button.addEventListener('click', function() {
                    // Cambiar el tipo del campo de contraseña
                    if (passwordField.type === 'password') {
                        passwordField.type = 'text';
                    } else {
                        passwordField.type = 'password';
                    }
                });
            }
        });
    }

    /**
     * Configura las condiciones para el paso 3
     */
    function setupStep3Conditions() {
        // Validación de nombre del negocio
        const nombreNegocioInput = document.getElementById('nombre_negocio');
        if (nombreNegocioInput) {
            nombreNegocioInput.addEventListener('input', function() {
                validateRequiredField(this);
            });

            nombreNegocioInput.addEventListener('blur', function() {
                validateRequiredField(this);
            });
        }

        // Validación de teléfono del negocio (solo números, máximo 9 dígitos)
        const telefonoNegocioInput = document.getElementById('telefono_negocio');
        if (telefonoNegocioInput) {
            // Permitir solo números
            telefonoNegocioInput.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 9) {
                    this.value = this.value.slice(0, 9);
                }
            });

            telefonoNegocioInput.addEventListener('blur', function() {
                validateTelefonoNegocio(this);
            });
        }

        // Validación de WhatsApp del negocio (solo números, exactamente 8 dígitos)
        const whatsappNegocioInput = document.getElementById('whatsapp_negocio');
        if (whatsappNegocioInput) {
            // Permitir solo números
            whatsappNegocioInput.addEventListener('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
                if (this.value.length > 8) {
                    this.value = this.value.slice(0, 8);
                }
            });

            whatsappNegocioInput.addEventListener('blur', function() {
                validateWhatsappNegocio(this);
            });
        }

        // Validación de descripción del negocio (máximo 200 palabras)
        const descripcionNegocioInput = document.getElementById('descripcion_negocio');
        if (descripcionNegocioInput) {
            descripcionNegocioInput.addEventListener('input', function() {
                validateDescripcionNegocio(this);
                updateWordCounter(this);
            });

            descripcionNegocioInput.addEventListener('blur', function() {
                validateDescripcionNegocio(this);
            });
        }

        // Validación de tipo de negocio
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        tipoNegocioRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                validateTipoNegocio();
            });
        });
    }

    /**
     * Valida el teléfono del negocio
     * @param {HTMLElement} field - Campo de teléfono
     * @returns {boolean} - True si el campo es válido
     */
    function validateTelefonoNegocio(field) {
        const value = field.value.trim();

        // El teléfono es opcional, así que si está vacío, es válido
        if (!value) {
            field.classList.remove('error');
            return true;
        }

        // Verificar que solo contenga números
        if (!/^\d+$/.test(value)) {
            field.classList.add('error');
            return false;
        }

        // Verificar que tenga máximo 9 dígitos
        if (value.length > 9) {
            field.classList.add('error');
            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Valida el WhatsApp del negocio
     * @param {HTMLElement} field - Campo de WhatsApp
     * @returns {boolean} - True si el campo es válido
     */
    function validateWhatsappNegocio(field) {
        const value = field.value.trim();

        // El WhatsApp es opcional, así que si está vacío, es válido
        if (!value) {
            field.classList.remove('error');
            return true;
        }

        // Verificar que solo contenga números
        if (!/^\d+$/.test(value)) {
            field.classList.add('error');
            return false;
        }

        // Verificar que tenga exactamente 8 dígitos
        if (value.length !== 8) {
            field.classList.add('error');

            // Mostrar popup de error si tiene menos de 8 dígitos
            if (value.length < 8) {
                window.showErrorPopup(
                    'Error en WhatsApp',
                    'El número de WhatsApp debe tener exactamente 8 dígitos.',
                    ['Faltan ' + (8 - value.length) + ' dígito(s)']
                );
            }

            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Valida la descripción del negocio
     * @param {HTMLElement} field - Campo de descripción
     * @returns {boolean} - True si el campo es válido
     */
    function validateDescripcionNegocio(field) {
        const value = field.value.trim();

        // La descripción es requerida
        if (!value) {
            field.classList.add('error');
            return false;
        }

        // Contar palabras
        const wordCount = countWords(value);

        // Verificar que no exceda las 200 palabras
        if (wordCount > 200) {
            field.classList.add('error');
            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Actualiza el contador de palabras
     * @param {HTMLElement} field - Campo de texto
     */
    function updateWordCounter(field) {
        const counter = document.getElementById('descripcion-counter');
        if (!counter) return;

        const wordCount = countWords(field.value.trim());
        counter.textContent = wordCount + '/200';

        // Cambiar color si excede el límite
        if (wordCount > 200) {
            counter.style.color = '#ff5252';
        } else {
            counter.style.color = '';
        }
    }

    /**
     * Cuenta las palabras en un texto
     * @param {string} text - Texto a analizar
     * @returns {number} - Número de palabras
     */
    function countWords(text) {
        if (!text) return 0;
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }

    /**
     * Configura las condiciones para el paso 4
     */
    function setupStep4Conditions() {
        // Validación de selección de plan
        const subscriptionRadios = document.getElementsByName('subscription');
        subscriptionRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                validateSubscriptionPlan();
            });
        });
    }

    /**
     * Configura las condiciones para el paso 5
     */
    function setupStep5Conditions() {
        // Validación de selección de factura o boleta
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');

        if (facturaRadio) {
            facturaRadio.addEventListener('change', function() {
                validateDocumentoSelection();
            });
        }

        if (boletaRadio) {
            boletaRadio.addEventListener('change', function() {
                validateDocumentoSelection();
            });
        }

        // Validación de campos de factura
        const facturaFields = [
            'empresa', 'rut_empresa', 'direccion_empresa',
            'giro_empresa', 'telefono_empresa', 'correo_empresa'
        ];

        facturaFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', function() {
                    if (facturaRadio && facturaRadio.checked) {
                        validateRequiredField(this);
                    }
                });
            }
        });
    }

    /**
     * Valida un campo requerido
     * @param {HTMLElement} field - Campo a validar
     * @returns {boolean} - True si el campo es válido
     */
    function validateRequiredField(field) {
        if (!field.value.trim()) {
            field.classList.add('error');
            return false;
        } else {
            field.classList.remove('error');
            return true;
        }
    }

    /**
     * Valida que los apellidos tengan al menos dos palabras
     * @param {HTMLElement} field - Campo de apellidos
     * @returns {boolean} - True si el campo es válido
     */
    function validateApellidos(field) {
        const value = field.value.trim();
        if (!value) {
            field.classList.add('error');
            return false;
        }

        const palabras = value.split(/\s+/).filter(word => word.length > 0);
        if (palabras.length < 2) {
            field.classList.add('error');
            return false;
        } else {
            field.classList.remove('error');
            return true;
        }
    }

    /**
     * Valida un RUT chileno
     * @param {HTMLElement} field - Campo de RUT
     * @returns {boolean} - True si el campo es válido
     */
    function validateRut(field) {
        const value = field.value.trim();
        if (!value) {
            field.classList.add('error');
            return false;
        }

        // Verificar que tenga entre 8 y 9 caracteres
        if (value.length < 8 || value.length > 9) {
            field.classList.add('error');
            window.showErrorPopup(
                'RUT INVÁLIDO',
                'El RUT debe tener entre 8 y 9 caracteres.',
                []
            );
            return false;
        }

        // Verificar que sea numérico, excepto por la última posición que puede ser 'K' o 'k'
        const lastChar = value.charAt(value.length - 1).toUpperCase();
        const restOfRut = value.substring(0, value.length - 1);

        // Verificar que el resto del RUT sea solo números
        if (!/^\d+$/.test(restOfRut)) {
            field.classList.add('error');
            window.showErrorPopup(
                'RUT INVÁLIDO',
                'El RUT debe contener solo números, excepto por el último dígito que puede ser K.',
                []
            );
            return false;
        }

        // Verificar que el último carácter sea un número o 'K'
        if (!/^\d$/.test(lastChar) && lastChar !== 'K') {
            field.classList.add('error');
            window.showErrorPopup(
                'RUT INVÁLIDO',
                'El último carácter del RUT debe ser un número o la letra K.',
                []
            );
            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Valida una fecha de nacimiento
     * @param {HTMLElement} field - Campo de fecha de nacimiento
     * @returns {boolean} - True si la fecha es válida
     */
    function validateFechaNacimiento(field) {
        const value = field.value.trim();

        // La fecha de nacimiento es requerida
        if (!value) {
            field.classList.add('error');
            return false;
        }

        // Verificar que sea una fecha válida
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            field.classList.add('error');
            return false;
        }

        // Verificar que la fecha no sea futura
        const today = new Date();
        if (date > today) {
            field.classList.add('error');
            window.showErrorPopup(
                'Fecha inválida',
                'La fecha de nacimiento no puede ser futura.',
                []
            );
            return false;
        }

        // Verificar que la persona tenga al menos 18 años
        const eighteenYearsAgo = new Date();
        eighteenYearsAgo.setFullYear(today.getFullYear() - 18);
        if (date > eighteenYearsAgo) {
            field.classList.add('error');
            window.showErrorPopup(
                'Edad insuficiente',
                'Debe ser mayor de 18 años para registrarse.',
                []
            );
            return false;
        }

        field.classList.remove('error');
        return true;
    }

    /**
     * Valida que la confirmación de contraseña coincida con la contraseña
     * @param {HTMLElement} confirmField - Campo de confirmación de contraseña
     * @param {HTMLElement} passwordField - Campo de contraseña
     * @returns {boolean} - True si el campo es válido
     */
    function validateConfirmPassword(confirmField, passwordField) {
        const confirmValue = confirmField.value.trim();
        const passwordValue = passwordField.value.trim();

        if (!confirmValue) {
            confirmField.classList.add('error');
            return false;
        }

        if (confirmValue !== passwordValue) {
            confirmField.classList.add('error');
            return false;
        } else {
            confirmField.classList.remove('error');
            return true;
        }
    }

    /**
     * Valida que se haya seleccionado una opción de local físico
     * @returns {boolean} - True si se ha seleccionado una opción
     */
    function validateLocalFisico() {
        const localFisicoRadios = document.getElementsByName('local_fisico');
        let selected = false;

        localFisicoRadios.forEach(radio => {
            if (radio.checked) {
                selected = true;
            }
        });

        const container = document.querySelector('.local-fisico-container');
        if (!selected && container) {
            container.classList.add('error-container');
            return false;
        } else if (container) {
            container.classList.remove('error-container');
            return true;
        }

        return selected;
    }

    /**
     * Valida que se haya seleccionado un tipo de negocio
     * @returns {boolean} - True si se ha seleccionado una opción
     */
    function validateTipoNegocio() {
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        let selected = false;

        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                selected = true;
            }
        });

        const container = document.querySelector('.business-type-container');
        if (!selected && container) {
            container.style.border = '1px solid #ff5252';
            return false;
        } else if (container) {
            container.style.border = '1px solid #e0e0e0';
            return true;
        }

        return selected;
    }

    /**
     * Valida que se haya seleccionado un plan de suscripción
     * @returns {boolean} - True si se ha seleccionado un plan
     */
    function validateSubscriptionPlan() {
        const subscriptionRadios = document.getElementsByName('subscription');
        let selected = false;

        subscriptionRadios.forEach(radio => {
            if (radio.checked) {
                selected = true;
            }
        });

        return selected;
    }

    /**
     * Valida que se haya seleccionado factura o boleta
     * @returns {boolean} - True si se ha seleccionado una opción
     */
    function validateDocumentoSelection() {
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');

        if (!facturaRadio || !boletaRadio) {
            return false;
        }

        const selected = facturaRadio.checked || boletaRadio.checked;

        const container = document.querySelector('.documento-container');
        if (!selected && container) {
            container.classList.add('error-container');
            return false;
        } else if (container) {
            container.classList.remove('error-container');

            // Si seleccionó factura, validar los campos de factura
            if (facturaRadio.checked) {
                return validateFacturaFields();
            }

            return true;
        }

        return selected;
    }

    /**
     * Valida los campos de factura
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateFacturaFields() {
        const facturaFields = [
            'empresa', 'rut_empresa', 'direccion_empresa',
            'giro_empresa', 'telefono_empresa', 'correo_empresa'
        ];

        let isValid = true;

        facturaFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                if (!validateRequiredField(field)) {
                    isValid = false;
                }
            }
        });

        return isValid;
    }

    /**
     * Actualiza el estado del botón del paso 2
     */
    function updateStep2Button() {
        const step2NextBtn = document.getElementById('step2-next');
        if (!step2NextBtn) return;

        const isValid = isStep2Valid();
        step2NextBtn.disabled = !isValid;

        if (isValid) {
            step2NextBtn.classList.remove('disabled');
        } else {
            step2NextBtn.classList.add('disabled');
        }
    }

    /**
     * Verifica si todos los campos del paso 2 son válidos
     * @returns {boolean} - True si todos los campos son válidos
     */
    function isStep2Valid() {
        const username = document.getElementById('username');
        const email = document.getElementById('email');
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        const localFisicoRadios = document.getElementsByName('local_fisico');

        // Verificar campos de texto requeridos
        if (!username || !username.value.trim()) return false;
        if (!email || !email.value.trim()) return false;
        if (!password || !password.value.trim()) return false;
        if (!confirmPassword || !confirmPassword.value.trim()) return false;

        // Verificar formato de email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.value.trim())) return false;

        // Verificar requisitos de contraseña
        const hasExactly8Chars = password.value.trim().length === 8;
        const hasUpperCase = /[A-Z]/.test(password.value.trim());
        const hasNumber = /\d/.test(password.value.trim());
        if (!hasExactly8Chars || !hasUpperCase || !hasNumber) return false;

        // Verificar que las contraseñas coincidan
        if (password.value.trim() !== confirmPassword.value.trim()) return false;

        // Verificar que se haya seleccionado una opción de local físico
        let localFisicoSeleccionado = false;
        localFisicoRadios.forEach(radio => {
            if (radio.checked) {
                localFisicoSeleccionado = true;
            }
        });
        if (!localFisicoSeleccionado) return false;

        return true;
    }

    /**
     * Actualiza el estado del botón del paso 3
     */
    function updateStep3Button() {
        const step3NextBtn = document.getElementById('step3-next');
        if (!step3NextBtn) return;

        const isValid = isStep3Valid();
        step3NextBtn.disabled = !isValid;

        if (isValid) {
            step3NextBtn.classList.remove('disabled');
        } else {
            step3NextBtn.classList.add('disabled');
        }
    }

    /**
     * Verifica si todos los campos del paso 3 son válidos
     * @returns {boolean} - True si todos los campos son válidos
     */
    function isStep3Valid() {
        const nombreNegocio = document.getElementById('nombre_negocio');
        const descripcionNegocio = document.getElementById('descripcion_negocio');
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');

        // Verificar campos requeridos
        if (!nombreNegocio || !nombreNegocio.value.trim()) return false;
        if (!descripcionNegocio || !descripcionNegocio.value.trim()) return false;

        // Verificar que la descripción no exceda las 200 palabras
        const wordCount = descripcionNegocio.value.trim().split(/\s+/).filter(word => word.length > 0).length;
        if (wordCount > 200) return false;

        // Verificar que se haya seleccionado un tipo de negocio
        let tipoNegocioSeleccionado = false;
        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                tipoNegocioSeleccionado = true;
            }
        });
        if (!tipoNegocioSeleccionado) return false;

        // Validar teléfono del negocio si está ingresado
        const telefonoNegocio = document.getElementById('telefono_negocio');
        if (telefonoNegocio && telefonoNegocio.value.trim()) {
            if (!/^\d+$/.test(telefonoNegocio.value.trim())) return false;
            if (telefonoNegocio.value.trim().length > 9) return false;
        }

        // Validar WhatsApp del negocio si está ingresado
        const whatsappNegocio = document.getElementById('whatsapp_negocio');
        if (whatsappNegocio && whatsappNegocio.value.trim()) {
            if (!/^\d+$/.test(whatsappNegocio.value.trim())) return false;
            if (whatsappNegocio.value.trim().length !== 8) return false;
        }

        return true;
    }

    /**
     * Actualiza el estado del botón del paso 4 para productos
     */
    function updateStep4ProductosButton() {
        const submitFormBtn = document.getElementById('submit-form');
        if (!submitFormBtn) return;

        const isValid = isStep4ProductosValid();
        submitFormBtn.disabled = !isValid;

        if (isValid) {
            submitFormBtn.classList.remove('disabled');
        } else {
            submitFormBtn.classList.add('disabled');
        }
    }

    /**
     * Verifica si todos los campos del paso 4 para productos son válidos
     * @returns {boolean} - True si todos los campos son válidos
     */
    function isStep4ProductosValid() {
        // Verificar si se seleccionó un plan
        const planSeleccionado = document.querySelector('#step4-productos input[name="subscription"]:checked');
        return !!planSeleccionado;
    }

    /**
     * Actualiza el estado del botón del paso 4 para servicios
     */
    function updateStep4ServiciosButton() {
        const submitFormServiciosBtn = document.getElementById('submit-form-servicios');
        if (!submitFormServiciosBtn) return;

        const isValid = isStep4ServiciosValid();
        submitFormServiciosBtn.disabled = !isValid;

        if (isValid) {
            submitFormServiciosBtn.classList.remove('disabled');
        } else {
            submitFormServiciosBtn.classList.add('disabled');
        }
    }

    /**
     * Verifica si todos los campos del paso 4 para servicios son válidos
     * @returns {boolean} - True si todos los campos son válidos
     */
    function isStep4ServiciosValid() {
        // Verificar si se seleccionó un plan
        const planSeleccionado = document.querySelector('#step4-servicios input[name="subscription"]:checked');
        return !!planSeleccionado;
    }
});
