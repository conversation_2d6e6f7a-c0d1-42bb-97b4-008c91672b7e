/**
 * price-formatter.js
 * Script para formatear campos de precio con símbolo de moneda y separadores de miles
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando formateador de precios...');
    // Inicializar todos los campos de precio
    initPriceInputs();

    // Verificar que el objeto global esté disponible
    if (window.priceFormatter) {
        console.log('Objeto priceFormatter disponible globalmente');
    } else {
        console.error('Error: Objeto priceFormatter no disponible');
    }
});

/**
 * Inicializa todos los campos de precio en la página
 */
function initPriceInputs() {
    // Seleccionar todos los campos con clase price-input
    const priceInputs = document.querySelectorAll('.price-input');
    console.log(`Encontrados ${priceInputs.length} campos de precio para formatear`);

    // Aplicar formato inicial y agregar event listeners
    priceInputs.forEach(input => {
        console.log(`Inicializando campo de precio:`, input.id || 'sin id');

        // Event listeners
        input.addEventListener('input', function(e) {
            // Asegurarse de que solo haya números y un punto decimal
            let value = e.target.value;
            value = value.replace(/[^0-9.]/g, '');

            // Asegurar que solo haya un punto decimal
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }

            e.target.value = value;
        });

        input.addEventListener('blur', function(e) {
            // Mantener el valor numérico en el input
            let value = e.target.value;
            if (value) {
                // Asegurarse de que sea un número válido
                const numValue = parseFloat(value);
                if (!isNaN(numValue)) {
                    e.target.value = numValue;
                }
            }
        });

        // Eliminamos el evento focus que seleccionaba todo el texto
    });

    // También formatear campos de tipo number con atributo data-price
    const numberPriceInputs = document.querySelectorAll('input[type="number"][data-price]');
    console.log(`Encontrados ${numberPriceInputs.length} campos numéricos de precio`);

    numberPriceInputs.forEach(input => {
        // Mantener como tipo number para asegurar que solo se ingresen números
        input.classList.add('price-input');

        // Event listeners
        input.addEventListener('input', function(e) {
            // Asegurarse de que solo haya números
            let value = e.target.value;
            value = value.replace(/[^0-9]/g, '');
            e.target.value = value;
        });
    });
}

/**
 * Maneja el evento input en campos de precio
 * @param {Event} e - Evento input
 */
function handlePriceInput(e) {
    // Obtener el valor actual
    let value = e.target.value;
    const originalValue = value;

    // Eliminar todo excepto números y punto decimal
    value = value.replace(/[^0-9.]/g, '');

    // Asegurar que solo haya un punto decimal
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }

    // Si está vacío, no hacer nada
    if (!value) {
        e.target.value = '';
        return;
    }

    // Actualizar el valor del input con el valor numérico limpio
    e.target.value = value;
    console.log(`Input limpiado: ${originalValue} -> ${e.target.value}`);

    // Buscar el elemento de visualización asociado y actualizarlo
    const displayElement = e.target.nextElementSibling;
    if (displayElement && displayElement.classList.contains('price-display')) {
        displayElement.textContent = '$';
    }
}

/**
 * Maneja el evento blur en campos de precio (cuando pierde el foco)
 * @param {Event} e - Evento blur
 */
function handlePriceBlur(e) {
    // Si está vacío, no hacer nada
    if (!e.target.value) {
        return;
    }

    // Convertir a número válido
    const numValue = parseFloat(e.target.value);
    if (!isNaN(numValue)) {
        e.target.value = numValue;
    }
}

/**
 * Maneja el evento focus en campos de precio (cuando recibe el foco)
 * @param {Event} e - Evento focus
 */
function handlePriceFocus() {
    // Ya no seleccionamos todo el texto al recibir el foco
    // para permitir ediciones parciales
}

/**
 * Formatea un valor numérico como precio con símbolo de moneda y separadores de miles
 * @param {string|number} value - Valor a formatear
 * @returns {string} - Valor formateado
 */
function formatPrice(value) {
    console.log(`Formateando precio: ${value} (tipo: ${typeof value})`);

    // Asegurarse de que value sea un número
    let numericString = extractNumericValue(value);
    console.log(`Valor numérico extraído: ${numericString}`);

    // Si no hay valor, devolver vacío
    if (!numericString) {
        console.log('Valor vacío, devolviendo cadena vacía');
        return '';
    }

    // Convertir a número (manteniendo decimales si existen)
    const numericValue = parseFloat(numericString);
    if (isNaN(numericValue)) {
        console.log('Valor no es un número válido, devolviendo cadena vacía');
        return '';
    }

    console.log(`Valor convertido a número: ${numericValue}`);

    // Formatear con separadores de miles sin decimales
    const formattedValue = '$' + Math.floor(numericValue).toLocaleString('es-CL');
    console.log(`Valor formateado final: ${formattedValue}`);
    return formattedValue;
}

/**
 * Extrae el valor numérico de un string de precio
 * @param {string} value - Valor del que extraer el número
 * @returns {string} - Valor numérico como string
 */
function extractNumericValue(value) {
    // Si es un número, convertirlo a string
    if (typeof value === 'number') {
        return value.toString();
    }

    // Si es null o undefined, devolver cadena vacía
    if (value === null || value === undefined) {
        return '';
    }

    // Asegurarse de que value sea string
    value = String(value);

    // Eliminar símbolos de moneda y espacios
    value = value.replace(/[$\s]/g, '');

    // Reemplazar comas por puntos si hay comas como separador decimal
    if (value.indexOf(',') !== -1 && value.indexOf('.') === -1) {
        value = value.replace(',', '.');
    }

    // Eliminar puntos que sean separadores de miles (mantener solo el último punto como decimal)
    if (value.indexOf('.') !== value.lastIndexOf('.')) {
        // Hay múltiples puntos, eliminar todos excepto el último
        const parts = value.split('.');
        const decimal = parts.pop(); // Último elemento (parte decimal)
        const integer = parts.join(''); // Unir el resto sin puntos
        value = integer + '.' + decimal;
    }

    // Si no hay punto decimal, mantener el valor como está
    return value;
}

// Exportar funciones para uso en otros módulos
window.priceFormatter = {
    formatPrice,
    extractNumericValue,
    initPriceInputs
};

