<?php
// Asegurar que no haya output antes de los headers
if (ob_get_level()) ob_end_clean();

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Allow-Credentials: true');

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../../../config/config.php';
require_once '../../../config/SessionManager.php';

// Iniciar la sesión
$sessionManager = SessionManager::getInstance();
$sessionManager->initializeSession();

// Verificar que el usuario esté autenticado
if (!$sessionManager->isLoggedIn()) {
    error_log("get_categorias.php - Usuario no autenticado");
    error_log("Session data: " . print_r($_SESSION, true));
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'No autorizado: Debe iniciar sesión'
    ]);
    exit;
}

// Verificar si se envió un tipo_id para filtrar categorías por tipo
$input = json_decode(file_get_contents('php://input'), true);
$tipo_id = $input['tipo_id'] ?? $_GET['tipo_id'] ?? null;

try {
    // Construir la consulta SQL, con filtro opcional por tipo
    $query = "SELECT id, nombre, id_tipo_categoria FROM tb_categorias";
    
    // Si hay un tipo_id especificado, filtrar por ese tipo
    if ($tipo_id) {
        $tipo_id = intval($tipo_id);
        $query .= " WHERE id_tipo_categoria = ?";
        $query .= " ORDER BY nombre";
        
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $tipo_id);
    } else {
        $query .= " ORDER BY nombre";
        $stmt = $conn->prepare($query);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $categorias = [];
    while ($row = $result->fetch_assoc()) {
        $categorias[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'categorias' => $categorias
    ]);
    
} catch (Exception $e) {
    error_log("Error en get_categorias.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener categorías: ' . $e->getMessage()
    ]);
}

$conn->close();