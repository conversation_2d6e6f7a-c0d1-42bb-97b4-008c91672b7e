<?php
// Configurar un archivo de log específico para este proceso
ini_set('log_errors', 1);
ini_set('error_log', '../logs/register_errors.log');

// Función para registrar mensajes de log con timestamp
function log_message($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    error_log("[$timestamp] [$level] $message");
}

// Registrar inicio del script
log_message("Iniciando process_register.php", "START");

// Asegurar que no hay output anterior
while (ob_get_level()) ob_end_clean();
ob_start();

// Habilitar la visualización de errores de PHP
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Configurar un manejador de errores personalizado
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $error_type = match($errno) {
        E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_USER_ERROR => 'FATAL ERROR',
        E_WARNING, E_CORE_WARNING, E_COMPILE_WARNING, E_USER_WARNING => 'WARNING',
        E_NOTICE, E_USER_NOTICE => 'NOTICE',
        E_DEPRECATED, E_USER_DEPRECATED => 'DEPRECATED',
        default => 'UNKNOWN'
    };

    log_message("PHP $error_type: $errstr in $errfile on line $errline", "ERROR");

    // Para errores fatales, terminar la ejecución
    if ($errno == E_ERROR || $errno == E_CORE_ERROR || $errno == E_COMPILE_ERROR || $errno == E_USER_ERROR) {
        exit(1);
    }

    return true; // Permitir que el manejador de errores estándar de PHP continúe
});

// Configurar un manejador de excepciones no capturadas
set_exception_handler(function($exception) {
    $error_message = "Excepción no capturada: " . $exception->getMessage() .
                     "\nStack trace: " . $exception->getTraceAsString();
    log_message($error_message, "EXCEPTION");

    // Definir la función de respuesta JSON si aún no está definida
    if (!function_exists('output_json_response')) {
        function output_json_response($data) {
            ob_clean(); // Limpiar cualquier salida anterior
            header('Content-Type: application/json');
            echo json_encode($data);
            exit();
        }
    }
    
    // Devolver respuesta JSON con error
    $response = [
        'success' => false,
        'message' => $exception->getMessage(),
        'debug' => true
    ];
    
    output_json_response($response);
});

// Registrar datos POST recibidos (sin información sensible)
$post_data = $_POST;
if (isset($post_data['password'])) $post_data['password'] = '********';
if (isset($post_data['confirm_password'])) $post_data['confirm_password'] = '********';
log_message("Datos POST recibidos: " . json_encode($post_data, JSON_UNESCAPED_UNICODE));

// Incluir la configuración de la base de datos y clases de seguridad
log_message("Cargando configuración de base de datos", "INFO");
try {
    require_once '../config/config.php';
    require_once '../config/InputValidator.php';
    require_once '../config/RateLimiter.php';
    log_message("Configuración de base de datos cargada correctamente", "INFO");
} catch (Throwable $e) {
    log_message("Error al cargar configuración de base de datos: " . $e->getMessage(), "ERROR");
    throw $e;
}

// Verificar conexión a la base de datos
if (!isset($conn) || $conn->connect_error) {
    log_message("Error de conexión a la base de datos: " . ($conn->connect_error ?? "Variable \$conn no definida"), "ERROR");
    header("Location: register.php?error=" . urlencode("Error de conexión a la base de datos. Por favor, inténtelo de nuevo."));
    exit();
}

log_message("Conexión a la base de datos verificada", "INFO");

// Verificar si hay errores de PHP
if (error_get_last() !== null) {
    log_message("Error de PHP detectado: " . print_r(error_get_last(), true), "ERROR");
    // Puedes agregar aquí más acciones, como redirigir a una página de error
}

// Verificar si el formulario fue enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Inicializar rate limiter
    $rateLimiter = new RateLimiter($conn);
    $clientIdentifier = RateLimiter::getIdentifier();

    // Verificar rate limiting
    $rateCheck = $rateLimiter->isAllowed($clientIdentifier, 'registration', 3, 3600); // 3 intentos por hora
    if (!$rateCheck['allowed']) {
        $rateLimiter->recordAttempt($clientIdentifier, 'registration');
        $response = [
            'success' => false,
            'message' => 'Demasiados intentos de registro. Intente nuevamente más tarde.',
            'blocked_until' => $rateCheck['blocked_until'] ?? null
        ];
        output_json_response($response);
    }

    // ----- CSRF -----
    session_start();
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'] ?? '')) {
        $rateLimiter->recordAttempt($clientIdentifier, 'registration');
        $response = ['success'=>false,'message'=>'Token CSRF inválido'];
        output_json_response($response);
    }
    log_message("Procesando solicitud POST", "INFO");
    try {
        // Recoger y sanitizar datos del paso 1: Información Personal
        log_message("Procesando datos de información personal", "INFO");

        // Validar y sanitizar nombres
        $nombres = InputValidator::sanitizeString($_POST['nombres'] ?? '', 100);
        if (empty($nombres) || InputValidator::detectSQLInjection($nombres)) {
            throw new Exception("Nombres inválidos");
        }

        // Validar y sanitizar apellidos
        $apellidos = InputValidator::sanitizeString($_POST['apellidos'] ?? '', 100);
        if (empty($apellidos) || InputValidator::detectSQLInjection($apellidos)) {
            throw new Exception("Apellidos inválidos");
        }

        // Validar RUT
        $rut = InputValidator::validateRUT($_POST['rut'] ?? '');
        if (!$rut) {
            throw new Exception("RUT inválido");
        }

        // Validar fecha de nacimiento
        $fecha_nacimiento = InputValidator::validateDate($_POST['fechaNacimiento'] ?? '');
        if (!$fecha_nacimiento) {
            throw new Exception("Fecha de nacimiento inválida o usuario menor de 18 años");
        }
        log_message("Fecha validada: $fecha_nacimiento", "INFO");

        // Validar sexo
        $sexo = $_POST['sexo'] ?? '';
        if (!in_array($sexo, ['masculino', 'femenino', 'otro'])) {
            throw new Exception("Sexo inválido");
        }

        // Validar teléfono
        $telefono = InputValidator::validatePhone($_POST['telefono'] ?? '');
        if (!$telefono) {
            throw new Exception("Teléfono inválido");
        }

        // Sanitizar región y comuna
        $region = InputValidator::sanitizeString($_POST['region'] ?? '', 50);
        $comuna = InputValidator::sanitizeString($_POST['comuna'] ?? '', 50);
        $direccion = InputValidator::sanitizeString($_POST['direccion'] ?? '', 150);

        if (empty($region) || empty($comuna) || empty($direccion)) {
            throw new Exception("Región, comuna o dirección inválidos");
        }

        // Recoger y sanitizar datos del paso 2: Datos de Cuenta
        log_message("Procesando datos de cuenta", "INFO");

        // Validar username
        $username = InputValidator::validateUsername($_POST['username'] ?? '');
        if (!$username) {
            throw new Exception("Nombre de usuario inválido");
        }

        // Validar email principal
        $email = InputValidator::validateEmail($_POST['email'] ?? '');
        if (!$email) {
            throw new Exception("Email inválido");
        }

        // Validar email de respaldo (opcional)
        $backup_email = null;
        if (!empty($_POST['backup_email'])) {
            $backup_email = InputValidator::validateEmail($_POST['backup_email']);
            if (!$backup_email) {
                throw new Exception("Email de respaldo inválido");
            }
        }

        // Validar contraseña con criterios de seguridad mejorados
        if (empty($_POST['password'])) {
            log_message("Contraseña no proporcionada", "WARNING");
            throw new Exception("La contraseña es obligatoria");
        }

        $passwordValidation = InputValidator::validatePassword($_POST['password']);
        if (!$passwordValidation['valid']) {
            log_message("Contraseña no cumple criterios de seguridad: " . $passwordValidation['message'], "WARNING");
            throw new Exception($passwordValidation['message']);
        }

        // Verificar que las contraseñas coincidan
        if ($_POST['password'] !== $_POST['confirm_password']) {
            throw new Exception("Las contraseñas no coinciden");
        }

        // Encriptar contraseña con opciones seguras
        $password = password_hash($_POST['password'], PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iteraciones
            'threads' => 3          // 3 hilos
        ]);

        // Validar local físico
        $local_fisico = $_POST['local_fisico'] ?? '';
        if (!in_array($local_fisico, ['si', 'no'])) {
            throw new Exception("Opción de local físico inválida");
        }

        // Recoger y sanitizar datos del paso 3: Datos del Negocio
        log_message("Procesando datos del negocio", "INFO");
        $nombre_negocio = trim($_POST['nombre_negocio'] ?? '');
        $telefono_negocio = !empty($_POST['telefono_negocio']) ? trim($_POST['telefono_negocio']) : null;
        $whatsapp_negocio = !empty($_POST['whatsapp_negocio']) ? trim($_POST['whatsapp_negocio']) : null;
        $descripcion_negocio = trim($_POST['descripcion_negocio'] ?? '');
        $tipo_negocio = $_POST['tipo_negocio'] ?? '';

        // Verificar campos obligatorios del paso 3
        if (empty($nombre_negocio) || empty($descripcion_negocio) || empty($tipo_negocio)) {
            log_message("Faltan campos obligatorios en el paso 3", "WARNING");
            throw new Exception("Faltan campos obligatorios en los datos del negocio");
        }

        // Categorías según tipo de negocio
        log_message("Procesando categorías según tipo de negocio: $tipo_negocio", "INFO");
        $categorias_venta = null;
        $categorias_servicios = null;
        $categorias_arriendo = null;

        if ($tipo_negocio === 'venta' && !empty($_POST['categorias_venta'])) {
            $categorias_venta = is_array($_POST['categorias_venta']) ?
                                implode(',', $_POST['categorias_venta']) :
                                $_POST['categorias_venta'];
            log_message("Categorías de venta seleccionadas: $categorias_venta", "INFO");
        } elseif ($tipo_negocio === 'servicios' && !empty($_POST['categorias_servicios'])) {
            $categorias_servicios = is_array($_POST['categorias_servicios']) ?
                                    implode(',', $_POST['categorias_servicios']) :
                                    $_POST['categorias_servicios'];
            log_message("Categorías de servicios seleccionadas: $categorias_servicios", "INFO");
        } elseif ($tipo_negocio === 'arriendo' && !empty($_POST['categorias_arriendo'])) {
            $categorias_arriendo = is_array($_POST['categorias_arriendo']) ?
                                   implode(',', $_POST['categorias_arriendo']) :
                                   $_POST['categorias_arriendo'];
            log_message("Categorías de arriendo seleccionadas: $categorias_arriendo", "INFO");
        } else {
            log_message("No se seleccionaron categorías para el tipo de negocio: $tipo_negocio", "WARNING");
        }

        // Recoger y sanitizar datos del paso 4: Plan
        log_message("Procesando datos del plan", "INFO");
        $subscription = $_POST['subscription'] ?? '';

        // Verificar campos obligatorios del paso 4
        if (empty($subscription)) {
            log_message("Falta seleccionar un plan de suscripción", "WARNING");
            throw new Exception("Debe seleccionar un plan de suscripción");
        }

        // Recoger y sanitizar datos del paso 5: Pago (si aplica)
        log_message("Procesando datos de pago", "INFO");
        $documento = isset($_POST['documento']) ? $_POST['documento'] : null;
        $empresa = isset($_POST['empresa']) ? trim($_POST['empresa']) : null;
        $rut_empresa = isset($_POST['rut_empresa']) ? trim($_POST['rut_empresa']) : null;
        $direccion_empresa = isset($_POST['direccion_empresa']) ? trim($_POST['direccion_empresa']) : null;
        $giro_empresa = isset($_POST['giro_empresa']) ? trim($_POST['giro_empresa']) : null;
        $telefono_empresa = isset($_POST['telefono_empresa']) ? trim($_POST['telefono_empresa']) : null;
        $correo_empresa = isset($_POST['correo_empresa']) ? trim($_POST['correo_empresa']) : null;

        // Preparar la consulta SQL
        log_message("Preparando consulta SQL para inserción", "INFO");
        $sql = "INSERT INTO tb_registros (
                    rut, nombre, apellido, fecha_nacimiento, sexo, telefono, region, comuna, direccion,
                    username, email, backup_email, password, nombre_negocio, descripcion_negocio, 
                    local_fisico, telefono_negocio, whatsapp_negocio, tipo_negocio,
                    plan_venta, plan_servicios_arriendo, plan_preferido, tipo_documento,
                    empresa_nombre, empresa_rut, empresa_giro, empresa_correo, empresa_telefono, 
                    empresa_direccion, accept_terms
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, ?, ?, ?,
                    ?, ?, ?, ?, ?,
                    ?, ?
                )";
                
        // Verificar que el número de campos coincida con el número de placeholders
        $placeholders_count = substr_count($sql, '?');
        log_message("Número de placeholders en la consulta: $placeholders_count", "INFO");

        // Preparar la sentencia
        log_message("Preparando statement", "INFO");
        $stmt = $conn->prepare($sql);

        if (!$stmt) {
            log_message("Error al preparar la consulta: " . $conn->error, "ERROR");
            throw new Exception("Error al preparar la consulta: " . $conn->error);
        }

        // Determinar qué plan usar según el tipo de negocio
        $plan_venta = null;
        $plan_servicios_arriendo = null;
        
        if ($tipo_negocio === 'venta') {
            $plan_venta = $subscription;
        } else if ($tipo_negocio === 'servicios-arriendo') {
            $plan_servicios_arriendo = $subscription;
        }
        
        // Determinar el plan preferido (viene del formulario o usamos el seleccionado)
        $plan_preferido = isset($_POST['plan_preferido']) ? $_POST['plan_preferido'] : $subscription;
        
        // Estado de aceptación de términos
        $accept_terms = isset($_POST['accept_terms']) ? 'Aceptado' : 'No Aceptado';
        
        // Contar el número total de columnas y parámetros
        $params_count = 30; // 30 parámetros en total
        $bind_types = str_repeat('s', $params_count); // Todos son strings
        
        log_message("Vinculando parámetros ($params_count en total)", "INFO");
        $bind_result = $stmt->bind_param(
            $bind_types,
            $rut, $nombres, $apellidos, $fecha_nacimiento, $sexo, $telefono, $region, $comuna, $direccion,
            $username, $email, $backup_email, $password, $nombre_negocio, $descripcion_negocio,
            $local_fisico, $telefono_negocio, $whatsapp_negocio, $tipo_negocio,
            $plan_venta, $plan_servicios_arriendo, $plan_preferido, $documento,
            $empresa, $rut_empresa, $giro_empresa, $correo_empresa, $telefono_empresa,
            $direccion_empresa, $accept_terms
        );

        if (!$bind_result) {
            log_message("Error al vincular parámetros: " . $stmt->error, "ERROR");
            throw new Exception("Error al vincular parámetros: " . $stmt->error);
        }

        // Ejecutar la consulta
        log_message("Ejecutando consulta SQL", "INFO");
        if ($stmt->execute()) {
            // Registro exitoso
            log_message("Registro exitoso. ID insertado: " . $stmt->insert_id, "SUCCESS");
            
            // Obtener el nombre del usuario para pasar a la página de éxito
            $nombre_usuario = $nombres;
            
            // Devolver respuesta JSON para petición AJAX
            $response = [
                'success' => true,
                'message' => 'Usuario registrado exitosamente',
                'user_id' => $stmt->insert_id,
                'nombre' => $nombre_usuario,
                'redirect' => 'registro_opciones.php'
            ];
            
            output_json_response($response);
        } else {
            log_message("Error al ejecutar la consulta: " . $stmt->error, "ERROR");
            throw new Exception("Error al registrar: " . $stmt->error);
        }

    } catch (Exception $e) {
        // Registrar intento fallido en rate limiter
        if (isset($rateLimiter) && isset($clientIdentifier)) {
            $rateLimiter->recordAttempt($clientIdentifier, 'registration');
        }

        // Manejar errores de forma segura
        $error_message = "Excepción capturada: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString();
        log_message($error_message, "ERROR");

        // Guardar el error en un archivo temporal para depuración (solo en desarrollo)
        if (defined('APP_ENV') && APP_ENV === 'development') {
            file_put_contents('../logs/last_error.txt', $error_message);
        }

        // Devolver respuesta JSON con error genérico (no exponer detalles internos)
        $userMessage = $e->getMessage();

        // Filtrar mensajes que podrían exponer información sensible
        $sensitivePatterns = [
            '/database/i',
            '/mysql/i',
            '/sql/i',
            '/connection/i',
            '/server/i',
            '/path/i',
            '/file/i'
        ];

        foreach ($sensitivePatterns as $pattern) {
            if (preg_match($pattern, $userMessage)) {
                $userMessage = "Error interno del servidor. Por favor, inténtelo más tarde.";
                break;
            }
        }

        $response = [
            'success' => false,
            'message' => $userMessage
        ];

        output_json_response($response);
    }
} else {
    // Si no es POST, devolver respuesta JSON con error
    log_message("Solicitud no es POST. Respondiendo con error", "WARNING");
    $response = [
        'success' => false,
        'message' => 'Método no permitido. Solo se acepta POST.',
        'debug' => true
    ];
    
    output_json_response($response);
}

// Registrar finalización del script
log_message("Finalizando process_register.php", "END");

// Función para asegurar que estamos generando respuestas JSON válidas
function output_json_response($data) {
    ob_clean(); // Limpiar cualquier salida anterior
    header('Content-Type: application/json');
    echo json_encode($data);
    exit();
}
?>
