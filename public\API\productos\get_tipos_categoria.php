<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Permitir CORS para desarrollo
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Accept, Authorization');
header('Content-Type: application/json');

// Si es una solicitud OPTIONS, responder inmediatamente con 200
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Log de inicio
    error_log("Iniciando get_tipos_categoria.php");
    error_log("REQUEST_URI: " . $_SERVER['REQUEST_URI']);
    error_log("SCRIPT_FILENAME: " . $_SERVER['SCRIPT_FILENAME']);

    // Ruta absoluta al archivo de configuración
    $configPath = realpath(__DIR__ . '/../../../config/config.php');
    error_log("Ruta al archivo de configuración: " . $configPath);

    if (!file_exists($configPath)) {
        error_log("ERROR: El archivo de configuración no existe en la ruta: " . $configPath);
        throw new Exception("Archivo de configuración no encontrado");
    }

    require_once $configPath;

    // Verificar variables de conexión
    error_log("Verificando variables de conexión: " .
              "HOST=" . (defined('DB_HOST') ? 'definido' : 'no definido') .
              ", USER=" . (defined('DB_USER') ? 'definido' : 'no definido'));

    // Verificar la conexión
    if (!isset($conn)) {
        throw new Exception("Variable de conexión no está definida");
    }

    if ($conn->connect_error) {
        throw new Exception("Error de conexión: " . $conn->connect_error);
    }

    // Log de conexión exitosa
    error_log("Conexión a base de datos establecida");

    // Verificar si la tabla existe
    $tableCheckQuery = "SHOW TABLES LIKE 'tb_tipo_categoria'";
    $tableResult = $conn->query($tableCheckQuery);

    if ($tableResult && $tableResult->num_rows == 0) {
        error_log("La tabla tb_tipo_categoria no existe en la base de datos");

        // Devolver datos de respaldo
        $tipos_categoria = [
            ['id' => 1, 'nombre' => 'Comida'],
            ['id' => 2, 'nombre' => 'Bebida'],
            ['id' => 3, 'nombre' => 'Servicios']
        ];
    } else {
        // Consulta para obtener todos los tipos de categoría
        $query = "SELECT id, nombre FROM tb_tipo_categoria ORDER BY nombre";
        error_log("Ejecutando consulta: " . $query);

        // Ejecutar la consulta
        $result = $conn->query($query);

        if ($result === false) {
            error_log("Error en la consulta: " . $conn->error);

            // Devolver datos de respaldo en caso de error
            $tipos_categoria = [
                ['id' => 1, 'nombre' => 'Comida'],
                ['id' => 2, 'nombre' => 'Bebida'],
                ['id' => 3, 'nombre' => 'Servicios']
            ];
        } else {
            $tipos_categoria = [];
            while ($row = $result->fetch_assoc()) {
                $tipos_categoria[] = [
                    'id' => $row['id'],
                    'nombre' => $row['nombre']
                ];
            }

            // Si no hay resultados, usar datos de respaldo
            if (empty($tipos_categoria)) {
                error_log("No se encontraron tipos de categoría en la base de datos, usando datos de respaldo");
                $tipos_categoria = [
                    ['id' => 1, 'nombre' => 'Comida'],
                    ['id' => 2, 'nombre' => 'Bebida'],
                    ['id' => 3, 'nombre' => 'Servicios']
                ];
            }
        }
    }

    // Log del resultado
    error_log("Tipos de categoría encontrados: " . count($tipos_categoria));

    // Devolver los resultados
    echo json_encode([
        'success' => true,
        'tipos' => $tipos_categoria,
        'count' => count($tipos_categoria)
    ]);

} catch (Exception $e) {
    error_log("Error en get_tipos_categoria.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener tipos de categoría: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}

// Cerrar la conexión
if (isset($conn)) {
    $conn->close();
}
