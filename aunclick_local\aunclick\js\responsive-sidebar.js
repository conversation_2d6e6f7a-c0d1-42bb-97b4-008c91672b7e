/**
 * Responsive Sidebar - Maneja el comportamiento del sidebar en modo responsive
 */

document.addEventListener('DOMContentLoaded', function() {
    // Crear un overlay para el sidebar
    createSidebarOverlay();

    // Inicializar eventos
    initSidebarEvents();

    // Verificar el estado inicial del sidebar en modo responsive
    checkInitialSidebarState();
});

/**
 * Verifica el estado inicial del sidebar en modo responsive
 */
function checkInitialSidebarState() {
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    // En modo responsive, asegurarse de que el sidebar esté expandido inicialmente
    if (window.innerWidth < 992) {
        sidebar.classList.add('expanded');
        showOverlay();
    }
}

/**
 * Crea un overlay para el sidebar en modo responsive
 */
function createSidebarOverlay() {
    // Eliminar overlay existente si hay alguno
    const existingOverlay = document.getElementById('sidebar-overlay');
    if (existingOverlay) {
        existingOverlay.parentNode.removeChild(existingOverlay);
    }

    // Crear el elemento overlay
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay';
    overlay.className = 'sidebar-overlay';

    // Establecer estilos inline para asegurar que funcione
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '1040';
    overlay.style.display = 'none';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.3s ease';

    // Agregar al body
    document.body.appendChild(overlay);

    // Agregar el evento de clic
    addOverlayClickEvent(overlay);
}

/**
 * Agrega el evento de clic al overlay
 */
function addOverlayClickEvent(overlay) {
    overlay.addEventListener('click', function() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.remove('expanded');
            hideOverlay();

            // Actualizar el ícono si existe
            const toggleIcon = document.getElementById('toggle-icon');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-bars';
            }
        }
    });
}

/**
 * Muestra el overlay
 */
function showOverlay() {
    const overlay = document.getElementById('sidebar-overlay');
    if (overlay) {
        overlay.style.display = 'block';
        setTimeout(() => {
            overlay.style.opacity = '1';
            overlay.classList.add('visible');
        }, 10);

        // Añadir clase al body para controlar el contenido principal
        document.body.classList.add('sidebar-expanded');
    }
}

/**
 * Oculta el overlay
 */
function hideOverlay() {
    const overlay = document.getElementById('sidebar-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        overlay.classList.remove('visible');

        // Remover clase del body
        document.body.classList.remove('sidebar-expanded');

        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300); // Mismo tiempo que la transición
    }
}

/**
 * Inicializa los eventos del sidebar
 */
function initSidebarEvents() {
    // Observar cambios en el sidebar
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    // Usar MutationObserver para detectar cambios en las clases del sidebar
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                const isExpanded = sidebar.classList.contains('expanded');
                if (isExpanded && window.innerWidth < 992) {
                    showOverlay();
                } else {
                    hideOverlay();
                }
            }
        });
    });

    // Configurar el observer
    observer.observe(sidebar, { attributes: true });

    // Manejar el botón de toggle
    const toggleBtn = document.getElementById('aside-toggle');
    if (toggleBtn) {
        // Asegurarse de que no haya eventos duplicados
        const newToggleBtn = toggleBtn.cloneNode(true);
        toggleBtn.parentNode.replaceChild(newToggleBtn, toggleBtn);

        newToggleBtn.addEventListener('click', function(e) {
            // Prevenir comportamiento por defecto
            e.preventDefault();
            e.stopPropagation();

            if (window.innerWidth < 992) {
                // Verificar estado actual
                const isExpanded = sidebar.classList.contains('expanded');

                // Toggle de la clase expanded
                if (isExpanded) {
                    sidebar.classList.remove('expanded');
                    hideOverlay();
                } else {
                    sidebar.classList.add('expanded');
                    showOverlay();
                }

                // Actualizar el ícono si existe
                const toggleIcon = document.getElementById('toggle-icon');
                if (toggleIcon) {
                    if (sidebar.classList.contains('expanded')) {
                        toggleIcon.className = 'fas fa-times';
                    } else {
                        toggleIcon.className = 'fas fa-bars';
                    }
                }
            } else {
                // Comportamiento normal en desktop
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));

                // Actualizar el ícono si existe
                const toggleIcon = document.getElementById('toggle-icon');
                if (toggleIcon) {
                    if (sidebar.classList.contains('collapsed')) {
                        toggleIcon.className = 'fas fa-bars';
                    } else {
                        toggleIcon.className = 'fas fa-times';
                    }
                }
            }
        });
    }

    // Manejar cambios de tamaño de ventana
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            hideOverlay();
        } else if (sidebar.classList.contains('expanded')) {
            showOverlay();
        }
    });
}
