// Global variables
let sidebar, productsSection, editProductSection, storeInfoSection, statsSection, categoriesSection;
let dashboardLink, productsNavLink, categoriesLink, storeNavLink;
let asideToggle, toggleIcon;

// Helper function
function getElementSafe(id) {
    const element = document.getElementById(id);
    if (!element) {
        console.warn(`Elemento con ID '${id}' no encontrado en el DOM`);
    }
    return element;
}

// Inicializar el estado del sidebar según el tamaño
function initSidebar() {
    if (!sidebar || !toggleIcon) return;

    const asideCollapsed = localStorage.getItem('asideCollapsed') === 'true';

    if (window.innerWidth < 992) {
        // En móvil siempre iniciamos collapsed (sin la clase expanded)
        sidebar.classList.remove('expanded');
    } else {
        // En desktop aplicamos preferencia del usuario
        if (asideCollapsed) {
            sidebar.classList.add('collapsed');
        } else {
            sidebar.classList.remove('collapsed');
        }
    }

    // Actualizar el icono
    updateToggleIcon();
}

// Función para actualizar el icono del toggle según estado
function updateToggleIcon() {
    if (!toggleIcon) return;

    const isCollapsed = sidebar.classList.contains('collapsed');
    const isMobile = window.innerWidth < 992;
    const isExpanded = sidebar.classList.contains('expanded');

    // Reglas simplificadas para el icono
    if ((isMobile && !isExpanded) || (!isMobile && isCollapsed)) {
        toggleIcon.className = 'fas fa-bars';
    } else {
        toggleIcon.className = 'fas fa-times';
    }
}

// Función para alternar el sidebar
function toggleSidebar() {
    if (!sidebar) return;

    if (window.innerWidth < 992) {
        // En móvil: toggleamos la clase 'expanded'
        sidebar.classList.toggle('expanded');
    } else {
        // En desktop: toggleamos la clase 'collapsed'
        sidebar.classList.toggle('collapsed');
        localStorage.setItem('asideCollapsed', sidebar.classList.contains('collapsed'));
    }

    // Actualizar ícono
    if (toggleIcon) {
        const isMobile = window.innerWidth < 992;
        const isExpanded = sidebar.classList.contains('expanded');
        const isCollapsed = sidebar.classList.contains('collapsed');

        if ((isMobile && isExpanded) || (!isMobile && !isCollapsed)) {
            toggleIcon.className = 'fas fa-times';
        } else {
            toggleIcon.className = 'fas fa-bars';
        }
    }

    console.log('Sidebar toggled:', sidebar.classList.contains('collapsed') ? 'collapsed' : 'expanded');
}

// Manejar clics fuera del sidebar para cerrarlo en pantallas grandes - DESACTIVADO
/*
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');

    // Solo aplicar en dispositivos de escritorio y cuando el sidebar esté expandido
    if (window.innerWidth > 992 && !sidebar.classList.contains('collapsed')) {
        // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
        if (!sidebar.contains(event.target) && event.target !== toggleBtn && !toggleBtn.contains(event.target)) {
            // Cerrar el sidebar
            sidebar.classList.add('collapsed');

            // Actualizar el ícono
            const toggleIcon = document.getElementById('toggle-icon');
            toggleIcon.classList.remove('fa-bars');
            toggleIcon.classList.add('fa-chevron-right');
        }
    }
});
*/

// Función genérica UNIFICADA para mostrar secciones
function showSection(sectionId) {
    console.log(`Mostrando sección: ${sectionId}`);

    // Primero ocultamos todas las secciones
    document.querySelectorAll('.content-section').forEach(section => {
        section.style.display = 'none';
        section.classList.remove('active'); // Remover clase active de todas las secciones
    });

    // Mostramos la sección requerida
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.classList.add('active'); // Agregar clase active a la sección objetivo
        console.log(`Sección ${sectionId} mostrada con display: block y class active desde showSection`);
    } else {
        console.error(`Sección ${sectionId} no encontrada`);
        // Mostrar mensaje de depuración
        alert(`Error: No se encontró la sección ${sectionId}. Por favor, contacta al administrador.`);
        return; // Salir si no se encuentra la sección
    }

    // Desactivamos todas las pestañas y navegación
    document.querySelectorAll('.tab-btn').forEach(tab => {
        tab.classList.remove('active');
    });

    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Activamos la navegación según la sección
    switch(sectionId) {
        case 'productsSection':
            if (productsNavLink) productsNavLink.classList.add('active');
            break;
        case 'editProductSection':
            if (productsNavLink) productsNavLink.classList.add('active');
            break;
        case 'storeInfoSection':
            if (storeNavLink) storeNavLink.classList.add('active');
            break;
        case 'statsSection':
            if (dashboardLink) dashboardLink.classList.add('active');
            // Si estamos mostrando el dashboard, inicializar los gráficos
            if (typeof Chart !== 'undefined') {
                initializeCharts();
                console.log('Gráficos inicializados desde showSection para statsSection');
            } else {
                console.warn('Chart.js no está disponible para inicializar los gráficos');
            }
            break;
        case 'categoriesSection':
            if (categoriesLink) categoriesLink.classList.add('active');
            break;
    }

    // Si es la sección de productos, actualizar vista según ancho
    if (sectionId === 'productsSection') {
        updateResponsiveView();
    }
}

// Funciones específicas para mostrar cada sección
function showProductsSection() {
    showSection('productsSection');
}

function showEditProductSection() {
    showSection('editProductSection');
}

function showStoreInfoSection() {
    console.log('Iniciando showStoreInfoSection');

    // Verificar que la sección existe
    const storeInfoSection = document.getElementById('storeInfoSection');
    if (!storeInfoSection) {
        console.error('Error: No se encontró la sección storeInfoSection');
        alert('Error: No se encontró la sección de Mi Tienda. Por favor, recarga la página.');
        return;
    }

    showSection('storeInfoSection');
    console.log('showStoreInfoSection completado');
}

function showStatsSection() {
    console.log('Iniciando showStatsSection');

    // Verificar que la sección existe
    const statsSection = document.getElementById('statsSection');
    if (!statsSection) {
        console.error('Error: No se encontró la sección statsSection');
        alert('Error: No se encontró la sección de Dashboard. Por favor, recarga la página.');
        return;
    }

    // Usar la función showSection para mostrar el dashboard
    // Esta función ya se encarga de inicializar los gráficos
    showSection('statsSection');
    console.log('showStatsSection completado');
}

function showCategoriesSection() {
    showSection('categoriesSection');
}

// Variable global para almacenar las instancias de los gráficos
let chartInstances = {};

// Función mejorada para inicializar los gráficos con datos más relevantes
function initializeCharts() {
    try {
        console.log('Inicializando gráficos del Dashboard...');

        // Destruir gráficos existentes para evitar errores
        destroyExistingCharts();

        // 1. Gráfico de Ventas Mensuales
        const visitsMonthlyChart = document.getElementById('visitsMonthlyChart');
        if (visitsMonthlyChart) {
            console.log('Inicializando gráfico de ventas mensuales');
            chartInstances.visitsMonthlyChart = new Chart(visitsMonthlyChart.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                    datasets: [{
                        label: 'Ventas 2023',
                        data: [1250000, 1480000, 1320000, 1650000, 1820000, 2100000, 1950000, 2300000, 2150000, 2420000, 2680000, 3100000],
                        borderColor: 'rgba(106, 27, 154, 1)',
                        backgroundColor: 'rgba(106, 27, 154, 0.1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true
                    }, {
                        label: 'Ventas 2022',
                        data: [980000, 1150000, 1080000, 1320000, 1450000, 1680000, 1520000, 1780000, 1650000, 1920000, 2150000, 2480000],
                        borderColor: 'rgba(156, 39, 176, 0.6)',
                        backgroundColor: 'rgba(156, 39, 176, 0.05)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Ventas Mensuales (CLP)',
                            font: {
                                size: 16
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('es-CL', { style: 'currency', currency: 'CLP' }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + (value/1000000).toFixed(1) + 'M';
                                }
                            }
                        }
                    }
                }
            });
        }

        // 2. Gráfico de Dispositivos de Acceso
        const devicesChart = document.getElementById('devicesChart');
        if (devicesChart) {
            console.log('Inicializando gráfico de dispositivos');
            chartInstances.devicesChart = new Chart(devicesChart.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['Móvil', 'Desktop', 'Tablet', 'Otros'],
                    datasets: [{
                        data: [58, 32, 8, 2],
                        backgroundColor: [
                            'rgba(233, 30, 99, 0.8)',
                            'rgba(33, 150, 243, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(156, 39, 176, 0.8)'
                        ],
                        borderColor: 'white',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 3. Gráfico de Productos Más Vendidos
        const topProductsChart = document.getElementById('topProductsChart');
        if (topProductsChart) {
            console.log('Inicializando gráfico de productos más vendidos');
            chartInstances.topProductsChart = new Chart(topProductsChart.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['Smartphone X12', 'Laptop Pro', 'Auriculares BT', 'Smart TV 55"', 'Tablet Air'],
                    datasets: [{
                        label: 'Unidades vendidas',
                        data: [124, 95, 86, 72, 65],
                        backgroundColor: [
                            'rgba(233, 30, 99, 0.7)',
                            'rgba(156, 39, 176, 0.7)',
                            'rgba(33, 150, 243, 0.7)',
                            'rgba(0, 188, 212, 0.7)',
                            'rgba(76, 175, 80, 0.7)'
                        ],
                        borderColor: [
                            'rgba(233, 30, 99, 1)',
                            'rgba(156, 39, 176, 1)',
                            'rgba(33, 150, 243, 1)',
                            'rgba(0, 188, 212, 1)',
                            'rgba(76, 175, 80, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `Vendidos: ${context.parsed.x} unidades`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            }
                        },
                        y: {
                            grid: {
                                display: false,
                                drawBorder: false
                            }
                        }
                    }
                }
            });
        }

        // 4. Gráfico de Interés por Categoría
        const categoriesChart = document.getElementById('categoriesChart');
        if (categoriesChart) {
            console.log('Inicializando gráfico de interés por categoría');
            chartInstances.categoriesChart = new Chart(categoriesChart.getContext('2d'), {
                type: 'radar',
                data: {
                    labels: ['Electrónica', 'Hogar', 'Moda', 'Deportes', 'Juguetes', 'Libros', 'Belleza'],
                    datasets: [{
                        label: 'Visitas',
                        data: [85, 65, 45, 40, 30, 25, 50],
                        backgroundColor: 'rgba(156, 39, 176, 0.2)',
                        borderColor: 'rgba(156, 39, 176, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(156, 39, 176, 1)',
                        pointRadius: 4
                    }, {
                        label: 'Ventas',
                        data: [70, 55, 35, 30, 25, 20, 40],
                        backgroundColor: 'rgba(233, 30, 99, 0.2)',
                        borderColor: 'rgba(233, 30, 99, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(233, 30, 99, 1)',
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            suggestedMin: 0,
                            suggestedMax: 100,
                            ticks: {
                                stepSize: 20,
                                backdropColor: 'transparent'
                            }
                        }
                    }
                }
            });
        }

        // 5. Nuevo gráfico: Tendencia de Stock
        const stockTrendChart = document.getElementById('stockTrendChart');
        if (stockTrendChart) {
            console.log('Inicializando gráfico de tendencia de stock');
            chartInstances.stockTrendChart = new Chart(stockTrendChart.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                    datasets: [{
                        label: 'Nivel de Stock',
                        data: [450, 420, 380, 520, 480, 430, 390, 350, 480, 520, 490, 430],
                        borderColor: 'rgba(0, 188, 212, 1)',
                        backgroundColor: 'rgba(0, 188, 212, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Punto de Reorden',
                        data: [400, 400, 400, 400, 400, 400, 400, 400, 400, 400, 400, 400],
                        borderColor: 'rgba(255, 87, 34, 0.8)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Tendencia de Stock vs. Punto de Reorden',
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 300,
                            max: 600
                        }
                    }
                }
            });
        }

        // 6. Nuevo gráfico: Rendimiento de Ventas por Hora
        const hourlyPerformanceChart = document.getElementById('hourlyPerformanceChart');
        if (hourlyPerformanceChart) {
            console.log('Inicializando gráfico de rendimiento por hora');
            chartInstances.hourlyPerformanceChart = new Chart(hourlyPerformanceChart.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: ['8:00', '9:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00'],
                    datasets: [{
                        label: 'Ventas por Hora',
                        data: [12, 19, 25, 32, 28, 22, 18, 24, 30, 35, 42, 38, 29, 20, 15],
                        backgroundColor: 'rgba(76, 175, 80, 0.7)',
                        borderColor: 'rgba(76, 175, 80, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Rendimiento de Ventas por Hora',
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        alert('Error al cargar los gráficos del Dashboard. Por favor, recarga la página.');
    }
}

// Función para destruir gráficos existentes antes de crear nuevos
function destroyExistingCharts() {
    console.log('Destruyendo gráficos existentes...');

    // Lista de IDs de canvas para los gráficos
    const chartCanvasIds = [
        'visitsMonthlyChart',
        'devicesChart',
        'topProductsChart',
        'categoriesChart',
        'stockTrendChart',
        'hourlyPerformanceChart'
    ];

    // Destruir cada instancia de gráfico existente
    for (const chartId of chartCanvasIds) {
        if (chartInstances[chartId]) {
            console.log(`Destruyendo gráfico: ${chartId}`);
            chartInstances[chartId].destroy();
            chartInstances[chartId] = null;
        }
    }

    // También podemos buscar y destruir cualquier instancia de Chart.js en el registro global
    if (Chart.instances) {
        Object.keys(Chart.instances).forEach(key => {
            const chart = Chart.instances[key];
            if (chart) {
                console.log(`Destruyendo gráfico adicional con ID: ${chart.id}`);
                chart.destroy();
            }
        });
    }

    console.log('Todos los gráficos existentes han sido destruidos');
}

// Crear tarjetas para visualización móvil
function createProductCards() {
    console.log("Creando tarjetas de productos");
    const productCardsContainer = document.getElementById('product-cards-container');
    if (!productCardsContainer) {
        console.error("No se encontró el contenedor de tarjetas con ID 'product-cards-container'");
        return;
    }

    // Limpiar tarjetas existentes
    productCardsContainer.innerHTML = '';

    // Obtener filas de la tabla de productos
    const productRows = document.querySelectorAll('.admin-table tbody tr');
    if (!productRows || productRows.length === 0) {
        console.warn("No se encontraron filas de productos en la tabla");
        productCardsContainer.innerHTML = '<p class="no-products-message">No hay productos disponibles</p>';
        return;
    }

    // Si hay una sola fila y es de carga o error, no crear tarjetas
    if (productRows.length === 1 &&
        (productRows[0].querySelector('.loading-row') ||
         productRows[0].querySelector('.error-row'))) {
        console.log("La tabla está en estado de carga o error, no se crean tarjetas.");
        return;
    }

    console.log(`Procesando ${productRows.length} productos`);

    // Crear tarjeta para cada producto
    productRows.forEach((row, index) => {
        try {
            // Crear un elemento de tarjeta
            const card = document.createElement('div');
            card.className = 'product-card';
            card.dataset.rowIndex = index;

            // Obtener ID del producto si está disponible
            const productId = row.cells[0] ? row.cells[0].textContent.trim() : '';
            if (productId) {
                card.dataset.productId = productId;
            }

            // Obtener datos del producto de la fila de la tabla con verificación
            const imageEl = row.querySelector('.product-image-cell img');
            const image = imageEl && imageEl.src ? imageEl.src : '../images/placeholder.png';

            const nameEl = row.querySelector('.product-name-cell .product-name');
            const productName = nameEl ? nameEl.textContent : 'Producto';

            const categoryEl = row.querySelector('.product-name-cell .product-category');
            const productCategory = categoryEl ? categoryEl.textContent : 'Categoría';

            // Obtener precio y posibles descuentos
            const priceEl = row.querySelector('.current-price');
            const price = priceEl ? priceEl.textContent : '';

            const originalPriceEl = row.querySelector('.original-price');
            const originalPrice = originalPriceEl ? originalPriceEl.textContent : '';

            // Obtener condición y estado
            const conditionEl = row.querySelector('.condition-badge');
            const condition = conditionEl ? conditionEl.textContent : '';
            const conditionClass = conditionEl && conditionEl.className.includes(' ') ?
                conditionEl.className.split(' ')[1] : '';

            const statusEl = row.querySelector('.status-badge');
            const status = statusEl ? statusEl.textContent : '';
            const statusClass = statusEl && statusEl.className.includes(' ') ?
                statusEl.className.split(' ')[1] : '';

            // Crear estructura HTML de la tarjeta con validaciones
            card.innerHTML = `
                <div class="card-image">
                    <img src="${image}" alt="${productName}">
                    <div class="card-badges">
                        ${condition ? `<span class="condition-badge ${conditionClass}">${condition}</span>` : ''}
                        ${status ? `<span class="status-badge ${statusClass}">${status}</span>` : ''}
                    </div>
                    ${originalPrice ? `<div class="discount-badge">OFERTA</div>` : ''}
                </div>
                <div class="card-body">
                    <h3 class="card-title">${productName}</h3>
                    <p class="card-category">${productCategory}</p>
                    <div class="card-price">
                        <span class="current-price">${price}</span>
                        ${originalPrice ? `<span class="original-price">${originalPrice}</span>` : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button class="card-btn edit-btn" type="button" data-id="${productId}">
                        <i class="fas fa-edit"></i> Editar
                    </button>
                    <button class="card-btn delete-btn" type="button" data-id="${productId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            // Agregar eventos a los botones
            setupCardButtons(card);

            // Agregar la tarjeta al contenedor
            productCardsContainer.appendChild(card);
        } catch (error) {
            console.error(`Error al crear tarjeta para fila ${index}:`, error);
        }
    });

    console.log(`Se crearon ${productCardsContainer.children.length} tarjetas de productos`);
}

// Configurar eventos para botones de tarjeta
function setupCardButtons(card) {
    // Botón editar
    const editBtn = card.querySelector('.edit-btn');
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            const productId = this.dataset.id;
            console.log(`Botón editar pulsado en tarjeta para producto ${productId}`);
            handleEditProduct(productId);
        });
    }

    // Botón eliminar
    const deleteBtn = card.querySelector('.delete-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            const productId = this.dataset.id;
            console.log(`Botón eliminar pulsado en tarjeta para producto ${productId}`);
            handleDeleteProduct(productId);
        });
    }
}

// Configurar eventos para botones de la tabla
function setupTableButtons() {
    const tableBody = document.querySelector('.admin-table tbody');
    if (!tableBody) return;

    console.log("Configurando event listener para botones de tabla");

    // Eliminar listener anterior si existe
    tableBody.removeEventListener('click', handleTableButtonClick);

    // Agregar nuevo listener con delegación de eventos
    tableBody.addEventListener('click', handleTableButtonClick);
}

function handleTableButtonClick(e) {
    const target = e.target.closest('.action-btn');
    if (!target) return;

    console.log("Botón de tabla clickeado:", target.className);

    if (target.classList.contains('edit-btn')) {
        const productId = target.dataset.id;
        if (productId) {
            console.log(`Editando producto con ID: ${productId}`);
            handleEditProduct(productId);
        } else {
            console.warn("No se pudo determinar el ID del producto a editar");
        }
    }
    else if (target.classList.contains('delete-btn')) {
        const productId = target.dataset.id;
        if (productId) {
            console.log(`Eliminando producto con ID: ${productId}`);
            handleDeleteProduct(productId);
        } else {
            console.warn("No se pudo determinar el ID del producto a eliminar");
        }
    }
}

// Función para eliminar producto
// Función para manejar la edición de productos
async function handleEditProduct(productId) {
    try {
        console.log('Iniciando edición de producto con ID:', productId);

        // Asegurarse de que el ID sea un número entero válido
        const cleanId = String(productId).replace(/[^0-9]/g, '');
        console.log('ID limpiado para la solicitud:', cleanId);

        // Intentar primero con get_product_details.php (POST)
        try {
            console.log('Intentando cargar datos con get_product_details.php');
            const detailsResponse = await fetch('../public/API/productos/get_product_details.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                body: JSON.stringify({ id: cleanId }),
                credentials: 'include'
            });

            if (detailsResponse.ok) {
                const data = await detailsResponse.json();
                console.log('Datos recibidos de get_product_details.php:', data);

                if (data.success) {
                    // Abrir panel de edición
                    openEditPanel(cleanId);

                    // Llenar formulario con datos del producto
                    fillEditForm(data.producto);

                    // Cargar categorías y subcategorías
                    await loadCategories(data.producto.categoria_id, data.producto.subcategoria_id);

                    return; // Salir si todo fue exitoso
                }
            }

            console.log('Fallback a get_product.php debido a error en get_product_details.php');
        } catch (detailsError) {
            console.error('Error al usar get_product_details.php:', detailsError);
            console.log('Fallback a get_product.php');
        }

        // Si llegamos aquí, intentar con get_product.php (GET)
        const response = await fetch(`../public/API/productos/get_product.php?id=${cleanId}`, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`Error al cargar datos del producto: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Datos del producto recibidos:', data);

        if (!data.success) {
            throw new Error(data.message || 'Error al cargar datos del producto');
        }

        const producto = data.producto;

        // Abrir panel de edición
        openEditPanel(productId);

        // Llenar formulario con datos del producto
        fillEditForm(producto);

        // Cargar categorías y subcategorías
        await loadCategories(producto.categoria_id, producto.subcategoria_id);

    } catch (error) {
        console.error('Error al editar producto:', error);
        showNotification('Error: ' + error.message, 'error');
    }
}

async function handleDeleteProduct(productId) {
    try {
        console.log(`Eliminando producto ID: ${productId}`);

        if (!productId) {
            throw new Error("No se proporcionó un ID de producto válido");
        }

        // Mostrar el modal de confirmación
        const modal = document.getElementById('deleteConfirmModal');
        const confirmBtn = document.getElementById('confirmDelete');
        const cancelBtn = document.getElementById('cancelDelete');
        const closeBtn = document.getElementById('closeDeleteModal');

        // Función para cerrar el modal
        const closeModal = () => {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        };

        // Mostrar el modal con animación
        modal.style.display = 'block';
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);

        // Manejar el cierre del modal
        closeBtn.onclick = closeModal;
        cancelBtn.onclick = closeModal;

        // Cuando se hace clic fuera del modal
        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };

        // Retornar una promesa que se resuelve cuando el usuario toma una decisión
        return new Promise((resolve, reject) => {
            confirmBtn.onclick = async () => {
                try {
                    // Realizar la llamada a la API para eliminar el producto
                    const response = await fetch('../public/API/productos/delete_product.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ id: productId }),
                        credentials: 'include'
                    });

                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Error al eliminar el producto');
                    }

                    const data = await response.json();

                    if (data.success) {
                        showNotification(`Producto ${productId} eliminado con éxito`, 'success');
                        // Recargar la lista de productos
                        await loadProducts();
                        resolve(true);
                    } else {
                        throw new Error(data.message || 'Error al eliminar el producto');
                    }
                } catch (error) {
                    console.error("Error al eliminar producto:", error);
                    showNotification(`Error al eliminar producto: ${error.message}`, 'error');
                    reject(error);
                } finally {
                    closeModal();
                }
            };
        });
    } catch (error) {
        console.error("Error al eliminar producto:", error);
        showNotification(`Error al eliminar producto: ${error.message}`, 'error');
    }
}

// Actualizar vista según ancho de pantalla
function updateResponsiveView() {
    console.log(`Actualizando vista responsiva. Ancho de ventana: ${window.innerWidth}px`);
    const isMobile = window.innerWidth <= 768;

    // Obtener elementos
    const adminTable = document.querySelector('.admin-table');
    const productCardsContainer = document.getElementById('product-cards-container');

    if (!adminTable || !productCardsContainer) {
        console.error("No se encontró la tabla o el contenedor de tarjetas");
        return;
    }

    // Mostrar vista correspondiente según ancho de pantalla
    if (isMobile) {
        console.log("Mostrando vista móvil (tarjetas)");
        adminTable.style.display = 'none';
        productCardsContainer.style.display = 'flex';
    } else {
        console.log("Mostrando vista desktop (tabla)");
        adminTable.style.display = 'table';
        productCardsContainer.style.display = 'none';
    }
}

// Función para inicializar las tarjetas responsivas
function initResponsiveProductCards() {
    createProductCards();
    setupTableButtons();
    updateResponsiveView();
}

// Actualizar el loadProducts para que sea más robusto
async function loadProducts() {
    try {
        console.log("Iniciando carga de productos");
        // Mostrar un indicador de carga en la tabla
        const tbody = document.querySelector('.admin-table tbody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="loading-row">
                        <i class="fas fa-spinner fa-spin"></i> Cargando productos...
                    </td>
                </tr>
            `;
        }

        // Obtener el ID de usuario del atributo data-user-id del body
        const userId = document.body.getAttribute('data-user-id');
        const userRole = document.body.getAttribute('data-user-role');

        console.log("Cargando productos para usuario:", userId, "con rol:", userRole);

        // Determinar la URL base actual
        const currentPath = window.location.pathname;
        let apiUrl;

        if (currentPath.includes('/public/')) {
            // Estamos en un archivo dentro de /public/
            apiUrl = './API/productos/get_products.php';
        } else {
            // Estamos en otra ubicación, usar ruta relativa
            apiUrl = '/API/productos/get_products.php';
        }

        console.log("Intentando cargar productos desde:", apiUrl);

        const response = await fetch(apiUrl, {
            method: 'GET',
            credentials: 'same-origin',  // Importante para mantener la sesión
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest', // Para indicar que es una petición AJAX
                'Cache-Control': 'no-cache, no-store, must-revalidate'
            }
        });

        console.log("Respuesta recibida:", response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error("Texto de error:", errorText);
            throw new Error(`Error al cargar productos: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log("Datos obtenidos:", data);

        // Verificar que la respuesta tenga la estructura esperada
        if (!data.success) {
            throw new Error(data.message || 'Respuesta sin éxito desde el servidor');
        }

        if (!Array.isArray(data.productos)) {
            console.error("Estructura de datos inesperada:", data);
            throw new Error('La respuesta no contiene un array de productos');
        }

        // Actualizar la tabla
        updateProductsTable(data.productos);

        // Recrear las tarjetas y configurar los botones
        createProductCards();
        setupTableButtons();

        // Actualizar la vista responsiva
        updateResponsiveView();

        console.log(`${data.productos.length} productos cargados exitosamente`);
    } catch (error) {
        console.error('Error cargando productos:', error);

        // Mostrar error en la tabla con botón para reintentar
        const tbody = document.querySelector('.admin-table tbody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="error-row">
                        Error al cargar productos: ${error.message}
                        <button id="retryLoadProducts" class="btn btn-primary btn-sm" style="margin-top: 10px;">
                            <i class="fas fa-sync-alt"></i> Reintentar
                        </button>
                    </td>
                </tr>
            `;

            // Agregar evento al botón de reintentar
            const retryBtn = document.getElementById('retryLoadProducts');
            if (retryBtn) {
                retryBtn.addEventListener('click', function() {
                    loadProducts();
                });
            }
        }

        showNotification('Error al cargar productos: ' + error.message, 'error');
    }
}

function updateProductsTable(products) {
    const tbody = document.querySelector('.admin-table tbody');
    if (!tbody) return;

    if (!products || products.length === 0) {
        tbody.innerHTML = `<tr><td colspan="11" class="no-data">No hay productos disponibles</td></tr>`;
        return;
    }

    tbody.innerHTML = products.map(product => `
        <tr data-product-id="${product.id}">
            <td>${product.id}</td>
            <td class="product-image-cell">
                <img src="${product.imagen_principal || '../images/placeholder.png'}"
                     alt="${product.nombre}"
                     class="product-image-preview">
            </td>
            <td class="product-name-cell">
                <div class="product-name">${product.nombre}</div>
                <div class="product-category">${product.marca || ''}</div>
            </td>
            <td>${product.descripcion ? product.descripcion.substring(0, 100) + '...' : (product.descripcion_corta || '')}</td>
            <td class="product-price">
                <div class="current-price">$${Math.round(Number(product.precio)).toLocaleString('es-CL')}</div>
                ${product.precio_original ? `
                    <div class="original-price">$${Math.round(Number(product.precio_original)).toLocaleString('es-CL')}</div>
                ` : ''}
            </td>
            <td class="${product.stock < 5 ? 'low-stock' : ''}">${product.stock}</td>
            <td>
                <span class="status-badge status-${product.estado ? product.estado.toLowerCase() : 'unknown'}">
                    ${product.estado || 'Desconocido'}
                </span>
            </td>
            <td>
                <span class="condition-badge condition-${product.condicion ? product.condicion.toLowerCase() : 'ninguno'}">
                    ${product.condicion || 'Ninguna'}
                </span>
            </td>
            <td>${product.categoria_nombre || ''}</td>
            <td>${product.subcategoria_nombre || ''}</td>
            <td class="actions">
                <div class="table-actions">
                    <button class="action-btn edit-btn" title="Editar producto" data-id="${product.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" title="Eliminar producto" data-id="${product.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Función para mostrar notificaciones
function showNotification(message, type = 'success') {
    // Si es una notificación de éxito de actualización de producto, no mostrarla
    if (type === 'success' && (
        message.includes('actualizado correctamente') ||
        message.includes('Producto actualizado')
    )) {
        console.log('Notificación de actualización suprimida:', message);
        return;
    }

    // Crear el elemento de notificación
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;

    // Agregar la notificación al DOM
    document.body.appendChild(notification);

    // Mostrar la notificación con animación
    setTimeout(() => notification.classList.add('show'), 100);

    // Remover la notificación después de 3 segundos
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Función para resetear el formulario
function resetProductForm() {
    const formInputs = [
        'productName', 'productDescription', 'productShortDescription', 'productPrice',
        'productOriginalPrice', 'productStock', 'productCategory', 'productSubcategory',
        'productTags', 'productSKU', 'productBrand', 'productColor', 'productWeight',
        'productDimensions'
    ];

    formInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';
        }
    });

    // Resetear radio buttons de condición
    const conditionRadio = document.querySelector('input[name="productCondition"][value="ninguno"]');
    if (conditionRadio) {
        conditionRadio.checked = true;
    }
}

// Función para cargar subcategorías - Desactivada si se usa CategoryManager
document.getElementById('productCategory')?.addEventListener('change', async function() {
    // Verificar si estamos usando el CategoryManager
    if (typeof window.productCategoryManager !== 'undefined') {
        console.log('El CategoryManager ya maneja los cambios de categoría');
        return; // El CategoryManager ya se encarga de esto
    }

    const categorySelect = this;
    const subcategorySelect = document.getElementById('productSubcategory');

    if (!subcategorySelect) return;

    try {
        // Usar método GET en lugar de POST
        const categoriaId = categorySelect.value;
        const url = `../public/API/productos/get_subcategorias.php?categoria_id=${encodeURIComponent(categoriaId)}`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });

        if (!response.ok) {
            throw new Error(`Error en la respuesta: ${response.status}`);
        }

        const responseText = await response.text();
        let data;

        try {
            data = JSON.parse(responseText);
        } catch (e) {
            console.error('Error al parsear JSON:', e, responseText);
            throw new Error('Respuesta del servidor inválida');
        }

        // Limpiar opciones actuales
        subcategorySelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';

        // Verificar la estructura de la respuesta
        if (data.success && Array.isArray(data.subcategorias)) {
            // Añadir nuevas opciones
            if (data.subcategorias.length === 0) {
                subcategorySelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                return;
            }

            data.subcategorias.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre;
                subcategorySelect.appendChild(option);
            });
        } else if (Array.isArray(data)) {
            // Formato alternativo
            if (data.length === 0) {
                subcategorySelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
                return;
            }

            data.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                subcategorySelect.appendChild(option);
            });
        } else {
            subcategorySelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
        }
    } catch (error) {
        console.error('Error al cargar subcategorías:', error);
        subcategorySelect.innerHTML = '<option value="">No hay subcategorías disponibles</option>';
    }
});

// Función para manejar el modal de imagen
function setupImageModal() {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    const closeBtn = document.querySelector('.close-modal');

    // Agregar evento de clic a todas las imágenes de productos (tanto en tabla como en tarjetas)
    document.addEventListener('click', function(e) {
        // Verificar si es una imagen en la tabla
        const productImageTable = e.target.closest('.product-image-preview');

        // Verificar si es una imagen en una tarjeta (vista móvil)
        const productImageCard = e.target.closest('.card-image img');

        // Si es cualquiera de los dos tipos de imagen
        if (productImageTable || productImageCard) {
            const imgSrc = productImageTable ? productImageTable.src : productImageCard.src;
            modal.style.display = 'block';
            modalImg.src = imgSrc;
            document.body.style.overflow = 'hidden'; // Evitar scroll
        }
    });

    // Cerrar modal al hacer clic en el botón de cerrar
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
            document.body.style.overflow = ''; // Restaurar scroll
        });
    }

    // Cerrar modal al hacer clic fuera de la imagen
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = ''; // Restaurar scroll
        }
    });

    // Cerrar modal con la tecla Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            modal.style.display = 'none';
            document.body.style.overflow = ''; // Restaurar scroll
        }
    });
}

// Inicializar la aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM completamente cargado');

    // Inicializar el modal de imagen
    setupImageModal();

    // Obtener referencias a elementos DOM principales
    sidebar = getElementSafe('sidebar');
    productsSection = getElementSafe('productsSection');
    editProductSection = getElementSafe('editProductSection');
    storeInfoSection = getElementSafe('storeInfoSection');
    statsSection = getElementSafe('statsSection');
    categoriesSection = getElementSafe('categoriesSection');
    dashboardLink = getElementSafe('dashboardLink');
    productsNavLink = getElementSafe('productsNavLink');
    categoriesLink = getElementSafe('categoriesLink');
    storeNavLink = getElementSafe('storeNavLink');
    asideToggle = getElementSafe('aside-toggle');
    toggleIcon = getElementSafe('toggle-icon');

    // Depuración: Verificar qué secciones están visibles al cargar
    console.log('Estado inicial de las secciones:');
    document.querySelectorAll('.content-section').forEach(section => {
        console.log(`${section.id}: ${getComputedStyle(section).display}`);
    });

    // Agregar manejadores de eventos para botones específicos
    const logoutBtn = getElementSafe('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const logoutUrl = this.href;
            window.location.replace(logoutUrl);
        });
    }

    // Agregar manejador de eventos para el botón Dashboard
    if (dashboardLink) {
        console.log('Configurando evento de clic para dashboardLink');
        dashboardLink.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Clic en Dashboard detectado');

            // Usar la función showStatsSection para mostrar el dashboard
            // Esta función ya verifica que la sección exista, oculta las demás secciones,
            // actualiza las clases activas e inicializa los gráficos
            showStatsSection();
        });
    }

    const saveProductBtn = getElementSafe('saveProductBtn');
    if (saveProductBtn) {
        saveProductBtn.addEventListener('click', handleSaveProduct);
    }

    // Inicialización para el panel de edición lateral
    setupEditProductPanel();

    // Añadir logs de depuración
    console.log('Manejadores de eventos del panel de edición configurados');

    // Iniciar la aplicación
    initApp();

    // Configurar overlay para cerrar paneles
    setupOverlays();

    // Reset overlay al inicio para garantizar un estado limpio
    resetOverlay();

    // Añadir manejador global para Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const editPanel = document.getElementById('editProductPanel');
            if (editPanel && getComputedStyle(editPanel).display !== 'none') {
                closeEditPanel();
            }

            const filterContainer = document.getElementById('filterContainer');
            if (filterContainer && getComputedStyle(filterContainer).display !== 'none') {
                closeFilterPanel();
            }
        }
    });

    // Añadir manejo específico para el botón de cerrar
    const closeEditBtn = document.getElementById('closeEdit');
    if (closeEditBtn) {
        closeEditBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }

    // Asegurarse que el botón de cerrar en el panel de edición funcione correctamente
    const panelCancelBtn = document.querySelector('#editProductPanel #cancelEditBtn');
    if (panelCancelBtn) {
        panelCancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }

    // Agregar manejador específico para el overlay
    const editOverlay = document.getElementById('editOverlay');
    if (editOverlay) {
        editOverlay.addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditPanel();
            }
        });
    }
});

// Configurar overlays para edición y filtros
function setupOverlays() {
    const editOverlay = document.getElementById('editOverlay');
    const filterOverlay = document.getElementById('filterOverlay');

    if (editOverlay) {
        editOverlay.addEventListener('click', closeEditPanel);
    }

    if (filterOverlay) {
        filterOverlay.addEventListener('click', closeFilterPanel);
    }

    console.log('Overlays configurados correctamente');
}

// Función para inicializar la aplicación
function initApp() {
    console.log("Inicializando aplicación...");

    // Configurar el sidebar
    initSidebar();

    // Ya no necesitamos configurar eventos para pestañas principales porque las eliminamos

    // Configurar botones de guardar y cancelar para cada formulario
    const saveProductBtn = document.getElementById('saveProductBtn');
    const cancelProductBtn = document.getElementById('cancelProductBtn');
    const saveStoreBtn = document.getElementById('saveStoreBtn');
    const cancelStoreBtn = document.getElementById('cancelStoreBtn');

    if (saveProductBtn) {
        saveProductBtn.addEventListener('click', handleSaveProduct);
    }

    if (cancelProductBtn) {
        cancelProductBtn.addEventListener('click', function() {
            showProductsSection();
        });
    }

    if (saveStoreBtn) {
        saveStoreBtn.addEventListener('click', handleSaveStore);
    }

    if (cancelStoreBtn) {
        cancelStoreBtn.addEventListener('click', function() {
            showProductsSection();
        });
    }

    // Configurar eventos para enlaces del sidebar
    // Nota: El evento para dashboardLink ahora se configura en el DOMContentLoaded
    /*
    if (dashboardLink) {
        dashboardLink.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Click en dashboardLink');

            // Forzar la visibilidad de las secciones
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // Mostrar directamente la sección de Dashboard
            const statsSection = document.getElementById('statsSection');
            if (statsSection) {
                statsSection.style.display = 'block';
                console.log('Sección statsSection mostrada directamente desde dashboardLink');

                // Actualizar clases activas en la navegación
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                });
                dashboardLink.classList.add('active');

                // Inicializar gráficos si Chart.js está disponible
                if (typeof Chart !== 'undefined') {
                    initializeCharts();
                    console.log('Gráficos inicializados desde dashboardLink');
                }
            } else {
                console.error('No se encontró la sección statsSection');
                alert('Error: No se encontró la sección de Dashboard. Por favor, recarga la página.');
            }
        });
    }
    */
    if (productsNavLink) {
        productsNavLink.addEventListener('click', function(e) {
            e.preventDefault();
            showProductsSection();
        });
    }
    if (categoriesLink) {
        categoriesLink.addEventListener('click', function(e) {
            e.preventDefault();
            showCategoriesSection();
        });
    }
    if (storeNavLink) {
        storeNavLink.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Click en storeNavLink');

            // Usar la función showStoreInfoSection para mostrar la sección de Mi Tienda
            // Esta función ya verifica que la sección exista y maneja los errores
            showStoreInfoSection();
        });
    }

    // Configurar botón toggle del sidebar
    if (asideToggle) {
        asideToggle.addEventListener('click', toggleSidebar);
    }

    // Configurar botón Nuevo Producto
    const newProductBtn = getElementSafe('newProductBtn');
    if (newProductBtn) {
        newProductBtn.addEventListener('click', function() {
            // Restablecer el formulario
            resetProductForm();

            // Actualizar título del formulario
            const sectionTitle = document.querySelector('#editProductSection .section-title');
            if (sectionTitle) {
                sectionTitle.innerHTML = '<i class="fas fa-plus"></i> Agregar Producto';
            }

            // Mostrar la sección de edición
            showEditProductSection();
        });
    }

    // Configurar botón Cancelar Edición
    const cancelEditBtn = getElementSafe('cancelEditBtn');
    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', showProductsSection);
    }

    // Cargar los productos inicialmente
    loadProducts();

    // Configurar botones de tabla y tarjetas
    setupTableButtons();

    // Verificar que las secciones existen antes de mostrarlas
    console.log("Verificando secciones antes de mostrarlas:");
    console.log("statsSection existe:", document.getElementById('statsSection') !== null);
    console.log("storeInfoSection existe:", document.getElementById('storeInfoSection') !== null);
    console.log("productsSection existe:", document.getElementById('productsSection') !== null);

    // Mostrar sección inicial (ahora es el Dashboard)
    console.log('Mostrando sección inicial (Dashboard) desde initApp');

    // Usar la función showStatsSection para mostrar el dashboard
    // Esta función ya verifica que la sección exista, oculta las demás secciones,
    // actualiza las clases activas e inicializa los gráficos
    showStatsSection();

    console.log("Aplicación inicializada correctamente");
}

// Safari Fix y window resize listener se mantienen fuera del DOMContentLoaded
// Safari Fix: Forzar un reflow después de la carga para asegurar la correcta visualización
setTimeout(function() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.style.display = 'none';
        // Force reflow
        void mainContent.offsetHeight;
        mainContent.style.display = '';
    }
}, 100);

// Ajustar sidebar y vista responsiva cuando cambia el tamaño de la ventana
window.addEventListener('resize', function() {
    initSidebar();
    if (document.getElementById('productsSection').style.display === 'block') {
        updateResponsiveView();
    }
});

// Definición de handleSaveProduct y otras funciones auxiliares
// Definir la función en el objeto window para que sea accesible globalmente
window.handleSaveProduct = async function() {
    console.log('=== INICIO handleSaveProduct ===');
    console.log('Evento de click en saveProductBtn detectado');

    try {
        console.log('Iniciando guardado de producto...');

        // Verificar que los elementos del formulario existen
        console.log('Verificando elementos del formulario...');
        const productNameEl = document.getElementById('productName');
        const productDescriptionEl = document.getElementById('productDescription');
        const productShortDescriptionEl = document.getElementById('productShortDescription');
        const productPriceEl = document.getElementById('productPrice');
        const productOriginalPriceEl = document.getElementById('productOriginalPrice');
        const productStockEl = document.getElementById('productStock');
        const productSKUEl = document.getElementById('productSKU');
        const productCategoryEl = document.getElementById('productCategory');
        const productTipoCategoriaEl = document.getElementById('productTipoCategoria');
        const negocioSelectEl = document.getElementById('selectNegocio');
        const productConditionEl = document.querySelector('input[name="productCondition"]:checked');

        console.log('Elementos encontrados:', {
            productNameEl,
            productDescriptionEl,
            productPriceEl,
            productStockEl,
            productCategoryEl,
            productTipoCategoriaEl,
            negocioSelectEl,
            productConditionEl
        });

        // Obtener el formulario
        const formData = {
            productName: productNameEl?.value,
            productDescription: productDescriptionEl?.value,
            productShortDescription: productShortDescriptionEl?.value,
            productPrice: productPriceEl?.value,
            productOriginalPrice: productOriginalPriceEl?.value,
            productStock: productStockEl?.value,
            productSKU: productSKUEl?.value,
            negocio_id: negocioSelectEl?.value,
            productCondition: productConditionEl?.value
        };

        // Obtener valores de categoría usando el nuevo gestor de categorías
        if (typeof getSelectedCategoryValues === 'function') {
            console.log('Usando CategoryManager para obtener valores de categoría');
            const categoryValues = getSelectedCategoryValues(false); // false indica que estamos en modo creación

            // Añadir valores de categoría al formData
            formData.productTipoCategoria = categoryValues.tipoId;
            formData.productCategory = categoryValues.categoriaId;
            formData.productSubcategory = categoryValues.subcategoriaId;
        } else {
            console.warn('CategoryManager no disponible, usando método antiguo');
            formData.productCategory = productCategoryEl?.value;
            formData.productTipoCategoria = productTipoCategoriaEl?.value;
            formData.productSubcategory = document.getElementById('productSubcategory')?.value || null;
        }

        console.log('Datos del formulario:', formData);

        // Validar campos requeridos
        const requiredFields = {
            productName: 'Nombre del producto',
            productDescription: 'Descripción',
            productPrice: 'Precio',
            productStock: 'Stock',
            productCategory: 'Categoría',
            negocio_id: 'Negocio'
        };

        // Validar tipo de categoría solo si existe el campo en el formulario
        if (document.getElementById('productTipoCategoria')) {
            requiredFields.productTipoCategoria = 'Tipo de categoría';
        }

        console.log('Validando campos requeridos...');
        for (const [field, label] of Object.entries(requiredFields)) {
            if (!formData[field]) {
                console.error(`Campo requerido faltante: ${label} (${field})`);
                throw new Error(`El campo ${label} es requerido`);
            }
        }
        console.log('Todos los campos requeridos están presentes');

        // Mostrar indicador de carga
        console.log('Buscando botón saveProductBtn...');
        const saveButton = document.getElementById('saveProductBtn');
        console.log('Botón encontrado:', saveButton);

        if (!saveButton) {
            console.error('ERROR: No se encontró el botón saveProductBtn en el DOM');
            throw new Error('No se encontró el botón de guardar');
        }

        console.log('Cambiando estado del botón a "cargando"...');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveButton.disabled = true;

        // Preparar datos para enviar al servidor
        console.log('Preparando datos para enviar al servidor...');
        const jsonData = JSON.stringify(formData);
        console.log('Datos JSON a enviar:', jsonData);

        // URL del endpoint
        const url = '../public/API/productos/save_product.php';
        console.log('URL del endpoint:', url);

        try {
            console.log('Iniciando petición fetch...');
            // Enviar datos al servidor
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: jsonData,
                credentials: 'include'
            });

            console.log('Respuesta del servidor recibida:', {
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                headers: [...response.headers.entries()]
            });

            if (!response.ok) {
                console.error('Error en la respuesta del servidor:', response.status, response.statusText);

                // Intentar obtener el mensaje de error
                let errorMessage;
                try {
                    const errorData = await response.json();
                    console.error('Datos de error:', errorData);
                    errorMessage = errorData.message || `Error del servidor: ${response.status}`;
                } catch (e) {
                    console.error('No se pudo parsear la respuesta de error como JSON');
                    const errorText = await response.text();
                    console.error('Texto de error:', errorText);
                    errorMessage = `Error del servidor: ${response.status} ${response.statusText}`;
                }

                throw new Error(errorMessage);
            }

            console.log('Parseando respuesta como JSON...');
            let responseText;
            try {
                responseText = await response.text();
                console.log('Texto de respuesta:', responseText);
                const data = JSON.parse(responseText);
                console.log('Datos recibidos:', data);

                if (data.success) {
                    console.log('Producto guardado exitosamente');
                    showNotification('Producto guardado exitosamente', 'success');
                    // Recargar la lista de productos
                    console.log('Recargando lista de productos...');
                    await loadProducts();
                    // Limpiar el formulario
                    console.log('Limpiando formulario...');
                    resetProductForm();
                    // Volver a la lista de productos
                    console.log('Volviendo a la lista de productos...');
                    showProductsSection();
                } else {
                    console.error('Error en la respuesta:', data);
                    throw new Error(data.message || 'Error al guardar el producto');
                }

                return data;
            } catch (e) {
                console.error('Error al parsear JSON:', e);
                console.error('Texto que causó el error:', responseText);
                throw new Error('Error al procesar la respuesta del servidor');
            }
        } catch (fetchError) {
            console.error('Error en la petición fetch:', fetchError);
            throw fetchError;
        }

    } catch (error) {
        console.error('=== ERROR EN handleSaveProduct ===');
        console.error('Error completo:', error);
        console.error('Stack trace:', error.stack);

        // Mostrar alerta para depuración
        alert('Error al guardar producto: ' + error.message);

        // Mostrar notificación
        showNotification('Error: ' + error.message, 'error');
    } finally {
        console.log('=== FIN handleSaveProduct (finally) ===');
        // Restaurar el botón
        const saveButton = document.getElementById('saveProductBtn');
        if (saveButton) {
            console.log('Restaurando botón saveProductBtn...');
            saveButton.innerHTML = '<i class="fas fa-save"></i> Guardar producto';
            saveButton.disabled = false;
        } else {
            console.error('No se encontró el botón saveProductBtn para restaurarlo');
        }
    }
}

// Función para guardar la información de la tienda
async function handleSaveStore() {
    try {
        // Obtener el negocio seleccionado
        const negocioSelect = document.getElementById('selectNegocio');
        if (!negocioSelect || !negocioSelect.value) {
            throw new Error('Debe seleccionar un negocio');
        }

        // Obtener los valores del formulario
        const formData = {
            negocio_id: negocioSelect.value,
            nombre: document.getElementById('storeName')?.value || '',
            descripcion: document.getElementById('storeDescription')?.value || '',
            direccion: document.getElementById('storeAddress')?.value || '',
            ciudad: document.getElementById('storeCity')?.value || '',
            region: document.getElementById('storeRegion')?.value || '',
            telefono: document.getElementById('storePhone')?.value || '',
            email: document.getElementById('storeEmail')?.value || '',
            facebook: document.getElementById('storeFacebook')?.value || '',
            twitter: document.getElementById('storeTwitter')?.value || '',
            instagram: document.getElementById('storeInstagram')?.value || '',
            youtube: document.getElementById('storeYoutube')?.value || '',
            whatsapp: document.getElementById('storeWhatsapp')?.value || '',
            latitud: document.getElementById('storeLatitude')?.value || '',
            longitud: document.getElementById('storeLongitude')?.value || '',
            horario: document.getElementById('storeHours')?.value || ''
        };

        console.log('Datos de la tienda a guardar:', formData);

        // Validar campos requeridos
        const requiredFields = ['nombre', 'descripcion', 'direccion', 'ciudad', 'region', 'telefono', 'email'];
        for (const field of requiredFields) {
            if (!formData[field]) {
                throw new Error(`El campo ${field} es requerido`);
            }
        }

        // Mostrar indicador de carga
        const saveButton = document.getElementById('saveStoreBtn');
        if (!saveButton) {
            throw new Error('No se encontró el botón de guardar en el DOM');
        }

        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveButton.disabled = true;

        // Enviar datos al servidor
        const response = await fetch('../public/API/negocios/save_negocio.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(formData),
            credentials: 'include'
        });

        console.log('Respuesta del servidor - Status:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json();
            console.error('Detalles del error del servidor:', errorData);
            throw new Error(errorData.message || 'Error en la respuesta del servidor');
        }

        const data = await response.json();
        console.log('Datos recibidos del servidor:', data);

        if (data.success) {
            // Mostrar notificación de éxito
            showNotification('Información de la tienda guardada exitosamente', 'success');

            // Cambiar a la pestaña de productos
            showProductsSection();
        } else {
            throw new Error(data.message || 'Error al guardar la información de la tienda');
        }

    } catch (error) {
        showNotification('Error: ' + error.message, 'error');
        console.error('Error completo:', error);
    } finally {
        // Restaurar el botón de guardar
        const saveButton = document.getElementById('saveStoreBtn');
        if (saveButton) {
            saveButton.innerHTML = '<i class="fas fa-save"></i> Guardar Cambios de Tienda';
            saveButton.disabled = false;
        }
    }
}

// Manejadores para el filtro
document.addEventListener('DOMContentLoaded', function() {
    const filterBtn = document.getElementById('filterBtn');
    const filterContainer = document.getElementById('filterContainer');
    const closeFilter = document.getElementById('closeFilter');
    const filterOverlay = document.getElementById('filterOverlay');

    // Abrir filtro
    filterBtn?.addEventListener('click', function() {
        if (filterContainer) {
            document.body.style.overflow = 'hidden';
            filterContainer.style.display = 'block';
            filterOverlay.style.display = 'block';

            // Forzar reflow
            void filterContainer.offsetWidth;
            void filterOverlay.offsetWidth;

            // Añadir clases show
            filterContainer.classList.add('show');
            filterOverlay.classList.add('show');
        }
    });

    // Cerrar filtro
    closeFilter?.addEventListener('click', function() {
        closeFilterPanel();
    });

    // Cerrar al hacer clic en el overlay
    filterOverlay?.addEventListener('click', function() {
        closeFilterPanel();
    });

    // Función para cerrar el panel de filtros
    function closeFilterPanel() {
        filterContainer.classList.remove('show');
        filterOverlay.classList.remove('show');

        setTimeout(() => {
            filterContainer.style.display = 'none';
            filterOverlay.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }

    // Aplicar filtros
    applyFilters?.addEventListener('click', function() {
        const filters = {
            search: document.getElementById('filterSearch')?.value || '',
            minPrice: document.getElementById('filterMinPrice')?.value || '',
            maxPrice: document.getElementById('filterMaxPrice')?.value || '',
            minStock: document.getElementById('filterMinStock')?.value || '',
            maxStock: document.getElementById('filterMaxStock')?.value || '',
            states: Array.from(document.querySelectorAll('input[type="checkbox"][value^="activo"], input[type="checkbox"][value^="borrador"], input[type="checkbox"][value^="agotado"]'))
                .filter(cb => cb.checked)
                .map(cb => cb.value),
            conditions: Array.from(document.querySelectorAll('input[type="checkbox"][value^="destacado"], input[type="checkbox"][value^="oferta"], input[type="checkbox"][value^="liquidacion"], input[type="checkbox"][value^="nuevo"], input[type="checkbox"][value^="exclusivo"]'))
                .filter(cb => cb.checked)
                .map(cb => cb.value),
            category: document.getElementById('filterCategory')?.value || '',
            subcategory: document.getElementById('filterSubcategory')?.value || ''
        };

        console.log("Aplicando filtros:", filters);

        // Filtrar productos en la tabla
        filterTableRows(filters);

        // Filtrar productos en la vista de tarjetas
        filterProductCards(filters);

        // Cerrar el filtro
        closeFilterPanel();

        // Mostrar notificación
        const activeCount = countVisibleProducts();
        showNotification(`Se encontraron ${activeCount} productos con los filtros aplicados`, 'success');
    });

    // Resetear filtros
    resetFilters?.addEventListener('click', function() {
        // Limpiar campos de texto y número
        document.getElementById('filterSearch').value = '';
        document.getElementById('filterMinPrice').value = '';
        document.getElementById('filterMaxPrice').value = '';
        document.getElementById('filterMinStock').value = '';
        document.getElementById('filterMaxStock').value = '';

        // Desmarcar checkboxes
        document.querySelectorAll('.filter-checkbox input[type="checkbox"]')
            .forEach(cb => cb.checked = false);

        // Resetear selects
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterSubcategory').value = '';

        // Mostrar todos los productos en la tabla
        document.querySelectorAll('.admin-table tbody tr').forEach(tr => {
            tr.style.display = '';
        });

        // Mostrar todos los productos en tarjetas
        document.querySelectorAll('.product-card').forEach(card => {
            card.style.display = '';
        });

        showNotification('Filtros restablecidos', 'success');
    });

    // Cargar las categorías y subcategorías para los filtros
    loadCategoriesForFilters();

    // Añadir clases activas a los checkboxes cuando se seleccionan
    document.querySelectorAll('.filter-checkbox input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const parent = this.closest('.filter-checkbox');
            if (this.checked) {
                parent.classList.add('active');
            } else {
                parent.classList.remove('active');
            }
        });
    });
});

// Función para filtrar filas de la tabla
function filterTableRows(filters) {
    const productos = Array.from(document.querySelectorAll('.admin-table tbody tr'));
    productos.forEach(producto => {
        let visible = shouldBeVisible(producto, filters, 'table');
        producto.style.display = visible ? '' : 'none';
    });
}

// Función para filtrar tarjetas de productos
function filterProductCards(filters) {
    const cards = Array.from(document.querySelectorAll('.product-card'));
    cards.forEach(card => {
        let visible = shouldBeVisible(card, filters, 'card');
        card.style.display = visible ? '' : 'none';
    });
}

// Función para determinar si un elemento debe ser visible según los filtros
function shouldBeVisible(element, filters, type) {
    let visible = true;

    // Extraer los datos dependiendo de si es una fila de tabla o una tarjeta
    const datos = extractElementData(element, type);

    // Filtrar por búsqueda
    if (filters.search && visible) {
        const searchTerm = filters.search.toLowerCase();
        if (!datos.nombre.includes(searchTerm) && !datos.descripcion.includes(searchTerm)) {
            visible = false;
        }
    }

    // Filtrar por precio
    if (visible && (filters.minPrice || filters.maxPrice)) {
        const precio = parseFloat(datos.precio);
        if (filters.minPrice && precio < filters.minPrice) visible = false;
        if (filters.maxPrice && precio > filters.maxPrice) visible = false;
    }

    // Filtrar por stock
    if (visible && (filters.minStock || filters.maxStock)) {
        const stock = parseInt(datos.stock);
        if (filters.minStock && stock < filters.minStock) visible = false;
        if (filters.maxStock && stock > filters.maxStock) visible = false;
    }

    // Filtrar por estado
    if (visible && filters.states.length > 0) {
        if (!filters.states.some(s => datos.estado.includes(s))) visible = false;
    }

    // Filtrar por condición
    if (visible && filters.conditions.length > 0) {
        if (!filters.conditions.some(c => datos.condicion.includes(c))) visible = false;
    }

    // Filtrar por categoría
    if (visible && filters.category) {
        if (datos.categoria !== filters.category) visible = false;
    }

    // Filtrar por subcategoría
    if (visible && filters.subcategory) {
        if (datos.subcategoria !== filters.subcategory) visible = false;
    }

    return visible;
}

// Función para extraer datos de un elemento (fila de tabla o tarjeta)
function extractElementData(element, type) {
    if (type === 'table') {
        // Extraer datos de una fila de tabla
        return {
            nombre: (element.querySelector('.product-name')?.textContent || '').toLowerCase(),
            descripcion: (element.querySelector('td:nth-child(4)')?.textContent || '').toLowerCase(),
            precio: (element.querySelector('.product-price')?.textContent || '0').replace(/[^0-9]/g, ''),
            stock: element.querySelector('td:nth-child(6)')?.textContent || '0',
            estado: (element.querySelector('.status-badge')?.textContent || '').toLowerCase(),
            condicion: (element.querySelector('.condition-badge')?.textContent || '').toLowerCase(),
            categoria: element.querySelector('td:nth-child(9)')?.textContent || '',
            subcategoria: element.querySelector('td:nth-child(9)')?.textContent || ''
        };
    } else {
        // Extraer datos de una tarjeta
        return {
            nombre: (element.querySelector('.card-title')?.textContent || '').toLowerCase(),
            descripcion: (element.querySelector('.card-category')?.textContent || '').toLowerCase(),
            precio: (element.querySelector('.current-price')?.textContent || '0').replace(/[^0-9]/g, ''),
            stock: '0', // Supongamos que no mostramos el stock en las tarjetas
            estado: (element.querySelector('.status-badge')?.textContent || '').toLowerCase(),
            condicion: (element.querySelector('.condition-badge')?.textContent || '').toLowerCase(),
            categoria: element.querySelector('.card-category')?.textContent || '',
            subcategoria: element.querySelector('.card-category')?.textContent || ''
        };
    }
}

// Función para contar productos visibles
function countVisibleProducts() {
    // Contar elementos visibles en la tabla (para desktop)
    const visibleTableRows = document.querySelectorAll('.admin-table tbody tr:not([style*="display: none"])').length;

    // Contar elementos visibles en tarjetas (para móvil)
    const visibleCards = document.querySelectorAll('.product-card:not([style*="display: none"])').length;

    // Devolver el número correspondiente según el modo de visualización actual
    return window.innerWidth <= 768 ? visibleCards : visibleTableRows;
}

// Función para cargar las categorías y subcategorías en los filtros
function loadCategoriesForFilters() {
    const categorySelect = document.getElementById('filterCategory');
    const subcategorySelect = document.getElementById('filterSubcategory');

    if (!categorySelect || !subcategorySelect) return;

    // Obtener todas las categorías únicas de los productos mostrados
    const categoryUniqueNames = new Set();
    document.querySelectorAll('.product-name-cell .product-category').forEach(el => {
        const category = el?.textContent?.trim();
        if (category) categoryUniqueNames.add(category);
    });

    // Limpiar y añadir opciones al select de categorías
    categorySelect.innerHTML = '<option value="">Todas las categorías</option>';
    categoryUniqueNames.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categorySelect.appendChild(option);
    });

    // Configurar evento de cambio de categoría para actualizar subcategorías
    categorySelect.addEventListener('change', function() {
        const selectedCategory = this.value;

        // Limpiar y añadir opciones al select de subcategorías
        subcategorySelect.innerHTML = '<option value="">Todas las subcategorías</option>';

        if (selectedCategory) {
            // Aquí deberías cargar las subcategorías correspondientes a la categoría seleccionada
            // como no tenemos una API específica para esto, podríamos simularlo
            // ejemplos de subcategorías
            const subcategories = ['Smartphones', 'Tablets', 'Laptops', 'Auriculares', 'Televisores'];

            subcategories.forEach(subcategory => {
                const option = document.createElement('option');
                option.value = subcategory;
                option.textContent = subcategory;
                subcategorySelect.appendChild(option);
            });
        }
    });
}



// Manejar clics fuera del sidebar para cerrarlo en modo responsive
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');

    // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
    if (window.innerWidth < 992 && sidebar.classList.contains('expanded')) {
        // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
        if (!sidebar.contains(event.target) && event.target !== toggleBtn && !toggleBtn.contains(event.target)) {
            // Cerrar el sidebar
            sidebar.classList.remove('expanded');

            // Actualizar el ícono
            const toggleIcon = document.getElementById('toggle-icon');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-bars';
            }
        }
    }
});



// Funciones para el panel de edición de productos
function setupEditProductPanel() {
    // Elementos DOM
    const editContainer = document.getElementById('editProductPanel');
    const closeEditBtn = document.getElementById('closeEdit');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const saveEditBtn = document.getElementById('saveEditBtn');
    const editImageBtn = document.getElementById('editImageBtn');
    const editImageInput = document.getElementById('editImageInput');
    const categorySelect = document.getElementById('editProductCategory');

    // Event Listeners
    if (closeEditBtn) closeEditBtn.addEventListener('click', closeEditPanel);
    if (cancelEditBtn) cancelEditBtn.addEventListener('click', closeEditPanel);
    if (saveEditBtn) saveEditBtn.addEventListener('click', saveProductChanges);
    if (editImageBtn) editImageBtn.addEventListener('click', () => editImageInput.click());
    if (editImageInput) editImageInput.addEventListener('change', handleImageUpload);
    if (categorySelect) categorySelect.addEventListener('change', loadSubcategories);

    // Delegación de eventos para botones de edición
    document.body.addEventListener('click', function(event) {
        const editBtn = event.target.closest('.edit-btn');
        if (!editBtn) return;

        const productId = editBtn.getAttribute('data-id');
        if (productId) {
            openEditPanel(productId);
            event.preventDefault();
            event.stopPropagation();
        }
    });

    console.log('Panel de edición inicializado');
}

// Función mejorada para abrir el panel de edición
function openEditPanel(productId) {
    const editContainer = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Establecer el ID del producto
    document.getElementById('editProductId').value = productId;

    // Primero mostrar el overlay
    if (editOverlay) {
        editOverlay.style.display = 'block';
        // Forzar reflow para que la transición funcione
        void editOverlay.offsetWidth;
        editOverlay.classList.add('show');
    }

    // Luego mostrar el panel con un pequeño delay
    editContainer.style.display = 'block';
    setTimeout(() => {
        editContainer.classList.add('show');
    }, 10);

    // Desactivar scroll en el body
    document.body.style.overflow = 'hidden';

    // Cargar datos del producto
    loadProductData(productId);
}

// Función mejorada para cerrar el panel - Solución definitiva
function closeEditPanel() {
    console.log('Cerrando panel de edición...');

    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // 1. Quitar clases show inmediatamente
    if (editPanel) editPanel.classList.remove('show');

    // 2. Forzar que el overlay se oculte completamente
    if (editOverlay) {
        editOverlay.classList.remove('show');
        editOverlay.style.display = 'none';
        editOverlay.style.opacity = '0';
        editOverlay.style.visibility = 'hidden';
        editOverlay.style.pointerEvents = 'none';
    }

    // 3. Restaurar scroll del body inmediatamente
    document.body.style.overflow = '';
    document.body.classList.remove('overlay-active');

    // 4. Con un pequeño retraso, ocultar completamente el panel
    setTimeout(() => {
        if (editPanel) editPanel.style.display = 'none';

        // 5. Forzar un reflow del DOM para actualizar la pantalla
        document.body.offsetHeight;

        console.log('Panel de edición cerrado completamente');
    }, 300);
}

// Función extra para garantizar que el overlay se reinicie correctamente
function resetOverlay() {
    console.log('Reseteo completo del overlay...');

    const editOverlay = document.getElementById('editOverlay');
    if (editOverlay) {
        // Eliminar cualquier estilo inline
        editOverlay.removeAttribute('style');

        // Aplicar estilos directo que garanticen que está oculto
        editOverlay.style.display = 'none';
        editOverlay.style.opacity = '0';
        editOverlay.style.visibility = 'hidden';
        editOverlay.style.pointerEvents = 'none';
        editOverlay.style.zIndex = '-1';

        // Eliminar cualquier clase que pueda estar causando problemas
        editOverlay.className = 'overlay overlay-reset';
    }

    // Asegurar que el body puede hacer scroll normalmente
    document.body.style.overflow = '';
    document.body.classList.remove('overlay-active');
}

// Cargar datos del producto
async function loadProductData(productId) {
    try {
        const response = await fetch('../public/API/productos/get_product_details.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: productId }),
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('Error al cargar datos del producto');
        }

        const data = await response.json();
        if (data.success && data.producto) {
            fillEditForm(data.producto);
        } else {
            throw new Error(data.message || 'Error al cargar datos del producto');
        }
    } catch (error) {
        console.error('Error al cargar datos del producto:', error);
        showNotification('Error: ' + error.message, 'error');
        closeEditPanel(); // Cerrar el panel si hay un error
    }
}

// Función auxiliar para llenar el formulario con los datos del producto
function fillEditForm(producto) {
    // Campos básicos
    const fields = {
        'editProductName': producto.nombre,
        'editProductDescription': producto.descripcion,
        'editProductShortDescription': producto.descripcion_corta,
        'editProductPrice': producto.precio,
        'editProductOriginalPrice': producto.precio_original,
        'editProductStock': producto.stock,
        'editProductSKU': producto.sku,
        'editProductBrand': producto.marca,
        'editProductColor': producto.color,
        'editProductWeight': producto.peso,
        'editProductDimensions': producto.dimensiones,
        'editProductTags': producto.etiquetas
    };

    // Llenar campos del formulario
    for (const [id, value] of Object.entries(fields)) {
        const element = document.getElementById(id);
        if (element) {
            element.value = value || '';
        }
    }

    // Radio buttons
    const estado = producto.estado || 'borrador';
    const estadoRadio = document.querySelector(`input[name="editProductStatus"][value="${estado}"]`);
    if (estadoRadio) estadoRadio.checked = true;

    const condicion = producto.condicion || 'ninguno';
    const condicionRadio = document.querySelector(`input[name="editProductCondition"][value="${condicion}"]`);
    if (condicionRadio) condicionRadio.checked = true;

    // Imagen
    const previewImg = document.getElementById('editImagePreviewImg');
    if (previewImg && producto.imagen_principal) {
        previewImg.src = producto.imagen_principal;
    } else {
        previewImg.src = '../images/placeholder.png';
    }

    // Categoría y subcategoría - Usar el nuevo gestor de categorías
    if (typeof setCategoriesForEditProduct === 'function') {
        console.log('Usando CategoryManager para establecer categorías');
        setCategoriesForEditProduct(producto);
    } else {
        console.warn('CategoryManager no disponible, usando método antiguo');
        if (producto.categoria_id) {
            const categorySelect = document.getElementById('editProductCategory');
            if (categorySelect) {
                categorySelect.value = producto.categoria_id;
                // Cargar subcategorías después de establecer la categoría
                loadSubcategories(producto.categoria_id, producto.subcategoria_id);
            }
        }
    }
}

// Cargar categorías
async function loadCategories(selectedCategoryId = null, selectedSubcategoryId = null) {
    try {
        const categorySelect = document.getElementById('editProductCategory');

        const response = await fetch('/projects/villarrica_click/public/API/productos/get_categorias.php', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });

        const data = await response.json();

        // Limpiar opciones
        categorySelect.innerHTML = '<option value="">Seleccionar categoría</option>';

        // Añadir categorías
        data.forEach(categoria => {
            const option = document.createElement('option');
            option.value = categoria.id;
            option.textContent = categoria.nombre || categoria.sub_categoria;
            categorySelect.appendChild(option);
        });

        // Seleccionar categoría si existe
        if (selectedCategoryId) {
            categorySelect.value = selectedCategoryId;
            loadSubcategoriesForCategory(selectedCategoryId, selectedSubcategoryId);
        }

    } catch (error) {
        console.error('Error al cargar categorías:', error);
    }
}

// Cargar subcategorías
function loadSubcategories() {
    const categoryId = document.getElementById('editProductCategory').value;
    if (categoryId) {
        loadSubcategoriesForCategory(categoryId);
    }
}

// Cargar subcategorías para una categoría
async function loadSubcategoriesForCategory(categoryId, selectedSubcategoryId = null) {
    try {
        console.log('Cargando subcategorías para categoría ID:', categoryId);
        const subcategorySelect = document.getElementById('editProductSubcategory');

        if (!subcategorySelect) {
            console.error('No se encontró el elemento editProductSubcategory');
            return;
        }

        // Usar parámetros GET en lugar de FormData
        const url = `/projects/villarrica_click/public/API/productos/get_subcategorias.php?categoria_id=${encodeURIComponent(categoryId)}`;
        console.log('URL de solicitud:', url);

        const response = await fetch(url, {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Accept': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });

        const data = await response.json();
        console.log('Datos recibidos:', data);

        // Limpiar opciones
        subcategorySelect.innerHTML = '<option value="">Seleccionar subcategoría</option>';

        // Verificar la estructura de la respuesta
        if (data.success && Array.isArray(data.subcategorias)) {
            // Añadir subcategorías
            data.subcategorias.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                subcategorySelect.appendChild(option);
            });
            console.log(`Se cargaron ${data.subcategorias.length} subcategorías`);
        } else if (Array.isArray(data)) {
            // Formato alternativo (array directo)
            data.forEach(subcategoria => {
                const option = document.createElement('option');
                option.value = subcategoria.id;
                option.textContent = subcategoria.nombre || subcategoria.sub_categoria;
                subcategorySelect.appendChild(option);
            });
            console.log(`Se cargaron ${data.length} subcategorías (formato array)`);
        } else {
            console.error('Formato de respuesta inesperado:', data);
        }

        // Seleccionar subcategoría si existe
        if (selectedSubcategoryId) {
            subcategorySelect.value = selectedSubcategoryId;
        }

    } catch (error) {
        console.error('Error al cargar subcategorías:', error);

        // Mostrar mensaje de error en la consola con más detalles
        console.error('Detalles del error:', {
            message: error.message,
            stack: error.stack,
            categoryId: categoryId
        });

        // Limpiar opciones y mostrar mensaje de error
        if (subcategorySelect) {
            subcategorySelect.innerHTML = '<option value="">Error al cargar subcategorías</option>';
        }
    }
}

// Manejar subida de imágenes
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Verificar tipo y tamaño
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showNotification('Solo se permiten archivos JPG, PNG o WEBP', 'error');
        return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
        showNotification('El archivo es demasiado grande. Máximo 5MB permitido.', 'error');
        return;
    }

    // Vista previa
    const reader = new FileReader();
    reader.onload = function(e) {
        document.getElementById('editImagePreviewImg').src = e.target.result;
    };
    reader.readAsDataURL(file);

    // Subir imagen
    uploadImage(file);
}

// Subir imagen al servidor
async function uploadImage(file) {
    try {
        const previewContainer = document.getElementById('editImagePreview');
        if (previewContainer) previewContainer.classList.add('uploading');

        const formData = new FormData();
        formData.append('imagen', file);
        formData.append('producto_id', document.getElementById('editProductId').value);

        const response = await fetch('../public/API/productos/upload_image.php', {
            method: 'POST',
            body: formData,
            credentials: 'include'
        });

        const data = await response.json();

        if (!data.success) throw new Error(data.message);

        document.getElementById('editImagePath').value = data.filepath;
        showNotification('Imagen subida correctamente', 'success');

    } catch (error) {
        console.error('Error al subir imagen:', error);
        showNotification('Error al subir imagen: ' + error.message, 'error');
    } finally {
        const previewContainer = document.getElementById('editImagePreview');
        if (previewContainer) previewContainer.classList.remove('uploading');
    }
}

// Guardar cambios del producto
async function saveProductChanges() {
    try {
        console.log('Iniciando guardado de cambios...');

        // Verificar que el ID del producto exista
        const productIdField = document.getElementById('editProductId');
        if (!productIdField || !productIdField.value) {
            throw new Error('No se pudo identificar el ID del producto a actualizar');
        }

        // Verificar todos los campos requeridos antes de continuar
        const requiredFields = [
            { id: 'editProductName', name: 'Nombre del producto' },
            { id: 'editProductDescription', name: 'Descripción' },
            { id: 'editProductPrice', name: 'Precio' },
            { id: 'editProductStock', name: 'Stock' },
            { id: 'editProductCategory', name: 'Categoría' }
        ];

        for (const field of requiredFields) {
            const element = document.getElementById(field.id);
            if (!element) {
                throw new Error(`Campo no encontrado en el DOM: ${field.id}`);
            }
            if (!element.value.trim()) {
                throw new Error(`El campo ${field.name} es obligatorio`);
            }
        }

        // Verificar radio buttons de estado y condición
        const estadoRadio = document.querySelector('input[name="editProductStatus"]:checked');
        if (!estadoRadio) {
            throw new Error('Debe seleccionar un estado para el producto');
        }

        const condicionRadio = document.querySelector('input[name="editProductCondition"]:checked');
        if (!condicionRadio) {
            throw new Error('Debe seleccionar una condición para el producto');
        }

        // Construir objeto con los datos del formulario
        const formData = {
            id: productIdField.value,
            negocio_id: document.getElementById('editNegocioId')?.value || null,
            nombre: document.getElementById('editProductName').value,
            descripcion: document.getElementById('editProductDescription').value,
            descripcion_corta: document.getElementById('editProductShortDescription')?.value || '',
            precio: document.getElementById('editProductPrice').value.replace(/\./g, '').replace(/,/g, ''),
            precio_original: document.getElementById('editProductOriginalPrice')?.value?.replace(/\./g, '').replace(/,/g, '') || null,
            stock: document.getElementById('editProductStock').value,
            sku: document.getElementById('editProductSKU')?.value || '',
            estado: estadoRadio.value,
            condicion: condicionRadio.value,
            etiquetas: document.getElementById('editProductTags')?.value || '',
            marca: document.getElementById('editProductBrand')?.value || '',
            color: document.getElementById('editProductColor')?.value || '',
            peso: document.getElementById('editProductWeight')?.value || null,
            dimensiones: document.getElementById('editProductDimensions')?.value || '',
            imagen_principal: document.getElementById('editImagePath')?.value || null
        };

        // Obtener valores de categoría usando el nuevo gestor de categorías
        if (typeof getSelectedCategoryValues === 'function') {
            console.log('Usando CategoryManager para obtener valores de categoría');
            const categoryValues = getSelectedCategoryValues(true); // true indica que estamos en modo edición

            // Añadir valores de categoría al formData
            formData.tipo_categoria_id = categoryValues.tipoId;
            formData.categoria_id = categoryValues.categoriaId;
            formData.subcategoria_id = categoryValues.subcategoriaId;
        } else {
            console.warn('CategoryManager no disponible, usando método antiguo');
            formData.categoria_id = document.getElementById('editProductCategory').value;
            formData.subcategoria_id = document.getElementById('editProductSubcategory')?.value || null;
        }

        console.log('Datos a enviar:', formData);

        // Mostrar indicador de carga
        const saveBtn = document.getElementById('saveEditBtn');
        if (!saveBtn) {
            throw new Error('No se encontró el botón de guardar en el DOM');
        }

        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Guardando...';
        saveBtn.disabled = true;

        // Realizar la petición al servidor
        const response = await fetch('../public/API/productos/update_product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(formData),
            credentials: 'include'
        });

        console.log('Respuesta del servidor - Status:', response.status, response.statusText);

        // Verificar si la respuesta fue exitosa
        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error en la respuesta:', errorText);
            throw new Error(`Error del servidor: ${response.status} ${response.statusText}`);
        }

        // Procesar la respuesta
        const data = await response.json();
        console.log('Datos recibidos del servidor:', data);

        if (data.success) {
            showNotification('Producto actualizado correctamente', 'success');
            closeEditPanel();

            // Recargar la lista de productos
            if (typeof loadProducts === 'function') {
                await loadProducts();
            }
        } else {
            throw new Error(data.message || 'Error desconocido al actualizar el producto');
        }

    } catch (error) {
        console.error('Error al guardar producto:', error);
        showNotification('Error al guardar cambios: ' + error.message, 'error');
    } finally {
        // Restaurar el botón de guardar
        const saveBtn = document.getElementById('saveEditBtn');
        if (saveBtn) {
            saveBtn.innerHTML = '<i class="fas fa-save"></i> Guardar cambios';
            saveBtn.disabled = false;
        }
    }
}

// Cerrar panel
function closeEditPanel() {
    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Quitar clase show y restablecer el desplazamiento del body
    editPanel.classList.remove('show');
    if (editOverlay) {
        editOverlay.classList.remove('show');
    }
    document.body.style.overflow = '';

    // Después de la transición, ocultar completamente
    setTimeout(() => {
        editPanel.style.display = 'none';
        if (editOverlay) {
            editOverlay.style.display = 'none';
        }
    }, 300);

    console.log('Panel de edición y overlay cerrados');
}

// Inicializar panel en DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    setupEditProductPanel(); // Asegúrate que esta línea ya existe

    // Asegurarse que el botón de cerrar funcione correctamente
    const closeEditBtn = document.getElementById('closeEdit');
    const cancelEditBtn = document.getElementById('cancelEditBtn'); // El botón de cancelar dentro del panel

    if (closeEditBtn) {
        closeEditBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }

    // El botón de cancelar en el panel de edición también debe cerrar el panel
    const panelCancelBtn = document.querySelector('#editProductPanel #cancelEditBtn');
    if (panelCancelBtn) {
        panelCancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }
});

// Mejorar la función de manejo de click fuera del panel (click en overlay)
document.addEventListener('click', function(event) {
    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Si el panel está visible y se hace clic en el overlay
    if (editPanel && editPanel.classList.contains('show') &&
        editOverlay && event.target === editOverlay) {
        closeEditPanel();
    }

    // ... (mantener el código existente para cerrar el sidebar si es necesario) ...
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');

    // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
    if (window.innerWidth < 992 && sidebar && sidebar.classList.contains('expanded')) {
        // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
        if (!sidebar.contains(event.target) && toggleBtn && !toggleBtn.contains(event.target)) {
            // Cerrar el sidebar
            sidebar.classList.remove('expanded');

            // Actualizar el ícono
            const toggleIcon = document.getElementById('toggle-icon');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-bars';
            }
        }
    }

    // ... (mantener el código existente para cerrar el panel de filtros si es necesario) ...
    const filterPanel = document.getElementById('filterContainer');
    const filterOverlay = document.getElementById('filterOverlay');
    if (filterPanel && filterPanel.classList.contains('show')) {
        const filterContent = filterPanel.querySelector('.filter-content');
        if (filterContent && !filterContent.contains(event.target) &&
            !event.target.closest('#filterBtn') && event.target !== filterOverlay) { // Evitar cerrar si se hace clic en el overlay del filtro
            // No cerrar aquí, el overlay del filtro tiene su propio listener
        }
    }
});

function closeEditPanel() {
    const editPanel = document.getElementById('editProductPanel');
    if (editPanel) {
        editPanel.classList.remove('show');
        setTimeout(() => {
            editPanel.style.display = 'none';
        }, 300);
    }
}

function closeFilterPanel() {
    const filterPanel = document.getElementById('filterContainer');
    if (filterPanel) {
        filterPanel.classList.remove('show');
        setTimeout(() => {
            filterPanel.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }
}

// Función para cargar los detalles de un producto al editar
async function fetchProductDetails(productId) {
    try {
        console.log('handleEditProduct llamada con ID:', productId);

        // Mostrar indicador de carga
        const saveEditBtn = document.getElementById('saveEditBtn');
        if (saveEditBtn) {
            saveEditBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cargando...';
            saveEditBtn.disabled = true;
        }

        // Realizar la llamada API con POST en lugar de GET
        const response = await fetch('../public/API/productos/get_product_details.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: productId }),
            credentials: 'include'
        });

        console.log('Respuesta del servidor - Status:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error('Error al cargar datos del producto');
        }

        const data = await response.json();
        console.log('Datos recibidos:', data);

        if (data.success && data.producto) {
            // Rellenar el formulario
            fillEditForm(data.producto);

            // Cargar categorías si es necesario
            const categoryId = data.producto.categoria_id;
            if (categoryId) {
                await loadCategories(categoryId, data.producto.subcategoria_id);
            } else {
                await loadCategories();
            }

            console.log('Datos del producto cargados correctamente');
        } else {
            throw new Error(data.message || 'Error al cargar datos del producto');
        }
    } catch (error) {
        console.error('Error al cargar datos del producto:', error);
        showNotification('Error: ' + error.message, 'error');
    } finally {
        // Restaurar el botón de guardar
        const saveEditBtn = document.getElementById('saveEditBtn');
        if (saveEditBtn) {
            saveEditBtn.innerHTML= '<i class="fas fa-save"></i> Guardar cambios';
            saveEditBtn.disabled = false;
        }
    }
}

// Cerrar panel
function closeEditPanel() {
    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Quitar clase show y restablecer el desplazamiento del body
    editPanel.classList.remove('show');
    if (editOverlay) {
        editOverlay.classList.remove('show');
    }
    document.body.style.overflow = '';

    // Después de la transición, ocultar completamente
    setTimeout(() => {
        editPanel.style.display = 'none';
        if (editOverlay) {
            editOverlay.style.display = 'none';
        }
    }, 300);

    console.log('Panel de edición y overlay cerrados');
}

// Inicializar panel en DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    setupEditProductPanel(); // Asegúrate que esta línea ya existe

    // Asegurarse que el botón de cerrar funcione correctamente
    const closeEditBtn = document.getElementById('closeEdit');
    const cancelEditBtn = document.getElementById('cancelEditBtn'); // El botón de cancelar dentro del panel

    if (closeEditBtn) {
        closeEditBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }

    // El botón de cancelar en el panel de edición también debe cerrar el panel
    const panelCancelBtn = document.querySelector('#editProductPanel #cancelEditBtn');
    if (panelCancelBtn) {
        panelCancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeEditPanel();
        });
    }
});

// Mejorar la función de manejo de click fuera del panel (click en overlay)
document.addEventListener('click', function(event) {
    const editPanel = document.getElementById('editProductPanel');
    const editOverlay = document.getElementById('editOverlay');

    // Si el panel está visible y se hace clic en el overlay
    if (editPanel && editPanel.classList.contains('show') &&
        editOverlay && event.target === editOverlay) {
        closeEditPanel();
    }

    // ... (mantener el código existente para cerrar el sidebar si es necesario) ...
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('aside-toggle');

    // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
    if (window.innerWidth < 992 && sidebar && sidebar.classList.contains('expanded')) {
        // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
        if (!sidebar.contains(event.target) && toggleBtn && !toggleBtn.contains(event.target)) {
            // Cerrar el sidebar
            sidebar.classList.remove('expanded');

            // Actualizar el ícono
            const toggleIcon = document.getElementById('toggle-icon');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-bars';
            }
        }
    }

    // ... (mantener el código existente para cerrar el panel de filtros si es necesario) ...
    const filterPanel = document.getElementById('filterContainer');
    const filterOverlay = document.getElementById('filterOverlay');
    if (filterPanel && filterPanel.classList.contains('show')) {
        const filterContent = filterPanel.querySelector('.filter-content');
        if (filterContent && !filterContent.contains(event.target) &&
            !event.target.closest('#filterBtn') && event.target !== filterOverlay) { // Evitar cerrar si se hace clic en el overlay del filtro
            // No cerrar aquí, el overlay del filtro tiene su propio listener
        }
    }
});

function closeEditPanel() {
    const editPanel = document.getElementById('editProductPanel');
    if (editPanel) {
        editPanel.classList.remove('show');
        setTimeout(() => {
            editPanel.style.display = 'none';
        }, 300);
    }
}

function closeFilterPanel() {
    const filterPanel = document.getElementById('filterContainer');
    if (filterPanel) {
        filterPanel.classList.remove('show');
        setTimeout(() => {
            filterPanel.style.display = 'none';
            document.body.style.overflow = '';
        }, 300);
    }
}



    document.addEventListener('DOMContentLoaded', function() {
        console.log('Inicializando overlays...');

        // Reiniciar overlay
        const editOverlay = document.getElementById('editOverlay');
        if (editOverlay) {
            editOverlay.className = 'overlay overlay-reset';
            editOverlay.style.display = 'none';
            editOverlay.style.opacity = '0';
            editOverlay.style.visibility = 'hidden';
            editOverlay.style.pointerEvents = 'none';
            editOverlay.style.zIndex = '-1';
        }

        // Asegurar que no hay problemas con el scroll del body
        document.body.classList.remove('overlay-active');
        document.body.classList.add('overlay-inactive');

        // Agregar event listener para cerrar el panel con el overlay
        if (editOverlay) {
            editOverlay.addEventListener('click', function() {
                console.log('Clic en overlay de edición');
                closeEditPanel();
            });
        }

        // Agregar event listener para la tecla Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const editPanel = document.getElementById('editProductPanel');
                if (editPanel && getComputedStyle(editPanel).display !== 'none') {
                    closeEditPanel();
                }
            }
        });
    });

    // Función para forzar la eliminación del overlay
    function forceRemoveOverlay() {
        const overlay = document.getElementById('editOverlay');
        if (overlay) {
            overlay.style.display = 'none';
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';
            overlay.style.pointerEvents = 'none';
            overlay.style.zIndex = '-1';
            document.body.style.overflow = '';
        }
    }

    // Sobrescribir la función closeEditPanel para garantizar que el overlay se elimine
    function closeEditPanel() {
        console.log('Cerrando panel de edición...');

        const editPanel = document.getElementById('editProductPanel');
        const editOverlay = document.getElementById('editOverlay');

        // 1. Quitar clases show inmediatamente
        if (editPanel) editPanel.classList.remove('show');

        // 2. Forzar que el overlay se oculte completamente de inmediato
        if (editOverlay) {
            editOverlay.classList.remove('show');
            editOverlay.style.display = 'none';
            editOverlay.style.opacity = '0';
            editOverlay.style.visibility = 'hidden';
            editOverlay.style.pointerEvents = 'none';
            editOverlay.style.zIndex = '-1';
        }

        // 3. Restaurar scroll del body inmediatamente
        document.body.style.overflow = '';
        document.body.classList.remove('overlay-active');

        // 4. Con un pequeño retraso, ocultar completamente el panel
        setTimeout(() => {
            if (editPanel) editPanel.style.display = 'none';

            // 5. Forzar un reflow del DOM para actualizar la pantalla
            document.body.offsetHeight;

            // 6. Llamar a la función de fuerza bruta para asegurar que el overlay desaparezca
            forceRemoveOverlay();

            console.log('Panel de edición cerrado completamente');
        }, 300);

        // 7. Programar una última verificación después de un tiempo
        setTimeout(forceRemoveOverlay, 500);
    }
