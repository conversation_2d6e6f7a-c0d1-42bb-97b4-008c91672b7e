/**
 * Sidebar Navigation - Maneja la interactividad entre el sidebar y las secciones de contenido
 */

document.addEventListener('DOMContentLoaded', function() {
    initSidebarToggle();
    initSidebarNavigation();
    initTabButtons();
    initializePage();
});

/**
 * Inicializa el comportamiento de toggle del sidebar
 */
function initSidebarToggle() {
    const sidebar = document.getElementById('sidebar');
    // Ahora usamos el nuevo botón toggle en el header
    const toggleButton = document.getElementById('header-aside-toggle');

    if (!sidebar) {
        // Silenciamos el error para evitar mensajes en la consola
        return;
    }

    if (!toggleButton) {
        // Silenciamos el warning para evitar mensajes en la consola
        // No retornamos aquí para permitir que el resto de la función se ejecute
    }

    // Cargar estado desde localStorage
    const savedState = localStorage.getItem('sidebarCollapsed');

    if (savedState === 'true' && window.innerWidth > 992) {
        sidebar.classList.add('collapsed');
        sidebar.classList.remove('extended');
    } else {
        // Por defecto, mostrar el sidebar expandido
        sidebar.classList.add('extended');
        sidebar.classList.remove('collapsed');
        localStorage.setItem('sidebarCollapsed', 'false');
    }

    // En modo responsive, colapsar el sidebar por defecto
    if (window.innerWidth < 992) {
        sidebar.classList.remove('expanded');
    }

    // No agregamos el evento al botón aquí porque ya lo maneja toggle-fix.js
}

/**
 * Inicializa la navegación entre secciones usando los enlaces del sidebar
 */
function initSidebarNavigation() {
    const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Quitar clase active de todos los enlaces
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
            });

            // Añadir clase active al enlace actual
            this.classList.add('active');

            // Ocultar todas las secciones
            const allSections = document.querySelectorAll('.content-section');
            allSections.forEach(section => {
                section.style.display = 'none';
            });

            // Mostrar la sección correspondiente según el ID del enlace
            const targetId = this.id;
            let sectionToShow = null;

            if (targetId === 'dashboardLink') {
                // Por defecto mostrar la sección de productos si es dashboard
                sectionToShow = document.getElementById('productsSection');
            } else if (targetId === 'statsLink') {
                sectionToShow = document.getElementById('statsSection');
            } else if (targetId === 'productsNavLink') {
                sectionToShow = document.getElementById('productsSection');
            } else if (targetId === 'categoriesLink') {
                sectionToShow = document.getElementById('categoriesSection');
            }

            if (sectionToShow) {
                sectionToShow.style.display = 'block';

                // Actualizar botón de pestaña activo
                const tabButtons = document.querySelectorAll('.tab-btn');
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');

                    // Relacionar secciones con pestañas
                    if (sectionToShow.id === 'productsSection' && btn.id === 'productsTab') {
                        btn.classList.add('active');
                    } else if (sectionToShow.id === 'storeInfoSection' && btn.id === 'storeTab') {
                        btn.classList.add('active');
                    } else if (sectionToShow.id === 'editProductSection' && btn.id === 'editProductTab') {
                        btn.classList.add('active');
                    }
                });
            } else {
                console.warn("No se encontró una sección correspondiente para: " + targetId);
            }

            // En dispositivos móviles, cerrar el sidebar después de hacer clic
            if (window.innerWidth < 992) {
                const sidebar = document.getElementById('sidebar');
                if (sidebar) {
                    sidebar.classList.remove('expanded');
                }
            }
        });
    });
}

/**
 * Inicializa la funcionalidad de los botones de pestaña
 */
function initTabButtons() {
    const tabButtons = document.querySelectorAll('.tab-btn');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remover clase activa de todos los botones
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // Añadir clase activa al botón actual
            this.classList.add('active');

            // Ocultar todas las secciones
            const allSections = document.querySelectorAll('.content-section');
            allSections.forEach(section => {
                section.style.display = 'none';
            });

            // Mostrar la sección correspondiente
            const targetId = this.id;
            let sectionToShow = null;

            if (targetId === 'productsTab') {
                sectionToShow = document.getElementById('productsSection');
            } else if (targetId === 'storeTab') {
                sectionToShow = document.getElementById('storeInfoSection');
            } else if (targetId === 'editProductTab') {
                sectionToShow = document.getElementById('editProductSection');
            }

            if (sectionToShow) {
                sectionToShow.style.display = 'block';
                // Sección mostrada
            }
        });
    });
}

/**
 * Inicializa la página con la vista por defecto
 */
function initializePage() {
    // Ocultar todas las secciones excepto la primera
    const allSections = document.querySelectorAll('.content-section');
    const firstSection = document.getElementById('productsSection');

    allSections.forEach(section => {
        if (section === firstSection) {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    });

    // Activar la primera pestaña y el primer enlace
    const firstTab = document.getElementById('productsTab');
    if (firstTab) {
        firstTab.classList.add('active');
    }

    const firstLink = document.getElementById('dashboardLink');
    if (firstLink) {
        firstLink.classList.add('active');
    }
}

// Agregar manejo de clics fuera del sidebar en dispositivos móviles
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.getElementById('header-aside-toggle');

    // Solo aplicar en dispositivos móviles y cuando el sidebar esté expandido
    if (window.innerWidth < 992 && sidebar && sidebar.classList.contains('expanded')) {
        // Verificar que el clic no fue dentro del sidebar ni en el botón de toggle
        if (!sidebar.contains(event.target) && toggleBtn && !toggleBtn.contains(event.target)) {
            // Cerrar el sidebar
            sidebar.classList.remove('expanded');
        }
    }
});

// Ajustar comportamiento cuando cambia el tamaño de la ventana
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');

    // En dispositivos móviles, mantener el estado actual
    // No colapsamos automáticamente para mejorar la experiencia de usuario
    if (window.innerWidth < 992 && sidebar) {
        // No hacemos nada, dejamos que el usuario controle el estado
    }
});