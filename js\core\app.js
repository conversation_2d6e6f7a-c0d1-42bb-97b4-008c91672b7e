// Importar módulos necesarios
import { showSection } from './ui.js';
import { setupOverlays } from './utils.js';
import { initSidebar } from '../components/sidebar.js';
import { loadProducts } from '../modules/products/product-api.js';
import { setupTableButtons, updateResponsiveView } from '../modules/products/product-list.js';
import { setupEditProductPanel, closeEditPanel } from '../modules/products/product-edit.js';
import { initFilterHandlers } from '../modules/products/product-filter.js';
import { initCategoriesModule } from '../modules/categories/categories.js';
import { initStatsModule } from '../modules/stats/stats.js';

/**
 * Inicializa los componentes principales de la aplicación.
 */
export async function initApp() {
    console.log('Iniciando initApp...');
    try {
        // Configurar overlays globales
        setupOverlays();
        console.log('Overlays configurados');

        // Inicializar la barra lateral
        initSidebar();
        console.log('Sidebar inicializado');

        // Configurar la vista responsive inicial y el listener
        updateResponsiveView();
        window.addEventListener('resize', updateResponsiveView);
        console.log('Vista responsive configurada');

        console.log('initApp completado.');

    } catch (error) {
        console.error('Error durante la inicialización de la app:', error);
        // Aquí podrías mostrar un mensaje de error al usuario
    }
}