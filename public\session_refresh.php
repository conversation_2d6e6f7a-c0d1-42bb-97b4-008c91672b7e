<?php
// Habilitar reportes de errores para depuración
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Iniciar el búfer de salida para evitar problemas con las cabeceras
ob_start();

// Incluir configuraciones
require_once '../config/config.php';
require_once '../config/SessionManager.php';

// Detectar si es una solicitud AJAX
$is_ajax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// Registrar la actividad
error_log("session_refresh.php - Inicio - Session ID: " . session_id());
error_log("session_refresh.php - SESSION: " . print_r($_SESSION, true));
error_log("session_refresh.php - COOKIE: " . print_r($_COOKIE, true));
error_log("session_refresh.php - Es AJAX: " . ($is_ajax ? 'Sí' : 'No'));

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Asegurar que tenemos una sesión válida
if (session_status() !== PHP_SESSION_ACTIVE) {
    $sessionManager->initializeSession();
}

// Verificar si existe un token de autenticación
$has_token = false;
$user_data = null;

if (!empty($_COOKIE['auth_token'])) {
    $token = $_COOKIE['auth_token'];
    error_log("session_refresh.php - Token encontrado: " . substr($token, 0, 10) . "...");
    
    // Buscar el token en la base de datos
    $stmt = $conn->prepare("SELECT * FROM sessions WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($row = $result->fetch_assoc()) {
        $user_id = $row['user_id'];
        error_log("session_refresh.php - Usuario encontrado por token: " . $user_id);
        
        // Obtener datos del usuario
        $user_stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $user_stmt->bind_param("i", $user_id);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();
        
        if ($user_data = $user_result->fetch_assoc()) {
            // Restaurar la sesión con los datos del usuario
            $_SESSION['user_id'] = $user_id;
            $_SESSION['email'] = $user_data['email'];
            $_SESSION['username'] = $user_data['username'];
            $_SESSION['role'] = $user_data['role'];
            $_SESSION['is_authenticated'] = true;
            
            // Actualizar tiempo de actividad
            $sessionManager->updateActivity();
            $has_token = true;
            
            error_log("session_refresh.php - Sesión restaurada para el usuario: " . $user_id);
        }
    }
}

// Verificar si hay un usuario en sesión
$authenticated = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);

error_log("session_refresh.php - Autenticado: " . ($authenticated ? 'Sí' : 'No'));
error_log("session_refresh.php - SESSION después: " . print_r($_SESSION, true));

// Preparar respuesta
$response = [
    'success' => $authenticated,
    'session_active' => $authenticated,
    'session_id' => session_id(),
    'has_token' => $has_token
];

if ($authenticated) {
    $response['user_id'] = $_SESSION['user_id'];
    $response['username'] = $_SESSION['username'] ?? '';
    $response['role'] = $_SESSION['role'] ?? '';
}

// Retornar la respuesta apropiada
if ($is_ajax) {
    // Si es AJAX, responder con JSON
    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    // Si no es AJAX, redirigir según el estado
    $return_to = isset($_GET['return_to']) ? $_GET['return_to'] : '/projects/villarrica_click/public/tienda_adm.php';
    
    if ($authenticated) {
        // Si autenticado, redirigir a donde se solicitó
        header("Location: $return_to");
    } else {
        // Si no autenticado, redirigir al login
        header("Location: /projects/villarrica_click/public/login.php?error=session_expired&return_to=" . urlencode($return_to));
    }
}

// Limpiar buffer y finalizar
ob_end_flush();
exit;