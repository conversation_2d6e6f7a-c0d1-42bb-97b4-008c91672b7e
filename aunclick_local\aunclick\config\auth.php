<?php
error_reporting(E_ALL & ~E_WARNING);

// Asegurar que buffer de salida está limpio
while (ob_get_level()) ob_end_clean();
ob_start();

require_once 'config.php';
require_once 'SessionManager.php';

// Inicializar el manejador de sesiones
$sessionManager = SessionManager::getInstance();

// Asegurar que la sesión está limpia antes de iniciar
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
}

$sessionManager->initializeSession();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $login = trim($_POST['username']); // Puede ser username o email
        $password = $_POST['password'];
        
        if (empty($login) || empty($password)) {
            throw new Exception('empty');
        }

        // Modificar la consulta para buscar por username o email
        $stmt = $conn->prepare("SELECT id, username, email, password, role FROM users WHERE username = ? OR email = ?");
        $stmt->bind_param("ss", $login, $login);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            error_log("Auth.php - Usuario no encontrado para login: " . $login);
            throw new Exception('user_not_found');
        }
        
        $user = $result->fetch_assoc();
        
        if (!password_verify($password, $user['password'])) {
            error_log("Auth.php - Contraseña incorrecta para usuario: " . $user['username']);
            throw new Exception('invalid_password');
        }

        // Crear sesión de usuario usando SessionManager
        $sessionManager->createUserSession([
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'user_id' => $user['id'] // Agregar explícitamente el user_id
        ]);
        
        // Guardar el ID del usuario en la sesión
        $_SESSION['user_id'] = $user['id'];
        
        error_log("Auth.php - Login exitoso para usuario: " . $user['username']);
        error_log("Auth.php - Datos de sesión después de login: " . print_r($_SESSION, true));
        
        // Forzar escritura de sesión antes de redireccionar
        session_write_close();
        
        // Asegurar que no hay output en el buffer
        if (ob_get_length()) ob_end_clean();
        
        // Prevenir caché
        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");
        
        // Redirigir a tienda_adm.php
        $redirect_url = BASE_URL . '/public/tienda_adm.php';
        header('Location: ' . $redirect_url);
        exit();

    } catch (Exception $e) {
        error_log("Auth.php - Error en login: " . $e->getMessage());
        // Limpiar cualquier sesión existente
        $sessionManager->destroySession();
        // Limpiar buffer antes de redireccionar
        if (ob_get_length()) ob_end_clean();
        header('Location: ' . BASE_URL . '/public/login.php?error=' . urlencode($e->getMessage()));
        exit();
    }
} else {
    if (ob_get_length()) ob_end_clean();
    header('Location: ' . BASE_URL . '/public/login.php');
    exit();
}