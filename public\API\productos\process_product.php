<?php
ini_set('display_errors', 1); // Activar temporalmente para detectar errores en la conversión
error_reporting(E_ALL);

// Función de registro para debug
function debugLog($message, $data = null) {
    // Intentar guardar en un archivo real para rastreo
    try {
        $logDir = $_SERVER['DOCUMENT_ROOT'] . '/projects/villarrica_click/logs';
        if (!is_dir($logDir)) {
            @mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/webp_conversion.log';
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] {$message}";

        if ($data !== null) {
            if (is_array($data) || is_object($data)) {
                $log_entry .= ": " . print_r($data, true);
            } else {
                $log_entry .= ": " . $data;
            }
        }

        @file_put_contents($logFile, $log_entry . "\n", FILE_APPEND);
    } catch (Exception $e) {
        // Si falla el log a archivo, usar memory logging como fallback
        global $debug_logs;
        if (!isset($debug_logs)) {
            $debug_logs = array();
        }
        $debug_logs[] = $message . (($data !== null) ? ": " . print_r($data, true) : "");
    }
}

// Nueva función simplificada para convertir imágenes a WebP
function convertToWebP($source, $destination, $quality = 80) {
    debugLog("Iniciando conversión WebP", ["source" => $source, "destination" => $destination]);

    // Verificar que el archivo existe
    if (!file_exists($source)) {
        debugLog("Error: Archivo fuente no existe", $source);
        return false;
    }

    // Identificar el tipo de imagen
    $imageInfo = getimagesize($source);
    if ($imageInfo === false) {
        debugLog("Error: No se pudo obtener información de la imagen");
        return false;
    }

    debugLog("Información de imagen:", $imageInfo);

    // Crear la imagen desde el archivo original según su tipo
    $image = null;
    switch ($imageInfo[2]) {
        case IMAGETYPE_JPEG:
            $image = @imagecreatefromjpeg($source);
            debugLog("Imagen JPEG detectada");
            break;
        case IMAGETYPE_PNG:
            $image = @imagecreatefrompng($source);
            // Preservar transparencia
            if ($image) {
                imagepalettetotruecolor($image);
                imagealphablending($image, true);
                imagesavealpha($image, true);
                debugLog("Imagen PNG detectada con transparencia preservada");
            }
            break;
        case IMAGETYPE_GIF:
            $image = @imagecreatefromgif($source);
            debugLog("Imagen GIF detectada");
            break;
        case IMAGETYPE_WEBP:
            // Si ya es WebP, simplemente copiar el archivo
            debugLog("Imagen ya está en formato WebP, copiando directamente");
            return copy($source, $destination);
            break;
        default:
            // Para otros tipos, intentar copiar directamente
            debugLog("Tipo de imagen no reconocido directamente: " . $imageInfo[2] . ", intentando copiar");
            return copy($source, $destination);
    }

    // Verificar si se pudo crear la imagen
    if (!$image) {
        debugLog("Error: No se pudo crear la imagen desde la fuente");
        return false;
    }

    // Verificar permisos en el directorio de destino
    $destDir = dirname($destination);
    if (!is_dir($destDir)) {
        debugLog("Creando directorio de destino", $destDir);
        if (!@mkdir($destDir, 0755, true)) {
            debugLog("Error: No se pudo crear el directorio de destino");
            return false;
        }
    }

    if (!is_writable($destDir)) {
        debugLog("Error: El directorio de destino no tiene permisos de escritura", $destDir);
        return false;
    }

    // Guardar como WebP
    $result = @imagewebp($image, $destination, $quality);

    // Verificar resultado
    if (!$result) {
        debugLog("Error al guardar imagen como WebP");
    } else {
        debugLog("Conversión a WebP exitosa", $destination);

        // Verificar que el archivo existe y tiene tamaño
        if (file_exists($destination) && filesize($destination) > 0) {
            debugLog("Archivo WebP creado correctamente con tamaño: " . filesize($destination));
        } else {
            debugLog("Archivo WebP no existe o tiene tamaño cero");
            $result = false;
        }
    }

    // Liberar memoria
    imagedestroy($image);

    return $result;
}

session_start();

// Asegurarse de que no haya output antes del JSON
if (ob_get_level() > 0) {
    ob_end_clean();
}
header('Content-Type: application/json');

// Intentar cargar la configuración con manejo de errores
try {
    $configPath = '../../config/config.php';

    if (!file_exists($configPath)) {
        // Intentar ruta alternativa
        $altConfigPath = '/var/www/aunclick/config/config.php';

        if (file_exists($altConfigPath)) {
            require_once $altConfigPath;
        } else {
            throw new Exception('No se pudo encontrar el archivo config.php');
        }
    } else {
        require_once $configPath;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error interno: ' . $e->getMessage()]);
    exit();
}

// Verificar conexión a la base de datos
if (!isset($conn) || $conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Error de conexión a la base de datos']);
    exit();
}

$response = ['success' => false];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Obtener datos
        $nombre = $conn->real_escape_string($_POST['nombre'] ?? '');
        $descripcion = $conn->real_escape_string($_POST['descripcion'] ?? '');

        // Limpiar y convertir el precio correctamente
        $precio_raw = $_POST['precio'] ?? '';
        // Mantener el punto decimal pero eliminar otros caracteres no numéricos
        $precio_limpio = preg_replace('/[^0-9.]/', '', $precio_raw);
        // Asegurar que solo hay un punto decimal (el último)
        if (substr_count($precio_limpio, '.') > 1) {
            $partes = explode('.', $precio_limpio);
            $decimal = array_pop($partes); // Última parte (decimal)
            $entero = implode('', $partes); // Unir partes enteras sin puntos
            $precio_limpio = $entero . '.' . $decimal;
        }
        $precio = floatval($precio_limpio);
        debugLog("Precio procesado", ["raw" => $precio_raw, "limpio" => $precio_limpio, "final" => $precio]);

        $stock = isset($_POST['stock']) ? intval($_POST['stock']) : 0;
        $categoria_id = isset($_POST['categoria_id']) ? intval($_POST['categoria_id']) : null;
        $condicion = $conn->real_escape_string($_POST['condicion'] ?? '');

        // Validar que se haya seleccionado una categoría
        if (!$categoria_id) {
            throw new Exception('Debe seleccionar una subcategoría');
        }

        // Manejo de la imagen - Modificado para garantizar conversión a WebP
        $imagen = '';
        if (isset($_FILES['imagen']) && $_FILES['imagen']['error'] === UPLOAD_ERR_OK) {
            $file_extension = strtolower(pathinfo($_FILES['imagen']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'avif'];

            if (!in_array($file_extension, $allowed_extensions)) {
                throw new Exception('Tipo de archivo no permitido. Use: ' . implode(', ', $allowed_extensions));
            }

            // Verificar tamaño
            if ($_FILES['imagen']['size'] <= 0) {
                throw new Exception('El archivo de imagen está vacío');
            }

            debugLog('Imagen recibida', [
                'nombre' => $_FILES['imagen']['name'],
                'tipo' => $_FILES['imagen']['type'],
                'tamaño' => $_FILES['imagen']['size'],
                'tmp_name' => $_FILES['imagen']['tmp_name'],
                'extension' => $file_extension
            ]);

            // Crear un nombre único para la imagen
            $unique_id = uniqid();

            // IMPORTANTE: Definir rutas correctamente
            // Ruta ABSOLUTA en el servidor donde guardar las imágenes
            $server_image_dir = $_SERVER['DOCUMENT_ROOT'] . '/images/productos/';

            // Crear el directorio si no existe
            if (!is_dir($server_image_dir)) {
                debugLog('Intentando crear directorio de imágenes', $server_image_dir);
                if (!@mkdir($server_image_dir, 0755, true)) {
                    debugLog('Error al crear directorio', $server_image_dir);
                    throw new Exception('No se pudo crear el directorio para guardar imágenes');
                }
            }

            // Verificar permiso de escritura en el directorio
            if (!is_writable($server_image_dir)) {
                debugLog('El directorio no tiene permisos de escritura', $server_image_dir);
                throw new Exception('El directorio de imágenes no tiene permisos de escritura');
            }

            // IMPORTANTE: Siempre guardar primero la imagen original (temporal)
            $temp_original = $server_image_dir . 'temp_' . $unique_id . '.' . $file_extension;

            debugLog('Moviendo archivo temporal a ubicación temporal', [
                'origen' => $_FILES['imagen']['tmp_name'],
                'destino' => $temp_original
            ]);

            if (!move_uploaded_file($_FILES['imagen']['tmp_name'], $temp_original)) {
                debugLog('Error al mover archivo temporal');
                throw new Exception('Error al procesar la imagen. Por favor, intente nuevamente.');
            }

            // Ahora intentamos convertir a WebP
            $webp_filename = $unique_id . '.webp';
            $webp_path = $server_image_dir . $webp_filename;

            debugLog('Intentando convertir a WebP', [
                'origen' => $temp_original,
                'destino' => $webp_path
            ]);

            $conversion_success = convertToWebP($temp_original, $webp_path);

            // Ruta para guardar en la BD
            if ($conversion_success) {
                // Si la conversión fue exitosa, usar la ruta WebP
                $imagen_bd = '/images/productos/' . $webp_filename;
                debugLog('Conversión exitosa, usando ruta WebP', $imagen_bd);

                // Eliminar el archivo temporal
                @unlink($temp_original);
            } else {
                // Si falló la conversión, mantener la original
                $imagen_bd = '/images/productos/' . 'temp_' . $unique_id . '.' . $file_extension;
                debugLog('Conversión fallida, usando archivo original', $imagen_bd);
            }

            $imagen_temp = $imagen_bd;
        } else {
            throw new Exception('Debe seleccionar una imagen para el producto');
        }

        // Insertar en la base de datos
        $sql = "INSERT INTO tb_productos (nombre, descripcion, precio, imagen, stock, categoria_id, condicion) VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception('Error preparando la consulta: ' . $conn->error);
        }

        $bind_result = $stmt->bind_param("ssdsiss", $nombre, $descripcion, $precio, $imagen_temp, $stock, $categoria_id, $condicion);
        if (!$bind_result) {
            throw new Exception('Error al vincular parámetros: ' . $stmt->error);
        }

        if ($stmt->execute()) {
            $new_id = $stmt->insert_id;

            // Obtener los datos de la categoría
            try {
                $sql_categoria = "SELECT sub_categoria FROM tb_categorias WHERE id = ?";
                $stmt_cat = $conn->prepare($sql_categoria);
                if (!$stmt_cat) {
                    throw new Exception('Error consultando categoría');
                }

                $stmt_cat->bind_param("i", $categoria_id);
                $stmt_cat->execute();
                $result_cat = $stmt_cat->get_result();

                if ($result_cat->num_rows > 0) {
                    $categoria_nombre = $result_cat->fetch_assoc()['sub_categoria'];
                } else {
                    $categoria_nombre = 'Desconocida';
                }
            } catch (Exception $cat_e) {
                $categoria_nombre = 'Error';
            }

            $response = [
                'success' => true,
                'message' => 'Producto agregado exitosamente',
                'product' => [
                    'id' => $new_id,
                    'nombre' => $nombre,
                    'descripcion' => $descripcion,
                    'precio' => $precio,
                    'imagen' => $imagen_temp,
                    'stock' => $stock,
                    'categoria_id' => $categoria_id,
                    'categoria_nombre' => $categoria_nombre,
                    'condicion' => $condicion
                ]
            ];
        } else {
            throw new Exception('Error al guardar el producto en la base de datos: ' . $stmt->error);
        }
    } else {
        throw new Exception('Método no permitido');
    }
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
}

echo json_encode($response);
exit();
?>