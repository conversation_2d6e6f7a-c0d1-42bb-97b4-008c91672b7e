<?php
/**
 * RateLimiter - Clase para limitar la velocidad de solicitudes
 * Previene ataques de fuerza bruta y spam
 */

class RateLimiter {
    private $conn;
    private $tableName = 'rate_limits';
    
    public function __construct($connection) {
        $this->conn = $connection;
        $this->createTableIfNotExists();
    }
    
    /**
     * Crea la tabla de rate limiting si no existe
     */
    private function createTableIfNotExists() {
        $sql = "CREATE TABLE IF NOT EXISTS {$this->tableName} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            action VARCHAR(100) NOT NULL,
            attempts INT DEFAULT 1,
            first_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            blocked_until TIMESTAMP NULL,
            INDEX idx_identifier_action (identifier, action),
            INDEX idx_blocked_until (blocked_until)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $this->conn->query($sql);
    }
    
    /**
     * Verifica si una acción está permitida
     */
    public function isAllowed($identifier, $action, $maxAttempts = 5, $timeWindow = 900) {
        $this->cleanupOldRecords();
        
        $stmt = $this->conn->prepare("
            SELECT attempts, blocked_until, first_attempt 
            FROM {$this->tableName} 
            WHERE identifier = ? AND action = ?
        ");
        
        $stmt->bind_param("ss", $identifier, $action);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            // Verificar si está bloqueado
            if ($row['blocked_until'] && strtotime($row['blocked_until']) > time()) {
                return [
                    'allowed' => false,
                    'reason' => 'blocked',
                    'blocked_until' => $row['blocked_until'],
                    'attempts' => $row['attempts']
                ];
            }
            
            // Verificar si está dentro de la ventana de tiempo
            $firstAttempt = strtotime($row['first_attempt']);
            if ((time() - $firstAttempt) < $timeWindow) {
                if ($row['attempts'] >= $maxAttempts) {
                    // Bloquear por el tiempo especificado
                    $blockedUntil = date('Y-m-d H:i:s', time() + $timeWindow);
                    $updateStmt = $this->conn->prepare("
                        UPDATE {$this->tableName} 
                        SET blocked_until = ? 
                        WHERE identifier = ? AND action = ?
                    ");
                    $updateStmt->bind_param("sss", $blockedUntil, $identifier, $action);
                    $updateStmt->execute();
                    
                    return [
                        'allowed' => false,
                        'reason' => 'rate_limit_exceeded',
                        'blocked_until' => $blockedUntil,
                        'attempts' => $row['attempts']
                    ];
                }
            } else {
                // Reiniciar contador si ha pasado la ventana de tiempo
                $this->resetAttempts($identifier, $action);
            }
        }
        
        return [
            'allowed' => true,
            'attempts' => $row['attempts'] ?? 0
        ];
    }
    
    /**
     * Registra un intento
     */
    public function recordAttempt($identifier, $action) {
        $stmt = $this->conn->prepare("
            INSERT INTO {$this->tableName} (identifier, action, attempts) 
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE 
                attempts = attempts + 1,
                last_attempt = CURRENT_TIMESTAMP
        ");
        
        $stmt->bind_param("ss", $identifier, $action);
        return $stmt->execute();
    }
    
    /**
     * Reinicia los intentos para un identificador y acción
     */
    public function resetAttempts($identifier, $action) {
        $stmt = $this->conn->prepare("
            DELETE FROM {$this->tableName} 
            WHERE identifier = ? AND action = ?
        ");
        
        $stmt->bind_param("ss", $identifier, $action);
        return $stmt->execute();
    }
    
    /**
     * Limpia registros antiguos
     */
    private function cleanupOldRecords() {
        // Eliminar registros más antiguos de 24 horas
        $stmt = $this->conn->prepare("
            DELETE FROM {$this->tableName} 
            WHERE last_attempt < DATE_SUB(NOW(), INTERVAL 24 HOUR)
            AND (blocked_until IS NULL OR blocked_until < NOW())
        ");
        
        $stmt->execute();
    }
    
    /**
     * Obtiene el identificador único para rate limiting
     */
    public static function getIdentifier() {
        $ip = self::getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Crear un hash único basado en IP y User Agent
        return hash('sha256', $ip . '|' . $userAgent);
    }
    
    /**
     * Obtiene la IP real del cliente
     */
    public static function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, 
                    FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Verifica si una IP está en lista blanca
     */
    public function isWhitelisted($ip) {
        $whitelist = [
            '127.0.0.1',
            '::1',
            // Agregar IPs de administradores aquí
        ];
        
        return in_array($ip, $whitelist);
    }
    
    /**
     * Bloquea permanentemente una IP (para casos extremos)
     */
    public function permanentBlock($identifier, $action, $reason = '') {
        $blockedUntil = date('Y-m-d H:i:s', strtotime('+1 year'));
        
        $stmt = $this->conn->prepare("
            INSERT INTO {$this->tableName} (identifier, action, attempts, blocked_until) 
            VALUES (?, ?, 999, ?)
            ON DUPLICATE KEY UPDATE 
                attempts = 999,
                blocked_until = ?,
                last_attempt = CURRENT_TIMESTAMP
        ");
        
        $stmt->bind_param("ssss", $identifier, $action, $blockedUntil, $blockedUntil);
        
        // Log del bloqueo permanente
        error_log("PERMANENT BLOCK: $identifier for action $action. Reason: $reason");
        
        return $stmt->execute();
    }
    
    /**
     * Obtiene estadísticas de rate limiting
     */
    public function getStats($timeframe = '1 HOUR') {
        $stmt = $this->conn->prepare("
            SELECT 
                action,
                COUNT(*) as total_attempts,
                COUNT(DISTINCT identifier) as unique_identifiers,
                SUM(CASE WHEN blocked_until > NOW() THEN 1 ELSE 0 END) as currently_blocked
            FROM {$this->tableName} 
            WHERE last_attempt > DATE_SUB(NOW(), INTERVAL $timeframe)
            GROUP BY action
        ");
        
        $stmt->execute();
        return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    }
}
?>
