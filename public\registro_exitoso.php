<?php
// Verificar si estamos en la raíz o en un subdirectorio
$config_path = file_exists('config/logger.php') ? 'config/' : '../config/';

// Incluir primero el sistema de logging
require_once $config_path . 'logger.php';

// Registrar inicio de la página
logInfo("Cargando página de registro exitoso");

// Incluir la configuración base
require_once $config_path . 'config.php';

// Capturar datos enviados (si hay)
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$nombre = isset($_GET['nombre']) ? htmlspecialchars($_GET['nombre']) : 'Usuario';

// Registrar el acceso exitoso
logInfo("Acceso a página de registro exitoso - User ID: $user_id");
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro Exitoso - Villarrica a un CLICK</title>
    
    <!-- Importación de fuentes -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Importación de iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #6a2a83;
            --success-color: #28a745;
            --text-color: #333;
            --light-bg: #f9f9f9;
            --border-color: #ddd;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #f5f5f5;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .success-icon {
            font-size: 80px;
            color: var(--success-color);
            margin-bottom: 20px;
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 32px;
        }
        
        p {
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        .info-box {
            background-color: var(--light-bg);
            border: 1px solid var(--border-color);
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            text-align: left;
        }
        
        .info-box h2 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 22px;
        }
        
        .info-box ul {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .info-box li {
            margin-bottom: 10px;
            position: relative;
        }
        
        .info-box li:before {
            content: "✓";
            color: var(--success-color);
            position: absolute;
            left: -20px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border-radius: 5px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            background-color: #551e6a;
        }
        
        .btn-outlined {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outlined:hover {
            background-color: #f0e6f4;
        }
        
        .footer {
            margin-top: 40px;
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <i class="fa-solid fa-circle-check success-icon"></i>
        <h1>¡Registro Exitoso!</h1>
        <p>¡Felicidades! Tu cuenta ha sido creada correctamente en Villarrica a un CLICK.</p>
        <p>Ya puedes comenzar a utilizar nuestra plataforma para hacer crecer tu negocio.</p>
        
        <div class="info-box">
            <h2>Próximos pasos:</h2>
            <ul>
                <li>Inicia sesión con tu nombre de usuario y contraseña</li>
                <li>Completa tu perfil con información adicional</li>
                <li>Personaliza tu página de negocio</li>
                <li>Agrega productos o servicios a tu catálogo</li>
                <li>Explora todas las funcionalidades disponibles</li>
            </ul>
        </div>
        
        <div class="buttons">
            <a href="login.php" class="btn">Iniciar Sesión</a>
            <a href="index.php" class="btn btn-outlined">Volver al Inicio</a>
        </div>
        
        <div class="footer">
            <p>Si tienes alguna pregunta, no dudes en contactarnos.</p>
            <p>&copy; <?php echo date('Y'); ?> Villarrica a un CLICK. Todos los derechos reservados.</p>
        </div>
    </div>
    
    <script>
        // Mostrar mensaje personalizado si hay datos disponibles
        document.addEventListener('DOMContentLoaded', function() {
            // Esta función se puede expandir para mostrar información personalizada
            // basada en los datos recibidos en la URL o desde una API
        });
    </script>
</body>
</html>