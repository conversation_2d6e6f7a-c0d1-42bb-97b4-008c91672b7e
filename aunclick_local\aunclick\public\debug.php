<?php
session_start();
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Depuración de Registro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            padding: 0;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h2 {
            color: #3498db;
            margin-top: 0;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .error {
            color: #e74c3c;
            font-weight: bold;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .warning {
            color: #f39c12;
            font-weight: bold;
        }
        .connection-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #eaf2f8;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Depuración de Registro</h1>
    
    <div class="debug-section">
        <h2>Estado de la Sesión</h2>
        <?php if (empty($_SESSION)): ?>
            <p class="warning">No hay datos en la sesión.</p>
        <?php else: ?>
            <p>Datos encontrados en la sesión:</p>
            <ul>
                <?php foreach ($_SESSION as $key => $value): ?>
                    <li><strong><?php echo htmlspecialchars($key); ?>:</strong> 
                        <?php if ($key === 'debug_sql'): ?>
                            <pre><?php echo htmlspecialchars($value); ?></pre>
                        <?php elseif (is_array($value)): ?>
                            <pre><?php print_r($value); ?></pre>
                        <?php else: ?>
                            <?php echo htmlspecialchars($value); ?>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
    
    <div class="debug-section">
        <h2>Prueba de Conexión a la Base de Datos</h2>
        <?php
        // Intentar conectar a la base de datos
        $servername = "localhost";
        $username = "root";
        $password = "";
        $dbname = "villarrica_click";
        
        try {
            $conn = new mysqli($servername, $username, $password, $dbname);
            
            if ($conn->connect_error) {
                echo '<p class="error">Error de conexión: ' . htmlspecialchars($conn->connect_error) . '</p>';
            } else {
                echo '<p class="success">Conexión exitosa a la base de datos.</p>';
                
                // Verificar si la tabla existe
                $result = $conn->query("SHOW TABLES LIKE 'tb_register'");
                if ($result->num_rows > 0) {
                    echo '<p class="success">La tabla tb_register existe.</p>';
                    
                    // Mostrar estructura de la tabla
                    echo '<h3>Estructura de la tabla:</h3>';
                    $result = $conn->query("DESCRIBE tb_register");
                    if ($result->num_rows > 0) {
                        echo '<table border="1" cellpadding="5" cellspacing="0">';
                        echo '<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Predeterminado</th><th>Extra</th></tr>';
                        while ($row = $result->fetch_assoc()) {
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($row['Field']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['Type']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['Null']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['Key']) . '</td>';
                            echo '<td>' . (isset($row['Default']) ? htmlspecialchars($row['Default']) : 'NULL') . '</td>';
                            echo '<td>' . htmlspecialchars($row['Extra']) . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                    } else {
                        echo '<p class="error">No se pudo obtener la estructura de la tabla.</p>';
                    }
                    
                    // Mostrar registros existentes
                    echo '<h3>Registros existentes:</h3>';
                    $result = $conn->query("SELECT * FROM tb_register");
                    if ($result->num_rows > 0) {
                        echo '<p>Número de registros: ' . $result->num_rows . '</p>';
                        echo '<table border="1" cellpadding="5" cellspacing="0">';
                        echo '<tr><th>ID</th><th>Nombres</th><th>Apellidos</th><th>RUT</th><th>Email</th><th>Nombre Negocio</th><th>Tipo Negocio</th><th>Subscription</th></tr>';
                        while ($row = $result->fetch_assoc()) {
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['nombres']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['apellidos']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['rut']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['email']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['nombre_negocio']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['tipo_negocio']) . '</td>';
                            echo '<td>' . htmlspecialchars($row['subscription']) . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                    } else {
                        echo '<p class="warning">No hay registros en la tabla.</p>';
                    }
                } else {
                    echo '<p class="error">La tabla tb_register no existe.</p>';
                }
            }
        } catch (Exception $e) {
            echo '<p class="error">Excepción: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>
    
    <div class="connection-info">
        <h2>Información de Conexión</h2>
        <p><strong>Servidor:</strong> <?php echo htmlspecialchars($servername); ?></p>
        <p><strong>Usuario:</strong> <?php echo htmlspecialchars($username); ?></p>
        <p><strong>Base de datos:</strong> <?php echo htmlspecialchars($dbname); ?></p>
        <p><strong>PHP Version:</strong> <?php echo phpversion(); ?></p>
        <p><strong>MySQL Version:</strong> 
        <?php 
        if (isset($conn) && !$conn->connect_error) {
            echo htmlspecialchars($conn->server_info);
        } else {
            echo 'No disponible';
        }
        ?>
        </p>
    </div>
</body>
</html>
