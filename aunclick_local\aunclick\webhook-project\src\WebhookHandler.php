<?php
/**
 * Clase WebhookHandler
 * 
 * Maneja el procesamiento de eventos webhook
 */
class WebhookHandler {
    /**
     * Verifica la firma del webhook para autenticar la solicitud
     * 
     * @param string $payload El cuerpo de la solicitud
     * @param string $receivedSignature La firma recibida
     * @param string $secret La clave secreta para verificación
     * @return bool
     */
    public function verificarFirma($payload, $receivedSignature, $secret) {
        if (empty($receivedSignature)) {
            return false;
        }
        
        $expectedSignature = hash_hmac('sha256', $payload, $secret);
        return hash_equals($expectedSignature, $receivedSignature);
    }
    
    /**
     * Procesa un evento según su tipo
     * 
     * @param string $tipoEvento El tipo de evento a procesar
     * @param array $data Los datos del evento
     * @return array Resultado del procesamiento
     * @throws Exception Si ocurre un error durante el procesamiento
     */
    public function procesarEvento($tipoEvento, $data) {
        switch ($tipoEvento) {
            case 'pago_completado':
                return $this->procesarPago($data);
                
            case 'usuario_registrado':
                return $this->registrarUsuario($data);
                
            case 'producto_actualizado':
                return $this->actualizarProducto($data);
                
            default:
                throw new Exception("Tipo de evento desconocido: $tipoEvento");
        }
    }
    
    /**
     * Procesa un evento de pago completado
     * 
     * @param array $data Datos del pago
     * @return array
     */
    private function procesarPago($data) {
        // Aquí iría la lógica para procesar el pago
        // Por ejemplo, actualizar estado en base de datos, enviar email, etc.
        
        // Simulación de procesamiento
        $idTransaccion = $data['id_transaccion'] ?? uniqid();
        $monto = $data['monto'] ?? 0;
        $estado = 'completado';
        
        // Aquí se realizaría la actualización en la base de datos
        
        return [
            'id_transaccion' => $idTransaccion,
            'estado' => $estado,
            'mensaje' => "Pago por $monto procesado exitosamente"
        ];
    }
    
    /**
     * Procesa un evento de registro de usuario
     * 
     * @param array $data Datos del usuario
     * @return array
     */
    private function registrarUsuario($data) {
        // Aquí iría la lógica para procesar el registro
        // Por ejemplo, enviar email de bienvenida, asignar permisos, etc.
        
        $idUsuario = $data['id_usuario'] ?? uniqid();
        $email = $data['email'] ?? '';
        
        // Aquí se realizarían acciones adicionales
        
        return [
            'id_usuario' => $idUsuario,
            'mensaje' => "Usuario $email registrado exitosamente"
        ];
    }
    
    /**
     * Procesa un evento de actualización de producto
     * 
     * @param array $data Datos del producto
     * @return array
     */
    private function actualizarProducto($data) {
        // Aquí iría la lógica para actualizar productos
        
        $idProducto = $data['id_producto'] ?? '';
        $nombre = $data['nombre'] ?? '';
        
        // Aquí se realizaría la actualización en la base de datos
        
        return [
            'id_producto' => $idProducto,
            'mensaje' => "Producto $nombre actualizado exitosamente"
        ];
    }
}
