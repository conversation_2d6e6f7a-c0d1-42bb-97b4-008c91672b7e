/* Estilos para la tabla de productos */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

/* Agregar estilos para el tooltip en la celda de descripción */
.admin-table td.description-cell {
    position: relative;
    cursor: pointer;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.admin-table td.description-cell::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 14px;
    border-radius: 4px;
    white-space: normal;
    max-width: 300px;
    width: max-content;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.admin-table td.description-cell:hover::before {
    visibility: visible;
    opacity: 1;
}

.admin-table th {
    background-color: var(--color-background);
    font-weight: 600;
    color: var(--color-text-dark);
}

.admin-table tr:hover {
    background-color: var(--color-hover);
}

.loading-row {
    text-align: center;
    color: var(--color-text-light);
    padding: 2rem !important;
}

.error-row {
    text-align: center;
    color: var(--color-danger);
    padding: 2rem !important;
}

/* Badges para estado y condición */
.status-badge,
.condition-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.publicado {
    background-color: var(--color-success-light);
    color: var(--color-success);
}

.status-badge.borrador {
    background-color: var(--color-warning-light);
    color: var(--color-warning);
}

.status-badge.agotado {
    background-color: var(--color-danger-light);
    color: var(--color-danger);
}

.condition-badge.destacado {
    background-color: #fff3cd;
    color: #856404;
}

.condition-badge.oferta {
    background-color: #d4edda;
    color: #155724;
}

.condition-badge.liquidacion {
    background-color: #f8d7da;
    color: #721c24;
}

.condition-badge.nuevo {
    background-color: #cce5ff;
    color: #004085;
}

.condition-badge.exclusivo {
    background-color: #e2e3e5;
    color: #383d41;
}

.condition-badge.ninguno {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* Botones de acción en la tabla */
.table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.btn-action {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-action.edit {
    background-color: var(--color-primary-light);
    color: var(--color-primary);
}

.btn-action.delete {
    background-color: var(--color-danger-light);
    color: var(--color-danger);
}

.btn-action:hover {
    opacity: 0.8;
}

/* Estilos para imágenes en la tabla */
.admin-table img {
    border-radius: 4px;
    object-fit: cover;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
