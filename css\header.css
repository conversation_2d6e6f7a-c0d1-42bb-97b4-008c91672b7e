/* Variables globales y reset */
:root {
    --purple-primary: #6a1b9a;
    --purple-light: #9c4dcc;
    --purple-dark: #38006b;
    --green-primary: #43a047;
    --green-light: #76d275;
    --green-dark: #00701a;
    --yellow-accent: #ffd54f;
    --yellow-light: #fff7c4;
    --text-primary: rgba(0,0,0,.9);
    --text-secondary: rgba(0,0,0,.55);
    --max-width: 1200px;
    --white: #ffffff;
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --header-height: 130px; /* Nueva variable para la altura del header */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Estilos del menú móvil */
.mobile-menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-menu.active {
    display: block;
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background: linear-gradient(-45deg, var(--purple-dark) 30%, var(--purple-primary) 100%);
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    overflow-y: auto;
    padding: 20px;
    z-index: 10000;
}

.mobile-menu.active .mobile-menu-content {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mobile-menu-header h2 {
    margin: 0;
    font-size: 20px;
    color: var(--yellow-accent);
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--yellow-accent);
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-menu-list li {
    margin-bottom: 10px;
}

.mobile-menu-list li a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: var(--yellow-accent);
    text-decoration: none;
    font-size: 16px;
    transition: all 0.3s ease;
}

.mobile-menu-list li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.mobile-menu-list li a i {
    margin-right: 15px;
    width: 20px;
    color: var(--yellow-accent);
}

.mobile-menu-list li a:hover i {
    color: white;
}

.mobile-menu-toggle {
    display: none;
}

@media screen and (max-width: 767px) {
    .mobile-menu-toggle {
        display: block;
        color: var(--yellow-accent);
        font-size: 24px;
        cursor: pointer;
        padding: 5px;
    }

    .nav-account {
        display: none;
    }
}

/* Variables globales de colores y medidas */
:root {
    --purple-primary: #6a1b9a;
    --purple-light: #9c4dcc;
    --purple-dark: #38006b;
    --green-primary: #43a047;
    --green-light: #76d275;
    --green-dark: #00701a;
    --yellow-accent: #ffd54f;
    --yellow-light: #fff7c4;
    --text-primary: rgba(0,0,0,.9);
    --text-secondary: rgba(0,0,0,.55);
    --max-width: 1200px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 16px;
}

.header {
    background: linear-gradient(45deg, var(--purple-primary) 40%, var(--yellow-accent) 90%);
    width: 100%;
}

.header-top {
    padding: 8px 0;
}

.header-top .container {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 24px;
    align-items: center;
}

.logo {
    padding: 8px 0;
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    font-family: 'Montserrat', sans-serif;
}

.logo-text .city {
    font-size: 24px;
    font-weight: 700;
    color: white;
    letter-spacing: -0.5px;
}

.logo-text .tagline {
    font-size: 14px;
    color: white;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.search-bar {
    position: relative;
    max-width: 600px;
    width: 100%;
    height: 42px;
    display: flex;
    align-items: center;
}

.search-bar input {
    width: 100%;
    height: 42px;
    padding: 12px 16px;
    border: 1px solid #000;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0,0,0,.2);
    font-size: 16px;
}

.search-button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    padding: 0 12px;
    border: none;
    background: none;
    cursor: pointer;
    color: #000;
}

.promo-banner img {
    height: 42px;
    width: auto;
    object-fit: contain;
}

.promo-banner {
    height: 42px;
    display: flex;
    align-items: center;
    margin: auto 0;
}

.header-nav {
    background: linear-gradient(-45deg, var(--purple-dark) 30%, var(--purple-primary) 100%);
    padding: 8px 0;
    border-top: 1px solid rgba(255,255,255,0.1);
    margin-top: 0;
}

.header-nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-categories, .nav-account {
    display: flex;
    list-style: none;
    gap: 10px;
}

.nav-categories {
    display: flex;
    justify-content: center;
    gap: 20px;
    align-items: center;
    margin: 0;
    padding: 0;
}

.nav-categories li {
    display: flex;
}

.nav-categories li a,
.nav-account li a {
    color: var(--yellow-accent);
    font-weight: 500;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 15px;
    transition: color 0.3s ease;
}

.nav-categories li a:hover,
.nav-account li a:hover {
    color: white;
    text-shadow: 0 0 2px rgba(0,0,0,0.2);
}

.nav-account li a span {
    font-size: 14px;
}

.nav-account li a i {
    margin-right: 8px;
    font-size: 14px;
}

.location-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    color: var(--yellow-accent);
    margin-left: -20px;
}

.location-item .location-icons {
    display: flex;
    align-items: center;
    gap: 2px;
}

.location-icons i:nth-child(2) {
    display: none;
}

.location-item i {
    color: var(--yellow-accent);
    margin-right: 2px;
}

.location-item:hover,
.location-item:hover i {
    color: white;
    transition: color 0.3s ease;
}

/* Estilos para el menú móvil */
.mobile-menu-toggle {
    display: none;
}

/* Estilos de modales */
.categories-modal,
.services-modal,
.rentals-modal {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
}

.categories-modal.active,
.services-modal.active,
.rentals-modal.active {
    display: block;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(106, 27, 154, 0.2);
}

.modal-title {
    color: var(--purple-primary);
    font-family: 'Montserrat', sans-serif;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--purple-primary);
    cursor: pointer;
}

.categories-list,
.services-list,
.rentals-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.categories-list li,
.services-list li,
.rentals-list li {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.categories-list li i,
.services-list li i,
.rentals-list li i {
    margin-right: 15px;
    color: var(--purple-primary);
}

.categories-list li:hover,
.services-list li:hover,
.rentals-list li:hover {
    background-color: rgba(106, 27, 154, 0.1);
}

/* Añadiendo estilo para mostrar los dropdowns */
.dropdown-content.show {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
    transform: translateX(-50%) translateY(0) !important;
}

/* Asegurarse que los dropdown-content estén ocultos por defecto */
.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
}

/* Estilos para el menú de usuario */
.user-menu-dropdown {
    position: relative;
}

.user-menu-trigger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    color: var(--yellow-accent);
    text-decoration: none;
    cursor: pointer;
    text-align: left; /* Asegura que el texto esté alineado a la izquierda */
}

.user-menu-trigger:hover {
    color: white;
}

.user-menu-modal {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    min-width: 200px;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    margin-top: 8px;
    text-align: left; /* Asegura que el contenido esté alineado a la izquierda */
}

.user-menu-modal.show {
    display: block;
}

.user-menu-list {
    list-style: none;
    padding: 8px 0;
    margin: 0;
}

.user-menu-list li a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.3s ease;
    text-align: left; /* Asegura que el texto esté alineado a la izquierda */
}

.user-menu-list li a:hover {
    background-color: rgba(106, 27, 154, 0.1);
    color: var(--purple-primary);
}

.user-menu-list li a i {
    margin-right: 12px;
    width: 20px;
    color: var(--purple-primary);
    text-align: center;
}

/* Responsive para el menú de usuario */
@media screen and (max-width: 767px) {
    .user-menu-modal {
        width: 100%;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateY(0) !important;
    }

    .user-menu-list li a {
        padding: 16px;
        font-size: 16px;
    }
}

/* Media queries para distintos tamaños de pantalla */
@media screen and (max-width: 1024px) {
    .header-top .container {
        grid-template-columns: auto 1fr;
        grid-template-rows: auto auto;
        gap: 15px;
        padding: 10px 20px;
    }

    .logo {
        grid-column: 1;
        grid-row: 1;
    }

    .search-bar {
        grid-column: 2;
        grid-row: 1;
        max-width: 100%;
    }

    .promo-banner {
        grid-column: 1 / -1;
        grid-row: 2;
        justify-content: center;
    }

    .promo-banner img {
        height: 35px;
        max-width: 100%;
    }

    .nav-categories {
        display: flex;
        justify-content: center;
        gap: 15px;
    }

    .nav-categories li a {
        font-size: 13px;
        padding: 5px 10px;
    }
    
    /* Estilos de modales para tablet */
    .categories-modal,
    .services-modal,
    .rentals-modal {
        width: 80vw;
        left: 10vw;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-title {
        font-size: 20px;
    }

    .categories-list li,
    .services-list li,
    .rentals-list li {
        padding: 12px 15px;
    }
}

@media screen and (max-width: 767px) {
    .header-top {
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .header-top .container {
        padding: 8px 15px;
        gap: 10px;
    }

    .logo-text .city {
        font-size: 20px;
    }

    .logo-text .tagline {
        font-size: 12px;
    }

    .search-bar {
        height: 35px;
    }

    .search-bar input {
        height: 35px;
        font-size: 14px;
    }

    .promo-banner img {
        height: 40px;
        padding: 0px 0;
    }

    .header-nav {
        padding: 0px 0;
        margin-top: 0;
        position: relative;
    }

    .header-nav .container {
        justify-content: space-between;
        padding: 5px 15px;
        display: grid;
        grid-template-columns: auto 1fr auto;
        align-items: center;
        min-height: 35px;
    }

    .nav-categories {
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .nav-categories li a {
        font-size: 12px;
        padding: 3px 8px;
        line-height: 1.2;
    }

    .location-item {
        margin-left: 0;
    }

    .location-item span {
        display: none;
    }

    .location-icons {
        display: flex;
        gap: 5px;
    }

    .mobile-menu-toggle {
        display: block;
        color: var(--yellow-accent);
        font-size: 24px;
        cursor: pointer;
        margin-left: auto;
        z-index: 1001;
    }

    .nav-right {
        position: static;
    }

    .nav-account {
        display: none;
    }

    .nav-account.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        width: 500px;
        background: white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 999;
    }

    .nav-account li {
        width: 100%;
        display: flex;
        align-items: center;
    }

    .nav-account li:first-child {
        border-top: none;
    }

    .nav-account li a {
        width: 100%;
        padding: 12px 15px;
        font-size: 14px;
        color: var(--text-primary);
        border-bottom: 1px solid rgba(106, 27, 154, 0.1);
        display: flex;
        align-items: center;
        text-align: left;
    }

    .nav-account li a i {
        color: var(--purple-primary);
        margin-right: 15px;
        width: 20px;
        text-align: center;
        font-size: 16px;
    }

    .nav-account li a:hover {
        background-color: rgba(106, 27, 154, 0.05);
    }

    .mobile-menu-toggle {
        color: var(--yellow-accent);
        font-size: 20px;
        padding: 5px;
    }
    
    .nav-account li a span {
        flex: 1;
        text-align: left;
    }
    
    /* Estilos de modales para móvil */
    .categories-modal,
    .services-modal,
    .rentals-modal {
        width: 100vw;
        left: 0;
        top: 0;
        height: 100vh;
        position: fixed;
        z-index: 1100;
    }

    .modal-header {
        padding: 10px;
    }

    .modal-title {
        font-size: 18px;
    }

    .modal-close {
        font-size: 24px;
        right: 10px;
    }

    .categories-list li,
    .services-list li,
    .rentals-list li {
        padding: 10px;
        font-size: 14px;
    }

    .categories-list li i,
    .services-list li i,
    .rentals-list li i {
        font-size: 16px;
    }
}

@media screen and (min-width: 1025px) {
    .nav-categories li {
        position: relative;
    }

    .categories-modal,
    .services-modal,
    .rentals-modal {
        position: absolute;
        top: 100%;
        left: 50%;
        width: 500px;
        border-radius: 8px;
        transition: opacity 0.3s ease, transform 0.3s ease;
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
        pointer-events: none;
        margin-top: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .categories-modal.active,
    .services-modal.active,
    .rentals-modal.active {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
        pointer-events: auto;
    }

    .modal-header {
        padding: 15px;
        background: linear-gradient(to right, var(--purple-primary), var(--purple-light));
    }

    .modal-title {
        font-size: 20px;
        color: #ffffff;
        font-weight: 700;
        text-align: center;
    }

    .modal-close {
        font-size: 24px;
        padding: 5px;
        color: #ffffff;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .categories-list li,
    .services-list li,
    .rentals-list li {
        padding: 12px 15px;
        font-size: 14px;
        border-bottom: 1px solid rgba(106, 27, 154, 0.1);
    }
}

header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    z-index: 1000;
}

body {
    margin-top: var(--header-height); /* Ajustar el margen superior para compensar el header fijo */
    padding-top: 1px; /* Evitar margin collapse */
}