/**
 * Estilos para el header moderno tipo Synex
 */

/* Botón toggle en el header (versión personalizada) */
.header-toggle-btn {
  position: relative;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 15px;
  z-index: 1000;
}

.header-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.header-toggle-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

.header-toggle-btn i {
  font-size: 18px;
}

.modern-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #6a1b9a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 999;
  color: white;
  transition: all 0.3s ease;
}

/* Estilos para el botón toggle del sidebar en el header */
#aside-toggle {
  position: relative !important;
  z-index: 1000 !important;
  width: 35px !important;
  height: 35px !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin-right: 10px !important;
  color: white !important;
}

/* Efecto hover para el toggle */
#aside-toggle:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Secciones del header */
.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
}

.header-left {
  min-width: 20%;
}

.header-center {
  flex: 1;
  justify-content: center;
}

.header-right {
  min-width: 20%;
  justify-content: flex-end;
  gap: 12px;
}

/* Barra de búsqueda */
.search-bar {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 6px 15px;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 500px;
}

.search-bar i {
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
}

.search-bar input {
  border: none;
  background: transparent;
  flex: 1;
  padding: 5px;
  outline: none;
  font-size: 14px;
  color: white;
}

.search-bar input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Botones de acción en el header */
.header-action {
  background: none;
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  color: white;
  transition: background-color 0.3s ease;
}

.header-action:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Indicador de notificación */
.notification-indicator {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #e53935;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Selector de idioma */
.language-selector {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  color: white;
}

.language-selector:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Perfil de usuario */
.user-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.user-profile:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.user-info {
  display: flex;
  flex-direction: column;
  text-align: right;
}

.user-name {
  font-weight: 500;
  font-size: 13px;
  color: white;
}

.user-role {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.user-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-toggle {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  color: white;
  display: none;
}

/* Responsive */
@media (max-width: 992px) {
  .modern-header {
    padding-left: 15px !important;
  }

  .header-center {
    display: none;
  }

  .user-info {
    display: none;
  }

  /* Mostrar menos acciones en móvil */
  .language-selector span,
  .theme-toggle {
    display: none;
  }

  /* Asegurar que el botón toggle esté visible */
  .header-toggle-btn {
    display: flex !important;
  }
}

@media (max-width: 576px) {
  .modern-header {
    padding-left: 15px !important;
  }

  /* Ocultar elementos menos importantes en pantallas muy pequeñas */
  .header-action:not(:last-child) {
    display: none;
  }

  /* Asegurar que el botón toggle esté visible */
  .header-toggle-btn {
    display: flex !important;
    margin-right: 10px !important;
  }
}
