// Fix for newProductBtn not working
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('Applying fix for newProductBtn...');

        // Function to show the edit product section
        function showEditProductSection() {
            try {
                console.log('Showing edit product section');
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    if (section.id === 'editProductSection') {
                        section.style.display = 'block';
                    } else {
                        section.style.display = 'none';
                    }
                });

                // Update active tab
                const tabButtons = document.querySelectorAll('.tab-btn');
                tabButtons.forEach(button => {
                    if (button.id === 'editProductTab') {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                });
            } catch (error) {
                // Silenciar errores
            }
        }

    // Function to reset the product form
    function resetProductForm() {
        try {
            console.log('Resetting product form');
            const formInputs = [
                'productName', 'productDescription', 'productShortDescription', 'productPrice',
                'productOriginalPrice', 'productStock', 'productCategory', 'productSubcategory',
                'productTags', 'productSKU', 'productBrand', 'productColor', 'productWeight',
                'productDimensions'
            ];

            formInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.value = '';
                }
            });

            // Reset radio buttons for condition
            const conditionRadio = document.querySelector('input[name="productCondition"][value="ninguno"]');
            if (conditionRadio) {
                conditionRadio.checked = true;
            }
        } catch (error) {
            // Silenciar errores
        }
    }

    // Get the newProductBtn
    const newProductBtn = document.getElementById('newProductBtn');
    if (newProductBtn) {
        console.log('Found newProductBtn, adding event listener');

        try {
            // Remove any existing event listeners by cloning and replacing the button
            const newBtn = newProductBtn.cloneNode(true);
            newProductBtn.parentNode.replaceChild(newBtn, newProductBtn);

            // Add the event listener to the new button
            newBtn.addEventListener('click', function(e) {
                try {
                    console.log('newProductBtn clicked');
                    e.preventDefault();

                    // Reset the form
                    resetProductForm();

                    // Update the section title
                    const sectionTitle = document.querySelector('#editProductSection .section-title');
                    if (sectionTitle) {
                        sectionTitle.innerHTML = '<i class="fas fa-plus"></i> Agregar Producto';
                    }

                    // Show the edit product section
                    showEditProductSection();
                } catch (clickError) {
                    // Silenciar errores
                }
            });

            console.log('Event listener added to newProductBtn');
        } catch (btnError) {
            // Silenciar errores
        }
    } else {
        // Silenciar el error
        console.debug('newProductBtn not found in the DOM');
    }
    } catch (mainError) {
        // Silenciar errores generales
    }
});
