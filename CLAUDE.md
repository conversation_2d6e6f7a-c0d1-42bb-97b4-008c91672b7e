# CLAUDE.md - Project Instructions


## Convenciones de Código

### PHP
- Usar `{}` para todos los bloques de control, incluso los de una sola línea
- Indentación con 4 espacios
- Comillas simples para strings a menos que sea necesario usar comillas dobles
- Comentarios descriptivos para funciones y bloques de código complejos

### JavaScript
- Comillas simples para strings
- Punto y coma al final de cada declaración
- Usar const/let en lugar de var
- Comentarios descriptivos para funciones y bloques de código complejos

### CSS
- Un selector por línea
- Propiedades en orden alfabético cuando sea posible
- Unidades en px o rem para fuentes y márgenes

## Resumen de Modificaciones

Después de cada conjunto de cambios, proporcionar un resumen breve y técnico de las modificaciones realizadas, enfocado en:
- Qué se modificó técnicamente
- Impacto de los cambios
- Posibles consideraciones

## Archivos Modificados

Listar todos los archivos modificados después de cada conjunto de cambios, solo identificándolos por su ruta sin detalles adicionales.



## Instrucciones 
