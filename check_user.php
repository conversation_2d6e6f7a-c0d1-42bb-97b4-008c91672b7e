<?php
// Script para verificar el usuario y la contraseña en la base de datos
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Incluir la configuración de la base de datos
require_once 'config/config.php';

// Función para mostrar resultados de manera segura
function showResult($data) {
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Verificar si se proporcionaron credenciales
$username = 'nico.cornejo';
$password = '1734';

echo "<h1>Verificación de Credenciales</h1>";
echo "<p>Verificando usuario: <strong>{$username}</strong></p>";

// Verificar la conexión a la base de datos
if (!$conn) {
    die("Error de conexión a la base de datos: " . mysqli_connect_error());
}

echo "<p>Conexión a la base de datos establecida correctamente.</p>";

// Buscar el usuario en la base de datos
$stmt = $conn->prepare("SELECT id, username, email, password, role FROM users WHERE username = ? OR email = ?");
if (!$stmt) {
    die("Error en la preparación de la consulta: " . $conn->error);
}

$stmt->bind_param("ss", $username, $username);
if (!$stmt->execute()) {
    die("Error en la ejecución de la consulta: " . $stmt->error);
}

$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "<p style='color: red;'>Usuario no encontrado en la base de datos.</p>";
    
    // Mostrar todos los usuarios disponibles
    echo "<h2>Usuarios disponibles en la base de datos:</h2>";
    $all_users = $conn->query("SELECT id, username, email, role FROM users");
    if ($all_users->num_rows > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th></tr>";
        while ($row = $all_users->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['username'] . "</td>";
            echo "<td>" . $row['email'] . "</td>";
            echo "<td>" . $row['role'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No hay usuarios en la base de datos.</p>";
    }
} else {
    $user = $result->fetch_assoc();
    echo "<p>Usuario encontrado:</p>";
    echo "<ul>";
    echo "<li>ID: " . $user['id'] . "</li>";
    echo "<li>Username: " . $user['username'] . "</li>";
    echo "<li>Email: " . $user['email'] . "</li>";
    echo "<li>Role: " . $user['role'] . "</li>";
    echo "</ul>";
    
    // Verificar la contraseña
    if (password_verify($password, $user['password'])) {
        echo "<p style='color: green;'>La contraseña es correcta.</p>";
    } else {
        echo "<p style='color: red;'>La contraseña es incorrecta.</p>";
        echo "<p>Hash almacenado: " . $user['password'] . "</p>";
        
        // Generar un nuevo hash para la contraseña proporcionada
        $new_hash = password_hash($password, PASSWORD_DEFAULT);
        echo "<p>Nuevo hash generado para la contraseña '{$password}': " . $new_hash . "</p>";
        
        // Verificar si el nuevo hash funciona
        if (password_verify($password, $new_hash)) {
            echo "<p style='color: green;'>El nuevo hash funciona correctamente.</p>";
            
            // Ofrecer actualizar la contraseña
            echo "<p>¿Desea actualizar la contraseña en la base de datos? <a href='?update=1'>Actualizar</a></p>";
        }
    }
}

// Actualizar la contraseña si se solicita
if (isset($_GET['update']) && $_GET['update'] == 1 && isset($user)) {
    $new_hash = password_hash($password, PASSWORD_DEFAULT);
    $update_stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
    $update_stmt->bind_param("si", $new_hash, $user['id']);
    
    if ($update_stmt->execute()) {
        echo "<p style='color: green;'>Contraseña actualizada correctamente.</p>";
    } else {
        echo "<p style='color: red;'>Error al actualizar la contraseña: " . $update_stmt->error . "</p>";
    }
    
    $update_stmt->close();
}

// Cerrar la conexión
$stmt->close();
$conn->close();
?>
