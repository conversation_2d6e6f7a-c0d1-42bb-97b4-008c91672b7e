/**
 * Funciones de validación para el formulario de registro
 * Este archivo contiene todas las funciones necesarias para validar los campos
 * del formulario y mostrar mensajes de error apropiados.
 *
 * Utiliza:
 * - js/form-conditions.js: Para las condiciones de validación
 * - js/error-popups.js: Para mostrar los pop-ups de error
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializando validación de formulario');

    // Configurar validación para todos los pasos
    setupValidation();

    /**
     * Configura la validación para todos los pasos del formulario
     */
    function setupValidation() {
        // Configurar validación para cada paso
        setupStep1Validation();
        setupStep2Validation();
        setupStep3Validation();
        setupStep4Validation();
        setupStep5Validation();
    }

    /**
     * Configura la validación para el paso 1
     */
    function setupStep1Validation() {
        const step1NextBtn = document.getElementById('step1-next');
        if (!step1NextBtn) return;

        // Reemplazar el evento click existente
        step1NextBtn.onclick = function(e) {
            e.preventDefault();
            if (validateStep1()) {
                showStep(2);
            }
        };
    }

    /**
     * Valida todos los campos del paso 1
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateStep1() {
        const step1 = document.getElementById('step1');
        if (!step1) return false;

        const requiredInputs = step1.querySelectorAll('input[required], select[required]');
        let isValid = true;
        let missingFields = [];

        // Validar campos requeridos
        requiredInputs.forEach(input => {
            const label = step1.querySelector(`label[for="${input.id}"]`);
            const labelText = label ? label.textContent : input.name;

            if (!input.value.trim()) {
                input.classList.add('error');
                isValid = false;
                missingFields.push(labelText);
            } else {
                input.classList.remove('error');
            }
        });

        // Validación específica para apellidos (debe tener dos palabras)
        const apellidosInput = document.getElementById('apellidos');
        if (apellidosInput && apellidosInput.value.trim()) {
            const palabras = apellidosInput.value.trim().split(/\s+/).filter(word => word.length > 0);
            if (palabras.length < 2) {
                apellidosInput.classList.add('error');
                isValid = false;
                if (!missingFields.includes('Apellidos')) {
                    missingFields.push('Apellidos (debe ingresar dos apellidos)');
                }
            }
        }

        // Si hay campos faltantes, mostrar popup
        if (!isValid) {
            window.showErrorPopup(
                'Campos incompletos',
                'Por favor, complete todos los campos requeridos:',
                missingFields
            );
        }

        return isValid;
    }

    /**
     * Configura la validación para el paso 2
     */
    function setupStep2Validation() {
        const step2NextBtn = document.getElementById('step2-next');
        if (!step2NextBtn) return;

        // Deshabilitar el botón inicialmente
        step2NextBtn.disabled = true;
        step2NextBtn.classList.add('disabled');

        // Validar campos al cargar la página
        validateStep2();

        // Validar campos cuando cambian
        const step2Fields = document.querySelectorAll('#step2 input, #step2 select');
        step2Fields.forEach(field => {
            field.addEventListener('input', function() {
                validateStep2();
                updateStep2Button();
            });
            field.addEventListener('change', function() {
                validateStep2();
                updateStep2Button();
            });
            field.addEventListener('blur', function() {
                validateStep2();
                updateStep2Button();
            });
        });

        // Reemplazar el evento click existente
        step2NextBtn.onclick = function(e) {
            e.preventDefault();
            if (validateStep2()) {
                showStep(3);
            }
        };

        // Función para actualizar el estado del botón
        function updateStep2Button() {
            const isValid = isStep2Valid();
            step2NextBtn.disabled = !isValid;

            if (isValid) {
                step2NextBtn.classList.remove('disabled');
            } else {
                step2NextBtn.classList.add('disabled');
            }
        }

        // Función para verificar si todos los campos requeridos están completos
        function isStep2Valid() {
            const username = document.getElementById('username');
            const email = document.getElementById('email');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');
            const localFisicoRadios = document.getElementsByName('local_fisico');

            // Verificar campos de texto requeridos
            if (!username || !username.value.trim()) return false;
            if (!email || !email.value.trim()) return false;
            if (!password || !password.value.trim()) return false;
            if (!confirmPassword || !confirmPassword.value.trim()) return false;

            // Verificar formato de email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email.value.trim())) return false;

            // Verificar requisitos de contraseña
            const hasExactly8Chars = password.value.trim().length === 8;
            const hasUpperCase = /[A-Z]/.test(password.value.trim());
            const hasNumber = /\d/.test(password.value.trim());
            if (!hasExactly8Chars || !hasUpperCase || !hasNumber) return false;

            // Verificar que las contraseñas coincidan
            if (password.value.trim() !== confirmPassword.value.trim()) return false;

            // Verificar que se haya seleccionado una opción de local físico
            let localFisicoSeleccionado = false;
            localFisicoRadios.forEach(radio => {
                if (radio.checked) {
                    localFisicoSeleccionado = true;
                }
            });
            if (!localFisicoSeleccionado) return false;

            return true;
        }
    }

    /**
     * Valida todos los campos del paso 2
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateStep2() {
        const step2 = document.getElementById('step2');
        if (!step2) return false;

        let isValid = true;
        let missingFields = [];
        let errorFields = [];

        // Validar nombre de usuario
        const username = document.getElementById('username');
        if (username && !username.value.trim()) {
            username.classList.add('error');
            isValid = false;
            missingFields.push('Nombre de Usuario');
        } else if (username) {
            username.classList.remove('error');
        }

        // Validar correo electrónico
        const email = document.getElementById('email');
        if (email) {
            if (!email.value.trim()) {
                email.classList.add('error');
                isValid = false;
                missingFields.push('Correo Electrónico');
            } else {
                // Verificar formato de correo
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email.value.trim())) {
                    email.classList.add('error');
                    isValid = false;
                    errorFields.push('Correo Electrónico (formato inválido)');
                } else {
                    email.classList.remove('error');
                }
            }
        }

        // Validar correo de respaldo (opcional)
        const backupEmail = document.getElementById('backup_email');
        if (backupEmail && backupEmail.value.trim()) {
            // Verificar formato de correo
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(backupEmail.value.trim())) {
                backupEmail.classList.add('error');
                isValid = false;
                errorFields.push('Correo de Respaldo (formato inválido)');
            } else {
                backupEmail.classList.remove('error');
            }
        } else if (backupEmail) {
            // Es opcional, así que quitamos cualquier error
            backupEmail.classList.remove('error');
        }

        // Validar contraseña
        const password = document.getElementById('password');
        if (password) {
            if (!password.value.trim()) {
                password.classList.add('error');
                isValid = false;
                missingFields.push('Contraseña');
            } else {
                // Verificar requisitos de la contraseña
                const hasExactly8Chars = password.value.trim().length === 8;
                const hasUpperCase = /[A-Z]/.test(password.value.trim());
                const hasNumber = /\d/.test(password.value.trim());

                if (!hasExactly8Chars || !hasUpperCase || !hasNumber) {
                    password.classList.add('error');
                    isValid = false;
                    errorFields.push('Contraseña (debe tener 8 caracteres, con una mayúscula y un número por lo menos)');

                    // Mostrar popup con el mensaje específico
                    window.showErrorPopup(
                        'Formato de contraseña incorrecto',
                        'La contraseña ha de tener 8 caracteres, con una mayúscula y un número por lo menos.',
                        []
                    );
                } else {
                    password.classList.remove('error');
                }
            }
        }

        // Validar confirmación de contraseña
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword) {
            if (!confirmPassword.value.trim()) {
                confirmPassword.classList.add('error');
                isValid = false;
                missingFields.push('Confirmar Contraseña');
            } else if (password && password.value.trim()) {
                // Verificar que las contraseñas coincidan
                if (confirmPassword.value.trim() !== password.value.trim()) {
                    password.classList.add('error');
                    confirmPassword.classList.add('error');
                    isValid = false;
                    errorFields.push('Las contraseñas no coinciden');

                    // Mostrar popup de error
                    window.showErrorPopup(
                        'Las contraseñas no coinciden',
                        'Las contraseñas no coinciden. Por favor, verifique ambos campos.',
                        []
                    );
                } else {
                    confirmPassword.classList.remove('error');
                }
            } else {
                confirmPassword.classList.remove('error');
            }
        }

        // Verificar que se haya seleccionado una opción de local físico
        const localFisicoRadios = document.getElementsByName('local_fisico');
        let localFisicoSeleccionado = false;

        localFisicoRadios.forEach(radio => {
            if (radio.checked) {
                localFisicoSeleccionado = true;
            }
        });

        if (!localFisicoSeleccionado) {
            isValid = false;
            missingFields.push('¿Tiene Local Físico?');

            // Resaltar visualmente el contenedor
            const localFisicoContainer = document.querySelector('.local-fisico-container');
            if (localFisicoContainer) {
                localFisicoContainer.classList.add('error-container');
            }
        } else {
            // Quitar resaltado si está seleccionado
            const localFisicoContainer = document.querySelector('.local-fisico-container');
            if (localFisicoContainer) {
                localFisicoContainer.classList.remove('error-container');
            }
        }

        // Si hay campos faltantes o con errores, mostrar popup
        if (!isValid) {
            let allErrors = [...missingFields];

            if (errorFields.length > 0) {
                allErrors = [...allErrors, ...errorFields];
            }

            window.showErrorPopup(
                'Campos incompletos o incorrectos',
                'Por favor, corrija los siguientes campos:',
                allErrors
            );
        }

        return isValid;
    }

    /**
     * Configura la validación para el paso 3
     */
    function setupStep3Validation() {
        const step3NextBtn = document.getElementById('step3-next');
        if (!step3NextBtn) return;

        // Reemplazar el evento click existente
        step3NextBtn.onclick = function(e) {
            e.preventDefault();
            if (validateStep3()) {
                // Determinar qué tipo de negocio se seleccionó
                const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
                let tipoNegocioSeleccionado = '';

                tipoNegocioRadios.forEach(radio => {
                    if (radio.checked) {
                        tipoNegocioSeleccionado = radio.value;
                    }
                });

                // Mostrar el paso 4 correspondiente según el tipo de negocio
                if (tipoNegocioSeleccionado === 'venta') {
                    showCustomStep('step4-productos');
                } else if (tipoNegocioSeleccionado === 'servicios' || tipoNegocioSeleccionado === 'arriendo') {
                    showCustomStep('step4-servicios');
                }
            }
        };
    }

    /**
     * Valida todos los campos del paso 3
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateStep3() {
        const step3 = document.getElementById('step3');
        if (!step3) return false;

        let isValid = true;
        let missingFields = [];
        let errorFields = [];

        // Validar nombre del negocio
        const nombreNegocio = document.getElementById('nombre_negocio');
        if (nombreNegocio && !nombreNegocio.value.trim()) {
            nombreNegocio.classList.add('error');
            isValid = false;
            missingFields.push('Nombre del Negocio');
        } else if (nombreNegocio) {
            nombreNegocio.classList.remove('error');
        }

        // Validar teléfono del negocio
        const telefonoNegocio = document.getElementById('telefono_negocio');
        if (telefonoNegocio && telefonoNegocio.value.trim()) {
            // Verificar que solo contenga números
            if (!/^\d+$/.test(telefonoNegocio.value.trim())) {
                telefonoNegocio.classList.add('error');
                isValid = false;
                errorFields.push('Teléfono del negocio (solo números)');
            } else if (telefonoNegocio.value.trim().length > 9) {
                telefonoNegocio.classList.add('error');
                isValid = false;
                errorFields.push('Teléfono del negocio (máximo 9 dígitos)');
            } else {
                telefonoNegocio.classList.remove('error');
            }
        }

        // Validar WhatsApp del negocio
        const whatsappNegocio = document.getElementById('whatsapp_negocio');
        if (whatsappNegocio && whatsappNegocio.value.trim()) {
            // Verificar que solo contenga números
            if (!/^\d+$/.test(whatsappNegocio.value.trim())) {
                whatsappNegocio.classList.add('error');
                isValid = false;
                errorFields.push('WhatsApp del negocio (solo números)');
            } else if (whatsappNegocio.value.trim().length !== 8) {
                whatsappNegocio.classList.add('error');
                isValid = false;
                errorFields.push('WhatsApp del negocio (debe tener 8 dígitos)');
            } else {
                whatsappNegocio.classList.remove('error');
            }
        }

        // Validar descripción del negocio
        const descripcionNegocio = document.getElementById('descripcion_negocio');
        if (descripcionNegocio && !descripcionNegocio.value.trim()) {
            descripcionNegocio.classList.add('error');
            isValid = false;
            missingFields.push('Descripción del Negocio');
        } else if (descripcionNegocio) {
            // Contar palabras
            const wordCount = descripcionNegocio.value.trim().split(/\s+/).filter(word => word.length > 0).length;

            // Verificar que no exceda las 200 palabras
            if (wordCount > 200) {
                descripcionNegocio.classList.add('error');
                isValid = false;
                errorFields.push('Descripción del Negocio (máximo 200 palabras)');
            } else {
                descripcionNegocio.classList.remove('error');
            }
        }

        // Validar tipo de negocio
        const tipoNegocioRadios = document.getElementsByName('tipo_negocio');
        let tipoNegocioSeleccionado = false;

        tipoNegocioRadios.forEach(radio => {
            if (radio.checked) {
                tipoNegocioSeleccionado = true;
            }
        });

        if (!tipoNegocioSeleccionado) {
            isValid = false;
            missingFields.push('Tipo de Negocio');

            // Resaltar visualmente el contenedor de tipo de negocio
            const businessTypeContainer = document.querySelector('.business-type-container');
            if (businessTypeContainer) {
                businessTypeContainer.style.border = '1px solid #ff5252';
            }
        } else {
            // Quitar resaltado si está seleccionado
            const businessTypeContainer = document.querySelector('.business-type-container');
            if (businessTypeContainer) {
                businessTypeContainer.style.border = '1px solid #e0e0e0';
            }
        }

        // Si hay campos faltantes o con errores, mostrar popup
        if (!isValid) {
            let allErrors = [...missingFields];

            if (errorFields.length > 0) {
                allErrors = [...allErrors, ...errorFields];
            }

            window.showErrorPopup(
                'Campos incompletos o con errores',
                'Por favor, corrija los siguientes campos:',
                allErrors
            );
        }

        return isValid;
    }

    /**
     * Configura la validación para el paso 4
     */
    function setupStep4Validation() {
        // Validación para el paso 4A (productos)
        const submitFormBtn = document.getElementById('submit-form');
        if (submitFormBtn) {
            submitFormBtn.onclick = function(e) {
                e.preventDefault();
                if (validateStep4('productos')) {
                    const planSeleccionado = document.querySelector('input[name="subscription"]:checked');
                    if (planSeleccionado.value === 'gratuita') {
                        window.showWelcomePopup();
                    } else {
                        showCustomStep('step5-pago');
                    }
                }
            };
        }

        // Validación para el paso 4B (servicios)
        const submitFormServiciosBtn = document.getElementById('submit-form-servicios');
        if (submitFormServiciosBtn) {
            submitFormServiciosBtn.onclick = function(e) {
                e.preventDefault();
                if (validateStep4('servicios')) {
                    const planSeleccionado = document.querySelector('input[name="subscription"]:checked');
                    if (planSeleccionado.value === 'gratuita') {
                        window.showWelcomePopup();
                    } else {
                        showCustomStep('step5-pago');
                    }
                }
            };
        }
    }

    /**
     * Valida los campos del paso 4
     * @param {string} tipo - Tipo de negocio ('productos' o 'servicios')
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateStep4(tipo) {
        // Verificar si se seleccionó un plan
        const planSeleccionado = document.querySelector('input[name="subscription"]:checked');

        if (!planSeleccionado) {
            window.showErrorPopup(
                'Plan no seleccionado',
                'Por favor, seleccione un plan de suscripción antes de continuar.'
            );
            return false;
        }

        return true;
    }

    /**
     * Configura la validación para el paso 5
     */
    function setupStep5Validation() {
        const finalizarRegistroBtn = document.getElementById('finalizar-registro');
        if (!finalizarRegistroBtn) return;

        finalizarRegistroBtn.onclick = function(e) {
            e.preventDefault();
            if (validateStep5()) {
                // Si todo está correcto, enviar el formulario
                document.getElementById('registerForm').submit();
            }
        };
    }

    /**
     * Valida todos los campos del paso 5
     * @returns {boolean} - True si todos los campos son válidos
     */
    function validateStep5() {
        const facturaRadio = document.getElementById('factura');
        const boletaRadio = document.getElementById('boleta');
        let isValid = true;
        let missingFields = [];

        // Verificar si se ha seleccionado una opción
        if (!facturaRadio || !boletaRadio || (!facturaRadio.checked && !boletaRadio.checked)) {
            isValid = false;
            missingFields.push('Seleccione si requiere factura o boleta');
        }

        // Si seleccionó factura, verificar que todos los campos estén llenos
        if (facturaRadio && facturaRadio.checked) {
            const facturaFields = [
                { id: 'empresa', label: 'Nombre de la Empresa' },
                { id: 'rut_empresa', label: 'RUT de la Empresa' },
                { id: 'direccion_empresa', label: 'Dirección de la Empresa' },
                { id: 'giro_empresa', label: 'Giro de la Empresa' },
                { id: 'telefono_empresa', label: 'Teléfono de la Empresa' },
                { id: 'correo_empresa', label: 'Correo de la Empresa' }
            ];

            facturaFields.forEach(field => {
                const input = document.getElementById(field.id);
                if (input && !input.value.trim()) {
                    input.classList.add('error');
                    isValid = false;
                    missingFields.push(field.label);
                } else if (input) {
                    input.classList.remove('error');
                }
            });
        }

        // Si hay campos faltantes, mostrar popup
        if (!isValid) {
            window.showErrorPopup(
                'Campos incompletos',
                'Por favor, complete todos los campos requeridos:',
                missingFields
            );
        }

        return isValid;
    }