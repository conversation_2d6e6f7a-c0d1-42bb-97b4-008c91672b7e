<?php
// Establecer cabeceras para evitar problemas de CORS y caché
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');

// Respuesta simple para probar
$response = [
    'success' => true,
    'message' => 'Prueba exitosa',
    'data' => [
        'timestamp' => time(),
        'received' => $_POST
    ]
];

// Devolver la respuesta
echo json_encode($response);
?>
